import os
import subprocess

def check_line_ending(file_path):
    with open(file_path, 'rb') as f:
        content = f.read()
        if b'\r\n' in content:
            return 'CRLF'
        elif b'\n' in content:
            return 'LF'
        else:
            return 'Unknown'


if __name__ == '__main__':

    # 需要查询的文件后缀名
    file_ext_list = {'.java', '.sh', '.yaml', '.py', '.xml', '.proto'}
    file_counts = {}
    file_paths = []
    # 屏蔽文件夹
    exclude_folders = {
        os.path.abspath(os.path.join('.', 'tools')),
        os.path.abspath(os.path.join('.', 'pyenv')),
        os.path.abspath(os.path.join('.', 'excel', 'client')),
        os.path.abspath(os.path.join('.', 'excel', 'Client')),
        os.path.abspath(os.path.join('.', 'excel', 'TranslationTool')),
        os.path.abspath(os.path.join('.', 'clientTools'))
    }
    for root, dirs, files in os.walk('.'):
        # 屏蔽的查询路径路径
        if any(os.path.abspath(root).startswith(exclude_folder) for exclude_folder in exclude_folders):
            continue
        for file in files:
            file_path = os.path.join(root, file)
            file_type = os.path.splitext(file)[1]
            if file_type in file_ext_list:
                line_ending = check_line_ending(file_path)
                if line_ending == 'CRLF':
                    if file_type in file_counts:
                        file_counts[file_type] += 1
                    else:
                        file_counts[file_type] = 1
                    print('File:', file_path)
                    print('Line ending:', line_ending)
                    file_paths.append(file_path)
    print('File counts:')
    for ext, count in file_counts.items():
        print(f'{ext}: {count}')
    convert_line_endings = input('\nDo you want to convert line endings to LF? (y/n): ').lower() == 'n'
    if not convert_line_endings:
        # 使用 dos2unix 命令转换文件
        for file_path in file_paths:
            subprocess.run(['dos2unix', file_path])
        print('\nLine endings converted to LF.')