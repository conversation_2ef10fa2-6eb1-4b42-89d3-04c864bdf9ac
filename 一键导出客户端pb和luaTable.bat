@echo off
chcp 65001

cd .\clientTools\NewNetTools
echo ============================【开始导出数据请稍后。。。】============================
..\..\tools\python-3.8.2\python.exe ExportPB.py 
echo ============================【导出已完成！可以打开LogNet.txt查看运行日志】============================
pause


::cd ..\..\excel\
::..\tools\python-3.8.2\python.exe TableHelperTemplate.py
::echo ============================success end in running 一键更新生成Table的lua对象.bat============================
pause