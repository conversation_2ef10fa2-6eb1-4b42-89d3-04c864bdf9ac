#!/bin/sh

CURRENTDIR=$(dirname $(readlink -f "$0")) || exit 1

allres=(`find excel/server/data/`)

resultpath=$1

mkdir -p $resultpath

ouputfile=$resultpath/changeResList.html

echo "changeres authers:<br />" > $ouputfile

echo '<table border="0">' >> $ouputfile


echo "<tr>" >> $ouputfile
echo "<td>resource</td>"  >> $ouputfile
echo "<td>auther</td>"  >> $ouputfile
echo "<td>isInPkg</td>" >> $ouputfile
echo "</tr>" >> $ouputfile

mkdir -p report/result/txt

resinfile=`grep -E 'data/' report/result/pkgname/pkgfilelist.html | grep -E '\.txt$' | wc -l`

for var in ${allres[@]}
do
    if [ -d "$var" ]; then
	    echo "skip dir $var"
	    continue
	fi

    localmd5=`grep -E $var$ report/patch.md5sum | awk -F" " '{print $1}' `
	onlinefile=${var//excel\/server/online}
	onlinemd5=`grep -E $onlinefile$ report/online.md5sum | awk -F" " '{print $1}'`

	if [ ! -n "$localmd5" ]; then
        echo "skip error file $var"
	    continue
	fi

	if [ "$localmd5" = "$onlinemd5" ]; then
	    echo "skip no change file $var"
	    continue
	fi

	if [ ! -f "$onlinefile" ]; then
	    echo "skip error online file $onlinefile local $var"
	    continue
	fi

	lastchangetime=0
	if [ -n "$onlinefile" ]; then
	    lastchangetime=`stat -c %Y $onlinefile`
	fi

	auther=""
	oldifs="$IFS"
    IFS=$'\n'

	logarr=(`git log --pretty=format:"%an" $var | head `)
    if [ $? -ne 0 ]; then
	     echo "svn get log fail set auther unknown"
         auther="unknown"
    else
        for log in ${logarr[@]}
	    do
	        tmpauther=`echo $log`

            if [ ! -n "$auther" ]; then
                auther=$tmpauther
            else
                auther=$auther,$tmpauther
            fi
	    done
	fi

    pkgfilename=${var//excel\/server\/data\//}
	isinpkg=`grep "$pkgfilename" report/result/pkgname/pkgfilelist.html`

	if [ -n "$isinpkg" ]; then
	    if [ $resinfile -lt 2000 ];then
	        extname="${var##*.}"
		    if [ "$extname" == "txt" ]; then
		        echo "generate diff for local $var online $onlinefile $resinfile"
				##diff -u $onlinefile $var | sed 's/$/&<br>/g' > report/result/txt/${var//\//_}".html"
				python tools/cmppatches/cmpResourceTxt.py $onlinefile $var report/result/txt/ ${var//\//_}".html"
		    fi
		else
		    echo "toomuch txt in pkg skip diff its too slow"
		fi
	    isinpkg="T"
	else
	    isinpkg=""
	fi

    IFS="$oldifs"

	echo "<tr>" >> $ouputfile

	echo "<td>$var</td>"  >> $ouputfile
	realauther=`echo $auther| sed 's/ //g' | xargs -n1 -d "," | sort -u | xargs`
	echo "<td>$realauther</td>"  >> $ouputfile
	echo "<td>$isinpkg</td>" >> $ouputfile

	echo "</tr>" >> $ouputfile

done

configcmpPath=$CURRENTDIR/configCmp
mkdir -p $configcmpPath/configdata/info
cp -f $ouputfile $configcmpPath/configdata/info
git remote show origin -n | grep URL > $configcmpPath/configdata/info/gitinfo4changehtml


