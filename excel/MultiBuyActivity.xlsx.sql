-- 多买多送活动配置表设计
-- 用于Excel配置表的SQL结构参考

-- 折扣池配置表
CREATE TABLE multi_buy_discount_pool (
    discount_id INT PRIMARY KEY COMMENT '折扣ID',
    discount_value INT NOT NULL COMMENT '折扣数值（80表示8折）',
    weight INT NOT NULL COMMENT '权重',
    activity_id INT NOT NULL COMMENT '关联活动ID',
    description VARCHAR(255) COMMENT '折扣描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_activity_id (activity_id),
    INDEX idx_active (is_active)
) COMMENT='多买多送折扣池配置表';

-- 活动配置表
CREATE TABLE multi_buy_activity_config (
    activity_id INT PRIMARY KEY COMMENT '活动ID',
    daily_buy_threshold INT NOT NULL COMMENT '每日购买阈值',
    commodity_ids TEXT COMMENT '参与商品ID列表（JSON格式）',
    discount_pool_ids TEXT COMMENT '折扣池ID列表（JSON格式）',
    default_discount INT DEFAULT 95 COMMENT '默认折扣（池子为空时使用）',
    enable_double_discount BOOLEAN DEFAULT TRUE COMMENT '是否启用双倍折扣功能',
    activity_name VARCHAR(255) COMMENT '活动名称',
    activity_desc TEXT COMMENT '活动描述',
    start_time BIGINT COMMENT '活动开始时间（秒级时间戳）',
    end_time BIGINT COMMENT '活动结束时间（秒级时间戳）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_time_range (start_time, end_time)
) COMMENT='多买多送活动配置表';

-- 示例数据插入
INSERT INTO multi_buy_discount_pool (discount_id, discount_value, weight, activity_id, description) VALUES
(1, 70, 10, 1001, '7折超级优惠'),
(2, 75, 15, 1001, '7.5折优惠'),
(3, 80, 25, 1001, '8折优惠'),
(4, 85, 30, 1001, '8.5折优惠'),
(5, 90, 20, 1001, '9折优惠');

INSERT INTO multi_buy_activity_config (activity_id, daily_buy_threshold, commodity_ids, discount_pool_ids, 
                                     activity_name, activity_desc, start_time, end_time) VALUES
(1001, 3, '[1001,1002,1003,1004,1005]', '[1,2,3,4,5]', 
 '新年多买多送', '每日购买3次即可解锁明日双倍折上折特权', 
 UNIX_TIMESTAMP('2025-01-20 00:00:00'), UNIX_TIMESTAMP('2025-02-20 23:59:59'));

-- 玩家活动数据统计表（用于运营分析）
CREATE TABLE player_multi_buy_activity_stats (
    uid BIGINT NOT NULL COMMENT '玩家UID',
    activity_id INT NOT NULL COMMENT '活动ID',
    total_buy_count INT DEFAULT 0 COMMENT '总购买次数',
    total_saved_money BIGINT DEFAULT 0 COMMENT '总节省金额',
    max_daily_buy_count INT DEFAULT 0 COMMENT '单日最高购买次数',
    double_discount_unlock_days INT DEFAULT 0 COMMENT '双倍折扣解锁天数',
    first_buy_time BIGINT COMMENT '首次购买时间',
    last_buy_time BIGINT COMMENT '最后购买时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (uid, activity_id),
    INDEX idx_activity_id (activity_id),
    INDEX idx_total_buy_count (total_buy_count),
    INDEX idx_total_saved_money (total_saved_money)
) COMMENT='玩家多买多送活动统计表';

-- 折扣使用记录表（用于运营分析）
CREATE TABLE multi_buy_discount_usage_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    uid BIGINT NOT NULL COMMENT '玩家UID',
    activity_id INT NOT NULL COMMENT '活动ID',
    commodity_id INT NOT NULL COMMENT '商品ID',
    discount_id INT NOT NULL COMMENT '使用的折扣ID',
    original_price INT NOT NULL COMMENT '原价',
    final_price INT NOT NULL COMMENT '折后价',
    saved_amount INT NOT NULL COMMENT '节省金额',
    has_double_discount BOOLEAN DEFAULT FALSE COMMENT '是否使用了双倍折扣',
    buy_time BIGINT NOT NULL COMMENT '购买时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_uid_activity (uid, activity_id),
    INDEX idx_activity_time (activity_id, buy_time),
    INDEX idx_discount_id (discount_id),
    INDEX idx_commodity_id (commodity_id)
) COMMENT='多买多送折扣使用记录表';

-- 活动每日统计表（用于运营监控）
CREATE TABLE multi_buy_activity_daily_stats (
    activity_id INT NOT NULL COMMENT '活动ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    participant_count INT DEFAULT 0 COMMENT '参与人数',
    total_purchases INT DEFAULT 0 COMMENT '总购买次数',
    total_discount_amount BIGINT DEFAULT 0 COMMENT '总折扣金额',
    double_discount_unlocks INT DEFAULT 0 COMMENT '双倍折扣解锁次数',
    avg_discount_rate DECIMAL(5,2) DEFAULT 0 COMMENT '平均折扣率',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (activity_id, stat_date),
    INDEX idx_stat_date (stat_date)
) COMMENT='多买多送活动每日统计表';

-- 创建视图：活动效果分析
CREATE VIEW v_multi_buy_activity_analysis AS
SELECT 
    a.activity_id,
    a.activity_name,
    COUNT(DISTINCT s.uid) as total_participants,
    SUM(s.total_buy_count) as total_purchases,
    SUM(s.total_saved_money) as total_saved_money,
    AVG(s.total_buy_count) as avg_purchases_per_user,
    AVG(s.total_saved_money) as avg_saved_per_user,
    SUM(s.double_discount_unlock_days) as total_double_discount_days
FROM multi_buy_activity_config a
LEFT JOIN player_multi_buy_activity_stats s ON a.activity_id = s.activity_id
WHERE a.is_active = TRUE
GROUP BY a.activity_id, a.activity_name;

-- 创建索引优化查询性能
CREATE INDEX idx_player_stats_composite ON player_multi_buy_activity_stats(activity_id, total_buy_count DESC, total_saved_money DESC);
CREATE INDEX idx_usage_log_composite ON multi_buy_discount_usage_log(activity_id, buy_time DESC, has_double_discount);
CREATE INDEX idx_daily_stats_composite ON multi_buy_activity_daily_stats(activity_id, stat_date DESC, participant_count DESC);
