syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";

// 折扣池配置
message MultiBuyDiscountPoolConf {
  option (resKey) = "discountId";
  optional int32 discountId = 1;        // 折扣ID
  optional int32 discountValue = 2;     // 折扣数值（如80表示8折）
  optional int32 weight = 3;            // 出现权重
  optional int32 activityId = 4;        // 关联活动ID
  optional string description = 5;       // 折扣描述
}

message table_MultiBuyDiscountPool {
  repeated MultiBuyDiscountPoolConf rows = 1;
}

// 活动配置
message MultiBuyActivityConf {
  option (resKey) = "activityId";
  optional int32 activityId = 1;        // 活动ID
  optional int32 dailyBuyThreshold = 2; // 每日购买阈值X
  repeated int32 commodityIds = 3;      // 参与活动的商品ID列表
  repeated int32 discountPoolIds = 4;   // 折扣池ID列表
  optional int32 defaultDiscount = 5;   // 默认折扣（池子为空时使用）
  optional bool enableDoubleDiscount = 6; // 是否启用双倍折扣功能
  optional string activityName = 7;     // 活动名称
  optional string activityDesc = 8;     // 活动描述
}

message table_MultiBuyActivity {
  repeated MultiBuyActivityConf rows = 1;
}

// 活动商品信息（用于客户端展示）
message ActivityCommodityInfo {
  optional int32 commodityId = 1;       // 商品ID
  optional int32 originalPrice = 2;     // 原价
  optional int32 currentDiscount = 3;   // 当前可用折扣
  optional int32 finalPrice = 4;        // 折后价格
  optional bool isDiscountUsed = 5;     // 今日是否已使用折扣
  optional int32 discountId = 6;        // 折扣ID
}

// 折扣计算结果
message MultiBuyDiscountResult {
  optional int32 baseDiscount = 1;      // 基础折扣
  optional int32 finalDiscount = 2;     // 最终折扣（考虑双倍折扣）
  optional int32 discountId = 3;        // 使用的折扣ID
  optional bool hasDoubleDiscount = 4;  // 是否应用了双倍折扣
  optional int32 savedAmount = 5;       // 节省金额
}
