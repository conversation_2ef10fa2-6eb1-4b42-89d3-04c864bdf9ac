{"ArenaAIConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\A_ArenaAI配置表.xlsx", "sheet": "Arena不同强度参数配置表"}, "ArenaBuffData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\B_ArenaBuff表.xlsx", "sheet": "Buff表"}, "ArenaBuffAbnormalData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\B_ArenaBuff表.xlsx", "sheet": "异常状态表"}, "ArenaBuffAbnormalDisplayData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\B_ArenaBuff表.xlsx", "sheet": "异常状态显示"}, "": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_自定义按键表.xlsx", "sheet": "各玩法参考"}, "ArenaBuffData_Card_Function": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\B_ArenaBuff表_卡牌_功能卡.xlsx", "sheet": "Buff表"}, "ArenaBuffData_Card_Attr": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\B_ArenaBuff表_卡牌_属性卡.xlsx", "sheet": "Buff表"}, "ArenaBuffData_Prop": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\B_ArenaBuff表_场景_场景道具.xlsx", "sheet": "Buff表"}, "ArenaBuffData_anqila": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\B_ArenaBuff表_安琪拉.xlsx", "sheet": "Buff表"}, "ArenaBuffData_test": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\B_ArenaBuff表_测试_参考案例.xlsx", "sheet": "Buff表"}, "ArenaSpecialMatchRoleSet": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\D_Arena_单局定制表.xlsx", "sheet": "英雄分配表"}, "ArenaSpecialMatchCardSet": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\D_Arena_单局定制表.xlsx", "sheet": "卡池表"}, "ArenaSpecialMatchRoundMapSet": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\D_Arena_单局定制表.xlsx", "sheet": "地图轮序表"}, "ArenaSpecialMatchRoundDamageConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\D_Arena_单局定制表.xlsx", "sheet": "伤害修正表"}, "ArenaMagicFieldSet": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\F_Arena法术场表.xlsx", "sheet": "法术场表"}, "ArenaRoundConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\H_Arena回合配置表.xlsx", "sheet": "回合配置"}, "ArenaHeroDeraultCardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\H_Arena英雄默认卡组表.xlsx", "sheet": "英雄默认卡组表"}, "ArenaKillUI": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\J_Arena局内付费特性配置表.xlsx", "sheet": "击杀播报表"}, "ArenaInGamePortraitFrame": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\J_Arena局内付费特性配置表.xlsx", "sheet": "局内头像框表"}, "ArenaSkillConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\J_Arena技能公共表.xlsx", "sheet": "技能配置表"}, "ArenaIndicatorData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\J_Arena技能指示器.xlsx", "sheet": "技能指示器表"}, "ArenaHeroAttrData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\J_Arena英雄属性表.xlsx", "sheet": "英雄升级属性"}, "ArenaHeroLevelUpData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\J_Arena英雄属性表.xlsx", "sheet": "英雄升级表"}, "ArenaHeroAttrNameData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\J_Arena英雄属性表.xlsx", "sheet": "属性枚举表"}, "ArenaHeroUnlockData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\J_Arena英雄解锁表.xlsx", "sheet": "英雄解锁"}, "ArenaAvatarSkillConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\J_Arena角色技能配置表.xlsx", "sheet": "角色技能配置表"}, "ArenaCardPackUpgradeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡包配置.xlsx", "sheet": "宝箱升级"}, "ArenaCardPackRuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡包配置.xlsx", "sheet": "宝箱规则"}, "ArenaCardPackGroupData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡包配置.xlsx", "sheet": "物品包组"}, "ArenaCardPackData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡包配置.xlsx", "sheet": "物品包内容"}, "ArenaCardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡牌配置表.xlsx", "sheet": "卡牌信息"}, "ArenaCardTagData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡牌配置表.xlsx", "sheet": "卡牌效果标签"}, "ArenaCardCatgoryData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡牌配置表.xlsx", "sheet": "卡牌大类"}, "ArenaCardQualityData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡牌配置表.xlsx", "sheet": "卡牌品质"}, "ArenaCardRandomGroupData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡牌随机表.xlsx", "sheet": "随机包组合表"}, "ArenaCardRandomData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡牌随机表.xlsx", "sheet": "随机包表"}, "ArenaCardRandomWeight": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡牌随机表.xlsx", "sheet": "颜色权重调整表"}, "ArenaCardFilterData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡牌随机表.xlsx", "sheet": "选秀英雄过滤表"}, "ArenaCardShowingGroupData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena卡牌随机表.xlsx", "sheet": "选秀随机组合表"}, "ArenaCardFilterDataV3": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena选秀表_3v3.xlsx", "sheet": "选秀英雄过滤表"}, "ArenaCardShowingGroupDataV3": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\K_Arena选秀表_3v3.xlsx", "sheet": "选秀随机组合表"}, "ArenaHitData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\M_Arena命中参数表.xlsx", "sheet": "命中参数"}, "ArenaTargetFilterData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\M_Arena目标筛选表.xlsx", "sheet": "目标筛选表"}, "ArenaTargetFilterRuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\M_Arena目标筛选规则表.xlsx", "sheet": "目标筛选规则表"}, "ArenaGlobalConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\Q_Arena全局配置表.xlsx", "sheet": "全局配置"}, "ArenaDamageEffectData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\S_Arena伤害治疗公式表.xlsx", "sheet": "伤害治疗公式表_卡牌"}, "ArenaDamageEffectData_Hero": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\S_Arena伤害治疗公式表.xlsx", "sheet": "伤害治疗公式表_英雄"}, "ArenaLevelFactor": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\S_Arena伤害治疗公式表.xlsx", "sheet": "等级系数表"}, "ArenaHitEffectData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\S_Arena受击表现表.xlsx", "sheet": "受击表现表"}, "ArenaHitEffectScaleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\S_Arena受击跳字特效配置表.xlsx", "sheet": "受击跳字特效配置表"}, "ArenaTestCardGroup": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\S_Arena数值测试表.xlsx", "sheet": "卡牌组合表"}, "ArenaTestDmgGroup": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\S_Arena数值测试表.xlsx", "sheet": "伤害序列表"}, "ArenaWeapon": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\W_Arena武器表.xlsx", "sheet": "武器配置"}, "ArenaHeroSystemData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\X_Arena英雄系统配置表.xlsx", "sheet": "英雄信息"}, "ArenaHeroOccupyData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\X_Arena英雄系统配置表.xlsx", "sheet": "英雄职业"}, "ArenaHeroSkillData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\X_Arena英雄系统配置表.xlsx", "sheet": "英雄技能"}, "ArenaHeroSkillTypeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\X_Arena英雄系统配置表.xlsx", "sheet": "英雄攻击类型"}, "ArenaHeroAvatarData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\X_Arena英雄系统配置表.xlsx", "sheet": "英雄Avatar表"}, "ArenaVoiceTrigger": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\Y_Arena语音播报.xlsx", "sheet": "语音触发"}, "ArenaVoice": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\Y_Arena语音播报.xlsx", "sheet": "语音库"}, "ArenaBulletSet": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Arena\\Z_Arena子弹表.xlsx", "sheet": "Sheet1"}, "AIGCNPCSingConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AIGCNPC配置.xlsx", "sheet": "唱歌"}, "AIGCNPCDanceConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AIGCNPC配置.xlsx", "sheet": "跳舞"}, "AIGCNPCErrMsg": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AIGCNPC配置.xlsx", "sheet": "错误码"}, "AIGCQuickChatData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AIGCNPC配置.xlsx", "sheet": "快捷聊天"}, "AIGCCommonConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AIGC配置.xlsx", "sheet": "Ugc地图常量"}, "AIInfoRecordSkinData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI个人信息表.xlsx", "sheet": "个人战绩及时装"}, "AIInfoPlatformData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI个人信息表.xlsx", "sheet": "平台侧Avatar"}, "AIRandomCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI个人信息表.xlsx", "sheet": "AI随机表"}, "AIInfoDifficultyData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI个人信息表.xlsx", "sheet": "AI强度表"}, "AIBehaveRule": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI个人信息表.xlsx", "sheet": "AI行为规则表"}, "AILevelSpecialCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI个人信息表.xlsx", "sheet": "AI等级特殊配置"}, "AIScriptConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI剧本配置表.xlsx", "sheet": "AI剧本模型"}, "AIParamConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI剧本配置表.xlsx", "sheet": "AI参数配置"}, "AIInfoNameImageData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI名称头像表.xlsx", "sheet": "名称和头像"}, "AIDataControlData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI数据控制表.xlsx", "sheet": "AI数据控制"}, "AIFashionSeason": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI服装表.xlsx", "sheet": "AI时装潮流值赛季表"}, "AIFashionRandom": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI服装表.xlsx", "sheet": "AI时装潮流值随机表"}, "AIModes": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AI模式表.xlsx", "sheet": "AI模式表"}, "AMSItemCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AMS道具.xlsx", "sheet": "AMS活动配置"}, "AMSPackageIdCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\A_AMS道具.xlsx", "sheet": "礼包配置"}, "BSBuffData_Prop": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\BS\\B_BSBuff表_场景道具.xlsx", "sheet": "Buff表"}, "ArenaCardBSKillLootData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\BS\\K_BS卡牌配置.xlsx", "sheet": "击杀掉落"}, "BSGlobalConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\BS\\Q_BS全局配置表.xlsx", "sheet": "全局配置"}, "BSHeroList": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\BS\\Y_BS英雄列表.xlsx", "sheet": "英雄信息"}, "BSHeroAttrData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\BS\\Y_BS英雄属性表.xlsx", "sheet": "英雄升级属性"}, "BSPotionConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\BS\\Y_BS药水配置表.xlsx", "sheet": "全局配置"}, "BpmGamePropsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_BPM道具.xlsx", "sheet": "道具配置"}, "BattlePassSeasonConf_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_BP通行证_nr3e_谁是狼人.xlsx", "sheet": "赛季配置"}, "BattlePassLevelRewardConf_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_BP通行证_nr3e_谁是狼人.xlsx", "sheet": "等级奖励"}, "TaskGroupData_bp_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_BP通行证_nr3e_谁是狼人.xlsx", "sheet": "任务组"}, "TaskConfData_bp_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_BP通行证_nr3e_谁是狼人.xlsx", "sheet": "任务配置"}, "BrGamePropsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_BR道具.xlsx", "sheet": "道具"}, "BrPropRegionData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_BR道具.xlsx", "sheet": "地图区域"}, "BrPropAirDropData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_BR道具.xlsx", "sheet": "空投"}, "OMDSupply": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_OMD补给.xlsx", "sheet": "主表"}, "ResPreparations": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_备战.xlsx", "sheet": "备战"}, "ResPreparationsWidgetConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_备战.xlsx", "sheet": "UI控件名"}, "PreparationsMapSelectConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_备战.xlsx", "sheet": "选地图"}, "ClientLocalKVConfForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_本地KV表.xlsx", "sheet": "客户端KV表-LetsGo"}, "UserLabelConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_标签定义.xlsx", "sheet": "定义"}, "VersionCompAdjustmentConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容修正.xlsx", "sheet": "Sheet1"}, "VersionCompFeatureConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容多玩法.xlsx", "sheet": "多玩法"}, "VersionCompCtrlConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "版本号控制"}, "VersionCompBattleConfData_lobby": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "大厅兼容组"}, "VersionCompBattleConfData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "UGC大厅兼容组"}, "VersionCompBattleConfData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "主玩法兼容组"}, "VersionCompBattleConfData_fps": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "FPS兼容组"}, "VersionCompBattleConfData_acm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "ACM兼容组"}, "VersionCompBattleConfData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "NR3E兼容组"}, "VersionCompBattleConfData_tyc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "TYC兼容组"}, "VersionCompBattleConfData_js": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "JS兼容组"}, "VersionCompBattleConfData_xiaowo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "小窝兼容组"}, "VersionCompBattleConfData_chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "chase兼容组"}, "VersionCompBattleConfData_farm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_版本兼容组.xlsx", "sheet": "农场兼容组"}, "DeviceWhiteListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "设备白名单"}, "DeviceBlackListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "设备黑名单"}, "DeviceModelWhiteListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "机型白名单"}, "InternalOpenIdWhiteListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "内部openId白名单"}, "TestOpenIdWhiteListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "外部openId白名单"}, "TestOpenidSign": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "外部白名单标签组"}, "TestBanIpCity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "ip屏蔽地域"}, "TraceWhiteListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "Trace染色名单"}, "WatermarkOpenIdWhiteListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "水印白名单"}, "PressTestOpenIdData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "压测好友openid列表"}, "UserBindPackageConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "平台白名单"}, "IdipForwardWhitelistData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "idip小区转发用户白名单"}, "AiLabMatchDyeWhiteListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_白名单.xlsx", "sheet": "ailab分组白名单"}, "BackpackTagForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_背包.xlsx", "sheet": "背包标签"}, "ResSuitCustomTag": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_背包.xlsx", "sheet": "橙装个性化设置标签"}, "BackpackItemQuality": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_背包.xlsx", "sheet": "品级描述"}, "BackpackCloakroomUnlock": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_背包.xlsx", "sheet": "搭配存储解锁"}, "BackpackSundryConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_背包.xlsx", "sheet": "杂项配置"}, "BackpackItemEquipForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_背包.xlsx", "sheet": "道具穿戴-不需要"}, "BackpackItemEquipPositionForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_背包.xlsx", "sheet": "穿戴位置解锁-不需要"}, "BackpackItemSellCheckForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\B_背包.xlsx", "sheet": "道具sellCheck-不需要"}, "RechargeTabSequenceConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "页签顺序"}, "RechargeNavigationData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "福利导航"}, "ActivityMainConfigForLetsGo_recharge": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "充值活动"}, "TaskConfData_recharge": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "充值任务"}, "TaskGroupData_recharge": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "充值任务组"}, "RechargeConfData_Ios": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "充值档位-IOS"}, "RechargeConfData_Android": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "充值档位-Android"}, "RechargeLevelConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "会员等级和奖励"}, "RechargeGiftConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "充值奖励礼物"}, "RechargeDepositData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "招财储蓄"}, "ResFirstChargeConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "首充活动"}, "MallCommodityConf_DirectBuy": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "特惠礼包"}, "MallCommodityConf_MonthCard": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "月卡礼包"}, "RechargeMonthCardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "月卡"}, "RechargeRebateShowData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "公测返利界面显示"}, "RechargeRebateData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "公测返利比"}, "InterServerGiftConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "一元幸启"}, "RechargeScratchOffTicketData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值配置表.xlsx", "sheet": "刮刮乐"}, "CurrencyCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值金币配表.xlsx", "sheet": "Sheet1"}, "TransferCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_充值金币配表.xlsx", "sheet": "transfer"}, "UGCRankingMenu": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_创作者排行榜.xlsx", "sheet": "排行页签配置"}, "SceneRewardConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_场景关卡.xlsx", "sheet": "奖励"}, "SceneRobotScoreConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_场景关卡.xlsx", "sheet": "机器人分数"}, "SceneCollectionConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_场景关卡.xlsx", "sheet": "场景收集物"}, "SceneStatisticConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_场景关卡.xlsx", "sheet": "场景数据统计"}, "SceneLevelMaxScoreConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_场景关卡.xlsx", "sheet": "关卡最大分数"}, "SceneGiftPackageData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_场景礼包.xlsx", "sheet": "场景礼包"}, "MallCommodityConf_SceneGiftPackage": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_场景礼包.xlsx", "sheet": "场景礼包商品"}, "SceneGiftShowStyle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_场景礼包.xlsx", "sheet": "场景礼包显示方式"}, "AchievementTaskConfData_arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_成就系统_成就任务_Arena.xlsx", "sheet": "成就任务"}, "AchievementTaskConfData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_成就系统_成就任务_nr3e.xlsx", "sheet": "成就任务"}, "AchievementTaskConfData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_成就系统_成就任务_主表.xlsx", "sheet": "成就任务"}, "AchievementTaskConfData_js": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_成就系统_成就任务_小窝.xlsx", "sheet": "成就任务"}, "AchievementConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_成就系统_页签.xlsx", "sheet": "成就总表"}, "RaffleTabData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "页签顺序"}, "RaffleAccessCfgData_Common": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "活动-赛季金币活跃卡池"}, "RaffleAccessCfgData_MonthActivity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "活动-月活动卡池"}, "RaffleAccessCfgData_Accessories": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "活动-配饰卡池"}, "RaffleAccessCfgData_Purple": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "活动-紫色卡池"}, "RaffleAccessCfgData_Card": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "活动-翻牌卡池"}, "RaffleAccessCfgData_Premium": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "活动-臻藏卡池"}, "RaffleAccessCfgData_SocialFission": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "活动-社交裂变卡池"}, "RaffleAccessCfgData_Vehicle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "活动-载具卡池"}, "RaffleAccessCfgData_Disk": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "活动-飞盘卡池"}, "RaffleAccessCfgData_Iaa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖.xlsx", "sheet": "活动-IAA卡池"}, "RaffleBIData_Discount": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_BI配置.xlsx", "sheet": "BI折扣"}, "RaffleBIData_TabOrder": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_BI配置.xlsx", "sheet": "BI页签顺序"}, "RaffleCfgData_Common": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖池-赛季金币活跃卡池"}, "RaffleCfgData_MonthActivity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖池-月活动卡池"}, "RaffleCfgData_Accessories": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖池-配饰卡池"}, "RaffleCfgData_Purple": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖池-紫色卡池"}, "RaffleCfgData_Card": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖池-翻牌卡池"}, "RaffleCfgData_Premium": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖池-至臻卡池"}, "RaffleCfgData_SocialFission": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖池-社交裂变卡池"}, "RaffleCfgData_Vehicle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖池-载具卡池"}, "RaffleCfgData_Disk": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖池-飞盘卡池"}, "RaffleCfgData_Iaa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖池-IAA卡池"}, "RaffleRewardCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "奖励"}, "RaffleMajorGDrawData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "大保底抽数掉率"}, "RaffleLuckyValueData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "幸运值配置"}, "MallCommodityConf_RaffleDiamondExchange": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_抽奖_奖池.xlsx", "sheet": "星钻兑换商城"}, "RedPacketBaseConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节礼盒.xlsx", "sheet": "礼盒基础设定"}, "RedPacketAmsConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节礼盒.xlsx", "sheet": "礼盒ams礼包单配置表"}, "RedPacketAmsReward": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节礼盒.xlsx", "sheet": "ams礼包单奖励映射表"}, "RedPacketReplyText": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节礼盒.xlsx", "sheet": "答谢语文本表"}, "RedPacketCommonConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节礼盒.xlsx", "sheet": "通用配置"}, "RedPacketPreviewConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节礼盒.xlsx", "sheet": "奖励展示配置"}, "RedPacketTaskShowConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节礼盒.xlsx", "sheet": "下方任务展示配置"}, "NewYearWishesData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节跨年全场系统祝福.xlsx", "sheet": "全服播报"}, "NewYearWishesButtonTimeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节跨年全场系统祝福.xlsx", "sheet": "玩家弹幕显示时间"}, "NewYearWishesTextData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节跨年全场系统祝福.xlsx", "sheet": "玩家祝福内容"}, "NewYearWishesOtherData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节跨年全场系统祝福.xlsx", "sheet": "玩家祝福其他配置"}, "NewYearWishesRandomFrames": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节跨年全场系统祝福.xlsx", "sheet": "头像框随机表"}, "NewYearWishesRandomPortraits": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_春节跨年全场系统祝福.xlsx", "sheet": "头像随机表"}, "TestLevelInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_测试关卡配置.xlsx", "sheet": "Sheet1"}, "TestTable": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_测试样例表格.xlsx", "sheet": "Sheet1"}, "FashionScoreCalcData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_潮流值计算.xlsx", "sheet": "Sheet1"}, "SuperCoreConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_超核玩家.xlsx", "sheet": "等级"}, "LuckyMoneyInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_超级红包配置.xlsx", "sheet": "超级红包"}, "GiftPackageConfForLetsGo_luckymoney": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_超级红包配置.xlsx", "sheet": "奖励礼包配置"}, "QQWXLoginErrorCode": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\C_错误码配置.xlsx", "sheet": "鉴权错误码配置"}, "LetsGoDeviceProfileInfoAndroid": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Android设备分级配置.xlsx", "sheet": "画质"}, "LetsGoDeviceScreenPercentageInfoAndroid": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Android设备分级配置.xlsx", "sheet": "分辨率"}, "LetsGoDeviceFPSInfoAndroid": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Android设备分级配置.xlsx", "sheet": "fps"}, "LetsGoDeviceProfileCommandAndroid": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Android设备控制台指令配置.xlsx", "sheet": "画质"}, "LetsGoDeviceScreenPercentageCommandAndroid": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Android设备控制台指令配置.xlsx", "sheet": "分辨率"}, "LetsGoDeviceFPSCommandAndroid": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Android设备控制台指令配置.xlsx", "sheet": "fps"}, "LetsGoCorrectCommandAndroid": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Android设备控制台指令配置.xlsx", "sheet": "高帧率下的细分指令"}, "DfPackageData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Df商店配置表.xlsx", "sheet": "Sheet1"}, "DfStoreItemData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Df商店配置表.xlsx", "sheet": "Sheet2"}, "DfBuyRespawnData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Df商店配置表.xlsx", "sheet": "复活购买"}, "DSAConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_ds管理配置.xlsx", "sheet": "ds管理配置"}, "DSLogConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_ds管理配置.xlsx", "sheet": "ds日志级别"}, "DSDevConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_ds管理配置.xlsx", "sheet": "dev配置"}, "LetsGoDeviceProfileInfoIOS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_IOS设备分级配置.xlsx", "sheet": "画质"}, "LetsGoDeviceScreenPercentageInfoIOS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_IOS设备分级配置.xlsx", "sheet": "分辨率"}, "LetsGoDeviceFPSInfoIOS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_IOS设备分级配置.xlsx", "sheet": "fps"}, "LetsGoDeviceProfileCommandIOS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_IOS设备控制台指令配置.xlsx", "sheet": "画质"}, "LetsGoDeviceScreenPercentageCommandIOS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_IOS设备控制台指令配置.xlsx", "sheet": "分辨率"}, "LetsGoDeviceFPSCommandIOS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_IOS设备控制台指令配置.xlsx", "sheet": "fps"}, "LetsGoCorrectCommandIOS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_IOS设备控制台指令配置.xlsx", "sheet": "高帧率下的细分指令"}, "TYCMapParaConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_TYC地图参数表.xlsx", "sheet": "地图参数"}, "LetsGoDeviceProfileInfoWindows": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Windows设备分级配置.xlsx", "sheet": "画质"}, "LetsGoDeviceScreenPercentageInfoWindows": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Windows设备分级配置.xlsx", "sheet": "分辨率"}, "LetsGoDeviceFPSInfoWindows": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Windows设备分级配置.xlsx", "sheet": "fps"}, "LetsGoDeviceProfileCommandWindows": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Windows设备控制台指令配置.xlsx", "sheet": "画质"}, "LetsGoDeviceScreenPercentageCommandWindows": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Windows设备控制台指令配置.xlsx", "sheet": "分辨率"}, "LetsGoDeviceFPSCommandWindows": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Windows设备控制台指令配置.xlsx", "sheet": "fps"}, "LetsGoCorrectCommandWindows": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_Windows设备控制台指令配置.xlsx", "sheet": "高帧率下的细分指令"}, "RegionIdcLoadCapabilityConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_地区IDC配置.xlsx", "sheet": "区域IDC负载能力"}, "RegionalConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_地区配置表.xlsx", "sheet": "地区属性"}, "RegionalMatchPoolConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_地区配置表.xlsx", "sheet": "地区匹配池"}, "GamePlayUpdateConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_多玩法更新.xlsx", "sheet": "多玩法"}, "DDPPoisonCircleInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大乱斗地图配置表.xlsx", "sheet": "毒圈数据"}, "DDPTornadoInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大乱斗地图配置表.xlsx", "sheet": "龙卷风数据"}, "DDPHitInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大乱斗技能配置.xlsx", "sheet": "受击类型表"}, "DDPSkillInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大乱斗技能配置.xlsx", "sheet": "技能信息表"}, "NPCData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅NPC.xlsx", "sheet": "NPC"}, "DialogueStageData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅NPC.xlsx", "sheet": "阶段"}, "DialogueStepData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅NPC.xlsx", "sheet": "步骤"}, "CommunityEntraceConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅入口配置.xlsx", "sheet": "入口配置表"}, "CommunityEntraceJumpConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅入口配置.xlsx", "sheet": "入口跳转配置表"}, "CommunityLevelLoadConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅关卡加载配置.xlsx", "sheet": "大厅关卡配置"}, "CommunityAllLevelConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅关卡加载配置.xlsx", "sheet": "全关卡配置"}, "CommunityInteractionLoadConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅关卡加载配置.xlsx", "sheet": "大厅交互物配置"}, "CommunitySwitchCDNTextureConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅关卡加载配置.xlsx", "sheet": "大厅换贴图配置"}, "LobbyChangeColorConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅关卡加载配置.xlsx", "sheet": "大厅换色配置"}, "SpawnMonsterConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅刷怪配置.xlsx", "sheet": "芝顿刷怪逻辑"}, "FireworkSystemTextData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅烟花配置.xlsx", "sheet": "春节烟花秀显示配置"}, "FireworkSystemTextColorData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅烟花配置.xlsx", "sheet": "烟花颜色配置"}, "FireworkCdData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅烟花配置.xlsx", "sheet": "烟花倒计时 "}, "SpringBgmData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅烟花配置.xlsx", "sheet": "跨年BGM"}, "ScenePlayConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法.xlsx", "sheet": "场景玩法"}, "SceneRandomBirthPosData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法.xlsx", "sheet": "随机出生点"}, "FireworkPartyTimeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法.xlsx", "sheet": "烟花秀_开启时间"}, "FireworksTexBanTimeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法.xlsx", "sheet": "烟花秀_禁止修改文本时间"}, "OfficialFireworkSpawnPosData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法.xlsx", "sheet": "烟花秀_官方释放点"}, "OffiFireworkEffectConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法.xlsx", "sheet": "烟花秀_官方烟花"}, "FireworkEffectConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法.xlsx", "sheet": "烟花秀_烟花效果"}, "FireworkPartyConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法.xlsx", "sheet": "烟花秀_杂项配置"}, "WishingTreeConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法.xlsx", "sheet": "春节许愿树配置"}, "HallCoinRewardConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法金币奖励.xlsx", "sheet": "奖励配置"}, "HallCoinLimitConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_大厅玩法金币奖励.xlsx", "sheet": "奖励上限"}, "BattleLikeContentData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_对局点赞.xlsx", "sheet": "Sheet1"}, "DanMuCfg": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_弹幕.xlsx", "sheet": "基础配置"}, "DanMuUgcBlockCfg": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_弹幕.xlsx", "sheet": "UGC弹幕区块划分"}, "InitialPoisonCircle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_毒圈配置.xlsx", "sheet": "初始参数"}, "PoisonCircle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_毒圈配置.xlsx", "sheet": "缩圈参数"}, "ExcludedPoisonCircle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_毒圈配置.xlsx", "sheet": "中心禁用区"}, "PoisonCircleTips": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_毒圈配置.xlsx", "sheet": "Tips"}, "LoginBackgroundConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_登录资源调用.xlsx", "sheet": "登录背景"}, "LoginSpineEffectConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_登录资源调用.xlsx", "sheet": "登录蒙皮"}, "LoadingPanelConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_登录资源调用.xlsx", "sheet": "加载界面"}, "LevelLoadingPanelConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_登录资源调用.xlsx", "sheet": "关卡加载界面底图"}, "AIServerEnvConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_第三方服务配置.xlsx", "sheet": "AI服务器环境配置"}, "PlayerLevelConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_等级.xlsx", "sheet": "等级档位"}, "ModUnlockLevelConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_等级.xlsx", "sheet": "模式解锁等级配置"}, "BackpackItemVideoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具展示视频表.xlsx", "sheet": "道具视频表"}, "BackpackItem_Currency": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表.xlsx", "sheet": "货币"}, "BackpackItem_Item": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表.xlsx", "sheet": "道具"}, "BackpackItem_QualifyingCard": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表.xlsx", "sheet": "段位升星保护券"}, "BackpackItem_Arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_Arena.xlsx", "sheet": "Arena物件"}, "BackpackItem_ReportAnim": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_NR3E.xlsx", "sheet": "报告动画"}, "BackpackItem_AttackAnim": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_NR3E.xlsx", "sheet": "攻击动画"}, "BackpackItem_NR3E_Interactive": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_NR3E.xlsx", "sheet": "互动道具"}, "BackpackItem_Meeting": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_NR3E.xlsx", "sheet": "会议表情"}, "BackpackItem_Identity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_NR3E.xlsx", "sheet": "狼人身份"}, "BackpackItem_TYC": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_TYC.xlsx", "sheet": "TYC"}, "BackpackItem_UgcBadge": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_UGC徽章.xlsm", "sheet": "徽章道具"}, "PlayerUgcBadgeConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_UGC徽章.xlsm", "sheet": "徽章配置"}, "AchievementReissueUgcBadgeConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_UGC徽章.xlsm", "sheet": "成就补发徽章"}, "BackpackItem_Emoji": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "表情"}, "BackpackItem_Action1P": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "单人动作"}, "BackpackItem_Action2P": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "双人动作"}, "BackpackItem_InteractiveProp": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "互动道具"}, "BackpackItem_Vehicle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "载具"}, "BackpackItem_Nameplate": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "铭牌"}, "BackpackItem_Portrait": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "头像"}, "BackpackItem_Frame": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "头像框"}, "BackpackItem_ChatBubble": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "气泡框"}, "PersonalityLabelData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "标签"}, "PersonalityStateData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "状态"}, "SpineEmojiConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化.xlsx", "sheet": "蒙皮表情包配置"}, "BackpackItem_Title": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化_称号.xlsx", "sheet": "称号"}, "BackpackItem_ProfileTheme": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化_背景.xlsx", "sheet": "界面主题"}, "ProfileThemeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_个性化_背景.xlsx", "sheet": "背景解锁"}, "BackpackItem_Farm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_农场.xlsx", "sheet": "农场道具"}, "BackpackItem_Xiaowo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_小窝.xlsx", "sheet": "小窝物件"}, "BackpackItem_Suit": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "套装"}, "BackpackItem_UpperGarment": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "上装"}, "BackpackItem_LowerGarment": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "下装"}, "BackpackItem_Gloves": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "手套"}, "BackpackItem_FaceOrnament": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "面饰"}, "BackpackItem_BackOrnament": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "背饰"}, "BackpackItem_HeadWear": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "头饰"}, "BackpackItem_HandOrnament": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "手部装扮"}, "BackpackItem_Face": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "脸部"}, "InGameItemResource": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "局内时装资源"}, "InGameFaceResource": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "局内脸部资源"}, "InGameOtherResource": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "局内其他资源"}, "InGameHandHoldResource": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "局内手持物资源"}, "SuitThemedInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "主题套装信息"}, "SuitSeasonInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "赛季套装信息"}, "LabelsInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装.xlsm", "sheet": "服饰标签信息"}, "SuitPreviewOffsetZ": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_时装_预览偏移值.xlsm", "sheet": "套装"}, "BackpackItem_Shuttle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_星梭.xlsx", "sheet": "星梭"}, "ShuttleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_星梭.xlsx", "sheet": "额外配置"}, "BackpackItemEffectData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_橙装特效.xlsx", "sheet": "橙装特效"}, "ItemStatusChangeData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_状态切换.xlsx", "sheet": "状态切换"}, "BackpackItem_PackageCommon": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_礼包.xlsx", "sheet": "固定礼包"}, "BackpackItem_PackageRandom": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_礼包.xlsx", "sheet": "随机礼包"}, "BackpackItem_PackagePick": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_礼包.xlsx", "sheet": "自选礼包"}, "BackpackItem_Kart": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\D_道具表_飞车.xlsx", "sheet": "道具"}, "FixConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_Fix服务器数据修复配置.xlsx", "sheet": "Sheet1"}, "TaskShareConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分享任务配置.xlsx", "sheet": "分享配置"}, "SnsShareConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分享配置表.xlsx", "sheet": "分享配置"}, "InviteTeamShareConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分享配置表.xlsx", "sheet": "组队分享"}, "UGCMapShareConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分享配置表.xlsx", "sheet": "UGC地图分享"}, "ChunkGroupData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分包.xlsx", "sheet": "包组"}, "LevelChunkData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分包.xlsx", "sheet": "关卡加载图"}, "StaticMountPakData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分包.xlsx", "sheet": "静态挂载pak"}, "AvatarChunkData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分包.xlsx", "sheet": "展示时装"}, "MainUIGroupData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分包.xlsx", "sheet": "主大厅UI分包表"}, "AvatarDanceData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分包.xlsx", "sheet": "舞蹈动作分包表"}, "ChunkDownloadSpeedData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分包下载参数.xlsx", "sheet": "网络限速"}, "ChunkDownloadTaskData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分包下载参数.xlsx", "sheet": "机型参数"}, "ChunkDownloadWhiteListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_分包下载参数.xlsx", "sheet": "场景白名单"}, "ServerIdipAreaConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_服务器IdipArea配置.xlsx", "sheet": "idipArea配置"}, "IdipDatamoreConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_服务器IdipArea配置.xlsx", "sheet": "idip服务datamore接口配置"}, "ServerVipData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_服务器域名解析保底.xlsx", "sheet": "vip保底"}, "ServerTextData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_服务器文本配置.xlsx", "sheet": "服务器文本列表"}, "NoticeMsgTypeTextData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_服务器文本配置.xlsx", "sheet": "玩家通知类型文本映射表"}, "ServerKvConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_服务器文本配置.xlsx", "sheet": "服务器KV配置表"}, "GameRule": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_服务器玩法规则索引.xlsx", "sheet": "Sheet1"}, "ResServerWhiteData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_服务器白名单.xlsx", "sheet": "白名单"}, "ResServerWhiteAddrData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_服务器白名单.xlsx", "sheet": "灰度服地址"}, "TYCBodyArmorConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\F_防弹衣.xlsx", "sheet": "防弹衣"}, "ResGCParamData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_GC配置.xlsx", "sheet": "登录后拍脸"}, "GMCmdConfForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_gm配置.xlsx", "sheet": "gm指令-LetsGo"}, "ServerCmdConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_gm配置.xlsx", "sheet": "后台cmd指令"}, "OMDLevelInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_OMD关卡信息.xlsx", "sheet": "关卡表"}, "OMDGameTypeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_OMD关卡信息.xlsx", "sheet": "兽人模式"}, "OMDLevelRuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_OMD关卡信息.xlsx", "sheet": "关卡规则"}, "OMDOrcpediaConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_OMD怪物百科.xlsx", "sheet": "怪物百科"}, "MainModeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_主界面模式.xlsx", "sheet": "主界面模式"}, "ClubCommonConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_公会.xlsx", "sheet": "公会基础配置"}, "PlayerHeatConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_公会.xlsx", "sheet": "活力值"}, "ClubLabelData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_公会.xlsx", "sheet": "标签信息"}, "ClubIconData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_公会.xlsx", "sheet": "图标信息"}, "ClubMemberActivitySortData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_公会.xlsx", "sheet": "成员动态展示排序"}, "ClubRecommendSortData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_公会.xlsx", "sheet": "公会推荐排序"}, "ClubLabelConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_公会.xlsx", "sheet": "（弃用）公会等级"}, "MarqueeNoticeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_公告.xlsx", "sheet": "跑马灯"}, "BroadcastNoticeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_公告.xlsx", "sheet": "广播Tips"}, "PreWarnMarqueeNoticeConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_公告.xlsx", "sheet": "全服预警跑马灯"}, "SelectLevelInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡切换配置.xlsx", "sheet": "Sheet1"}, "LevelDropConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡掉落.xlsx", "sheet": "关卡掉落配置"}, "LevelDropLimitConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡掉落.xlsx", "sheet": "限时奖励掉落"}, "LevelDropDimensionData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡掉落.xlsx", "sheet": "掉落维度配置"}, "InLevelEventUploadData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡数据上报.xlsx", "sheet": "UploadEvent"}, "LevelModuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡模式.xlsx", "sheet": "Sheet1"}, "LevelScorecardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡积分规则.xlsx", "sheet": "Sheet1"}, "LevelRoundRandomRuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡轮次随机表.xlsx", "sheet": "关卡轮次"}, "LevelRoundDispersionConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡轮次随机表.xlsx", "sheet": "关卡去重规则"}, "LevelInfoData_AC": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_AC.xlsx", "sheet": "Sheet1"}, "LevelInfoData_AI": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_AI.xlsx", "sheet": "Sheet1"}, "LevelInfoData_arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_arena.xlsx", "sheet": "Sheet1"}, "LevelInfoData_BS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_BS.xlsx", "sheet": "Sheet1"}, "LevelInfoData_Chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_Chase.xlsx", "sheet": "Sheet1"}, "LevelInfoData_dnd": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_dnd.xlsx", "sheet": "Sheet1"}, "LevelInfoData_FPS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_fps.xlsx", "sheet": "Sheet1"}, "LevelInfoData_JS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_JS.xlsx", "sheet": "Sheet1"}, "LevelInfoData_MAYDAY": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_MAYDAY.xlsx", "sheet": "Sheet1"}, "LevelInfoData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_nr3e.xlsx", "sheet": "Sheet1"}, "LevelInfoData_OMD": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_omd.xlsx", "sheet": "Sheet1"}, "LevelInfoData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_ugc.xlsx", "sheet": "Sheet1"}, "LevelInfoData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_主表.xlsx", "sheet": "Sheet1"}, "LevelExtraTimeInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_主表.xlsx", "sheet": "Sheet2"}, "LevelUGCInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡配置_主表.xlsx", "sheet": "Sheet3"}, "LevelRandomInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡随机.xlsx", "sheet": "关卡配置"}, "LevelRandomGroupData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡随机.xlsx", "sheet": "随机权重配置"}, "LevelDimensionData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关卡随机.xlsx", "sheet": "维度配置"}, "RelationConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关系链.xlsx", "sheet": "关系链配置表"}, "IntimateRelationConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关系链.xlsx", "sheet": "亲密关系配置"}, "IntimateRelationLevelConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关系链.xlsx", "sheet": "亲密关系等级配置"}, "RelationBattleModeIntimacyConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关系链.xlsx", "sheet": "玩法对应亲密度配置"}, "ValentineDayConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_关系链.xlsx", "sheet": "情人节特效配置"}, "FeatureKeySwitchConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_功能key开关.xlsx", "sheet": "关闭功能key"}, "FeatureOpenConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_功能解锁.xlsx", "sheet": "功能解锁"}, "UgcExpData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_工匠值配置表.xlsx", "sheet": "工匠值配置表"}, "UgcExpCalcParamsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_工匠值配置表.xlsx", "sheet": "造梦点计算公式参数"}, "WorkLevelData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_工匠等级配置表.xlsx", "sheet": "工匠等级配置表"}, "LobbyNavConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_广场导航列表配置.xlsx", "sheet": "广场导航列表"}, "LobbyJumpMsgBoxConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_广场跳转弹窗配置.xlsx", "sheet": "广场跳转自动弹窗"}, "LobbyExitToastConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_广场跳转弹窗配置.xlsx", "sheet": "广场结束时的toast提示"}, "LobbyConfigData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_广场配置.xlsx", "sheet": "主广场"}, "LobbyConfigData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_广场配置.xlsx", "sheet": "UGC广场"}, "BroadcastSetting": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_广播设定.xlsx", "sheet": "消息设定"}, "GameRuleUrlConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_规则配置.xlsx", "sheet": "链接映射"}, "PianoConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_钢琴配置表.xlsx", "sheet": "钢琴配置"}, "PianoPlayConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\G_钢琴配置表.xlsx", "sheet": "钢琴播放配置"}, "ActivityCenterReverseData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H-活动中心预约.xlsx", "sheet": "活动配置"}, "QRCodeCalculationRuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_合家欢.xlsx", "sheet": "红包云设置"}, "TeamPhotoBackgroundConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_合影资产表.xlsx", "sheet": "合影背景"}, "TeamPhotoActionTemplateConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_合影资产表.xlsx", "sheet": "合影模版"}, "TeamPhotoCustomConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_合影资产表.xlsx", "sheet": "合影参数"}, "TeamPhotoEditTabConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_合影资产表.xlsx", "sheet": "合影编辑页签"}, "ReturningUserConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_回流系统.xlsx", "sheet": "回归页签"}, "ReturningUserConstsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_回流系统.xlsx", "sheet": "常量"}, "ReturningTabConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_回流系统.xlsx", "sheet": "页签配置"}, "OutfitDeliveryData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_回流系统.xlsx", "sheet": "签到"}, "ReturningNavigationData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_回流系统.xlsx", "sheet": "福利总览"}, "ReturningRecommendData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_回流系统.xlsx", "sheet": "新内容推荐"}, "ReturningRecommend2Data": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_回流系统.xlsx", "sheet": "新内容推荐2期"}, "ReturningUserWarmRoundLimitData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_回流系统.xlsx", "sheet": "回流温暖局"}, "ReturningDailyBenefitsLimitData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_回流系统.xlsx", "sheet": "今日福利配置"}, "ReturnPushFaceSettingData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_回流系统.xlsx", "sheet": "回流分流配置"}, "WholeGameProcessRuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_99公益.xlsx", "sheet": "全服进度"}, "ActivityMainConfigForLetsGo_UgcActivity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_UGC新年.xlsx", "sheet": "活动中心分表"}, "TaskGroupData_UgcActivity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_UGC新年.xlsx", "sheet": "活动任务组"}, "TaskConfData_UgcActivity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_UGC新年.xlsx", "sheet": "活动任务"}, "UgcNewYearConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_UGC新年.xlsx", "sheet": "年兽活动"}, "UgcNewYearCollectionConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_UGC新年.xlsx", "sheet": "分活动"}, "UgcNewYearConstConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_UGC新年.xlsx", "sheet": "常量"}, "AnimalHandbookConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_动物图鉴.xlsx", "sheet": "动物图鉴配置"}, "AnimalHandbookSpeciesConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_动物图鉴.xlsx", "sheet": "物种配置"}, "AnimalHandbookColonyConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_动物图鉴.xlsx", "sheet": "聚集地配置"}, "AnimalHandbookUltimatePrizeConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_动物图鉴.xlsx", "sheet": "终极大奖配置"}, "AnimalHandbookMiscConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_动物图鉴.xlsx", "sheet": "杂项配置"}, "ActivityHYWarmUpData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_半周年预热.xlsx", "sheet": "半周年庆预热活动"}, "ActivityHYWarmUpCheckinData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_半周年预热.xlsx", "sheet": "每日打卡配置"}, "ActivityHYWarmUpProgressData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_半周年预热.xlsx", "sheet": "打卡进度配置"}, "ActRecruiteConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_召集令.xlsx", "sheet": "基础配置"}, "ActivityMonopolyMainData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_大富翁.xlsx", "sheet": "主表"}, "ActivityMonopolyGridData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_大富翁.xlsx", "sheet": "格子表"}, "ActivityMonopolyLotteryData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_大富翁.xlsx", "sheet": "抽奖表"}, "ActivityMonopolyRoundRewardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_大富翁.xlsx", "sheet": "圈奖励"}, "ActivityMonopolyStepRewardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_大富翁.xlsx", "sheet": "步数奖励"}, "UltramanThemeConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_奥特曼主题.xlsx", "sheet": "活动阶段"}, "UltramanThemeCollectionConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_奥特曼主题.xlsx", "sheet": "收集进度奖励"}, "UltramanTaskJumpConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_奥特曼主题.xlsx", "sheet": "任务跳转"}, "EntertainIntelligenceStationConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_娱乐情报站.xlsx", "sheet": "娱乐情报站"}, "LeagueHeroesHeroConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_峡谷英雄传.xlsx", "sheet": "英雄信息"}, "LuckyBalloonConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_幸运气球.xlsx", "sheet": "通用配置"}, "LuckyBalloonRewardConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_幸运气球.xlsx", "sheet": "奖池配置"}, "LuckyBalloonBlessConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_幸运气球.xlsx", "sheet": "幸运签内容"}, "TaskPictureUrlConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_庆典签到.xlsx", "sheet": "任务奖励图"}, "TimeLimitedCheckInActivityConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_开学返利.xlsx", "sheet": "开学返利配置"}, "WishesCameTrueGiftConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_心愿全实现.xlsx", "sheet": "自选奖励"}, "WishesCameTrueGiftCostConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_心愿全实现.xlsx", "sheet": "自选奖励节点进度"}, "WishesCameTrueInvitationRewardsConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_心愿全实现.xlsx", "sheet": "邀请助力"}, "ActivityCheckInManualData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_打卡手册.xlsx", "sheet": "打开手册配置"}, "CheckInPlanActivityWeekConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_打卡计划.xlsx", "sheet": "每周打卡配置表"}, "CheckInPlanActivityDrawConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_打卡计划.xlsx", "sheet": "抽奖配置表"}, "CheckInPlanRewardConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_打卡计划.xlsx", "sheet": "奖励图配置表"}, "CheckInPlanActivityTextConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_打卡计划.xlsx", "sheet": "文本配置表"}, "TakeawayConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_挂机外卖.xlsx", "sheet": "活动配置"}, "TakeawayBoxConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_挂机外卖.xlsx", "sheet": "宝箱配置"}, "FridayCollectionConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_星期五导航栏.xlsx", "sheet": "活动信息配置"}, "FridayCollectionMiscConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_星期五导航栏.xlsx", "sheet": "导航页配置"}, "SpringRedPacketConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_春节红包.xlsx", "sheet": "开学返利配置"}, "SpringBlessingCollectionConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_春节集福.xlsx", "sheet": "春节集福活动"}, "SpringBlessingCardConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_春节集福.xlsx", "sheet": "福字卡牌配置"}, "SpringBlessingCardRewardConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_春节集福.xlsx", "sheet": "福字奖励库"}, "SpringBlessingSponsorConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_春节集福.xlsx", "sheet": "赞助商配置表"}, "UpdateForesightActivityExtraInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_版本前瞻.xlsx", "sheet": "全服进度"}, "WealthBankCheckInRewardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_理财银行.xlsx", "sheet": "签到奖励"}, "WealthBankConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_理财银行.xlsx", "sheet": "杂项配置"}, "ActivityClubChallengeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_社团挑战.xlsx", "sheet": "社团挑战主表"}, "ActivityClubChallengeMatchData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_社团挑战.xlsx", "sheet": "社团挑战玩法配置表"}, "ActivityClubChallengeMatchSettlementData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_社团挑战.xlsx", "sheet": "社团挑战玩法结算配置表"}, "ActivityClubChallengeStarLightRewardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_社团挑战.xlsx", "sheet": "社团挑战贡献奖励表"}, "LuckyStarHandBookData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_福星手账薄.xlsx", "sheet": "通用配置"}, "LuckyStarHandBookStarsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_福星手账薄.xlsx", "sheet": "福星配置"}, "LuckyStarHandBookStarPoolData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_福星手账薄.xlsx", "sheet": "福星池"}, "LuckyStarHandBookBlessData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_福星手账薄.xlsx", "sheet": "祝福内容"}, "AccumulateBlessingsActivityConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_积攒福气.xlsx", "sheet": "积攒福气"}, "ActivityChapterTaskData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_章节任务.xlsx", "sheet": "章节任务"}, "ActivityTeamRankData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_组队排位.xlsx", "sheet": "组队排位"}, "ActivityBrainiacConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_脑力达人.xlsx", "sheet": "赛事介绍"}, "StickerConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_贴纸簿.xlsx", "sheet": "消耗配置"}, "StickerNormalRewardConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_贴纸簿.xlsx", "sheet": "普通奖励配置"}, "StickerBingoRewardConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_贴纸簿.xlsx", "sheet": "连线奖励配置"}, "StickerChapterRewardConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_贴纸簿.xlsx", "sheet": "章节奖励配置"}, "CompetitionWarmUpActivityConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_赛事热身活动.xlsx", "sheet": "活动阶段配置"}, "CompetitionWarmUpScoreConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_赛事热身活动.xlsx", "sheet": "积分规则配置"}, "ActivityFlyingChessData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_走格子.xlsx", "sheet": "主表配置"}, "ActivityFlyingChessGridData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_走格子.xlsx", "sheet": "格子配置"}, "ActivityFlyingChessRoundRewardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_走格子.xlsx", "sheet": "圈数奖励配置"}, "ActivityFlyingChessMapInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_走格子.xlsx", "sheet": "地图显示配置"}, "ActivityFlyingChessPlotData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_走格子.xlsx", "sheet": "剧情详情配置"}, "ActivityFlyingChessGetWayData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_走格子.xlsx", "sheet": "更多获取方式配置"}, "ActivitySuperLinearRedeemData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_超级线性兑换.xlsx", "sheet": "游园会配置"}, "ActivitySuperLinearRandomData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_超级线性兑换.xlsx", "sheet": "随机步数"}, "TrainingCampSportsmanConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_运动特训营.xlsx", "sheet": "运动员配置"}, "ActivityLotteryDrawRewardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_部分不放回抽奖.xlsx", "sheet": "奖池配置"}, "ActivityLotteryDrawCostData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_部分不放回抽奖.xlsx", "sheet": "消耗配置"}, "MusicOrderRefreshConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_酷洛米的手账本.xlsx", "sheet": "每日订单配置"}, "MusicOrderConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_酷洛米的手账本.xlsx", "sheet": "订单内容配置"}, "MusicOrderModeDropConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_酷洛米的手账本.xlsx", "sheet": "模式掉落配置"}, "MusicOrderNoteDropConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_酷洛米的手账本.xlsx", "sheet": "音符配置"}, "MusicOrderMiscConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_酷洛米的手账本.xlsx", "sheet": "杂项配置"}, "MusicOrderMiscTextConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_酷洛米的手账本.xlsx", "sheet": "杂项文本配置"}, "MusicOrderTaskConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动_酷洛米的手账本.xlsx", "sheet": "订单数量累计任务"}, "ActivityMainConfigForLetsGo_common": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置.xlsx", "sheet": "活动配置"}, "TaskGroupData_activity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置.xlsx", "sheet": "活动任务组"}, "TaskConfData_activity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置.xlsx", "sheet": "活动任务"}, "ActivityTabData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置.xlsx", "sheet": "活动页签"}, "ActivityGopenIdWhiteListConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置_gopenid白名单.xlsx", "sheet": "活动白名单"}, "ActivityMainConfigForLetsGo_design": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置_策划专用.xlsx", "sheet": "活动配置"}, "TaskGroupData_activitydesign": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置_策划专用.xlsx", "sheet": "活动任务组"}, "TaskConfData_activitydesign": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置_策划专用.xlsx", "sheet": "活动任务"}, "ActivityMainConfigForLetsGo_yunying": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置_运营.xlsx", "sheet": "活动配置"}, "TaskGroupData_activityyunying": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置_运营.xlsx", "sheet": "活动任务组"}, "TaskConfData_activityyunying": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置_运营.xlsx", "sheet": "活动任务"}, "ActivityPermanentExchange": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动中心配置_运营.xlsx", "sheet": "常驻兑换"}, "ActivityFunParaData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动功能参数.xlsx", "sheet": "活动功能参数"}, "NewActivityPilotEntranceData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动导航.xlsx", "sheet": "入口配置"}, "NewActivityPilotTabData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动导航.xlsx", "sheet": "活动配置"}, "ActivityHYNCheckInConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动导航.xlsx", "sheet": "半周年庆打卡配置"}, "SquadActivityData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动小队.xlsx", "sheet": "友情小队"}, "MultiPlayerSquadData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动小队.xlsx", "sheet": "多人小队"}, "ActivityLabelConfigForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_活动标签配置.xlsx", "sheet": "活动标签"}, "ResAgreementsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_海外登录协议.xlsx", "sheet": "海外登录协议"}, "RedEnvelopeSpawnerSettingData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_红包雨.xlsx", "sheet": "红包云设置"}, "RedEnvelopeActivityData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_红包雨.xlsx", "sheet": "红包活动时间"}, "RedEnvelopeTurnSettingData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_红包雨.xlsx", "sheet": "每日轮次设置"}, "RedEnvelopeActServerSettingData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_红包雨.xlsx", "sheet": "红包雨服务器活动设置"}, "RedEnvelopPackageItemIdData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_红包雨.xlsx", "sheet": "红包雨礼包游戏道具设置"}, "RedEnvelopeAcInGameRewardRaffleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_红包雨.xlsx", "sheet": "游戏内道具奖池"}, "RedDotConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_红点配置.xlsx", "sheet": "红点配置"}, "MonetaryObtain": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_货币获取和使用.xlsx", "sheet": "货币获取页面"}, "MonetarySpend": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_货币获取和使用.xlsx", "sheet": "货币使用页面"}, "CpuHardwareBlackListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\H_黑名单_设备型号.xlsx", "sheet": "Sheet1"}, "IAAConfData_Settlement": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\I_IAA配置.xlsx", "sheet": "结算"}, "IAAConfData_Raffle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\I_IAA配置.xlsx", "sheet": "抽奖"}, "IAAShareConfData_Raffle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\I_IAA配置.xlsx", "sheet": "分享"}, "IPBlackListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\I_IP黑名单.xlsx", "sheet": "Sheet1"}, "TYCNormalBuildingConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_TYC普通建筑.xlsx", "sheet": "普通建筑"}, "ReportEntryConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_举报.xlsx", "sheet": "举报入口"}, "ReportContentData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_举报.xlsx", "sheet": "举报内容"}, "GunGameData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_军事竞赛玩法配置.xlsx", "sheet": "军备竞赛配置"}, "GunGameData_Team": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_军事竞赛玩法配置.xlsx", "sheet": "军备竞赛配置-团队"}, "ResGunGameKillReportData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_军事竞赛玩法配置.xlsx", "sheet": "结算武器击杀上报"}, "PlotConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_剧情表.xlsx", "sheet": "剧情表"}, "LoadingTips": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_加载中Tips.xlsx", "sheet": "LoadingTips"}, "RewardCompensateConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖励补领.xlsx", "sheet": "奖励补领"}, "GiftPackageConfForLetsGo_scratchofftickets": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖励配置_刮刮乐.xlsx", "sheet": "奖励礼包配置"}, "GiftPackageConfForLetsGo_giftpack": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖励配置_礼包.xlsx", "sheet": "奖励礼包配置"}, "MainCupsStageConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "总进度"}, "ModeCupsStageConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "玩法进度"}, "CupRuleConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "玩法奖励规则"}, "QualifyingLevelDimensionConditionData_cups": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "规则维度"}, "CupAdditionConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "加成生效条件"}, "CupAdditionFashionValueConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "加成条件-时尚分"}, "CupAdditionRelationConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "加成条件-社交"}, "CupAdditionPlayModeConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "加成条件-玩法"}, "TaskConfData_cup": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "奖杯任务"}, "TaskGroupData_cup": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "奖杯任务组"}, "CupsConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_奖杯征程.xlsx", "sheet": "其他杂项配置"}, "DeviceAspectRatioData_IOS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_机型配置表.xlsx", "sheet": "IOS机型功能配置表"}, "DeviceAspectRatioData_Android": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_机型配置表.xlsx", "sheet": "Android机型功能配置表"}, "DeviceFoldList": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_机型配置表.xlsx", "sheet": "折叠机型"}, "DeviceForceResetList": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_机型配置表.xlsx", "sheet": "ForceResetList"}, "PlayerInfoSubTabData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_界面页签.xlsx", "sheet": "个人信息主页"}, "JSReliableAIConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_竞速保底AI配置表.xlsx", "sheet": "Sheet1"}, "SpeedCompensationConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_竞速关落后补偿.xlsx", "sheet": "竞速关落后补偿"}, "JSFallBehindConfForSkate": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_竞速玩法_杂项其它.xlsx", "sheet": "滑板补偿"}, "JSFallBehindConfForBike": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_竞速玩法_杂项其它.xlsx", "sheet": "自行车补偿"}, "ResJSPropConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_竞速道具.xlsx", "sheet": "竞速道具"}, "ResJSPropRankRangeConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_竞速道具.xlsx", "sheet": "竞速道具赛人数分档"}, "ResJSPropMiscConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_竞速道具.xlsx", "sheet": "竞速道具生成等杂项规则"}, "ResJSPropsGenWeightConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_竞速道具.xlsx", "sheet": "各排名随机道具"}, "OverseasTextHotFixEntryData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_紧急热更翻译对照表.xlsx", "sheet": "紧急翻译配置"}, "GamePlayChasingLightCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_聚光灯玩法配置.xlsx", "sheet": "聚光灯玩儿法配置"}, "ResPlayerBillboard": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_角色信息外显.xlsx", "sheet": "距离配置"}, "FriendAddDistance": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\J_角色信息外显.xlsx", "sheet": "亲密度增加距离配置"}, "TYCScientistClientConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\K_TYC科学家.xlsx", "sheet": "野怪表"}, "ClientServerConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\K_客户端网络配置.xlsx", "sheet": "客户端网络参数配置"}, "ClientNetTextConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\K_客户端网络配置.xlsx", "sheet": "客户端网络错误码文本配置"}, "CrossRouteConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\K_跨区互通.xlsx", "sheet": "跨区关系"}, "CrossRpcFilterConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\K_跨区互通.xlsx", "sheet": "rpc命令过滤"}, "MonsterData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoMonster\\G_怪物配置.xlsx", "sheet": "Sheet1"}, "SceneFlowData_bnb": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_bnb.xlsx", "sheet": "Sheet1"}, "SceneFlowData_chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_chase.xlsx", "sheet": "奖励"}, "SceneFlowData_FPS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_FPS.xlsx", "sheet": "Sheet1"}, "SceneFlowData_JS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_JS.xlsx", "sheet": "Sheet1"}, "SceneFlowData_LevelPrivate": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_LevelPrivate.xlsx", "sheet": "Sheet1"}, "SceneFlowData_NPC": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_NPC.xlsx", "sheet": "Sheet1"}, "SceneFlowData_OMD": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_OMD.xlsx", "sheet": "Sheet1"}, "SceneFlowData_NR3E": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_副玩法.xlsx", "sheet": "Sheet1"}, "SceneFlowData_AnimalParty": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_动物派对.xlsx", "sheet": "Sheet1"}, "SceneFlowData_Ground": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_地形.xlsx", "sheet": "Sheet1"}, "SceneFlowData_Community": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_大厅.xlsx", "sheet": "Sheet1"}, "SceneFlowData_Furniture": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_家具.xlsx", "sheet": "Sheet1"}, "SceneFlowData_Monster": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_怪物.xlsx", "sheet": "Sheet1"}, "SceneFlowData_Gears": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_机关.xlsx", "sheet": "Sheet1"}, "SceneFlowData_Weapon": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_武器.xlsx", "sheet": "Sheet1"}, "SceneFlowData_Item": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_物品.xlsx", "sheet": "Sheet1"}, "SceneFlowData_Decorate": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_装饰.xlsx", "sheet": "Sheet1"}, "SceneFlowData_Logic": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_逻辑.xlsx", "sheet": "Sheet1"}, "SceneFlowData_Prop": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物_道具.xlsx", "sheet": "Sheet1"}, "SceneFlowData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\LetsGoPlaceable\\C_场景放置物测试表.xlsx", "sheet": "Sheet1"}, "WolfKillRoadToMasterData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_狼人玩法配置.xlsx", "sheet": "大师之路"}, "WolfKillDecorationAniData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_狼人玩法配置.xlsx", "sheet": "报告攻击动画"}, "WolfKillDecorationReactData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_狼人玩法配置.xlsx", "sheet": "互动道具"}, "WolfKillRoleInfoRateData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_狼人玩法配置.xlsx", "sheet": "专精点数加成"}, "WolfKillRoleInfoBaseData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_狼人玩法配置.xlsx", "sheet": "专精基准值"}, "MiscConfForLetsGo_chat": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "基础配置"}, "GlobalWorldChatGroupList": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "公共聊天配置"}, "ChatSystemMsgConfDataForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "系统消息文案配置"}, "ChatConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "聊天配置（服务器）"}, "AreaChatGroupList": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "区域聊天配置"}, "ChatClientConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "客户端常量配置"}, "ChatRoomConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "私密聊天室配置"}, "ChatEmojiCellList": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "表情配置"}, "ChatLobbyInteractionSystemConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "大厅互动系统消息文案配置"}, "TeamBattleBroadcastResConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "队伍播报配置"}, "ChatMagicVoiceList": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天.xlsx", "sheet": "魔音列表"}, "QuickTextConfDataLobby": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天快捷文本.xlsx", "sheet": "大厅文本"}, "QuickTextConfDataInLevel": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天快捷文本.xlsx", "sheet": "局内默认文本"}, "CustomRoomQuickTextConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天快捷文本.xlsx", "sheet": "自定义房间快捷文本"}, "CustomRoomQuickEmojiConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天快捷文本.xlsx", "sheet": "自定义房间快捷Emoji"}, "FarmTextConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天快捷文本.xlsx", "sheet": "农场模式文本"}, "QuickTextConfDataOther": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天快捷文本.xlsx", "sheet": "其他模式文本"}, "ChatInteractiveData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天快捷文本.xlsx", "sheet": "观战互动配置"}, "SayHiTextConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天快捷文本.xlsx", "sheet": "陌生人打招呼文本"}, "SayHiEntranceConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天快捷文本.xlsx", "sheet": "陌生人打招呼入口"}, "SayHiPopConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天快捷文本.xlsx", "sheet": "陌生人加好友"}, "ChatContentEntryData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_聊天文案信息表.xlsx", "sheet": "文本映射表"}, "LevelRoundRuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\L_轮次规则.xlsx", "sheet": "Sheet1"}, "Mayday_MapConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\D_地图表.xlsx", "sheet": "普通模式配置"}, "Mayday_PropConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\D_道具表.xlsx", "sheet": "Sheet1"}, "Mayday_LevelPropConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\G_关卡生成配置.xlsx", "sheet": "关卡地图配置"}, "Mayday_PropWeightConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\G_关卡生成配置.xlsx", "sheet": "关卡物资配置"}, "Mayday_KPIConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\K_KPI配置.xlsx", "sheet": "普通模式配置"}, "Mayday_PlayerInfoConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\K_PlayerInfo配置.xlsx", "sheet": "普通模式配置"}, "Mayday_ShopConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\S_商店表.xlsx", "sheet": "普通模式配置"}, "Mayday_LevelWeatherData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\T_天气配置表.xlsx", "sheet": "天气表"}, "Mayday_LevelWeatherStateData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\T_天气配置表.xlsx", "sheet": "什么星球"}, "Mayday_LevelWeatherWeightData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\T_天气配置表.xlsx", "sheet": "什么天气"}, "TextEntryData_Mayday": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Mayday\\W_文本表_文本配置_Mayday.xlsx", "sheet": "文本配置"}, "MidasProductConfData_ItemBuy": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\M_midas配置.xlsx", "sheet": "米大师物品ID配置"}, "MidasProductConfData_Charge": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\M_midas配置.xlsx", "sheet": "代币充值档位配置"}, "MidasPageDooActivityConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\M_midas配置.xlsx", "sheet": "页匠活动配置"}, "MidasSandboxZoneConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\M_midas配置.xlsx", "sheet": "沙箱分区配置"}, "MidasReleaseZoneConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\M_midas配置.xlsx", "sheet": "现网分区配置"}, "SensitiveFilterData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\M_敏感词过滤.xlsx", "sheet": "敏感词过滤配置"}, "NR3E1LevelPropData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E1_躲猫猫场景道具表.xlsx", "sheet": "Prop"}, "NR3E1LevelPropRefreshData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E1_躲猫猫场景道具表.xlsx", "sheet": "Refresh"}, "NR3E1MapModuleMapData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E1_躲猫猫随机地图配置.xlsx", "sheet": "地图"}, "NR3E1MapModulePoolData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E1_躲猫猫随机地图配置.xlsx", "sheet": "模块池"}, "NR3E1MapModuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E1_躲猫猫随机地图配置.xlsx", "sheet": "模块"}, "NR3E3VocationData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E3_职业表.xlsx", "sheet": "玩法阵营角色"}, "NR3E3RandomPackData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E3_职业表.xlsx", "sheet": "身份随机包"}, "NR3E3VocationGroupData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E3_职业表.xlsx", "sheet": "身份组"}, "NR3E3Preparations_RandomPackData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E3_职业表.xlsx", "sheet": "备战系统-身份随机包"}, "CustomRoomData_NR3E3": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E3_职业表.xlsx", "sheet": "自定义房间配置"}, "CustomRoom_NR3E3VocationGroup": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E3_职业表.xlsx", "sheet": "自定义房间身份"}, "CustomRoom_NR3E3CampConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E3_职业表.xlsx", "sheet": "自定义房间人数"}, "NR3E3RandomReturnData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E3_职业表.xlsx", "sheet": "回流温暖局职业配置"}, "NR3E1GameConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E_游戏基础配置.xlsx", "sheet": "E1GameConfig"}, "NR3E2GameConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E_游戏基础配置.xlsx", "sheet": "E2GameConfig"}, "NR3E3GameConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_NR3E_游戏基础配置.xlsx", "sheet": "E3GameConfig"}, "FarmBuffEffectConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场BUFF表.xlsx", "sheet": "BUFF效果表"}, "FarmBuffConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场BUFF表.xlsx", "sheet": "BUFF表"}, "FarmBuffRainBowConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场BUFF表.xlsx", "sheet": "BUFF七彩石表"}, "FarmBuffSkillConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场BUFF表.xlsx", "sheet": "BUFF技能表"}, "FarmCropConf_Crop": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场养殖物表_作物.xlsx", "sheet": "养殖物配置"}, "FarmCropLevelConf_Crop": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场养殖物表_作物.xlsx", "sheet": "养殖物升级配置"}, "FarmCropConf_Animal": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场养殖物表_动物.xlsx", "sheet": "养殖物配置"}, "FarmCropLevelConf_Animal": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场养殖物表_动物.xlsx", "sheet": "养殖物升级配置"}, "TerrainConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场地格表.xlsx", "sheet": "地格表"}, "FarmTileLevelConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场地格表.xlsx", "sheet": "地格资源表"}, "FarmBuildingTypeConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场建筑表.xlsx", "sheet": "建筑类型"}, "FarmBuildingLevelUpConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场建筑表.xlsx", "sheet": "建筑升级"}, "FarmGridLevelUpConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场建筑表.xlsx", "sheet": "养殖地块升级"}, "FarmBuildingSkinConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场建筑表.xlsx", "sheet": "建筑皮肤"}, "FarmWarningConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场数据预警表.xlsx", "sheet": "每日获得资源阈值"}, "FarmSysConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场系统配置表.xlsx", "sheet": "系统配置"}, "FarmItemConf_Crop": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场道具表_作物.xlsx", "sheet": "作物道具"}, "FarmItemConf_Animal": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场道具表_动物.xlsx", "sheet": "动物道具"}, "FarmItemConf_Base": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场道具表_基础.xlsx", "sheet": "基础道具"}, "FarmItemConf_Building": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场道具表_建筑.xlsx", "sheet": "建筑道具"}, "FarmItemConf_Fish": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场道具表_钓鱼.xlsx", "sheet": "鱼类道具"}, "FarmFishConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "鱼类配置"}, "FarmFishBaitConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "鱼饵配置"}, "FarmFishScoreConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "鱼分配置"}, "FarmFishCardPackConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "鱼卡配置"}, "FarmFishCardPackDropConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "卡包掉落"}, "FarmFishLevelConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "熟练度升级配置"}, "FarmFishPeriodConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "收获周期配置"}, "FarmFishBowlConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "鱼缸配置"}, "FarmFishPoolLevelConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "鱼塘等级配置"}, "FarmFishPoolLayerConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "深度层数配置"}, "FarmFishHandbookAwardConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_农场钓鱼表.xlsx", "sheet": "鱼图鉴表"}, "NicknameWidthsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_昵称宽度表.xlsx", "sheet": "昵称宽度"}, "NicknameElementData_Adj": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_昵称词库.xlsx", "sheet": "形容词"}, "NicknameElementData_Aux": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_昵称词库.xlsx", "sheet": "连接词"}, "NicknameElementData_N": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_昵称词库.xlsx", "sheet": "名词"}, "NicknameElementData_Suffix": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\N_昵称词库.xlsx", "sheet": "后缀"}, "UGCAIBehaveRule": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_PUGC通用AI表.xlsx", "sheet": "表情调用概率"}, "UGCAIDifficultyParam": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_PUGC通用AI表.xlsx", "sheet": "AI等级设置"}, "UGCAIMapToLevel": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_PUGC通用AI表.xlsx", "sheet": "UGCAI关卡映射"}, "TYCSkinConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_TYC皮肤.xlsx", "sheet": "皮肤"}, "MatchAiLabGrpWarmRoundScoreCaseData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配AiLab对照组配置.xlsx", "sheet": "AiLab对照组匹配温暖局分数"}, "MatchAiLabCGrpWarmRoundAILevel": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配AiLab对照组配置.xlsx", "sheet": "AiLab对照组温暖局AI强度"}, "MatchAiLabCGrpWarmRoundLimitData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配AiLab对照组配置.xlsx", "sheet": "AiLab对照组温暖局触发限制"}, "MatchAiLabCGrpAIRandomCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配AiLab对照组配置.xlsx", "sheet": "AiLab对照组AI随机表"}, "MatchAiLabCGrpDynamicTeamRobotsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配AiLab对照组配置.xlsx", "sheet": "AiLab对照组动态AI配置"}, "MatchAiLabCGrpExpandScopeLimitData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配AiLab对照组配置.xlsx", "sheet": "AiLab对照组维度上下限限制"}, "MatchMMRSelfRankSToChangeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配MMR.xlsx", "sheet": "MMR结算个人排名静态映射"}, "MatchMmrDegreeMapQualifyingData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配MMR.xlsx", "sheet": "MMR类型段位类型映射"}, "MatchSpecSideMapMmrData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配MMR.xlsx", "sheet": "MMR玩法阵营映射"}, "MatchMMRBattleRoyalRankBaseScoreData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配MMR.xlsx", "sheet": "BR玩法基础分配置"}, "MatchTypeSideMMRWinExpData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配MMR.xlsx", "sheet": "MMR玩法阵营胜率修正"}, "MatchMmrSideOrRoleMinDimenScoreData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配MMR.xlsx", "sheet": "MMR预先阵营最低分修正"}, "MatchMmrAttenuationData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配MMR.xlsx", "sheet": "MMR衰减"}, "MatchWarmRoundMmrCorrectingData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配MMR与温暖局.xlsx", "sheet": "温暖局MMR分修正"}, "MatchMWConstsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配MMR与温暖局.xlsx", "sheet": "匹配MMR与温暖局常量"}, "MatchRuleData_acm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_acm.xlsx", "sheet": "规则"}, "MatchRoomInfoData_acm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_acm.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_acm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_acm.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_acm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_acm.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_acm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_acm.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchRuleData_arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_arena.xlsx", "sheet": "规则"}, "MatchRoomInfoData_arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_arena.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_arena.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_arena.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_arena.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_arena.xlsx", "sheet": "特殊局虚拟匹配房间"}, "MatchRuleData_BS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_BS.xlsx", "sheet": "规则"}, "MatchRoomInfoData_BS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_BS.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_BS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_BS.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_BS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_BS.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_BS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_BS.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_BS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_BS.xlsx", "sheet": "特殊局虚拟匹配房间"}, "MatchRuleData_chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_chase.xlsx", "sheet": "规则"}, "MatchRoomInfoData_chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_chase.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_chase.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_chase.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_chase.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchRuleData_competition": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_competition.xlsx", "sheet": "规则"}, "MatchRoomInfoData_competition": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_competition.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_competition": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_competition.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_competition": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_competition.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_competition": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_competition.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchRuleData_dnd": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_dnd.xlsx", "sheet": "规则"}, "MatchRoomInfoData_dnd": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_dnd.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_dnd": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_dnd.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_dnd": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_dnd.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_dnd": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_dnd.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchRuleData_fps": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_fps.xlsx", "sheet": "规则"}, "MatchRoomInfoData_fps": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_fps.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_fps": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_fps.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_fps": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_fps.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_fps": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_fps.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchRuleData_JS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_JS.xlsx", "sheet": "规则"}, "MatchRoomInfoData_JS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_JS.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_JS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_JS.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_JS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_JS.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_JS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_JS.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchRuleData_Mayday": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_Mayday.xlsx", "sheet": "规则"}, "MatchRoomInfoData_Mayday": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_Mayday.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_Mayday": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_Mayday.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_Mayday": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_Mayday.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_Mayday": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_Mayday.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchRuleData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_nr3e.xlsx", "sheet": "规则"}, "MatchRoomInfoData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_nr3e.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_nr3e.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_nr3e.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_nr3e.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchSideLimitConfigData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_nr3e.xlsx", "sheet": "阵营人数限制"}, "MatchRuleData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_ugc.xlsx", "sheet": "规则"}, "MatchRoomInfoData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_ugc.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_ugc.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_ugc.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_ugc.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchFilterValTypeDimeExpandData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_ugc.xlsx", "sheet": "过滤器值型维度放宽规则"}, "MatchDimensionData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "维度配置"}, "MatchConstsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "匹配常量"}, "MatchSvrDevAllocData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "匹配服测试环境分配表"}, "MatchSvrAllocData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "匹配服正式环境分配表"}, "MatchDynamicTeamPlayersData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "对局真人数动态规则_后面为分表"}, "MatchRuleData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "规则"}, "MatchRoomInfoData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "房间配置"}, "MatchDynamicTeamRobotsData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "动态AI配置"}, "MatchDynamicMaxTimeoutData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "动态最大超时配置"}, "MatchSimulatorDynamicMaxTimeoutData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "模拟器动态最大超时配置"}, "MatchFilterValTypeDimeExpandData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "过滤器值型维度放宽规则"}, "MatchRouteWhiteListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_主表.xlsx", "sheet": "匹配路由白名单"}, "MatchTeamSizeMatchingRuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配_特殊撮合规则.xlsx", "sheet": "队伍人数撮合特殊规则"}, "MatchFillBackResData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配回填配置.xlsx", "sheet": "匹配回填配置"}, "MatchWarmRoundSelfRankSToChangeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配温暖局.xlsx", "sheet": "温暖分结算个人排名静态映射"}, "SecondaryGameplayNewbieWarmRoundData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配温暖局.xlsx", "sheet": "副玩法特殊新手温暖局配置"}, "MatchWarmRoundLimitData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配温暖局.xlsx", "sheet": "温暖局触发限制"}, "MatchWarmRoundScoreCaseData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配温暖局.xlsx", "sheet": "匹配温暖局分数"}, "MatchWarmRoundAILevel": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配温暖局.xlsx", "sheet": "温暖局AI强度"}, "MatchExpandScopeLimitData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_匹配维度上下限限制.xlsx", "sheet": "维度上下限限制"}, "BlockingWordData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_屏蔽词库.xlsx", "sheet": "屏蔽词库"}, "CameraFilter": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_拍照滤镜配置.xlsx", "sheet": "Sheet1"}, "PushFaceData_Total": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_拍脸.xlsx", "sheet": "拍脸内容"}, "QDTSeasonCfgData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "赛季相关"}, "SeasonQualifyingMailData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "赛季重置邮件奖励"}, "QDTDegreeTypeData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "关卡维度配置"}, "QDTEspecialIntegralData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "局外得分"}, "QDTQualifyingInheritData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "段位继承配置"}, "ProtectedScoreData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_acm_daluandou": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_acm_大乱斗.xlsx", "sheet": "保分加分"}, "QDTSeasonCfgData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "赛季相关"}, "SeasonQualifyingMailData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "赛季重置邮件奖励"}, "QDTDegreeTypeData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "关卡维度配置"}, "QDTEspecialIntegralData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "局外得分"}, "QDTQualifyingInheritData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "段位继承配置"}, "ProtectedScoreData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_chase_dawangbiezhuawo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_chase_大王别抓我.xlsx", "sheet": "保分加分"}, "QDTSeasonCfgData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "赛季相关"}, "SeasonQualifyingMailData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "赛季重置邮件奖励"}, "QDTDegreeTypeData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "关卡维度配置"}, "QDTEspecialIntegralData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "局外得分"}, "QDTQualifyingInheritData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "段位继承配置"}, "ProtectedScoreData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_fps_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_武器大师.xlsx", "sheet": "保分加分"}, "QDTSeasonCfgData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "赛季相关"}, "SeasonQualifyingMailData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "赛季重置邮件奖励"}, "QDTDegreeTypeData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "关卡维度配置"}, "QDTEspecialIntegralData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "局外得分"}, "QDTQualifyingInheritData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "段位继承配置"}, "ProtectedScoreData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_fps_tuweimenghuandao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_fps_突围梦幻岛.xlsx", "sheet": "保分加分"}, "QDTSeasonCfgData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "赛季相关"}, "SeasonQualifyingMailData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "赛季重置邮件奖励"}, "QDTDegreeTypeData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "关卡维度配置"}, "QDTEspecialIntegralData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "局外得分"}, "QDTQualifyingInheritData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "段位继承配置"}, "ProtectedScoreData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_js_jisufeiche": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_JS_极速飞车.xlsx", "sheet": "保分加分"}, "QDTSeasonCfgData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "赛季相关"}, "SeasonQualifyingMailData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "赛季重置邮件奖励"}, "QDTDegreeTypeData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "关卡维度配置"}, "QualifyingLevelDimensionIdData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "维度集合配置"}, "QDTEspecialIntegralData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "局外得分"}, "QDTQualifyingInheritData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "段位继承配置"}, "ProtectedScoreData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_lightning_jiangbeizhengduo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "保分加分"}, "RankingSnapshotRewardConfData_Lightning": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "榜单结算奖励"}, "QualifyingIntegralSettlementData_Lightning": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "结算方式"}, "QualifyingContinueWinData_Lightning": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_lightning_闪电赛.xlsx", "sheet": "连胜奖励"}, "QDTSeasonCfgData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "赛季相关"}, "SeasonQualifyingMailData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "赛季重置邮件奖励"}, "QDTDegreeTypeData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "关卡维度配置"}, "QDTEspecialIntegralData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "局外得分"}, "QDTQualifyingInheritData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "段位继承配置"}, "ProtectedScoreData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_Moba": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_Moba.xlsx", "sheet": "保分加分"}, "QDTSeasonCfgData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "赛季相关"}, "SeasonQualifyingMailData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "赛季重置邮件奖励"}, "QDTDegreeTypeData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "关卡维度配置"}, "QualifyingLevelDimensionIdData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "维度集合配置"}, "QDTQualifyingInheritData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "段位继承配置"}, "QDTEspecialIntegralData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "局外得分"}, "ProtectedScoreData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_nr3e_wodixingdong": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_卧底行动.xlsx", "sheet": "保分加分"}, "QDTSeasonCfgData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "赛季相关"}, "SeasonQualifyingMailData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "赛季重置邮件奖励"}, "QDTDegreeTypeData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "关卡维度配置"}, "QualifyingLevelDimensionIdData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "维度集合配置"}, "QDTEspecialIntegralData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "局外得分"}, "QDTQualifyingInheritData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "段位继承配置"}, "ProtectedScoreData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_nr3e_sheishilangren": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_谁是狼人.xlsx", "sheet": "保分加分"}, "QDTSeasonCfgData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "赛季相关"}, "SeasonQualifyingMailData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "赛季重置邮件奖励"}, "QDTDegreeTypeData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "关卡维度配置"}, "QualifyingLevelDimensionIdData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "维度集合配置"}, "QDTEspecialIntegralData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "局外得分"}, "QDTQualifyingInheritData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "段位继承配置"}, "ProtectedScoreData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_nr3e_duomaomao": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位&保分配置_nr3e_躲猫猫.xlsx", "sheet": "保分加分"}, "QDTSeasonCfgData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位_main_主玩法.xlsx", "sheet": "赛季相关"}, "QDTDegreeTypeData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位_main_主玩法.xlsx", "sheet": "段位信息"}, "RankKingDegreeData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位_main_主玩法.xlsx", "sheet": "王者段位"}, "QDTQualifyingIntegralData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位_main_主玩法.xlsx", "sheet": "通关积分"}, "QualifyingLevelScoreData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位_main_主玩法.xlsx", "sheet": "关卡积分"}, "QualifyingLevelDimensionScoreData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位_main_主玩法.xlsx", "sheet": "关卡维度积分"}, "QualifyingLevelDimensionConditionData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位_main_主玩法.xlsx", "sheet": "关卡维度配置"}, "QDTEspecialIntegralData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位_main_主玩法.xlsx", "sheet": "局外得分"}, "QDTQualifyingInheritData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位_main_主玩法.xlsx", "sheet": "段位继承配置"}, "ProtectedScoreData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位保分配置_main_主玩法.xlsx", "sheet": "排位关卡保分"}, "ProtectedScoreWinData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位保分配置_main_主玩法.xlsx", "sheet": "排位夺冠保分"}, "ProtectedScoreWinStreakData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位保分配置_main_主玩法.xlsx", "sheet": "连胜保分"}, "ProtectedScoreAdditionalData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位段位保分配置_main_主玩法.xlsx", "sheet": "保分加分"}, "MatchDegreeTypeGroupData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位玩法分组.xlsx", "sheet": "玩法排位分组"}, "QDTQualifyingScoreData_main_zhuwanfa": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排位积分文本Key_main_主玩法.xlsx", "sheet": "排位积分"}, "RankingDisplayConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排行榜.xlsx", "sheet": "展示配置"}, "RankingSnapshotRewardConfData_Common": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排行榜.xlsx", "sheet": "榜单奖励"}, "RankingRewardConfData_Activity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排行榜.xlsx", "sheet": "榜单奖励_Legacy"}, "RankingRuleConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排行榜.xlsx", "sheet": "排序规则"}, "RankingConfData_Common": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排行榜.xlsx", "sheet": "排行榜单_常规"}, "RankingConfData_Qualify": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排行榜.xlsx", "sheet": "排行榜单_段位"}, "RankingConfData_Ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排行榜.xlsx", "sheet": "排行榜单_UGC"}, "RankingConfData_Level": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排行榜.xlsx", "sheet": "排行榜单_关卡"}, "RankingConfData_Activity": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排行榜.xlsx", "sheet": "排行榜单_活动"}, "RankingConfData_PlayMode": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\P_排行榜.xlsx", "sheet": "排行榜单_玩法"}, "AddQQFriendWhiteList": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_QQ好友申请白名单.xlsx", "sheet": "Sheet1"}, "QqGroupTeamPlayConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_qq群组队玩法配置.xlsx", "sheet": "玩法配置"}, "PilotEntranceData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_庆典导航.xlsx", "sheet": "入口配置"}, "PilotTabData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_庆典导航.xlsx", "sheet": "活动配置"}, "ChannelEntranceData_WeChat": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_渠道入口配置.xlsx", "sheet": "渠道入口配置-微信"}, "ChannelEntranceData_ForQQ": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_渠道入口配置.xlsx", "sheet": "渠道入口配置-手Q"}, "ChannelDisableSceneData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_渠道入口配置.xlsx", "sheet": "渠道入口屏蔽场景"}, "PrayData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_祈福.xlsx", "sheet": "结果"}, "PrayPropertyData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_祈福.xlsx", "sheet": "词条"}, "PrayLunarCalendarConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_祈福.xlsx", "sheet": "农历词条"}, "PrayLunarSpecialConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Q_祈福.xlsx", "sheet": "农历特殊词条"}, "RightUpInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_RightUpInfo.xlsx", "sheet": "页签排序"}, "RightUpOrderConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_RightUpInfo.xlsx", "sheet": "时间控制"}, "TaskGroupData_wxGameDayTask": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_小游戏每日胜局奖励.xlsx", "sheet": "任务组"}, "TaskConfData_wxGameDayTask": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_小游戏每日胜局奖励.xlsx", "sheet": "任务"}, "TaskGroupListData_wxGameTaskGroupListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_小游戏每日胜局奖励.xlsx", "sheet": "任务组合集"}, "TaskGroupData_wxGameShare_WX": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_微信游戏圈分享.xlsx", "sheet": "任务组"}, "TaskConfData_wxGameShare_WX": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_微信游戏圈分享.xlsx", "sheet": "微信分享触发任务"}, "WXGameShareTaskShareContentData_WX": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_微信游戏圈分享.xlsx", "sheet": "任务对应微信素材"}, "TaskTabData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "页签顺序"}, "TaskConfData_newbie": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "新手任务"}, "TaskConfData_common": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "日常周常任务"}, "TaskConfData_level": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "等级任务"}, "TaskConfData_season": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "赛季任务"}, "TaskConfData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "UGC任务"}, "TaskConfData_achievement": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "成就任务"}, "TaskConfData_mode": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "经典模式每日通关任务"}, "TaskConfData_collection": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "时装图鉴任务"}, "TaskConfData_back": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "回归任务"}, "TaskConfData_download": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "下载任务"}, "TaskConfData_channel": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "平台渠道任务"}, "NewbieTaskMiscData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "新手任务杂项"}, "TaskConfData_star": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "追梦星途"}, "TaskGroupData_common": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "任务组"}, "UgcCreativeRoadData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "创作之路"}, "TaskConfData_qrcode": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用.xlsx", "sheet": "合家欢任务"}, "TaskConfData_bp": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_策划专用_BP.xlsx", "sheet": "BP任务"}, "TaskConfData_season_lightning": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_闪电赛专用.xlsx", "sheet": "闪电赛任务"}, "TaskGroupData_common_lightning": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_任务表_闪电赛专用.xlsx", "sheet": "任务组"}, "ResRoguelikeTaskData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽任务配置.xlsx", "sheet": "任务表"}, "ResRoguelikeTaskOtherData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽任务配置.xlsx", "sheet": "任务其他数据"}, "ResRoguelikeMarkBaseEffectData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽印记生成表.xlsx", "sheet": "印记基础效果表"}, "ResRoguelikeMarkEntryData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽印记生成表.xlsx", "sheet": "印记词条表"}, "ResRoguelikeMarkSpecialEffectData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽印记生成表.xlsx", "sheet": "印记特殊效果表"}, "ResRoguelikeMarkShapeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽印记生成表.xlsx", "sheet": "印记形状表"}, "ResRoguelikeMarkBoardShapeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽印记生成表.xlsx", "sheet": "印记板形状表"}, "ResRoguelikeMarkLevelDropData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽印记生成表.xlsx", "sheet": "印记关卡掉落"}, "ResRoguelikeMarkBoxData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽印记生成表.xlsx", "sheet": "印记随机池"}, "ResRoguelikeExclusiveMarkData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽印记生成表.xlsx", "sheet": "专有印记配置表"}, "ResRoguelikeMarkMaterialData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽印记生成表.xlsx", "sheet": "印记升级分解耗材表"}, "ResRoguelikeTalentData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽天赋配置.xlsx", "sheet": "天赋表"}, "ResRoguelikeGetTalentData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽天赋配置.xlsx", "sheet": "天赋结算表"}, "ResRoguelikeMonsterData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽战斗NPC表.xlsx", "sheet": "怪物属性表"}, "ResRoguelikeMonsterHandBookData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽战斗NPC表.xlsx", "sheet": "怪物属性图鉴显示表"}, "ResRoguelikeMonsterParameter": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽战斗NPC表.xlsx", "sheet": "怪物数值系数表"}, "ResRoguelikeMonsterHPParameter": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽战斗NPC表.xlsx", "sheet": "无尽关卡怪物血量系数表"}, "ResRoguelikeSkillData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽技能配置.xlsx", "sheet": "Sheet1"}, "ResRoguelikeLevelSelectData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽玩法表.xlsx", "sheet": "关卡随机表"}, "ResRoguelikeLevelData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽玩法表.xlsx", "sheet": "关卡生成表"}, "ResRoguelikeProcessData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽玩法表.xlsx", "sheet": "关卡流程表"}, "ResRoguelikeEventData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽玩法表.xlsx", "sheet": "关卡事件表"}, "ResRoguelikeCharacterGradeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽玩法表.xlsx", "sheet": "角色等级表"}, "ResRoguelikeSpecialLevelData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽玩法表.xlsx", "sheet": "特殊关卡参数配置表"}, "ResRoguelikeEndlessLevelData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽玩法表.xlsx", "sheet": "无尽模式关卡参数表"}, "ResRoguelikeSeasonData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽赛季配置.xlsx", "sheet": "赛季配置"}, "ResRoguelikeSeasonMilestoneData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽赛季配置.xlsx", "sheet": "里程碑"}, "ResRoguelikeSeasonDayTaskData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽赛季配置.xlsx", "sheet": "每日任务"}, "ResRoguelikeSeasonWeekTaskData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽赛季配置.xlsx", "sheet": "每周任务"}, "ResRoguelikeSeasonTaskData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽赛季配置.xlsx", "sheet": "赛季任务"}, "ResRoguelikeEndlessRewardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽赛季配置.xlsx", "sheet": "无尽关卡奖励"}, "ResRoguelikeChestData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽道具配置表.xlsx", "sheet": "宝箱表"}, "ResRoguelikePropsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\R_肉鸽道具配置表.xlsx", "sheet": "道具表"}, "TYCCrystalsStoreConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_TYC商城.xlsx", "sheet": "水晶商店"}, "MallCommodityConf_Arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_Arena.xlsx", "sheet": "商城-Arena"}, "MallCommodityConf_NR3E3": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_NR3E.xlsx", "sheet": "狼人杀兑换商店"}, "MallCommodityConf_NR3E3_DirectPurchase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_NR3E.xlsx", "sheet": "狼人杀直购"}, "ResMallNR3EShopTab": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_NR3E.xlsx", "sheet": "商店标签配置"}, "MallCommodityConf_Common": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_兑换.xlsx", "sheet": "兑换商品"}, "MallCommodityConf_ActivityNormalExchange": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_兑换.xlsx", "sheet": "【运营活动】常规兑换"}, "MallCommodityConf_ActivityChapterTaskExchange": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_兑换.xlsx", "sheet": "【运营活动】章节任务"}, "MallCommodityConf_ActivityBigExchange": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_兑换.xlsx", "sheet": "【运营活动】大奖常规兑换"}, "MallCommodityConf_ActivityTakeawayExchange": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_兑换.xlsx", "sheet": "【运营活动】挂机外卖"}, "MallCommodityConf_ActivitySpringFestival": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_兑换.xlsx", "sheet": "【运营活动】春节庆典"}, "MallCommodityConf_ReturnFriendshipFire": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_兑换.xlsx", "sheet": "【回流】友谊火种"}, "MallInfoConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_兑换类型.xlsx", "sheet": "商城和兑换类型"}, "MallCommodityConf_FarmItem": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_农场.xlsx", "sheet": "商城-道具"}, "MallRecommendPageConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-推荐页"}, "MallCommodityConf_Suit": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-套装"}, "MallCommodityConf_UpperGarment": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-上装"}, "MallCommodityConf_LowerGarment": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-下装"}, "MallCommodityConf_Gloves": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-手套"}, "MallCommodityConf_HeadWear": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-头饰"}, "MallCommodityConf_FaceOrnament": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-面饰"}, "MallCommodityConf_BackOrnament": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-背饰"}, "MallCommodityConf_HandOrnament": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-手部装扮"}, "MallCommodityConf_Faces": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-脸部"}, "MallCommodityConf_Emoji": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-表情"}, "MallCommodityConf_Movement": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-单人动作"}, "MallCommodityConf_PairMovement": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-双人动作"}, "MallCommodityConf_InteractiveProp": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-互动道具"}, "MallCommodityConf_Vehicle": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-载具"}, "MallCommodityConf_Item": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-道具"}, "MallCommodityConf_Hot": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城-热销"}, "MallCommodityConf_AvatarGift": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "avatar礼包（新）"}, "ShopTagForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_商品.xlsx", "sheet": "商城标签"}, "ResMallSeasonShopTab": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_赛季兑换.xlsx", "sheet": "商店标签配置"}, "MallCommodityConf_SeasonShop": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_赛季兑换.xlsx", "sheet": "兑换商品【赛季商店】"}, "MallCommodityConf_Intimate": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_商城_默契商店.xlsx", "sheet": "兑换商品【默契商店】"}, "ShootBuffConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_射击Buff表.xlsx", "sheet": "客户端"}, "ShootBuffConfigDataUGCSkill": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_射击Buff表_UGCRPG.xlsx", "sheet": "客户端"}, "ShootEventConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_射击事件表.xlsx", "sheet": "客户端"}, "BioChaseSkillCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_生化追击技能配置.xlsx", "sheet": "抓企鹅玩法配置"}, "BioChaseSkillCfgData_Fight": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_生化追击技能配置_对决.xlsx", "sheet": "抓企鹅玩法配置"}, "BioChaseRuleCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_生化追击玩法规则配置.xlsx", "sheet": "抓企鹅玩法配置"}, "BioChaseRoleCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_生化追击角色配置.xlsx", "sheet": "抓企鹅玩法配置"}, "BioChaseRoleCfgData_Fight": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_生化追击角色配置_对决.xlsx", "sheet": "抓企鹅玩法配置"}, "SceneConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_社交场景.xlsx", "sheet": "场景配置"}, "SceneMapBirthConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_社交场景.xlsx", "sheet": "玩家出生点"}, "LiveDirectorTimerConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_社交场景.xlsx", "sheet": "视频流刷新时间配置"}, "SocialPerformanceData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_社交表演.xlsx", "sheet": "社交表演配置"}, "CommunityConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_社群配置表.xlsx", "sheet": "社群入口"}, "SettingRightsConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_设置权限配置.xlsx", "sheet": "隐私权限"}, "CustomerServiceEntries": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_设置权限配置.xlsx", "sheet": "客服入口配置"}, "CompetitionRankRewardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_赛事奖励表.xlsx", "sheet": "赛事排名奖励"}, "CompetitionScoreRewardData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_赛事奖励表.xlsx", "sheet": "赛事积分奖励"}, "SeasonConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_赛季.xlsx", "sheet": "赛季总表"}, "SeasonSubmoduleConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_赛季.xlsx", "sheet": "赛季子模块配置表"}, "SeasonTalkConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_赛季.xlsx", "sheet": "赛季对话"}, "SeasonFashionMetaBattleDataConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_赛季.xlsx", "sheet": "时尚手册战斗元数据"}, "SeasonFashionBattleDataConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_赛季.xlsx", "sheet": "时尚手册战斗数据"}, "SeasonFashionGameTimesShowConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_赛季.xlsx", "sheet": "战斗次数数据展示"}, "SeasonFashionModelDataConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_赛季.xlsx", "sheet": "模式数据"}, "SeasonFashionSubGroupIdConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_赛季.xlsx", "sheet": "子组ID数据"}, "LightningGameSurvivalData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_闪电战配置表.xlsx", "sheet": "关卡晋级"}, "LightningGameTipData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_闪电战配置表.xlsx", "sheet": "闪电战提示文本"}, "ClientKVConfForLetsGo_Lightning": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_闪电战配置表.xlsx", "sheet": "杂项配置"}, "LightningTimeTextData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\S_闪电战配置表.xlsx", "sheet": "时间文本配置"}, "OMDRatingConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_OMD等级评价表.xlsx", "sheet": "评价项"}, "OMDScoreToEvaluationConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_OMD等级评价表.xlsx", "sheet": "积分评价"}, "TABTestData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_TAB实验配置.xlsx", "sheet": "TABTest配置"}, "HotResOverData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_Test管理端覆盖.xlsx", "sheet": "管理端覆盖测试"}, "TYCTowerClientConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_TYC塔.xlsx", "sheet": "野怪表"}, "TYCRatingConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_TYC等级评价表.xlsx", "sheet": "评价项"}, "SubConditionParamListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_条件列表.xlsx", "sheet": "子条件参数"}, "SpecRewardConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_特殊单次奖励配置.xlsx", "sheet": "特殊奖励配置"}, "JumpConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_跳转功能表.xlsx", "sheet": "Sheet1"}, "JumpToShowFingerData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_跳转显示手指.xlsx", "sheet": "跳转手指"}, "PermitRewardConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_通行证.xlsx", "sheet": "通行证等级奖励"}, "PermitTaskConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_通行证.xlsx", "sheet": "通行证任务配置值"}, "PermitConstantConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\T_通行证.xlsx", "sheet": "通行证常量配置"}, "UGCNPCInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGCNPC模板信息表.xlsx", "sheet": "模板"}, "UGCRoundGamePropsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGCRoundGame道具.xlsx", "sheet": "道具配置"}, "UGCAIBuildTypeConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_AI生成_建筑.xlsx", "sheet": "建筑类型配置"}, "UGCAIBuildFoundationConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_AI生成_建筑.xlsx", "sheet": "地基预设"}, "UGCAIBuildColorConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_AI生成_建筑.xlsx", "sheet": "配色方案"}, "UGCAIBuildItemConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_AI生成_建筑.xlsx", "sheet": "元件类型"}, "UGCAIBuildParameterConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_AI生成_建筑.xlsx", "sheet": "参数类型"}, "UGCAITypeSectionConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_AI生成_通用.xlsx", "sheet": "AIType分段"}, "UGCAIMagicGraphConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_AI生成_魔法图片.xlsx", "sheet": "Sheet1"}, "UGCNPCAppearance_Suit": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_NPC外观表.xlsx", "sheet": "套装"}, "UGCNPCAppearance_UpperGarment": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_NPC外观表.xlsx", "sheet": "上装"}, "UGCNPCAppearance_LowerGarment": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_NPC外观表.xlsx", "sheet": "下装"}, "UGCNPCAppearance_Gloves": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_NPC外观表.xlsx", "sheet": "手套"}, "UGCNPCAppearance_FaceOrnament": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_NPC外观表.xlsx", "sheet": "面饰"}, "UGCNPCAppearance_HeadWear": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_NPC外观表.xlsx", "sheet": "头饰"}, "UGCNPCAppearance_BackOrnament": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_NPC外观表.xlsx", "sheet": "背饰"}, "UGCNPCAppearance_Face": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_NPC外观表.xlsx", "sheet": "脸部"}, "UGCDialogue_Face": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_剧情编辑配置表.xlsx", "sheet": "面部表情"}, "UGCDialogue_Animation": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_剧情编辑配置表.xlsx", "sheet": "角色动作"}, "UGCDialogue_Emoji": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_剧情编辑配置表.xlsx", "sheet": "表情图标"}, "UGCDialogue_box": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_剧情编辑配置表.xlsx", "sheet": "聊天框配置"}, "UGCDialogue_Sound": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_剧情编辑配置表.xlsx", "sheet": "音色配置表"}, "ResUGCOfiicialAnim": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_官方动作配置表.xlsx", "sheet": "Sheet1"}, "ConcertConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_演唱会.xlsx", "sheet": "演唱会配置"}, "ConcertStarConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_演唱会.xlsx", "sheet": "明星"}, "ConcertExpressionConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_演唱会.xlsx", "sheet": "应援表情"}, "ConcertLightBoardConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_演唱会.xlsx", "sheet": "应援牌样式"}, "ConcertTimesConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_演唱会.xlsx", "sheet": "场次时间"}, "UGCCameraPreset_Main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_相机预设.xlsx", "sheet": "默认相机"}, "UGCCameraPreset_Combine": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_相机预设_Combine.xlsx", "sheet": "默认相机"}, "UGCCameraPreset_FPS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_相机预设_FPS.xlsx", "sheet": "默认相机"}, "UGCCustomEquipType": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC_装备.xlsx", "sheet": "装备类型"}, "UgcNavBaoDiConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC保底配置.xlsx", "sheet": "导航栏保底配置"}, "UgcPathConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC关卡路径配置.xlsx", "sheet": "UGC地图路径配置"}, "UgcImageBagTab": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC图片管理.xlsx", "sheet": "UgcImageBagTabs"}, "UGCUIEditorBagTabs": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC图片管理.xlsx", "sheet": "UgcUIEditBagTabs"}, "UGCImageBagInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC图片管理.xlsx", "sheet": "UgcImage"}, "UGCUIEditorBagInfo_UIProgress": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC图片管理.xlsx", "sheet": "UgcUIProgress"}, "UGCUIEditorBagInfo_UITEM": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC图片管理.xlsx", "sheet": "UgcUITemplate"}, "UGCUIEditorBagInfo_Functions": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC图片管理.xlsx", "sheet": "UgcUIFunctions"}, "UGCMapDescConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC地图描述.xlsx", "sheet": "Ugc地图描述"}, "UgcMapAwardTagConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC地图标签.xlsx", "sheet": "地图标签1"}, "UgcMapScoreLabelConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC地图评分.xlsx", "sheet": "地图评分标签"}, "ScenePerformanceLevelData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC地图评级与屏蔽表.xlsx", "sheet": "机型对应场景性能等级表"}, "ScenePerformanceOperatingData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC地图评级与屏蔽表.xlsx", "sheet": "地图屏蔽与弹窗表"}, "ScenePublishRiskRemind": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC地图评级与屏蔽表.xlsx", "sheet": "发布风险提醒表"}, "UGCTextHighConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC官方地图文本框控制_海外.xlsx", "sheet": "文本框控制"}, "UGCSkillEditorSkillIcon": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "UGCSkillEditorSkillIcon"}, "UGCSkillEditorBuffIcon": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "UGCBuff图标"}, "UGCSkillEditorBagTab": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "UGCSkillEditorBagTab"}, "UGCSkillEditorProjectile": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "UGC技能编辑器投掷物资产"}, "UGCSkillEditorSummon": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "UGC技能编辑器召唤资产表"}, "UGCSkillEditorAnimation": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "技能动画资产表"}, "UGCSkillEditorGetItem": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "UGC技能编辑器道具表"}, "UGCSkillEditorPreviewCharacter": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "预览角色选择表"}, "UGCSkillEditorPreviewWeapon": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "预览武器选择表"}, "UGCSkillIndicatorData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "UGC技能指示器"}, "UGCSkillEditorPreviewBg": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC技能编辑器.xlsx", "sheet": "预览背景选择表"}, "UGCAudioInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC效果器.xlsx", "sheet": "UGC音频配置"}, "UGCParticleInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC效果器.xlsx", "sheet": "UGC特效配置"}, "UGCAudioTabInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC效果器.xlsx", "sheet": "UGC音频分类"}, "UGCParticleTabInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC效果器.xlsx", "sheet": "UGC特效分类"}, "UgcStarWorldDifficultyGroup": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC新版星海巡游.xlsx", "sheet": "金奖励难度组合配置"}, "UgcStarWorldLevelConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC新版星海巡游.xlsx", "sheet": "星球配置"}, "UgcStarWorldDifficultyReward": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC新版星海巡游.xlsx", "sheet": "奖励配置"}, "PlayerUgcLevelConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC星世界等级.xlsx", "sheet": "星世界等级配置"}, "PlayerUgcLevelEnumConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC星世界等级.xlsx", "sheet": "常量配置"}, "PlayerUgcActivityDegreeGetRatioConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC星世界等级.xlsx", "sheet": "活跃度获取系数配置"}, "UgcImageDisplayData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC显示器.xlsx", "sheet": "图标显示器图标表"}, "UGCEditorWeaponAction": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC武器动作资源表.xlsx", "sheet": "武器动作资产表"}, "UgcRocketBulletSelectorData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC火箭炮弹选择器.xlsx", "sheet": "UGC火箭炮弹发射器"}, "AigcDefaultPromptData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC灵感配置.xlsx", "sheet": "预设提示词配置"}, "AigcDefaultThemeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC灵感配置.xlsx", "sheet": "预设换色主题"}, "UgcBulletSelectorData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC炮弹选择器.xlsx", "sheet": "UGC发射器"}, "UGCActorInfo_Construction": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表.xlsx", "sheet": "积木"}, "UGCActorInfo_Stratagem": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表.xlsx", "sheet": "玩法"}, "UGCActorInfo_Nature": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表.xlsx", "sheet": "自然"}, "UGCActorInfo_Building": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表.xlsx", "sheet": "建筑"}, "UGCActorInfo_Decorate": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表.xlsx", "sheet": "装扮"}, "UGCActorInfo_Furniture": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表.xlsx", "sheet": "室内"}, "UGCActorInfo_Character": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表.xlsx", "sheet": "角色"}, "InPlayUGCActorInfo_Construction": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表_游玩.xlsx", "sheet": "积木"}, "InPlayUGCActorInfo_Stratagem": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表_游玩.xlsx", "sheet": "玩法"}, "InPlayUGCActorInfo_Nature": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表_游玩.xlsx", "sheet": "自然"}, "InPlayUGCActorInfo_Building": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表_游玩.xlsx", "sheet": "建筑"}, "InPlayUGCActorInfo_Decorate": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表_游玩.xlsx", "sheet": "装扮"}, "InPlayUGCActorInfo_Furniture": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表_游玩.xlsx", "sheet": "室内"}, "InPlayUGCActorInfo_Character": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC物件表_游玩.xlsx", "sheet": "角色"}, "UgcParticleMaterialData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC特效编辑.xlsx", "sheet": "特效编辑材质表"}, "UgcParticleParamNameToIndex": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC特效编辑.xlsx", "sheet": "特效参数映射"}, "UgcWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "配色白名单"}, "UgcAiImageWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "参考图白名单"}, "UgcCoCreateWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "共创白名单"}, "UgcAiAnicapWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "视频动捕白名单"}, "UgcAiVoiceWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "语音生成白名单"}, "UgcAiAnswerWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "NPC对话白名单"}, "UgcUpdateMapWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "二次发布白名单"}, "UgcVisualProgramWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "可视化编程白名单"}, "UgcCreateChatGroupWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "一键拉群白名单"}, "UgcDialogueImageWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "剧情编辑插入图片白名单"}, "UgcSkillEditorWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "技能编辑器白名单"}, "UgcBuyGoodsCreatorListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "UGC内购名单"}, "UgcCustomSkeletonAnimWhiteListConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单.xlsx", "sheet": "自定义骨骼动画白名单"}, "UgcWhiteListPlayerGroupConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单新.xlsx", "sheet": "用户组玩家名单"}, "UgcWhiteListModuleConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC白名单新.xlsx", "sheet": "开白配置"}, "UgcUploadConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC管理.xlsx", "sheet": "Sheet1"}, "UgcCommonConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC管理.xlsx", "sheet": "cdn"}, "UgcBucketConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC管理.xlsx", "sheet": "bucket"}, "UgcRoutePolarisInsConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC管理.xlsx", "sheet": "北极星配置"}, "UgcRouteInsConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC管理.xlsx", "sheet": "静态配置"}, "UgcCommandUrlConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC管理.xlsx", "sheet": "管理端地址"}, "UgcCosPathConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC管理.xlsx", "sheet": "COS路径"}, "UGCEditorMapTab": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UGCEditorMapTab"}, "UGCActorAdvancedInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "物件特性"}, "UGCCharacteristicsInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "特性枚举"}, "UGCEditorItemSort": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "物件排序"}, "UGCEditorPrefabTab": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "PrefabTab"}, "UGCEditorBagTab": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "BagTab"}, "UGCEditorItemTag": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "ItemTag"}, "UGCResourceInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "云资源信息"}, "UGCArtisanGradeInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "Ugc工匠等级占用值"}, "UGCMapCommonConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "Ugc地图常量"}, "UGCEditorGroupTag": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "Ugc组合Tags"}, "UGCEditorMapLoadingTemplate": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "Loading界面模板"}, "UGCRoomSetting": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UGCRoomSetting"}, "UGCEditorMapType": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapType"}, "UGCEditorMapGroup": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapGroup"}, "UGCEditorMapModule": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapModule"}, "UGCEditorMapProperty": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapProperty"}, "UGCEditorMapTag": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapTag"}, "UGCEditorMapTagBlock": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapTagBlock"}, "UGCUIEditorBagTab": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcUIEditBagTab"}, "UGCUIEditorBagInfo_Btn": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcUIButton"}, "UGCUIEditorBagInfo_TXT": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcUIText"}, "UGCUIEditorBagInfo_IMG": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcUIImage"}, "UGCUIEditorBagInfo_TEM": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcUITemplate"}, "UGCUIEditorBagInfo_Function": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcUIFunction"}, "UGCEditorMapTemplate": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapTemplate"}, "UGCEditorMapDataAutoSave": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapSettingAutoSave"}, "UGCEditorMapDataAutoSaveCloud": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapSettingAutoSaveCloud"}, "UGCEditorCameraSpeedOptions": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapSettingCamera"}, "UGCEditorBarrageTypeOptions": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcMapSettingBarrage"}, "UGCEditorQuickOperation": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UgcQuickOperation"}, "UGCEditorSignalInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC编辑器.xlsx", "sheet": "UGCEditorSignalInfo"}, "CustomAnimConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC自定义动画.xlsx", "sheet": "自定义动画映射"}, "CustomModelingMapConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC自定义造型物件映射.xlsx", "sheet": "映射数据"}, "UgcForbiddenTopicsData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC话题黑名单.xlsx", "sheet": "地图话题紧急黑名单"}, "UGCResConfNew": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC资源社区配置.xlsx", "sheet": "资源类型"}, "UGCResConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC资源社区配置.xlsx", "sheet": "资源类型(旧)"}, "UGCResConf_Category": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC资源社区配置.xlsx", "sheet": "资源大类"}, "UGCResConf_Subcategory": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC资源社区配置.xlsx", "sheet": "资源小类"}, "UGCResConf_Label": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC资源社区配置.xlsx", "sheet": "资源标签"}, "UgcPropItemConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC道具.xlsx", "sheet": "UGCItem"}, "UgcPropTabConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC道具.xlsx", "sheet": "BagTab"}, "UgcMelodyGroupConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "节奏单色音组配置"}, "UgcEffectTypeConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "音效音色类型配置"}, "UgcNameAdpterConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "音名转换配置"}, "UgcNameHighIconConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "高阶音名图标配置"}, "UgcNameSimpleIconConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "简谱音名图标配置"}, "UgcEffectWiseConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "音效Wise配置"}, "UgcRhythmWiseConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "节奏Wise配置"}, "UgcBassWiseConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "低音Wise配置"}, "UgcMelodyWiseConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "旋律Wise配置"}, "UgcEffectBankConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "音效Bank配置"}, "UgcBassBankConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "低音Bank配置"}, "UgcRhythmBankConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "节奏Bank配置"}, "UgcMelodyBankConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\U_UGC音乐触发器.xlsx", "sheet": "旋律Bank配置"}, "TDSWeaponLevelUpConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_TD武器升級.xlsx", "sheet": "客户端"}, "TYCWeaponClientConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_TYC武器配置.xlsx", "sheet": "TYC"}, "TYCWeaponAttrConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_TYC武器配置.xlsx", "sheet": "武器属性表"}, "TYCWeaponSkinData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_TYC武器配置.xlsx", "sheet": "武器皮肤表"}, "TYCItemChangeReasonConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_TYC物品用途表 .xlsx", "sheet": "TYC道具变化原因"}, "UGCWeaponSkinConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_UGC武器IP角色替换.xlsx", "sheet": "武器皮肤配置"}, "UGCWeaponSkinForIP": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_UGC武器IP角色替换.xlsx", "sheet": "IP角色皮肤组"}, "Equalizer": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_俄罗斯方块配置.xlsx", "sheet": "Sheet1"}, "TextEntryData_Level": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_关卡.xlsx", "sheet": "文本配置"}, "TextEntryRelationRecommend": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_关系链.xlsx", "sheet": "推荐理由"}, "TextEntryRelationApply": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_关系链.xlsx", "sheet": "添加理由"}, "TextEntryRelationCommunityFacilityID": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_关系链.xlsx", "sheet": "大厅设施名称"}, "TextEntryRelationDDPProp": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_关系链.xlsx", "sheet": "大乱斗道具名称"}, "TextEntryRelationDMMProp": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_关系链.xlsx", "sheet": "躲猫猫道具名称"}, "TextEntryXiaowo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_小窝.xlsx", "sheet": "文本配置"}, "ServerTextConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_提示.xlsm", "sheet": "文本"}, "ServerTextPatchConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_提示_服务器补丁.xlsm", "sheet": "服务器补丁文本"}, "TextEntryData_Main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置.xlsx", "sheet": "文本配置"}, "TextItemChangeReasonData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置.xlsx", "sheet": "道具变化文本"}, "TextEntryData_SuitStory": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置.xlsx", "sheet": "皮肤故事"}, "TextEntryData_AC": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_AC.xlsx", "sheet": "文本配置"}, "TextEntryData_Arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_Arena.xlsx", "sheet": "文本配置"}, "TextEntryData_chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_chase.xlsx", "sheet": "Sheet1"}, "TextEntryData_FPS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_FPS.xlsx", "sheet": "文本配置"}, "TextEntryNR3EData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_NR3E.xlsx", "sheet": "文本配置"}, "TextEntryNR3EData_Competition": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_NR3E_赛事.xlsx", "sheet": "文本配置"}, "TextEntryData_System": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_System.xlsx", "sheet": "文本配置"}, "TextEntryData_TYC": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_TYC.xlsx", "sheet": "文本配置"}, "TextEntryData_UGCRPG": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_UGCRPG.xlsx", "sheet": "文本配置"}, "TextEntryData_UGCProgramme": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_UGC可视化编程.xlsx", "sheet": "文本配置"}, "TextEntryData_Club": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_文本配置_社团.xlsx", "sheet": "文本配置"}, "ClientRuleTextData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_系统提示.xlsm", "sheet": "规则"}, "TextLuaData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_自动扫描生成_代码文本.xlsx", "sheet": "文本"}, "TextClientExcel": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_自动生成_Excel.xlsm", "sheet": "文本"}, "ResTextRuleDescData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_规则说明_主表.xlsm", "sheet": "规则"}, "ResTextRuleDescData_RedEnvelope": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_文本表_规则说明_红包雨.xlsm", "sheet": "规则"}, "FPSWeaponSkinConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_武器IP角色替换.xlsx", "sheet": "武器皮肤配置"}, "FPSWeaponSkinForIP": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_武器IP角色替换.xlsx", "sheet": "IP角色皮肤组"}, "FPSUISkinForIP": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_武器IP角色替换.xlsx", "sheet": "UI皮肤配置"}, "FPSWeaponAttachmentConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_武器配件配置.xlsx", "sheet": "武器配件表"}, "FPSWeaponAttachmentPositionConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_武器配件配置.xlsx", "sheet": "武器配件槽位表"}, "FPSWeaponExceptionAttachmentsConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_武器配件配置.xlsx", "sheet": "特殊武器配件关系表"}, "FPSWeaponClientConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_武器配置.xlsx", "sheet": "总表"}, "FPSWeaponPropertyConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_武器配置.xlsx", "sheet": "枪械配置"}, "FPSWeaponUnLockConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_武器配置.xlsx", "sheet": "枪械解锁条件"}, "FPSWeaponClientConfData_UGC": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_武器配置_ugc.xlsx", "sheet": "总表"}, "MatchWarmScoreData_Chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_温暖分_Chase.xlsx", "sheet": "温暖分"}, "PlayerGrayRuleConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩家灰度规则.xlsx", "sheet": "道具"}, "SelfStateData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩家状态.xlsx", "sheet": "自定义按键配置"}, "MatchTypeData_acm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_acm.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_acm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_acm.xlsx", "sheet": "玩法匹配"}, "MatchTypeData_arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_arena.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_arena": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_arena.xlsx", "sheet": "玩法匹配"}, "MatchTypeData_BS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_BS.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_BS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_BS.xlsx", "sheet": "玩法匹配"}, "MatchTypeData_Chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_Chase.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_Chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_Chase.xlsx", "sheet": "玩法匹配"}, "MatchTypeData_competition": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_competition.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_competition": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_competition.xlsx", "sheet": "玩法匹配"}, "MatchTypeData_dnd": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_dnd.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_dnd": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_dnd.xlsx", "sheet": "玩法匹配"}, "MatchTypeData_fps": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_fps.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_fps": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_fps.xlsx", "sheet": "玩法匹配"}, "MatchTypeData_JS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_JS.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_JS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_JS.xlsx", "sheet": "玩法匹配"}, "MatchTypeData_Mayday": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_Mayday.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_Mayday": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_Mayday.xlsx", "sheet": "玩法匹配"}, "MatchTypeData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_nr3e.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_nr3e.xlsx", "sheet": "玩法匹配"}, "MatchTypeData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_ugc.xlsx", "sheet": "玩法"}, "MatchRuleRangeData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_ugc.xlsx", "sheet": "玩法匹配"}, "GameRuleConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_ugc.xlsx", "sheet": "UGC玩法规则"}, "UgcMatchRoomRuleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_ugc.xlsx", "sheet": "UGC房间匹配规则"}, "UgcMatchRoomParamData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_ugc.xlsx", "sheet": "UGC匹配大厅参数"}, "MatchTypeData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_主表.xlsx", "sheet": "玩法"}, "MatchModeTypeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_主表.xlsx", "sheet": "模式"}, "MatchRuleRangeData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_主表.xlsx", "sheet": "玩法匹配"}, "DisplayPointMapData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_主表.xlsx", "sheet": "展示位置映射"}, "ReadyGoTipsConfig_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_主表.xlsx", "sheet": "开局提示"}, "MatchTypeData_coming": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_即将上线.xlsx", "sheet": "玩法"}, "MatchModeSortInfo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_排序.xlsx", "sheet": "玩法排序"}, "MatchRecommendData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_推荐.xlsx", "sheet": "推荐玩法"}, "MatchCampGradeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_杂项其它.xlsx", "sheet": "副玩法成绩"}, "MatchCampGradeDimensionData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_杂项其它.xlsx", "sheet": "副玩法成绩计算维度"}, "MatchCampData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_杂项其它.xlsx", "sheet": "玩法阵营角色"}, "MatchBattleEventListData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_杂项其它.xlsx", "sheet": "玩法结算事件"}, "MatchDateData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_活动日历_主表.xlsx", "sheet": "活动日历"}, "MatchNewTagDateData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_活动日历_主表.xlsx", "sheet": "新玩法提示"}, "MatchDateData_rank": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_活动日历_排位.xlsx", "sheet": "活动日历"}, "MatchPublicInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_组队外显信息.xlsx", "sheet": "组队外显信息"}, "RecommendMatchInTeam": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_组队建议.xlsx", "sheet": "玩法排序"}, "MatchLevelPassScoreData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_结算得分.xlsx", "sheet": "通关得分"}, "LevelPerformanceScoreData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_结算得分.xlsx", "sheet": "关卡表现得分"}, "LevelScoreGradeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_结算得分.xlsx", "sheet": "积分对应成绩"}, "CustomRoomData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_自定义房间.xlsx", "sheet": "自定义房间玩法"}, "PinBlockInfoData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_自定义房间.xlsx", "sheet": "pin黑名单"}, "MatchUnlockConditionData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_解锁.xlsx", "sheet": "玩法解锁"}, "MatchTypeDetailPageGroupInfoData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_详情页.xlsx", "sheet": "玩法"}, "MatchTypeData_jumpTo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_跳转.xlsx", "sheet": "玩法"}, "RoomSideRoleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式_阵营.xlsx", "sheet": "玩法阵营角色"}, "MatchTypeConflictOutlookData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式屏蔽.xlsx", "sheet": "玩法模式屏蔽"}, "UgcTemplateConflictOutlookData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式屏蔽.xlsx", "sheet": "Ugc地图模版屏蔽"}, "MatchControlData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法模式控制.xlsx", "sheet": "玩法模式控制"}, "ResPropConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_玩法道具.xlsx", "sheet": "道具"}, "QAInvestData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_问卷系统.xlsx", "sheet": "问卷配置"}, "QAInvestCallbackUrlConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\W_问卷系统.xlsx", "sheet": "问卷回调地址"}, "ReputationBehaviourDef": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_信誉分.xlsx", "sheet": "行为定义"}, "ReputationScoreRuleConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_信誉分.xlsx", "sheet": "信誉分规则"}, "BehaviorScoreChangeConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_信誉分.xlsx", "sheet": "行为分数变化"}, "ReputationSysCommonConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_信誉分.xlsx", "sheet": "通用配置"}, "WXGameHudMenuConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小游戏切片_主界面.xlsx", "sheet": "主界面菜单"}, "WXGameHudBannerConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小游戏切片_主界面.xlsx", "sheet": "运营活动banner位"}, "WXGameGuidConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小游戏切片_新手图.xlsx", "sheet": "新手图"}, "MiniGamesData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小游戏玩法接入.xlsx", "sheet": "小游戏接入"}, "XiaowoLvConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝升级.xlsx", "sheet": "小窝升级"}, "XiaowoPropsConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝商品表.xlsx", "sheet": "Sheet1"}, "XiaowoMapConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝地图.xlsx", "sheet": "小窝地图"}, "XiaowoInitFurniture": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝地图.xlsx", "sheet": "创建小窝下发物件"}, "XiaowoSampleRoomObject": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝地图.xlsx", "sheet": "官方方案物件"}, "DropConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝掉落.xlsx", "sheet": "掉落配置"}, "MoneyTreeShakeDrop": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝摇钱树.xlsx", "sheet": "摇树掉落"}, "MoneyTreeWaterDrop": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝摇钱树.xlsx", "sheet": "浇水掉落"}, "ShakeCostConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝摇钱树.xlsx", "sheet": "付费摇树"}, "PlantLevelConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝摇钱树.xlsx", "sheet": "种植等级"}, "XiaowoHotConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝热度.xlsx", "sheet": "小窝热度"}, "FarmingConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝种田表.xlsx", "sheet": "种田配置表"}, "FarmingHandBookConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝种田表.xlsx", "sheet": "种田图鉴表"}, "FarmingLevelConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝种田表.xlsx", "sheet": "种田图鉴等级"}, "XiaowoSysConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝系统配置表.xlsx", "sheet": "系统配置表"}, "XiaowoPianoConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝钢琴配置表.xlsx", "sheet": "钢琴配置"}, "XiaowoGuzhengConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝钢琴配置表.xlsx", "sheet": "古筝配置"}, "XiaowoSuoNaConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_小窝钢琴配置表.xlsx", "sheet": "唢呐配置"}, "LevelGuideConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手关卡.xlsx", "sheet": "新手关卡"}, "LevelGuideGroupData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手关卡.xlsx", "sheet": "新手关卡组"}, "PlayerStarterDressConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手创角表.xlsx", "sheet": "初始时装表"}, "PlayerStarterFaceConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手创角表.xlsx", "sheet": "初始面部表"}, "MainGuideData_acm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_acm.xlsx", "sheet": "新引导总表"}, "StepGuideData_acm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_acm.xlsx", "sheet": "新引导步骤"}, "MainGuideData_chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_chase.xlsx", "sheet": "新引导总表"}, "StepGuideData_chase": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_chase.xlsx", "sheet": "新引导步骤"}, "MainGuideData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_nr3e.xlsx", "sheet": "新引导总表"}, "StepGuideData_nr3e": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_nr3e.xlsx", "sheet": "新引导步骤"}, "MainGuideData_omd": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_OMD.xlsx", "sheet": "新引导总表"}, "StepGuideData_omd": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_OMD.xlsx", "sheet": "新引导步骤"}, "MainGuideData_tyc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_TYC.xlsx", "sheet": "新引导总表"}, "StepGuideData_tyc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_TYC.xlsx", "sheet": "新引导步骤"}, "MainGuideData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_UGC.xlsx", "sheet": "新引导总表"}, "StepGuideData_ugc": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_UGC.xlsx", "sheet": "新引导步骤"}, "MainGuideData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_主玩法.xlsx", "sheet": "新引导总表"}, "StepGuideData_main": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_主玩法.xlsx", "sheet": "新引导步骤"}, "TriggerGuidePeriodData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_主玩法.xlsx", "sheet": "触发时段"}, "GuideABTestData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_主玩法.xlsx", "sheet": "ABTest配置"}, "GuideWarmBattleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_主玩法.xlsx", "sheet": "温暖局引导"}, "ABTestMatchTypeData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_主玩法.xlsx", "sheet": "ABTest玩法模式配置"}, "MatchModeSortInfo_ABTest": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_主玩法.xlsx", "sheet": "玩法排序"}, "MainGuideData_cloud": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_云游.xlsx", "sheet": "新引导总表"}, "StepGuideData_cloud": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_云游.xlsx", "sheet": "新引导步骤"}, "MainGuideData_farm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_农场.xlsx", "sheet": "新引导总表"}, "StepGuideData_farm": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_农场.xlsx", "sheet": "新引导步骤"}, "MainGuideData_home": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_小窝.xlsx", "sheet": "新引导总表"}, "StepGuideData_home": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_小窝.xlsx", "sheet": "新引导步骤"}, "MainGuideData_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_武器大师.xlsx", "sheet": "新引导总表"}, "StepGuideData_wuqidashi": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_武器大师.xlsx", "sheet": "新引导步骤"}, "MainGuideData_js": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_竞速.xlsx", "sheet": "新引导总表"}, "StepGuideData_js": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手引导表_竞速.xlsx", "sheet": "新引导步骤"}, "ResUGCGuideTips": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新手提示_UGC.xlsx", "sheet": "新手提示"}, "NewYearPilotEntranceData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新春导航.xlsx", "sheet": "入口配置"}, "NewYearPilotTabData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_新春导航.xlsx", "sheet": "新春活动配置"}, "AdministrationCodeConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_行政编码.xlsx", "sheet": "行政编码"}, "RecommendMatchTypeConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\X_限时娱乐推荐.xlsx", "sheet": "Sheet1"}, "OMDMonsterWaveData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_OMD野怪波次.xlsx", "sheet": "波次"}, "OMDMonsterPoolData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_OMD野怪波次.xlsx", "sheet": "怪物池"}, "OMDWeaponPoolData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_OMD野怪波次.xlsx", "sheet": "武器池"}, "OMDMonsterData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_OMD野怪配置.xlsx", "sheet": "野怪表"}, "TDSMonsterWaveData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TDS野怪波次.xlsx", "sheet": "波次"}, "TDSMonsterPoolData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TDS野怪波次.xlsx", "sheet": "怪物池"}, "TDSCoinDropData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TDS野怪波次.xlsx", "sheet": "金币掉落"}, "TDSCoinGroupData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TDS野怪波次.xlsx", "sheet": "金币组配置"}, "TDSWeaponWaveConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TDS野怪波次.xlsx", "sheet": "武器轮次表"}, "TDMonsterWaveData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TD野怪波次.xlsx", "sheet": "波次"}, "TDMonsterPoolData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TD野怪波次.xlsx", "sheet": "怪物池"}, "TDWeaponPoolData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TD野怪波次.xlsx", "sheet": "武器池"}, "TDMonsterBuffConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TD野怪波次.xlsx", "sheet": "野怪buff"}, "TYCDropConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TYC掉落.xlsx", "sheet": "掉落表"}, "TYCDroneClientConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TYC无人机.xlsx", "sheet": "野怪表"}, "TYCPropConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TYC道具.xlsx", "sheet": "主表"}, "TYCMapMonsterWaveData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TYC野怪波次.xlsx", "sheet": "地图野怪波次"}, "TYCRebirthMonsterWaveData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TYC野怪波次.xlsx", "sheet": "转生小怪波次表"}, "TYCRebirthBossWaveData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TYC野怪波次.xlsx", "sheet": "转生boss波次表 "}, "TYCMonsterBigWaveData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TYC野怪波次.xlsx", "sheet": "大波次"}, "TYCMonsterSmallWaveData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TYC野怪波次.xlsx", "sheet": "小波次"}, "TYCWaveChartData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TYC野怪波次.xlsx", "sheet": "波次时序图"}, "TYCMonsterData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_TYC野怪配置.xlsx", "sheet": "野怪表"}, "StreamOpenIdWhitelistData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_一键开播.xlsx", "sheet": "开播白名单"}, "GameLiveTaskConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_一键开播任务.xlsx", "sheet": "直播任务"}, "QuickRewardConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_一键领奖.xlsx", "sheet": "一键领奖"}, "EntertainmentGuideConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_娱乐向导.xlsx", "sheet": "娱乐向导"}, "EntertainmentGuideTaskConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_娱乐向导.xlsx", "sheet": "打卡条件&玩法任务&手册任务"}, "GameSettingConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_游戏设置.xlsx", "sheet": "设置-默认值"}, "LanguagesData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_语种表.xlsx", "sheet": "语种"}, "LanguagesGvoiceData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_语种表.xlsx", "sheet": "Gvoice属性"}, "SnsInvitationConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_邀请配置表.xlsx", "sheet": "邀请配置"}, "MailTemplateConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_邮件.xlsx", "sheet": "邮件模板"}, "MailIniConfForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_邮件.xlsx", "sheet": "邮件整体配置项"}, "GlobalMailConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_邮件_全服邮件.xlsx", "sheet": "全服邮件"}, "UGCSfxAudioConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之UGC音效.xlsx", "sheet": "音效"}, "CharacterVoiceConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之人声.xlsx", "sheet": "角色语音"}, "EmotionVoiceConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之人声.xlsx", "sheet": "情绪轮盘"}, "OtherVoiceConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之人声.xlsx", "sheet": "预留待用"}, "CharacterVoiceParam": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之人声.xlsx", "sheet": "通用参数"}, "GrabVoiceMappingConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之人声.xlsx", "sheet": "抱起彩蛋语音"}, "CharacterVoiceConfigFPS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之人声FPS.xlsx", "sheet": "角色语音"}, "CharacterVoiceParamFPS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之人声FPS.xlsx", "sheet": "通用参数"}, "CharacterVoiceIgnoreFPS": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之人声FPS.xlsx", "sheet": "玩法配置"}, "CharacterVoiceConfigMOBA": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之人声Moba.xlsx", "sheet": "角色语音"}, "EventPlayCdConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之全局参数.xlsx", "sheet": "事件的最小间隔"}, "BankUnloadConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之全局参数.xlsx", "sheet": "Bank卸载时间"}, "AnimFxAudioConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之动画特效声音.xlsx", "sheet": "Sheet1"}, "MusicGroupConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之埋点.xlsx", "sheet": "音乐组配置"}, "SoundPointConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之埋点.xlsx", "sheet": "局外背景音乐"}, "SfxPhyMatConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之物理材质对照.xlsx", "sheet": "物理材质对照表"}, "SoundMusicConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之背景音乐.xlsx", "sheet": "Sheet1"}, "SfxAudioConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之音效.xlsx", "sheet": "音效"}, "UIAudioConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之音效.xlsx", "sheet": "UI音效"}, "EnvAudioConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_音频之音效.xlsx", "sheet": "长音效"}, "Preview": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_预览.xlsx", "sheet": "预览"}, "PreviewCameraConfig": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Y_预览.xlsx", "sheet": "预览背景"}, "OMDLevelMonstersConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_OMD关卡怪物信息.xlsx", "sheet": "关卡兽人类型表"}, "OMDLevelMonsterMessageConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_OMD关卡怪物信息.xlsx", "sheet": "兽人信息表"}, "OMDWeaponClientConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_OMD武器配置表.xlsx", "sheet": "OMD武器配置"}, "OMDWeaponAttrConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_OMD武器配置表.xlsx", "sheet": "OMD武器属性配置"}, "OMDWeaponUpgradeConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_OMD武器配置表.xlsx", "sheet": "OMD武器普通升级"}, "OMDWeaponSkinData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_OMD武器配置表.xlsx", "sheet": "OMD武器黄金皮肤表"}, "OMDWeaponSuitSkinData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_OMD武器配置表套装皮肤.xlsx", "sheet": "OMD套装武器皮肤表"}, "OMDPropsConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_OMD道具表.xlsx", "sheet": "道具表"}, "OMDPropsLevelConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_OMD道具表.xlsx", "sheet": "道具等级细表"}, "OMDInitialPropsConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_OMD道具表.xlsx", "sheet": "初始持有道具"}, "TYCBuffConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC战斗Buff.xlsx", "sheet": "BUFF"}, "TYCTriggerConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC战斗Trigger.xlsx", "sheet": "trigger"}, "TYCBulletConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC战斗子弹表.xlsx", "sheet": "道具"}, "TYCSkillConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC战斗技能表.xlsx", "sheet": "技能"}, "TYCTrapConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC战斗陷阱表.xlsx", "sheet": "Trap"}, "TYCTrapUpgradeConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC战斗陷阱表.xlsx", "sheet": "陷阱升级表"}, "TYCTrapSkinConfData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC战斗陷阱表.xlsx", "sheet": "陷阱皮肤表"}, "TYCReincarnateNormalLevelConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC转生配置.xlsx", "sheet": "转生普通等级"}, "TYCReincarnatePeakLevelConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC转生配置.xlsx", "sheet": "转生巅峰等级"}, "TYCCarVehicleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC载具.xlsx", "sheet": "汽车载具"}, "TYCHelicopterVehicleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC载具.xlsx", "sheet": "直升机载具"}, "TYCFixWingVehicleData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_TYC载具.xlsx", "sheet": "固定翼载具"}, "LobbyIconSwitchJoy": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_主界面轮播-娱乐活动专属.xlsx", "sheet": "ICON配置"}, "LobbyIconSwitchJoyConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_主界面轮播-娱乐活动专属.xlsx", "sheet": "轮播机制"}, "LobbyIconSwitchJoyCondition": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_主界面轮播-娱乐活动专属.xlsx", "sheet": "条件配置"}, "LobbyIconSwitchArr": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_主界面轮播ICON.xlsx", "sheet": "轮播ICON集合设置"}, "LobbyIconSwitch": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_主界面轮播ICON.xlsx", "sheet": "ICON配置"}, "LobbyIconSwitchConf": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_主界面轮播ICON.xlsx", "sheet": "轮播机制"}, "LobbyIconSwitchCondition": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_主界面轮播ICON.xlsx", "sheet": "条件配置"}, "DSAZoneData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_战斗服信息配置.xlsx", "sheet": "战斗服区域配置"}, "CaughtPenguinCfgData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_抓企鹅玩法配置.xlsx", "sheet": "抓企鹅玩法配置"}, "RecruitTopicData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_招募主题.xlsx", "sheet": "招募主题"}, "DsMiscConfForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_杂项配置.xlsx", "sheet": "DS杂项配置-LetsGo"}, "ClientKVConfForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_杂项配置.xlsx", "sheet": "客户端KV表-LetsGo"}, "TeamShowClientKVConfForLetsGo": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_杂项配置.xlsx", "sheet": "组队秀客户端KV表-LetsGo"}, "ClientKVConfForLetsGo_MainGame": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_杂项配置_主玩法.xlsx", "sheet": "客户端KV杂项"}, "CustomConfigData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_自定义按键表.xlsx", "sheet": "自定义按键配置"}, "ResourceRequireData": {"file": "F:\\UGit\\LetsGoDevelop1\\letsgo_common\\excel\\xls\\Z_资源文件下发配置.xlsx", "sheet": "不需要下发的资源文件"}}