# -*- encoding:utf8 -*-
import os
import sys
import glob
import subprocess

root_dir='../../../'

def write_file(filename, lines):
    with open(filename, 'w', encoding='utf-8') as f:
        for line in lines:
            f.write(line)

def search_import(filename):
    cmd = '''find {} -name '*.java' -not -path '{}WeA/common/src/generated/*' -not -path '{}WeA/common/common/tmpGenerateCode/*' -not -path '{}WeA/common/src/main/java/com/tencent/resourceloader/resclass/{}.java' | xargs  grep '{}' | wc -l'''.format(root_dir, root_dir, root_dir, root_dir, filename, filename)
    #print(cmd)
    return subprocess.getoutput(cmd)

def deal_res_java(filepath, rm_files):
    filename = filepath.split('/')[-1].replace('.java', '')
    search_count = search_import(filename)

    if search_count == "0":
        print("no use:" + filename)
        rm_files.append(filename)
        os.system('rm ' + filepath)

def rm_resconfig(rm_files):
    resconfig_path = 'excel/proto/resConfig.xml'
    with open(resconfig_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    new_lines = []
    rm_next_scope = False
    has_change = False
    for line in lines:
        new_lines.append(line)

        if rm_next_scope:
            pop_line = new_lines.pop()
            if '>' in line:
                rm_next_scope = False
            continue

        for rm_file in rm_files:
            if rm_file in line:
                rm_next_scope = True
                has_change = True
                while True:
                    pop_line = new_lines.pop()
                    if '>' in pop_line:
                        rm_next_scope = False
                    if '<' in pop_line:
                        break
                break

    if has_change:
        write_file(resconfig_path, new_lines)



def main():
    files = subprocess.getoutput("find ../src/main/java/com/tencent/resourceloader/resclass -name '*.java'")
    filelist = files.split()

    rm_files = []
    for res_java in filelist:
        deal_res_java(res_java, rm_files)
        #break
    rm_resconfig(rm_files)

if __name__ == '__main__':
    main()