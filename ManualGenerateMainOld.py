# encoding=utf-8
import os
import platform
import sys
import argparse
import time

from subprocess import Popen, PIPE

from timipy.util.batch_run import batch_run
from timipy import timiPath
from timipy.attrTool import attr_generator
from timipy.csproto import cs_protocol_generator
from timipy.dbTool import tcaplus_generator
from timipy.dsCfgTool import gen_ds_config
from timipy.g6_irpc import g6_irpc_generator
from timipy.adminpb import admin_proto_generator
from timipy.analyzepb import analyze_proto_generator
from timipy.monitorTool import monitor_generator
from timipy.ssproto import ss_protocol_generator
from timipy.tlog import parse_tmpl
from timipy.util import Util
from timipy.util import sync
from timipy.xlsRes import gen_java_class
from timipy.xlsRes import updateErrorCode
from timipy.xlsRes import genResEnumWeAEnum
from timipy.hotres import hotres_gen_java_class
from WeaProject import WeaProject


def timeit(func):
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} cost {end - start} secs")
        return result
    return wrapper

GENERATE_JAVA_OUT = timiPath.java_src_to_path
GENERATE_PB_JAVA_TMP_OUT = timiPath.generate_path
GENERATE_TLOG_XML_OUT = os.path.join(timiPath.project_aoe_run_path, 'common/resource/xml')
RPC_DEFAULT_IMPL = os.getenv('RPC_DEFAULT_IMPL', 'true').lower() == 'true'

# Util.rm_r(GENERATE_PB_JAVA_TMP_OUT)
os.makedirs(GENERATE_PB_JAVA_TMP_OUT, exist_ok=True)

curpypath = os.path.split(os.path.realpath(__file__))[0]
sys.path.append(os.path.join(curpypath, "excel"))
import conv2pb

GENERATE_ISOLATION_SWITCH=False
GENERATE_ISOLATION_HOME=os.path.normpath(os.path.join(curpypath, "..", "..", "protocol"))

TCAPLUS_PROTO_OPTION_PATH = os.path.join(timiPath.common_path, "svr_tools", "WeA/pyHome/db/tcaplus/proto")

weaProj = None

def initWeaProj(catName, genHome, genIsolation):
  global weaProj
  if weaProj is not None:
    return
  weaProj = WeaProject(catName, genHome=genHome, genIsolation=genIsolation)
  # add project entries
  weaProj.add(entryName="keywords", workDir=".", protoHomes=["excel/basic/proto"], deps=[])
  weaProj.add(entryName="res", workDir=".", protoHomes=["excel/server/proto"], deps=["keywords"])\
    .addPkgTransfer(os.path.join("com", "tencent", "wea", "xlsRes", "keywords"), "keywords")
  weaProj.add(entryName="base_common", workDir=".", protoHomes=["protos/base_common_proto"], deps=[])
  weaProj.add(entryName="attr", workDir=".", protoHomes=["protos/cs_attr_proto"], deps=["keywords"])
  weaProj.add(entryName="common", workDir=".", protoHomes=["protos/common_proto"], deps=["res", "attr", "base_common"])
  weaProj.add(entryName="tcaplus", workDir=".", protoHomes=[TCAPLUS_PROTO_OPTION_PATH, "protos/db_proto/proto"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="simulator4j", workDir=".", protoHomes=["protos/simulator4j_proto/proto"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="other", workDir=".", protoHomes=["protos/other_proto/proto"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="admin", workDir=".", protoHomes=["protos/admin_proto"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="analyze", workDir=".", protoHomes=["protos/analyze_proto/proto"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="hotres", workDir=".", protoHomes=["protos//hotres_proto/proto"], deps=["common", "base_common"])
  weaProj.add(entryName="cs", workDir=".", protoHomes=["protos/cs_proto/proto"], deps=["res", "attr", "common", "base_common"])
  weaProj.add(entryName="sr", workDir=".", protoHomes=["protos/sr_proto/proto"], deps=["res", "attr", "common", "base_common"])
  weaProj.add(entryName="ss", workDir=".", protoHomes=[timiPath.tmp_ss_proto_dir], deps=["res", "attr", "common", "tcaplus", "base_common"])
  weaProj.add(entryName="api", workDir=".", protoHomes=[timiPath.tmp_api_proto_dir], deps=["ss", "res", "attr", "common", "tcaplus", "base_common"])
  weaProj.add(entryName="g6", workDir=".", protoHomes=["protos/g6_irpc"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="grpc", workDir=".", protoHomes=["protos/grpc_proto/proto"], deps=["res", "common", "base_common"])
  # check project legal
  weaProj.check()

def runBat(batFile, *args):
  """
      run the bat files on Windows platform
  """
  if platform.system() != "Windows":
    batFile = batFile.replace('.bat', '.sh')
  fullpath = os.path.join(timiPath.common_path, batFile)
  d = os.path.dirname(fullpath)
  n = os.path.basename(fullpath)
  if not Util.suppress_useless_warnings():
    Util.logger.warning("runBat batFile=%s dir=%s f=%s", batFile, d, n)
    p = Popen([fullpath, *args], cwd=d)
    stdout, stderr = p.communicate()
    rc = p.returncode
    if rc != 0:
      raise RuntimeError('runBat Error, file: {}'.format(batFile))
  else:
    p = Popen([fullpath, *args], cwd=d, stdout=PIPE, stderr=PIPE, text=True)
    _, stderr = p.communicate()
    rc = p.returncode
    if rc != 0:
      raise RuntimeError(f'runBat Error, file: {batFile}, error: {stderr.strip()}')

def runCmd(cmd, working_dir='.', shell=True):
  if platform.system() != "Windows":
    if platform.system() == "Linux":
      protoc = 'tools/protoc_linux'
      grpc_java = 'tools/protoc-gen-grpc-java'
    else:
      # osx use tools protoc
      protoc = 'tools/protoc'
      grpc_java = 'protoc-gen-grpc-java-1.55.1-osx-x86_64.exe'

    cmd = cmd.replace('protoc3.exe', protoc).replace('protoc-gen-grpc-java.exe', grpc_java)
  working_dir = os.path.join(timiPath.common_path, working_dir)
  if not Util.suppress_useless_warnings():
    Util.logger.warning("runCmd %s %s %s", cmd, working_dir, shell)
    p = Popen(cmd, shell=shell, cwd=working_dir)
    stdout, stderr = p.communicate()
    rc = p.returncode
    if rc != 0:
      raise RuntimeError(f'runCmd Error:{rc} cmd:{cmd}')
  else:
    p = Popen(cmd, shell=shell, cwd=working_dir, stdout=PIPE, stderr=PIPE, text=True)
    _, stderr = p.communicate()
    if p.returncode != 0:
      raise RuntimeError(f'runCmd Error {stderr.strip()}')
    
def gatherProto(protoHome, workDir):
    if os.path.isabs(protoHome):
      protoHome = os.path.relpath(protoHome, os.getcwd())
    if os.path.isabs(workDir):
      workDir = os.path.relpath(workDir, os.getcwd())
    
    indices = {}
    for r, _, files in os.walk(protoHome):
      if r.endswith("google") or r.endswith(os.path.join("google", "protobuf")):
        continue
      indices[r] = files
    return indices
    
def compileProto(projName, protoHomes, incPaths, workDir, **kwargs):
  if not GENERATE_ISOLATION_SWITCH:
    dst_path = kwargs.get("dstPath")
    if dst_path is None:
      raise Exception(f'need appointed dstPath')
  else:
    dst_path = os.path.join(GENERATE_ISOLATION_HOME, "src", "main", "java")
    # if os.path.exists(dst_path):
    #   shutil.rmtree(dst_path)
    os.makedirs(dst_path, exist_ok=True)
  dst_path = os.path.normpath(dst_path)
  
  allProto = []
  for protoHome in protoHomes:
    indices = gatherProto(protoHome, workDir)
    for p, files in indices.items():
      if p not in incPaths:
        incPaths.append(p) 
      if len(files) > 0:
        allProto.append(os.path.join(p, "*.proto"))
  
  cmd = 'protoc3.exe'
  if "plugin" in kwargs:
    cmd += f' --plugin={kwargs.get("plugin")} --{projName}-java_out={dst_path}'
  else:
    cmd += f' --java_out={dst_path}'
  cmd += f' {" ".join(map(lambda inc:"--proto_path="+inc, incPaths))} {" ".join(allProto)}'
  # print(f'compile project:{projName} -- "{cmd}"')
  runCmd(cmd, workDir)


def convCRLF2LF(real_path):
  if platform.system().lower() != 'windows':
    return
  with open(real_path, 'rb') as in_file:
    in_data = in_file.read()
    out_data = in_data.replace(b'\r\n', b'\n')

  with open(real_path, 'wb') as out_file:
    out_file.write(out_data)

def scanAndConvCRLF2LF(fullPath):
  if platform.system().lower() != 'windows':
    return
  all = os.walk(fullPath)
  for path, dir_list, file_list in all:
    for file_name in file_list:
      real_path = os.path.join(path, file_name)
      # print('conv file:', real_path)
      convCRLF2LF(real_path)


def syncWithCRLFConv(srcPath, destPath, ignoreExisting = False):
  scanAndConvCRLF2LF(srcPath)
  sync.sync(srcPath, destPath, ignoreExisting)


def copyTreeWithCRLFConv(srcPath, patterns, destPath, addSVN=False, checkDiff=False, onlyInsert=False):
  scanAndConvCRLF2LF(srcPath)
  Util.copytrees(srcPath, patterns, destPath, addSVN, checkDiff, onlyInsert)


def copyFilesWithCRLFConv(srcPath, destPath, addSVN=False, checkDiff=False, onlyInsert=False):
  scanAndConvCRLF2LF(srcPath)
  Util.copyfiles(srcPath, destPath, addSVN, checkDiff, onlyInsert)
  
# generate attr files and copy

def runServerAttr(genJavaPath, genProtoPath, isall):
  attr_generator.generateAttr(packageName="com.tencent.wea",
                              loadDir=os.path.join(timiPath.common_path, "attr"),
                              dumpJavaPath=genJavaPath,
                              dumpProtoPath=genProtoPath,
                              syncProtoBase=True)
  scanAndConvCRLF2LF(genProtoPath)
  # attr有独立目录，可以直接同步
  syncWithCRLFConv(os.path.join(genProtoPath, "cs_attr_proto"),
            os.path.join(timiPath.common_path, "protos/cs_attr_proto/"))
  # syncWithCRLFConv(os.path.join(genProtoPath, "ss_attr_proto"),
  #           os.path.join(timiPath.common_path, "protos/ss_attr_proto/"))

  if not isall:
    syncWithCRLFConv(
        os.path.join(os.path.join(genJavaPath, timiPath.generate_package), "attr"),
        os.path.join(GENERATE_JAVA_OUT, "attr"))
    # runCmd(
    #   'protoc3.exe --java_out=' + genJavaPath + ' --proto_path=excel/server/proto --proto_path=protos/common_proto --proto_path=protos/ss_attr_proto protos/ss_attr_proto/*.proto',
    #   '.')
    # compileProto(projName="attr"
    #        , dstPath=genJavaPath
    #        , protoHomes=["protos/cs_attr_proto"]
    #        , incPaths=["excel/server/proto"]
    #        , workDir=".")
    runCmd(**weaProj.getProtoCompile("attr", dstPath=genJavaPath))
    # runCmd(
    #   'protoc3.exe --java_out=' + genJavaPath + ' --proto_path=excel/server/proto --proto_path=protos/common_proto --proto_path=protos/cs_attr_proto protos/cs_attr_proto/*.proto',
    #   '.')
    copyTreeWithCRLFConv(os.path.join(genJavaPath, "com/tencent/wea/protocol"),
                    "*.java",
                    os.path.join(GENERATE_JAVA_OUT, "protocol"),
                    addSVN=False, checkDiff=True)

# generate db files and copy

def runServerTcaplus(genpath):
  tcaplus_generator.main(loadDir="protos/db_proto/proto", dumpDir=genpath
                          , protoPaths=["excel/basic/proto", "excel/server/proto", 
                                        "protos/common_proto", "protos/cs_attr_proto",
                                        "protos/base_common_proto", TCAPLUS_PROTO_OPTION_PATH])
  # compileProto(projName="tcaplus"
  #               , dstPath=genpath
  #               , protoHomes=["protos/db_proto/proto"]
  #               , incPaths=["excel/server/proto", "protos/common_proto", "protos/cs_attr_proto"]
  #               , workDir=".")
  runCmd(**weaProj.getProtoCompile("tcaplus", dstPath=genpath))
  syncWithCRLFConv(
    os.path.join(os.path.join(genpath, timiPath.generate_package), "tcaplus"),
    os.path.join(GENERATE_JAVA_OUT, "tcaplus"))

def runServerSimulator4j(genpath):
  os.makedirs(genpath, exist_ok=True)
  # compileProto(projName="simulator4j"
  #         , dstPath=genpath
  #         , protoHomes=["protos/simulator4j_proto/proto"]
  #         , incPaths=["excel/server/proto", "protos/common_proto", "protos/cs_attr_proto"]
  #         , workDir=".")
  runCmd(**weaProj.getProtoCompile("simulator4j", dstPath=genpath))
  # runCmd(
  #   'protoc3.exe --java_out=' + genpath + ' --proto_path=protos/common_proto --proto_path=excel/server/proto --proto_path=protos/cs_attr_proto --proto_path=protos/simulator4j_proto/proto protos/simulator4j_proto/proto/*.proto',
  #   '.')
  copyTreeWithCRLFConv(genpath, "*.java",
                 os.path.join(timiPath.projects_path,
                              "simulator4j/src/main/java/"),
                 addSVN=False, checkDiff=True)

def runServerRes(genpath):
  os.makedirs(genpath, exist_ok=True)
  conv2pb.tryCompileProtos(os.path.join(curpypath, "excel"), is_domestic=True)
  scanAndConvCRLF2LF(os.path.join(timiPath.common_path, 'excel/server/proto'))
  
  # compileProto(projName="res"
  #         , dstPath=genpath
  #         , protoHomes=["excel/server/proto"]
  #         , incPaths=[], workDir=".")
  runCmd(**weaProj.getProtoCompile("keywords", dstPath=genpath))
  runCmd(**weaProj.getProtoCompile("res", dstPath=genpath))
  transfers = weaProj.getPkgTransfer("res")
  if len(transfers) > 0:
    for cmd in transfers:
      sync.sync(**cmd)
  # runCmd(
  #   'protoc3.exe --java_out=' + genpath + ' --proto_path=excel/server/proto excel/server/proto/*.proto',
  #   '.')
  fileList = [genpath + "/com/tencent/wea/xlsRes/ResTestCaseOuterClass.java",
              genpath + "/com/tencent/wea/xlsRes/BuildingResOuterClass.java"]
  for filePath in fileList:
    try:
      if os.path.exists(filePath):
        os.remove(filePath)
        # print "remove {}".format(filePath)
    except:
      pass
  syncWithCRLFConv(os.path.join(genpath, "com/tencent/wea/xlsRes"),
            os.path.join(GENERATE_JAVA_OUT, "xlsRes"))
  gen_java_class.main()

def runOtherProtocol(gen_path):
  os.makedirs(gen_path, exist_ok=True)
  # compileProto(projName="other"
  #         , dstPath=gen_path
  #         , protoHomes=["protos/other_proto/proto"]
  #         , incPaths=["excel/server/proto", "protos/common_proto", "protos/cs_attr_proto"]
  #         , workDir=".")
  runCmd(**weaProj.getProtoCompile("other", dstPath=gen_path))
  # runCmd(
  #   'protoc3.exe --java_out=' + gen_path + ' --proto_path=excel/server/proto --proto_path=protos/common_proto --proto_path=protos/cs_attr_proto --proto_path=protos/other_proto protos/other_proto/proto/*.proto',
  #   '.')
  syncWithCRLFConv(os.path.join(gen_path, "com/tencent/wea/other"),
            os.path.join(GENERATE_JAVA_OUT, "other"))

def runAdminProtocol(gen_path):
  os.makedirs(gen_path, exist_ok=True)
  # compileProto(projName="admin"
  #         , dstPath=gen_path
  #         , protoHomes=["protos/admin_proto"]
  #         , incPaths=["excel/server/proto", "protos/common_proto", "protos/cs_attr_proto"]
  #         , workDir=".")
  runCmd(**weaProj.getProtoCompile("admin", dstPath=gen_path))
  # runCmd(
  #   'protoc3.exe --java_out=' + gen_path + ' --proto_path=excel/server/proto --proto_path=protos/common_proto --proto_path=protos/cs_attr_proto --proto_path=protos/admin_proto protos/admin_proto/*.proto',
  #   '.')
  syncWithCRLFConv(os.path.join(gen_path, "com/tencent/wea/adminpb"),
            os.path.join(GENERATE_JAVA_OUT, "adminpb"))

  genAdminPbHelper(os.path.join(gen_path, "adminpb"))


def runAnalyzeProtocol(gen_path):
  os.makedirs(gen_path, exist_ok=True)
  runCmd(**weaProj.getProtoCompile("analyze", dstPath=gen_path))
  syncWithCRLFConv(os.path.join(gen_path, "com/tencent/wea/analyze"),
                   os.path.join(GENERATE_JAVA_OUT, "analyzepb"))

  genAnalyzePbHelper(os.path.join(gen_path, "analyzepb"))


def genAnalyzePbHelper(outputpath):
  analyze_proto_generator.genAnalyzeProto(outputpath,
                                          rpc_default_impl=RPC_DEFAULT_IMPL)
  outterpath = os.path.join(outputpath, "..")
  copyFilesWithCRLFConv(os.path.join(outputpath, '*.java'),
                        os.path.join(outterpath, 'com/tencent/wea/analyzepb'),
                        addSVN=False, checkDiff=False)
  analyzepb_path = os.path.join(GENERATE_JAVA_OUT,
                                "analyzepb")

  copyTreeWithCRLFConv(os.path.join(outterpath, "com/tencent/wea/analyzepb/"),
                       '*.java',
                       analyzepb_path,
                       addSVN=False, checkDiff=True)


def runHotResProtocol(gen_path):
  os.makedirs(gen_path, exist_ok=True)
  # compileProto(projName="hotres"
  #         , dstPath=gen_path
  #         , protoHomes=["protos/hotres_proto/proto"]
  #         , incPaths=["protos/common_proto"]
  #         , workDir=".")
  runCmd(**weaProj.getProtoCompile("hotres", dstPath=gen_path))
  # runCmd(
  #   'protoc3.exe --java_out=' + gen_path + ' --proto_path=protos/common_proto --proto_path=protos/hotres_proto/proto protos/hotres_proto/proto/*.proto',
  #   '.')
  syncWithCRLFConv(os.path.join(gen_path, "com/tencent/wea/hotRes"),
            os.path.join(GENERATE_JAVA_OUT, "hotRes"))
  hotres_gen_java_class.main()

def runServerDsCfg():
  dsa_cfg_path = os.path.join(timiPath.common_path,
                              "../../../run/dsa/cfg/dsa_conf/")
  dsc_cfg_path = os.path.join(timiPath.common_path, "../../../run/dsc/cfg/")
  gen_ds_config.genDsaConfig('all', 'all', dsa_cfg_path, dsc_cfg_path, 'main')


def removeUnnecessaryFile():
  fileList = [os.path.join(timiPath.common_path,
                           "protos/ss_proto/generated/service/LockService.java"),
              os.path.join(timiPath.common_path,
                           "protos/ss_proto/generated/service/MapService.java")]
  for filePath in fileList:
    try:
      if os.path.exists(filePath):
        os.remove(filePath)
        # print "remove {}".format(filePath)
    except:
      pass

def runServerSrProto(genpath):
  sr_generate_path = os.path.join(timiPath.common_path, 'protos/sr_proto/generated')
  sr_wea_entry = weaProj.get("sr")
  if sr_wea_entry is None:
      print("sr proto not exist")
      return

  # xml的位置
  sr_msg_types_xml_path = os.path.join(timiPath.common_path, 'protos/sr_proto/sr_msg_types.xml')
  # 获取sr的proto
  sr_proto_home = [os.path.abspath(os.path.join(sr_wea_entry.workDir, d)) for d in sr_wea_entry.getProto()]
  sr_proto_generator_path = os.path.join(timiPath.common_path, 'protos/sr_proto/run4server.bat')
  # 获取sr依赖的proto
  sr_dep = [os.path.abspath(os.path.join(sr_wea_entry.workDir, d)) for d in weaProj.gatherProtoIndices(sr_wea_entry, set())]

  # python pe 路径
  if sys.platform == 'win32':
    python3_path = os.path.join(timiPath.common_path, 'tools/python-3.8.2/python.exe')
  else:
    python3_path = os.path.join(timiPath.common_path, 'tools/python_linux/bin/python3')

  # protoc路径
  protobuf_bin_path = os.path.join(timiPath.common_path, 'tools')

  # 会切换work_dir 一律用绝对路径
  runBat(sr_proto_generator_path,
         '"' + ','.join(sr_proto_home) + '"',
         '"' +  ','.join(sr_dep) + '"',
         os.path.abspath(python3_path),
         os.path.abspath(protobuf_bin_path),
         os.path.abspath(sr_msg_types_xml_path),
         )

  scanAndConvCRLF2LF(sr_generate_path)
  convCRLF2LF(sr_msg_types_xml_path)
  # 这里怎么冒出一个tcaplus的xml?
  convCRLF2LF(os.path.join(timiPath.common_path, 'protos/db_proto/proto/tcaplus_db.xml'))
  copyFilesWithCRLFConv(os.path.join(timiPath.common_path,
                              'protos/sr_proto/generated/SrMsgTypes.java'),
                 genpath + '/com/tencent/wea/protocol/',
                 addSVN=False, checkDiff=True)


def runServerProtocol(genpath, isall):
  tmpoutPath = genpath
  ss_proto_dir = timiPath.tmp_ss_proto_dir
  batch_run([
    (runServerCsProto, (os.path.join(genpath, "csproto"),), {}),
    (runServerSsProto, (os.path.join(genpath, "ssproto"),), {}),
    (runAdminProtocol, (genpath,), {}),
    (runAnalyzeProtocol, (genpath,), {}),
    (runServerSrProto, (genpath,), {})
  ], initializer=initWeaProj, initargs=("protocol", GENERATE_ISOLATION_HOME, GENERATE_ISOLATION_SWITCH))
  # batch_run([
  #   (compileProto, (), dict(projName="common", dstPath=tmpoutPath, protoHomes=["protos/common_proto"]
  #                     , incPaths=["excel/server/proto", "protos/cs_attr_proto"], workDir=".")),
  #   (compileProto, (), dict(projName="attr", dstPath=tmpoutPath, protoHomes=["protos/cs_attr_proto"]
  #                     , incPaths=["excel/server/proto"], workDir=".")),
  #   (compileProto, (), dict(projName="cs", dstPath=tmpoutPath, protoHomes=["protos/cs_proto/proto", "protos/sr_proto/proto"]
  #                     , incPaths=["excel/server/proto", "protos/common_proto", "protos/cs_attr_proto"], workDir=".")),
  #   (compileProto, (), dict(projName="ss", dstPath=tmpoutPath, protoHomes=[ss_proto_dir]
  #                     , incPaths=["excel/server/proto", "protos/common_proto", "protos/cs_attr_proto"
  #                                 , "protos/db_proto/proto", "protos/cs_proto/proto"], workDir=".")),
  #   (compileProto, (), dict(projName="g6", dstPath=tmpoutPath, protoHomes=["protos/g6_irpc"]
  #                     , incPaths=["excel/server/proto", "protos/common_proto", "protos/cs_attr_proto"], workDir=".")),
  #   (compileProto, (), dict(projName="grpc", dstPath=tmpoutPath, protoHomes=["protos/grpc_proto/proto"]
  #                     , incPaths=["excel/server/proto", "protos/common_proto"], workDir=".")),
  #   (compileProto, (), dict(projName="grpc", dstPath=tmpoutPath, protoHomes=["protos/grpc_proto/proto"]
  #                     , plugin=f"protoc-gen-grpc-java.exe"
  #                     , incPaths=["excel/server/proto", "protos/common_proto"], workDir=".")),
  # ])
  batch_run([
    (runCmd, (), weaProj.getProtoCompile("base_common", dstPath=tmpoutPath)),
    (runCmd, (), weaProj.getProtoCompile("common", dstPath=tmpoutPath)),
    (runCmd, (), weaProj.getProtoCompile("attr", dstPath=tmpoutPath)),
    (runCmd, (), weaProj.getProtoCompile("cs", dstPath=tmpoutPath)),
    (runCmd, (), weaProj.getProtoCompile("sr", dstPath=tmpoutPath)),
    # 显然不应该编译mod的协议的pb
    # (runCmd, (), weaProj.getProtoCompile("mod", dstPath=tmpoutPath)),
    (runCmd, (), weaProj.getProtoCompile("ss", dstPath=tmpoutPath)),
    (runCmd, (), weaProj.getProtoCompile("api", dstPath=tmpoutPath)),
    (runCmd, (), weaProj.getProtoCompile("g6", dstPath=tmpoutPath)),
    (runCmd, (), weaProj.getProtoCompile("grpc", dstPath=tmpoutPath)),
    (runCmd, (), weaProj.getProtoCompile("grpc", dstPath=tmpoutPath, plugin=f"protoc-gen-grpc-java.exe")),
  ], initializer=initWeaProj, initargs=("protocol", GENERATE_ISOLATION_HOME, GENERATE_ISOLATION_SWITCH))

  batch_run([
    (runServerG6IrpcProto, [os.path.join(genpath, "g6_irpc")], {}),
    (runServerGrpcProto, [os.path.join(genpath, "grpc")], {})
  ], initializer=initWeaProj, initargs=("protocol", GENERATE_ISOLATION_HOME, GENERATE_ISOLATION_SWITCH))

  removeUnnecessaryFile()

  if not isall:
    copyTreeWithCRLFConv(os.path.join(tmpoutPath, "com/tencent/wea/protocol/"),
                   '*.java',
                   os.path.join(GENERATE_JAVA_OUT, 'protocol/'),
                   addSVN=False, checkDiff=True)

    # 拷贝mod的协议java
    need_copy_mod_name = set()
    for root, dirs, files in os.walk(tmpoutPath):
        for dir_name in dirs:
            relative_path = os.path.relpath(os.path.join(root, dir_name), tmpoutPath).replace('\\', '/')
            if not relative_path.startswith("com/tencent/wea/"):
                continue
            if "/protocol/" not in relative_path:
                continue

            parts = relative_path.split('/')
            # com/tencent/wea/{mod_name}/protocol/
            if len(parts) < 5 or parts[4] != "protocol":
                continue

            mod_name = parts[3]
            need_copy_mod_name.add(mod_name)

    for mod_name in need_copy_mod_name:
        copyTreeWithCRLFConv(os.path.join(tmpoutPath, "com", "tencent", "wea", mod_name, "protocol"),
                             '*.java',
                             os.path.join(GENERATE_JAVA_OUT, f"{mod_name}/protocol/"),
                             addSVN=False, checkDiff=True)
        print(f"copy {mod_name} protocol")

    ignoreList = ["TcaplusDbWrapper.java",
                  "MsgTypes.java",
                  "RpcMsgTypes.java",
                  "ResMapAllServerLite.java",
                  "IdipCmdIdUtil.java",
                  "SrMsgTypes.java"]
    Util.removeDeletedMsg(os.path.join(timiPath.common_path,
                                       '../../projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs'),
                          os.path.join(timiPath.common_path,
                                       'protos/cs_proto/generated/client'),
                          ["AbstractPbMsgClientHandler.java",
                           "AbstractPbMsgClientNtfHandler.java",
                           "AbstractPbMsgClientS2CHandler.java",
                           "PbMsgClientHandlerFactory.java",
                           "ServerMsgParser.java"])
    Util.removeDeletedMsg(tmpoutPath,
                          os.path.join(GENERATE_JAVA_OUT, 'protocol/'),
                          ignoreList)


def runServerTlog(genpath):
  parse_tmpl.generateTLog(os.path.join(timiPath.protos_path, "tlog_proto"), genpath
                          , packageName = "com.tencent.wea"
                          , simpleMeta = ["OnlineCnt", "SafeReportFlow","MidasAccountFlow",
                                          "ClearBestRecoder", "UgcGoodsChangeStatusFlow",
                                          "UgcBuyGoodsPublishFlow", "UGCShopInfoTable",
                                          "MapBasicInfoTable","MapSkyboxInfoTable",
                                          "MapActorInfoTable","GroupActorInfoTable", "UGCAchievemenInfoTable",
                                          "LOGIDAILABRECMD8001","LOGIDAILABRECMD8001DEV",
                                          "LOGIDAILABRECMD8011","LOGIDAILABRECMD8011DEV",
                                          "MidasPayFlow", "SecAntiDataFlow", "CreditScoreFeedback",
                                          "SecVerifyFlow", "EventPointFlow", "RichMonopolyActivityRankFlow"])
  syncWithCRLFConv(
    os.path.join(os.path.join(genpath, timiPath.generate_package), "tlog/flow"),
    os.path.join(GENERATE_JAVA_OUT, "tlog/flow"))
  syncWithCRLFConv(
    os.path.join(os.path.join(genpath, timiPath.generate_package),
                 "tlog/letsgods"),
    os.path.join(GENERATE_JAVA_OUT, "tlog/letsgods"))
  # copyTreeWithCRLFConv(genpath, '*.java', GENERATE_JAVA_OUT + '/com/tencent/wea/tlog/flow',
  #                addSVN=True, checkDiff=True)
  # Util.removeDeletedMsg(GENERATE_JAVA_OUT + '/com/tencent/wea/tlog/flow', 'protos/tlog_proto/generated', [])
  copyFilesWithCRLFConv(os.path.join(timiPath.protos_path, "tlog_proto/*.xml"),
                 GENERATE_TLOG_XML_OUT, addSVN=False,
                 checkDiff=True)


def rsyncAllJavaProtocol():
    generate_java_path = os.path.join(
        os.path.join(GENERATE_PB_JAVA_TMP_OUT, "all/java"),
        timiPath.generate_package)
    syncWithCRLFConv(os.path.join(generate_java_path, "protocol"),
                     os.path.join(GENERATE_JAVA_OUT, "protocol"))

    syncWithCRLFConv(os.path.join(generate_java_path, "attr"),
                     os.path.join(GENERATE_JAVA_OUT, "attr"))

    # 拷贝mod的协议java
    need_copy_mod_name = set()
    mod_scan_generate_java_path = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "all", "java")
    for root, dirs, files in os.walk(mod_scan_generate_java_path):
        for dir_name in dirs:
            relative_path = os.path.relpath(os.path.join(root, dir_name), mod_scan_generate_java_path).replace('\\', '/')
            if not relative_path.startswith("com/tencent/wea/"):
                continue
            if "/protocol/" not in relative_path:
                continue

            parts = relative_path.split('/')
            if len(parts) < 5:
                continue
            if parts[4] != "protocol":
                continue
            mod_name = parts[3]
            need_copy_mod_name.add(mod_name)

    for mod_name in need_copy_mod_name:
        syncWithCRLFConv(os.path.join(mod_scan_generate_java_path, "com", "tencent", "wea", mod_name, "protocol"),
                         os.path.join(GENERATE_JAVA_OUT, f"{mod_name}/protocol"))
        print(f"copy {mod_name} protocol")


    syncWithCRLFConv(os.path.join(generate_java_path, "rpc"),
                     os.path.join(GENERATE_JAVA_OUT, "rpc"))
    syncWithCRLFConv(os.path.join(generate_java_path, "attr"),
                     os.path.join(GENERATE_JAVA_OUT, "attr"))


def runUpdateErrCode():
  updateErrorCode.main()


def runGenResEnumWeAEnum():
    genResEnumWeAEnum.generate()


def runGenResEnumWeAEnumXml():
    genResEnumWeAEnum.generate_xml()

def runServerG6IrpcProto(outputpath):
  g6_irpc_generator.genG6IrpcProto(outputpath,
                                   rpc_default_impl=RPC_DEFAULT_IMPL)
  outterpath = os.path.join(outputpath, "..")
  copyFilesWithCRLFConv(os.path.join(outputpath, '*.java'),
                 os.path.join(outterpath, 'com/tencent/wea/g6/irpc/service'),
                 addSVN=False, checkDiff=False)
  g6_irpc_path = os.path.join(GENERATE_JAVA_OUT,
                              "g6/irpc")

  copyTreeWithCRLFConv(os.path.join(outterpath, "com/tencent/wea/g6/irpc/"),
                 '*.java',
                 g6_irpc_path,
                 addSVN=False, checkDiff=True)


def genAdminPbHelper(outputpath):
  admin_proto_generator.genAdminProto(outputpath,
                                   rpc_default_impl=RPC_DEFAULT_IMPL)
  outterpath = os.path.join(outputpath, "..")
  copyFilesWithCRLFConv(os.path.join(outputpath, '*.java'),
                 os.path.join(outterpath, 'com/tencent/wea/adminpb'),
                 addSVN=False, checkDiff=False)
  adminpb_path = os.path.join(GENERATE_JAVA_OUT,
                              "adminpb")

  copyTreeWithCRLFConv(os.path.join(outterpath, "com/tencent/wea/adminpb/"),
                 '*.java',
                 adminpb_path,
                 addSVN=False, checkDiff=True)


def runServerGrpcProto(outputpath):
  outterpath = os.path.join(outputpath, "..")
  copyFilesWithCRLFConv(os.path.join(outputpath, '*.java'),
                 os.path.join(outterpath, 'com/tencent/wea/grpc/service'),
                 addSVN=False, checkDiff=False)
  grpc_path = os.path.join(GENERATE_JAVA_OUT,
                           "grpc")

  copyTreeWithCRLFConv(os.path.join(outterpath, "com/tencent/wea/grpc/"),
                 '*.java',
                 grpc_path,
                 addSVN=False, checkDiff=True)


def runServerSsProto(outputpath):
  # ss协议加上版本号字段
  ss_protocol_generator.add_proto_version_to_all_ss_proto()
  ss_protocol_generator.genSsProto(outputpath, rpc_default_impl=RPC_DEFAULT_IMPL)
  outterpath = os.path.join(outputpath, "..")
  copyFilesWithCRLFConv(os.path.join(outputpath, 'RpcMsgTypes.java'),
                 outterpath + '/com/tencent/wea/protocol/',
                 addSVN=False, checkDiff=True)
  copyFilesWithCRLFConv(os.path.join(outputpath, 'IdipCmdIdUtil.java'),
                 outterpath + '/com/tencent/wea/protocol/idip/',
                 addSVN=False, checkDiff=True)
  copyTreeWithCRLFConv(os.path.join(outputpath, 'forward'), '*.java',
                 os.path.join(GENERATE_JAVA_OUT, 'forward'),
                 addSVN=False, checkDiff=False)
  removeUnnecessaryFile()
  copyTreeWithCRLFConv(os.path.join(outputpath, 'api_service'), '*.java',
                       os.path.join(GENERATE_JAVA_OUT + '/rpc/api_service'),
                       addSVN=False, checkDiff=False)
  copyTreeWithCRLFConv(os.path.join(outputpath, 'service'), '*.java',
                 os.path.join(GENERATE_JAVA_OUT + '/rpc/service'),
                 addSVN=False, checkDiff=False)
  copyTreeWithCRLFConv(os.path.join(outterpath, "com/tencent/wea/protocol/"),
                 '*.java',
                 os.path.join(GENERATE_JAVA_OUT, 'protocol/'),
                 addSVN=False, checkDiff=True)

  idippath = os.path.join(outputpath, "idip")
  for root, dirs, files in os.walk(idippath):
    if root != idippath:
      continue
    for dir in dirs:
      if dir.endswith("svr") and dir != "gamesvr":
        factory_path = os.path.join(timiPath.projects_path,
                                    dir + "/src/main/java/com/tencent/wea/rpc/idiphandler/IdipMsgHandlerFactory.java")
        if os.path.exists(factory_path):
          os.remove(factory_path)
        syncWithCRLFConv(os.path.join(idippath, dir),
                  os.path.join(timiPath.projects_path,
                               dir + "/src/main/java/com/tencent/wea/rpc/idiphandler"),
                  ignoreExisting=True)
        continue
      factory_path = os.path.join(timiPath.projects_path,
                                  dir + "/src/main/java/com/tencent/wea/playerservice/idiphandler/IdipMsgHandlerFactory.java")
      if os.path.exists(factory_path):
        os.remove(factory_path)
      scanAndConvCRLF2LF(os.path.join(idippath, dir))
      copyFilesWithCRLFConv(os.path.join(idippath, dir + "/*.java"),
                     os.path.join(timiPath.projects_path,
                                  dir + "/src/main/java/com/tencent/wea/playerservice/idiphandler"),
                     addSVN=False, checkDiff=True, onlyInsert=True)             


def runServerCsProto(outputpath):
  cs_protocol_generator.genCsProto(outputpath)
  scanAndConvCRLF2LF(outputpath)
  outterpath = os.path.join(outputpath, "..")
  copyFilesWithCRLFConv(os.path.join(outputpath, 'MsgTypes.java'),
                 os.path.join(outterpath, 'com/tencent/wea/protocol/'),
                 addSVN=False, checkDiff=False)
  syncWithCRLFConv(os.path.join(outputpath, "gamesvr/handler"),
            os.path.join(timiPath.projects_path,
                         "gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler"),
            ignoreExisting=True)
  syncWithCRLFConv(os.path.join(outputpath, "gamesvr/commonforward"),
            os.path.join(timiPath.projects_path,
                         "gamesvr/src/main/java/com/tencent/wea/playerservice/commonforward"),
            ignoreExisting=True)
  copyFilesWithCRLFConv(os.path.join(outputpath, "gamesvr/*.java"),
                 os.path.join(timiPath.projects_path,
                              "gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler"),
                 addSVN=False, checkDiff=True)

  copyFilesWithCRLFConv(os.path.join(outputpath, "simulator/*.java"),
                 os.path.join(timiPath.projects_path,
                              "simulator4j/src/main/java/com/tencent/wea/simulator4j/cs"),
                 addSVN=False, checkDiff=True)

  for root, dirs, files in os.walk(os.path.join(outputpath, "simulator")):
    if root != os.path.join(outputpath, "simulator"):
      continue
    for dir in dirs:
      syncWithCRLFConv(os.path.join(outputpath, "simulator/" + dir),
                os.path.join(timiPath.projects_path,
                             "simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/" + dir),
                ignoreExisting=True)

  for root, dirs, files in os.walk(outputpath):
    if root != outputpath:
      continue
    for dir in dirs:
      if dir.endswith("svr") and dir != "gamesvr":
        syncWithCRLFConv(os.path.join(outputpath, dir + "/handler"),
                  os.path.join(timiPath.projects_path,
                               dir + "/src/main/java/com/tencent/wea/cshandler/handler"),
                  ignoreExisting=True)
        copyFilesWithCRLFConv(os.path.join(outputpath, dir + "/*.java"),
                       os.path.join(timiPath.projects_path,
                                    dir + "/src/main/java/com/tencent/wea/cshandler"),
                       addSVN=False, checkDiff=True)
        syncWithCRLFConv(os.path.join(outputpath, dir + "/gridOperator"),
                         os.path.join(timiPath.projects_path,
                                      dir + "/src/main/java/com/tencent/wea/farmservice/managers/gridOperator"),
                         ignoreExisting = True
        )


def runMonitorDashboardJson():
  print("开始生成监控中心的dashboard json")
  monitor_file_path = os.path.abspath(os.path.join(timiPath.timiutil_path,
                                                   "src/main/java/com/tencent/timiutil/monitor/MonitorId.java"))
  base_dashboard_json_path = os.path.abspath(GENERATE_PB_JAVA_TMP_OUT)
  monitor_generator.generate(monitor_file_path, base_dashboard_json_path)


# 注意生成路径，每个模块尽量用独立目录，且目录里都是生成代码，这样rsync时就可以将不需要的文件删除
def runServerAll():
  # all mode 生成代码到 tmpGenerateCode all 路径之下
  
  weaProj.reset()

  # gen java path
  generate_java_path = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "all/java")
  # gen proto path
  generate_proto_path = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "all/proto")
  # 先清理生成目录
  Util.rm_r(generate_java_path)
  Util.rm_r(generate_proto_path)

  os.makedirs(generate_java_path, exist_ok=True)
  os.makedirs(generate_proto_path, exist_ok=True)

  batch_run([
    (runServerAttr, (generate_java_path, generate_proto_path, True), {}),
    (runServerTcaplus, (generate_java_path,), {}),
    (runServerRes, (generate_java_path,), {}),
    (runServerProtocol, (generate_java_path, True), {}),
    (runServerTlog, (generate_java_path,), {}),
    (runUpdateErrCode, (), {}),
    (runMonitorDashboardJson, (), {}),
    (runOtherProtocol, (generate_java_path,), {}),
    (runHotResProtocol, (generate_java_path,), {}),
    (runGenResEnumWeAEnum, (), {}),
  ], initializer=initWeaProj, initargs=("protocol", GENERATE_ISOLATION_HOME, GENERATE_ISOLATION_SWITCH))
  rsyncAllJavaProtocol()

def runServerSep(args):
  mode = args.mode

  # 先清理目录，再生成
  if mode == 'attr':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/attr/java")
    protopath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/attr/proto")
    Util.rm_r(javapath)
    Util.rm_r(protopath)
    runServerAttr(javapath, protopath, False)
  elif mode == 'tcaplus':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/tcaplus")
    Util.rm_r(javapath)
    runServerTcaplus(javapath)
  elif mode == 'res':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/xlsRes/java")
    Util.rm_r(javapath)
    runServerRes(javapath)
    ##runServerDsCfg()
  elif mode == 'protocol':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/protocol")
    Util.rm_r(javapath)
    runServerProtocol(javapath, False)
  elif mode == 'tlog':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/tlog")
    Util.rm_r(javapath)
    runServerTlog(javapath)
  elif mode == 'updateErr':
    # javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/updateErr")
    runUpdateErrCode()
  elif mode == 'simulator4j':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/simulator4j")
    Util.rm_r(javapath)
    runServerSimulator4j(javapath)
  elif mode == 'monitor':
    runMonitorDashboardJson()
  elif mode == 'other':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/other")
    Util.rm_r(javapath)
    runOtherProtocol(javapath)
  elif mode == 'hotres':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/hotRes/java")
    Util.rm_r(javapath)
    runHotResProtocol(javapath)
  elif mode == 'adminpb':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/adminpb")
    Util.rm_r(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/adminpb"))
    runAdminProtocol(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/adminpb"))
  elif mode == 'generateResEnumWeAEnum':
      runGenResEnumWeAEnum()
  elif mode == 'generateResEnumWeAEnumXml':
      runGenResEnumWeAEnumXml()
  else:
      print('unknown cmd')


# svn_adder.add2SVN(os.path.abspath(GENERATE_JAVA_OUT), True)

def main(args):
  initWeaProj("protocol", genHome=GENERATE_ISOLATION_HOME, genIsolation=args.isolation)
  if args.mode == 'all':
    runServerAll()
  else:
    runServerSep(args)

if __name__ == "__main__":
  argparse = argparse.ArgumentParser(prog='config_patch', description='config patch tool')
  argparse.add_argument('mode', type=str, default='all', help='the target mode')
  argparse.add_argument('--isolation', action='store_true', help='whether isolation?')
  args = argparse.parse_args()
  print("got args", args)
  GENERATE_ISOLATION_SWITCH = args.isolation
  start_time = time.time()
  main(args)
  # runServerSep("protocol")
  end_time = time.time()
  print("generate code cost {} secs".format(end_time - start_time))
  # runServerProtocol(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/protocol"), False)
  # runServerAttr(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/attr"), False)
  # runServerDb(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/db"), False)
  # runServerRes(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/xlsRes"), False)
