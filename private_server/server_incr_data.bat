@echo off
chcp 65001
SETLOCAL ENABLEDELAYEDEXPANSION

:: 设置Git仓库的根目录，需要根据实际情况修改路径
pushd "%~dp0\.."

set "repoRoot=%cd%"
echo %cd%

:: 设置数据目录和增量数据目录的路径
set "dataDir=%repoRoot%\excel\server\data"
set "incrDataDir=%repoRoot%\excel\server\incr_data"

:: 删除增量数据目录
rmdir /s /q "%incrDataDir%"

:: 确保增量数据目录存在
if not exist "%incrDataDir%" mkdir "%incrDataDir%"

:: 获取有变化的文件列表（新增和修改），并复制到增量数据目录
for /f "delims=" %%i in ('git diff --name-only HEAD "%dataDir%"') do (
    echo 复制变更文件: %%i
    xcopy /S /Q /Y /F "%%i" "%incrDataDir%\"
)

:: 获取新添加但还没提交的文件列表，并复制到增量数据目录
for /f "delims=" %%i in ('git ls-files --others --exclude-standard "%dataDir%"') do (
    echo 复制新增文件: %%i
    xcopy /S /Q /Y /F "%%i" "%incrDataDir%\"
)

:: 返回原始目录
popd

echo 处理完成。

ENDLOCAL