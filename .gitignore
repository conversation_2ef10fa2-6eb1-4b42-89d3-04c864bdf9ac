# 此为注释 – 将被 Git 忽略

# 忽略 build/ 目录下的所有文件
build/
__pycache__
*.pyc
*.log*
log.txt
.python-version
tags
.gradle/
.vscode
*.swp
*.iml
.idea
.vs/
*.swp
online
out/
report
*.hprof
*.DS_Store
WeA/.gradle
WeA/.idea
WeA/common/common/tmpGenerateCode
WeA/common/common/tGenCode
WeA/common/Content
WeA/common/LetsGo
WeA/common/clientTools
WeA/common/src/main/resources/common.properties
WeA/common/src/main/java/com/tencent/*/*.class
WeA/common/src/generated/main/java/com/tencent/wea/
WeA/protocol
run/*/lib/*.jar
run/*/lib/*.so
run/common/ipdb.dat
run/dep_env_init/windows/j2sdk-windows-x86_64-8u232
run/dep_env_init/windows/javafx-sdk-overlay
run/dep_env_init/windows/TencentKona-11.0.18.b1
run/*/polaris/backup
run/*/tsssdk_log
run/*/bin/tsssdk_log
run/tools/async-profiler/build

run/dsa/bin/df.*.txt
run/dsa/bin/free.*.txt

!run/simulator4j/cfg/*.tmp

helm_chart_release/chart/*
helm_chart_release/chart/repo
helm_chart_release/chart/*/values.yaml

tools/wea-game-monitor/repo
tools/gradle
tools/plugin4nk/bin

WeA/buildJdkVersion.txt
WeA/build.properties
WeA/build.env
run/common/build.properties
run/common/buildJdkVersion.txt
run/common/resource/data/*.*
run/common/resource/data/cocrobot/*
run/common/resource/resConfig*xml
run/common/resource/xml/tlog.xml
run/common/resource/res_convert_map.txt
run/common/resource/xml/cs_meta.xml
run/common/tcaplusproto/*_db.xml

run/common/dscfg
run/common/resource/xml/event.xml
run/common/resource/xml/event_global_cfg.xml
run/common/resource/xml/submode

run/tcaplusclient/conf/config.xml.*

apps/log/tagent/
apps/log/tcenterd/
apps/log/tcmcenter/
apps/log/tconnd4tcm/
apps/operateLog/
apps/system_script/tbusmgr4tcm.xml
apps/tagent/bin/start_tagent.sh
apps/tagent/bin/stop_tagent.sh
apps/tagent/bin/tmp/
apps/tagent/cfg/tagent.xml
apps/tagent/log/
apps/tbus_channels_1001.bak
apps/tcenterd/bin/cmd_tcenterd.sh
apps/tcenterd/bin/start_tcenterd.sh
apps/tcenterd/bin/stop_tcenterd.sh
apps/tcenterd/bin/tcenterd_tagents.xml
apps/tcenterd/cfg/tcenterd.xml
apps/tcm/bin/cmd_tcmcenter.sh
apps/tcm/bin/reload_tcmcenter.sh
apps/tcm/bin/start_tcmcenter.sh
apps/tcm/bin/stop_tcmcenter.sh
apps/tcm/bin/tcm_bak/
apps/tcm/cfg/developer/host.xml
apps/tcm/cfg/developer/proc.xml
apps/tcm/cfg/developer/proc_deploy.xml
apps/tcm/cfg/proc.xml
apps/tcm/cfg/tcmcenter.xml
apps/tcm/cfg/tcmconsole.xml
apps/tcm/conf/
apps/tcm/log/
apps/tcm/tcm_water_log/
apps/tconnd/bin/cmd_tconnd.sh
apps/tconnd/bin/start_tconnd.sh
apps/tconnd/bin/stop_tconnd.sh
apps/tconnd/cfg/tconnd.xml
apps/version.history.txt
run/CleanTable.py
run/ModifyDB.py
run/common/resource/xml/allservers.xml
arthas-output
run/*/arthas_*.*.*.*.*
run/*/conf/config_*.*.*.*.properties
run/*/conf/realtime_config_*.*.*.*.properties
run/*/conf/tlog_conf_*.*.*.*.xml
run/*/curl_*.*.*.*.*
run/*/check_*.*.*.*.*
run/*/debugPrint_*.*.*.*.*
run/*/*_*.*.*.*.pid
run/*/*_*.*.*.*_stop.pid
run/*/history.txt
run/*/jvm.g1_*.*.*.*.options
run/*/kill_*.*.*.*.*
run/*/log4j2_*.*.*.*.xml
run/*/logback_*.*.*.*.xml
run/*/reload_*.*.*.*.*
run/*/config_and_reload_*.*.*.*.*
run/*/restart_*.*.*.*.*
run/*/run.start.cmd.txt
run/*/runJdkVersion.txt
run/*/startCheckCmd_*.*.*.*.*
run/*/start_*.*.*.*.*
run/*/stop_*.*.*.*.*
run/*/groovy_*.*.*.*.*
run/generate/
run/log/
run/svr_tools

run/simulator4j/cfg/config.pb
run/simulator4j/cfg/jvmOption.conf
run/simulator4j/history.txt
run/simulator4j/properties/temp.properties
run/simulator4j/realtime.properties
run/simulator4j/run.start.cmd.txt
run/tbuspp/bin/crontab.bak
run/tbuspp/bin/tbusppbaseagent.pid
run/tbuspp/bin/tbusppmetrics.pid
run/tbuspp/cfg/base_agent.conf
run/tbuspp/log/
run/tcaplusclient/conf/config.self
run/tcaplusclient/conf/config.xml
run/tcaplusclient/lib/tcaplusclient

run/tconnd4*/tconnd_*.*.*.*.sh
run/tconnd4*/conf/tconnd_*.*.*.*.xml*
run/tconnd4*/conf/tconnd_log_*.*.*.*.xml*
run/tconnd4*/check_*.*.*.*.sh
run/tconnd4*/reload_*.*.*.*.sh
run/tconnd4*/restart_*.*.*.*.sh
run/tconnd4*/start_*.*.*.*.sh
run/tconnd4*/stop_*.*.*.*.sh
run/tconnd4*/kill_*.*.*.*.sh

run/tools/async-profiler/*.svg
run/tools/async-profiler/*.txt
run/tools/debug_port_iptables_config.sh
run/tools/debug_port_only_accept_local.sh
run/tools/javaSetEnv/javaSetEnv.sh
run/tools/jstack/*/
run/tools/tbus/business_tbus_show.sh
run/tools/tbus/tbus_ntoa.py

!run/dsc/bin/*.so
!run/dsc/lib/*.so
!run/dsa/bin/*.so
!run/dsa/lib/*.so
!run/ds_scaler/bin/*.so
!run/ds_scaler/lib/*.so
!run/dsmgr_client/bin/*.so
!run/dsmgr_client/lib/*.so

run/dsa/bin/base_server.pid
run/dsa/cfg/server.yaml
run/dsa/lib/gtrace_conf.xml
run/dsa/log/
run/dsa/ds/

run/dsc/bin/base_server.pid
run/dsc/cfg/server.yaml
run/dsc/lib/gtrace_conf.xml
run/dsc/log/

tools/idea/Idea-CodeStyle.xml

run/dsmgr_client/bin/base_server.pid
run/dsmgr_client/cfg/server.yaml
run/dsmgr_client/lib/gtrace_conf.xml
run/dsmgr_client/log/
run/dsa/bin/ds_gate_mem.backup
run/dsa/bin/dsa_mem.backup

run/authagentold/log/
run/authagentold/cfg/*.*.*.*.xml
!run/authagentold/lib/*.so

run/authagent/conf/authsvr_conf_*.*.*.*.json
run/authagent/conf/authsvr_tlog_*.*.*.*.xml

run/dsa/lib/approuter_log.xml
run/dsc/lib/approuter_log.xml
run/dsa/lib/gtrace_log.xml
run/dsc/lib/gtrace_log.xml

run/rmicenter/*.json

tconnd/generate
tconnd/log
tconnd/patchReport
tconnd/tcmdeploy
tconnd/tconnd4*/conf/tconnd_*.xml
tconnd/tconnd4*/bin/tconnd_*.xml
tconnd/tcmscripts/*.pyc
tconnd/authagent4*/bin/*.xml
tconnd/authagent4*/cfg/*.xml
tconnd/tbuspp/log
WeA/common/common
/run/dsa/cfg/dsa_conf/dsa_config.lua
/run/dsc/cfg/dsc_comm.json
/run/dsa/cfg/dsa_conf/fleet_config.lua
run/dep_env_init/windows/TencentKona-11.0.18.b1_jdk_fiber_windows-x86_64_signed

run/gamesvr/conf/grp_dsa_info_*.*.*.*.json
/run/battlesvr/heapDumpStart_*.*.*.*.*

# profile的工具例外
!run/tools/async-profiler/build
/run/tconndcluster/conf/tconnd_*.*.*.*.xml

.classpath
.project
.settings
/run/dsa/log

# ds core生成脚本
core_scan_sync.sh
WeA/projects/dirsvr/src/main/java/com/tencent/wea/cshandler/DirsvrPbMsgHandlerFactory.java
WeA/projects/dirsvr/src/main/java/com/tencent/wea/rpc/idiphandler/IdipMsgHandlerFactory.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/GamesvrPbMsgHandlerFactory.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/idiphandler/IdipMsgHandlerFactory.java
WeA/projects/gamesvrZone/src/main/java/com/tencent/wea/playerservice/idiphandler/IdipMsgHandlerFactory.java
WeA/projects/idipsvr/src/main/java/com/tencent/wea/rpc/idiphandler/IdipMsgHandlerFactory.java
WeA/projects/scenesvr/src/main/java/com/tencent/wea/cshandler/ScenesvrPbMsgHandlerFactory.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/PbMsgClientHandlerFactory.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/ServerMsgParser.java
WeA/projects/translatesvr/src/main/java/com/tencent/wea/rpc/idiphandler/IdipMsgHandlerFactory.java
WeA/projects/xiaowosvr/src/main/java/com/tencent/wea/rpc/idiphandler/IdipMsgHandlerFactory.java

WeA/projects/clubsvr/src/main/java/com/tencent/wea/rpc/idiphandler/IdipMsgHandlerFactory.java
WeA/projects/dirsvr/src/main/java/com/tencent/wea/cshandler/DirsvrPbMsgHandlerFactory.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/GamesvrPbMsgHandlerFactory.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/interaction/Router.java
WeA/projects/scenesvr/src/main/java/com/tencent/wea/cshandler/ScenesvrPbMsgHandlerFactory.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/ServerMsgParser.java
run/common/resource/xml/tlog.xml
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/club/ClubPinShareMapMsgHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/IntellectualActiviyNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ReceiveSpringBlessingCardNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/SpringPrayClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/club/ClubPinShareMapClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/player/PlayerPrayShareClientHandler.java
WeA/projects/xiaowosvr/src/main/java/com/tencent/wea/rpc/idiphandler/AqDoXiaoWoBulletinBanReqHandler.java
WeA/common/src/main/java/com/tencent/wea/namedenum/*/*
WeA/timiutil/src/main/java/com/tencent/wea/namedenum/*/*
WeA/common/src/main/java/com/tencent/resourceloader/resclass/AiLabMatchDyeWhiteListData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/FashionScoreCalcData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/FeatureKeySwitchConf.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/MatchAiLabCGrpAIRandomCfgData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/MatchAiLabCGrpWarmRoundAILevel.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/MatchAiLabCGrpWarmRoundLimitData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/MatchAiLabCGrpWarmRoundReturningAddExtraFactorData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/MatchAiLabCGrpWarmRoundReturningSubExtraFactorData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/MatchAiLabGrpWarmRoundScoreCaseData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/RaffleTabData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/ReturningUserConfData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/SpringBlessingSponsorConfData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/UgcCreateChatGroupWhiteListConfig.java
WeA/projects/clubsvr/src/main/java/com/tencent/wea/rpc/idiphandler/SpringH5BatchRandomClubInfoReqHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/GetAccumulateBlessingsOnlineTimeMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/GetReturnActivityRewardMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/ListUseItemChangeRecordMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/ListUseItemShareRecordMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/RedPacketClickRedDotMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/RedPacketEnterSceneMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/RedPacketPatchInfoMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/RedPacketQuerySingleMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/UltramanThemeAcitvityTeamMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/UltramanThemeAddSelfCollectionMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/UltramanThemeSelfCollectionProgressMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/club/ClubRedDotClearMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/fps/FpsGetDropLimitInfoMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/rank/FetchTopRankByScoreMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/tyc/TYCFpsGetSettingsMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/tyc/TYCFpsSetSettingsMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/idiphandler/DoCleanUserRegionRankReqHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/idiphandler/DoUgcMapHandlerReqHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/idiphandler/LuckyStarGetGiveInfoReqHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/idiphandler/LuckyStarGetHandbookInfoReqHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/idiphandler/LuckyStarReceiveGiveStarReqHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/idiphandler/AqDoModifyUgcRankScoreReqHandler.java
WeA/projects/idipsvr/src/main/java/com/tencent/wea/rpc/idiphandler/DoCleanUserRegionRankReqHandler.java
WeA/projects/idipsvr/src/main/java/com/tencent/wea/rpc/idiphandler/DoUgcMapHandlerReqHandler.java
WeA/projects/idipsvr/src/main/java/com/tencent/wea/rpc/idiphandler/LuckyStarGetGiveInfoReqHandler.java
WeA/projects/idipsvr/src/main/java/com/tencent/wea/rpc/idiphandler/LuckyStarGetHandbookInfoReqHandler.java
WeA/projects/idipsvr/src/main/java/com/tencent/wea/rpc/idiphandler/LuckyStarReceiveGiveStarReqHandler.java
WeA/projects/idipsvr/src/main/java/com/tencent/wea/rpc/idiphandler/SpringH5BatchRandomClubInfoReqHandler.java
WeA/projects/idipsvr/src/main/java/com/tencent/wea/rpc/idiphandler/SpringH5GetClubInfoReqHandler.java
WeA/projects/idipsvr/src/main/java/com/tencent/wea/rpc/idiphandler/SpringH5GetMyClubInfoReqHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/GetAccumulateBlessingsOnlineTimeClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/GetReturnActivityRewardClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ListUseItemChangeRecordClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ListUseItemShareRecordClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/RedPacketClickRedDotClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/RedPacketEnterSceneClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/RedPacketPatchInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/RedPacketQuerySingleClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ReturnActivityEndNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ReturnActivityGenericNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ReturningActiviyStartNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/TimeLimitedCheckInActivityDataChangeNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/UltramanThemeAcitvityTeamClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/UltramanThemeAddSelfCollectionClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/UltramanThemeSelfCollectionProgressClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/battle/QuitBattleNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/club/ClubRedDotClearClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/fps/FpsGetDropLimitInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/player/PlayerXiaowoAttrNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/rank/FetchTopRankByScoreClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/tyc/TYCFpsGetSettingsClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/tyc/TYCFpsSetSettingsClientHandler.java
WeA/projects/clubsvr/src/main/java/com/tencent/wea/rpc/idiphandler/IdipMsgHandlerFactory.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/GamesvrPbMsgHandlerFactory.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/interaction/Router.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/interaction/WishActivityHelpBind.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/PbMsgClientHandlerFactory.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/ServerMsgParser.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ActivityPrayerCardGiveClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ActivityPrayerCardInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ActivityPrayerCardLookClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/GetQualifiedActivityMarqueeIdClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/album/PlayerPictureLikeClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/album/PlayerPictureLikeHisClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/arena/ArenaSettingsClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/bag/UpdateFavoriteItemsClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/battle/BattleEndSettlementClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/room/RoomCoMatchReadyClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/starp/StarPGuideStepFlowClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/ugc/UgcRecommendSetClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/wolfkill/WolfKillNewUserTipsClientHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/album/PlayerPictureLikeHisMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/album/PlayerPictureLikeMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/arena/ArenaSettingsMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/ugc/UgcRecommendSetMsgHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/*
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/starp*

/run/dsa/cfg/ds_conf/ds_conf.json
/run/tools/syncTool/rsync_config.bat

/run/common/resource/xml/idip/*.xml
/run/common/resource/xml/idip/*/*.xml
tools/bx_data_tools/src/libs/Protocol/
tools/bx_data_tools/src/tcaplusso/pbhelper.cpp
tools/bx_data_tools/bin/tcaplusdbsvr
tools/bx_data_tools/bin/libtcaplusso.so
run/common/resource/xml/event.xml
run/scripts/generate.error
run/tools/dstool/dsa_g6_log_scan.ini
run/starpbattlesvr/heapDumpStart_*.*.*.*.*
tools/j6_data_tools/src/libs/Protocol/
tools/j6_data_tools/src/tcaplusso/pbhelper.cpp
tools/j6_data_tools/bin/collectsvr/collectsvr
tools/j6_data_tools/bin/tcaplusso/tcaplusdbsvr
tools/j6_data_tools/bin/tcaplusso/libtcaplusso.so

WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/UpdateWerewolfFullReduceActivityCartClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/WerewolfFullReduceActivityCartClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/WerewolfFullReduceActivityCartOrderClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/hok/
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/information/PlayerSettingShowAnimationButtonClientHandler.java
tools/j6_data_tools/bin/collectagentsvr/collectagentsvr
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/ActivitySquadAcquireRewardMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/ActivitySquadOpenFinalTreasureMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/starp/StarPGetMyContactRecordListMsgHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ActivitySquadAcquireRewardClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ActivitySquadFarmWateringTreeClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ActivitySquadKickMemberClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ActivitySquadOpenFinalTreasureClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/MobaSquadDrawRedPacketRefreshNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/PrayerCardRewardReachLimitNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/arena/ArenaGetHeroBattleStatisticClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/birthday/BirthdayBlessingRecordUpdateNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/birthday/BirthdayCardItemUpdateNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/birthday/BirthdayRemindClearClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/birthday/BirthdayRemindInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/birthday/BirthdayRemindNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/chat/BatchGetLatestMessageExtraInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/player/UpdatedReadyBattleOrnamentationBagInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/room/RoomGetCurRoundInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/room/RoomRoundInfoNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/room/RoomUgcMultiRoundCoinUseClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/starp/StarPGetMyContactRecordListClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/ugc/UgcMapExtraConfigEditClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/ugc/UgcMapExtraConfigGetClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/wolfkill/WolfKillTreasureDataClientHandler.java

WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/album/PlayerAlbumPicBatchAtTargetMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/album/PlayerAlbumPictureEditMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/album/PlayerAlbumPictureTextCheckMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/album/PlayerBatchPictureSettingMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/coc/CocGMCommandMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/idiphandler/SendItemDataIntoBagReqHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/interaction/PicAtTarget.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/SquadTaskExtraInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/album/PlayerAlbumPicBatchAtTargetClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/album/PlayerAlbumPicTargetAtNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/album/PlayerAlbumPictureEditClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/album/PlayerAlbumPictureTextCheckClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/album/PlayerBatchPictureSettingClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/arena/ArenaArenaCardHotInfoNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/arena/ArenaCardHotInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/arena/ArenaSeasonStatInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/arena/ArenaSeasonStatInfoNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/arena/SetSprayPaintClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/arena/UseSprayPaintClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/chat/ChatModuleInfoChangeNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/chat/ChatModuleInfoNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/coc/CocGMCommandClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/ugc/UgcUploadLoadingClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/ugc/UgcUploadLobbyCoverClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/wolfkill/WolfKillTreasureShareClientHandler.java
WeA/projects/ugcappsvr/src/main/java/com/tencent/wea/cshandler/handler/AppUgcUploadLoadingMsgHandler.java
WeA/projects/ugcappsvr/src/main/java/com/tencent/wea/cshandler/handler/AppUgcUploadLobbyCoverMsgHandler.java

WeA/common/src/main/java/com/tencent/resourceloader/resclass/TaskOptionalRewardData.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/ActivityGetMultiPlayerSquadIsFullClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/bag/OpenGiftPackageAnimationNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/farm/FarmSetLiuYanMessagePermissionClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/season/GetSeasonReviewRedPointInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/season/UpdatedSeasonReviewRedPointInfoClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/ugc/UgcMatchLobbyDetailExChangeClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/ugc/UgcMatchLobbyDetailExMultiClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/ugc/UgcMatchLobbyDetailPlayRecordClientHandler.java

run/common/resource/data/coc_gm_user_copy_data/
run/common/resource/xml/event_global_cfg.xml
run/common/resource/xml/starp/
run/common/resource/xml/submode/

WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/InflateRedPacketInflateMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/InflateRedPacketKickOutMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/InflateRedPacketQuitMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/activity/InflateRedPacketReceiveMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/bag/BagDressUpSettingMsgHandler.java
WeA/projects/gamesvr/src/main/java/com/tencent/wea/playerservice/cshandler/handler/recharge/MallActivityInflateRedPacketPurchaseMsgHandler.java
WeA/projects/roomsvr/src/main/java/com/tencent/wea/csforward/
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/InflateRedPacketInflateClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/InflateRedPacketKickOutClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/InflateRedPacketQuitClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/InflateRedPacketReceiveClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/activity/InflateRedPacketUpdateNtfClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/bag/BagDressUpSettingClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/chat/ChatBindModuleClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/chat/ChatUnbindModuleClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/player/PlayerHallStableStateClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/player/PlayerIntimateRelationRedPointClientHandler.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/recharge/MallActivityInflateRedPacketPurchaseClientHandler.java

WeA/common/src/main/java/com/tencent/resourceloader/resclass/ActivityRestaurantThemedData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/ActivityRestaurantThemedFoodData.java
WeA/common/src/main/java/com/tencent/resourceloader/resclass/ActivityRestaurantThemedMiscData.java
WeA/projects/simulator4j/src/main/java/com/tencent/wea/simulator4j/cs/gamesvr/player/UpdatePlayerLimitExperienceRedPointClientHandler.java
run/tcaplusclient/config.self
run/tcaplusclient/config.xml.*
.gfcopilotindexignore
.codebuddy
.codebuddyignore
.augment-guidelines
.augmentignore
