syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "ResKeywords.proto";
import "common.proto";
import "ResMultiBuyActivity.proto";

// 获取活动信息
message MultiBuyActivityInfo_C2S_Msg {
  optional int32 activityId = 1;        // 活动ID
}

message MultiBuyActivityInfo_S2C_Msg {
  optional int32 activityId = 1;        // 活动ID
  optional int32 dailyBuyCount = 2;     // 今日购买次数
  optional int32 dailyBuyThreshold = 3; // 购买阈值
  optional bool hasDoubleDiscount = 4;  // 是否享受双倍折扣
  optional bool doubleDiscountUsedToday = 5; // 今日是否已使用双倍折扣
  repeated com.tencent.wea.xlsRes.ActivityCommodityInfo commodities = 6; // 活动商品列表
  optional int64 totalSavedMoney = 7;   // 累计节省金额
  optional int32 remainingDiscounts = 8; // 剩余可用折扣数量
}

// 购买活动商品
message MultiBuyActivityBuy_C2S_Msg {
  optional int32 activityId = 1;        // 活动ID
  optional int32 commodityId = 2;       // 商品ID
  optional int32 buyNum = 3;            // 购买数量
}

message MultiBuyActivityBuy_S2C_Msg {
  optional int32 result = 1;            // 购买结果
  optional int32 usedDiscount = 2;      // 使用的折扣
  optional int32 finalPrice = 3;        // 实际支付价格
  optional bool unlockDoubleDiscount = 4; // 是否解锁明日双倍折扣
  optional int32 savedAmount = 5;       // 本次节省金额
  optional int32 discountId = 6;        // 使用的折扣ID
}

// 活动状态变化通知
message MultiBuyActivityStatusNtf {
  optional int32 activityId = 1;        // 活动ID
  optional int32 dailyBuyCount = 2;     // 今日购买次数
  optional bool hasDoubleDiscount = 3;  // 是否享受双倍折扣
  optional bool unlockDoubleDiscount = 4; // 是否刚解锁明日双倍折扣
  repeated int32 usedDiscountIds = 5;   // 已使用的折扣ID
  optional int64 totalSavedMoney = 6;   // 累计节省金额
  optional bool doubleDiscountUsedToday = 7; // 今日是否已使用双倍折扣
}

// 获取折扣预览
message MultiBuyDiscountPreview_C2S_Msg {
  optional int32 activityId = 1;        // 活动ID
  optional int32 commodityId = 2;       // 商品ID
}

message MultiBuyDiscountPreview_S2C_Msg {
  optional int32 commodityId = 1;       // 商品ID
  optional int32 originalPrice = 2;     // 原价
  optional int32 previewDiscount = 3;   // 预览折扣
  optional int32 previewPrice = 4;      // 预览价格
  optional bool willUseDoubleDiscount = 5; // 是否会使用双倍折扣
  optional int32 estimatedSaving = 6;   // 预计节省金额
}
