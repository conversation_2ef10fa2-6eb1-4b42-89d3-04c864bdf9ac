# -*- encoding:utf8 -*-
import os
import sys
import glob
import subprocess

root_dir = "../../../"

def to_camel_case_special(snake_str):
    return "".join(x[0].upper()+x[1:] for x in snake_str.split("_"))

def write_file(filename, lines):
    with open(filename, 'w', encoding='utf-8') as f:
        for line in lines:
            f.write(line)

def deal_res_proto(filename):
    import_files = search_where_import(filename.split('/')[-1].replace('.proto', ''))

    with open(filename, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    new_lines=[]
    has_change = False
    find_table = False
    table_line = ""
    message_line = ""
    for line in lines:
        if '@noCheck' in line:
            # 目前 @noCheck 是文件级别
            # 跳过手动标记的proto:间接import的文件不能被搜索到,但也能使用其字段
            return
        new_lines.append(line)

        if find_table:
            # 'message table_xx' 的下一行即 'repeated row_message rows = 1;'
            find_table = False

            has_nosvr = "noSvr" in table_line
            if has_nosvr:
                #标记过的不再处理
                continue

            message_line = line
            if 'repeated' not in message_line or len(message_line.split()) < 2:
                continue
            row_message = message_line.split()[1]
            if is_proto_use_message(row_message):
                print(row_message)
                continue
            search_count = search_message(import_files, row_message)

            if search_count == "0":
                # 表级别nosvr
                has_change = True
                add_nosvr(new_lines, [table_line])
            else:
                fields = get_fields(lines, row_message)
                nosvr_fields = []
                for field in fields:
                    field_search_count = search_message_field(import_files, to_camel_case_special(field))
                    check_field_search_count = search_message_field_incheck(table_line, row_message, to_camel_case_special(field))
                    if field_search_count == "0" and check_field_search_count == "0":
                        # 字段级别nosvr
                        has_change = True
                        nosvr_fields.append(field)
                add_nosvr_field(new_lines, row_message, nosvr_fields)

        if 'table_' in line:
            table_line = line
            if len(table_line.split()) < 2:
                continue
            table_message = table_line.split()[1]
            if is_proto_use_message(table_message):
                print(table_message)
                continue
            find_table = True


    if has_change:
        write_file(filename, new_lines)

def search_where_import(proto_filename):
    # 返回包含该proto的文件列表: 间接包含、import xx.xlsRes:* 等实际使用但不能被搜索到的文件,可以在proto中 @noCheck 标记
    import_proto_files = '''find {} -name '*.java' -not -path '{}WeA/common/src/generated/*' -not -path '{}WeA/common/common/tmpGenerateCode/*' | xargs  grep -H 'import.*{}' | awk -F: '{{print $1}}' | uniq '''.format(root_dir, root_dir, root_dir, proto_filename)
    return subprocess.getoutput(import_proto_files)

def search_message_field(files, field_name):
    cmd = "echo '{}' | xargs  grep {} | wc -l".format(files, field_name)
    #print(cmd)
    return subprocess.getoutput(cmd)

def search_message(files, message_name):
    cmd = "echo '{}' | xargs  grep {} | wc -l".format(files, message_name)
    return subprocess.getoutput(cmd)

def search_message_field_incheck(table_line, message_name, field_name):
    table_name = table_line.split()[1].replace('table_', '')
    if '{' in table_name:
        return '1'
    cmd = "find excel/pb_checker -name '*{}*.py' -o -name '*{}*.py' | xargs  grep -i {} | wc -l".format(table_name, message_name, field_name)
    #print(cmd)
    return subprocess.getoutput(cmd)

def get_proto_use_message():
    # 返回 protos 中被使用的 message_name 列表,这些返回给客户端的需要和其字段一致,不能标记nosvr
    cmd = "grep -Eo 'com.tencent.wea.xlsRes.[_a-zA-Z]*' protos/ -r  | awk -F: '{print $2}' | awk -F. '{print $5}' |sort |uniq"
    return subprocess.getoutput(cmd)

def is_proto_use_message(message_name):
    global proto_use_messages
    return message_name in proto_use_messages

def get_fields(proto_lines, message_name):
    fields = []
    find_message = False
    find_brace1 = False
    skip_keys = []
    for line in proto_lines:
        if "message " + message_name + " " in line or "message " + message_name + "{" in line:
            find_message = True
            continue
        if not find_message:
            continue

        if '(resKey)' in line and len(line.split('=')) >= 2:
            skip_keys = line.split('=')[1].strip(' ";\n').split(',')
            continue

        if '=' in line:
            field = line.split('=')[0].split()[-1]
            if field not in skip_keys:
                fields.append(field)

        if '}' in line:
            break
    return fields

def note_nosvr(src_lines, dst_lines, is_add):
    for index, line in enumerate(src_lines):
        for dst_line in dst_lines:
            if dst_line in line:
                if is_add:
                    # 添加@nosvr注释
                    add_str = "// @noSvr"
                    if "//" in line:
                        add_str = " @noSvr"
                    src_lines[index] = line[:-1] + add_str + '\n'
                else:
                    # 去除@nosvr注释
                    src_lines[index] = src_lines[index].replace('@noSvr', '')

def add_nosvr(src_lines, dst_lines):
    note_nosvr(src_lines, dst_lines, True)

def add_nosvr_field(src_lines, message_name, nosvr_fields):
    if len(nosvr_fields) == 0:
        return
    find_message = False
    find_brace1 = False
    for index, line in enumerate(src_lines):
        if "message " + message_name + " " in line or "message " + message_name + "{" in line:
            find_message = True
            continue
        if not find_message:
            continue

        if '=' in line:
            field = line.split('=')[0].split()[-1]
            if field in nosvr_fields and '@noSvr' not in line:
                add_str = "// @noSvr"
                if "//" in line:
                    add_str = " @noSvr"
                src_lines[index] = line[:-1] + add_str + '\n'

proto_use_messages = ''
def main():
    global proto_use_messages
    proto_use_messages = get_proto_use_message()
    files = subprocess.getoutput("find excel/proto -name '*.proto'")
    filelist = files.split()
    for protofile in filelist:
        deal_res_proto(protofile)
        #exit(0)


if __name__ == '__main__':
    main()
