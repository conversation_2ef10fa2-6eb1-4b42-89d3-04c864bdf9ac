#!/bin/bash

# 时间输入参数
START_TIME=${1:-"yesterday 12:00"}  # 第一个参数，默认昨天12点
END_TIME=${2:-"today 12:00"}        # 第二个参数，默认今天12点
TIMEZONE=${3:-"+0800"}              # 第三个参数，默认东八区

# 文件名处理函数
sanitize_filename() {
  echo "$1" | sed -e 's/[^A-Za-z0-9._-]/_/g' -e 's/__+/_/g'
}

# 生成输出文件名
FILE_START=$(date -d "$START_TIME" +"%Y%m%d_%H%M" 2>/dev/null || echo "custom_start")
FILE_END=$(date -d "$END_TIME" +"%Y%m%d_%H%M" 2>/dev/null || echo "custom_end")
OUTPUT_FILE="changes_${FILE_START}_to_${FILE_END}.txt"
OUTPUT_FILE=$(sanitize_filename "$OUTPUT_FILE")

# 执行差异对比
echo "正在分析时间段："
echo "起始点：$START_TIME"
echo "结束点：$END_TIME"
echo "时区：$TIMEZONE"

# 获取时间范围内的最早和最新提交
START_COMMIT=$(git rev-list -n1 --before="$START_TIME" HEAD)
END_COMMIT=$(git rev-list -n1 --before="$END_TIME" HEAD)

# 检查是否获取到有效提交
if [ -z "$START_COMMIT" ] || [ -z "$END_COMMIT" ]; then
  echo "错误：无法在指定时间范围内找到有效提交"
  exit 1
fi

git diff --name-status \
  $START_COMMIT \
  $END_COMMIT \
  > "$OUTPUT_FILE"

# 结果分析
if [ ! -s "$OUTPUT_FILE" ]; then
  echo "结果：未检测到文件变更"
  rm -f "$OUTPUT_FILE"
else
  echo "==================== 变更统计 ===================="
  echo "文件总数：$(wc -l < "$OUTPUT_FILE")"
  echo "变更类型分布："
  awk '
    {
      count[$1]++
      total++
    } 
    END {
      print "A\t添加\t" count["A"]+0
      print "D\t删除\t" count["D"]+0 
      print "M\t修改\t" count["M"]+0
      print "R\t重命名\t" count["R"]+0
      print "C\t复制\t" count["C"]+0
      print "T\t类型变更\t" count["T"]+0
      print "--------------------------------"
      print "总计\t\t" total
    }' "$OUTPUT_FILE" | column -t -s $'\t'
  
  echo "详细报告已保存至：$OUTPUT_FILE"
fi