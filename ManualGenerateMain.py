# encoding=utf-8
import os
import platform
import sys
import argparse
import time
import shutil
import re

SCRIPT_PATH = os.path.abspath(os.path.split(os.path.realpath(__file__))[0])
COMMON_HOME = SCRIPT_PATH
sys.path.append(COMMON_HOME)
from subprocess import Popen, PIP<PERSON>

def locateSvrTools():
  expectedDirInCmn = os.path.join(COMMON_HOME, "svr_tools")
  if os.path.exists(expectedDirInCmn):
    return expectedDirInCmn
  expectedDirInSvr = os.path.abspath(os.path.join(COMMON_HOME, "..", "..", "..", "run", "svr_tools"))
  if os.path.exists(expectedDirInSvr):
    return expectedDirInSvr
  return os.getenv("WEA_SVR_TOOLS_HOME")

svrToolsHome = locateSvrTools()
if not svrToolsHome or not os.path.exists(svrToolsHome):
  raise Exception(f"locate svr tools:{svrToolsHome} failed, checkout it first")
sys.path.append(os.path.join(svrToolsHome, "WeA"))

from pyHome import WeAPath
from pyHome.attr import attr_generator as AttrGenerator
from pyHome.db.tcaplus import tcaplus_generator as TcaplusGenerator
from pyHome.tlog import parse_tmpl as TLogGenerator
from pyHome.res import gen_java_class as ResClassGenerator
from pyHome.res import hotres_gen_java_class as HotResClassGenerator
from pyHome.util import Util, sync
from pyHome.util.WeaProject import WeaProject
from pyHome.util.batch_run import batch_run
from pyHome.util.protocol_run_cmd import run_cmd
from pyHome.protocol import gen_protocol
from pyHome.analyzepb import analyze_proto_generator
from pyHome.protocol.gen_protocol_path_args import GenProtocolPathArgs
from pyHome.error_code import gen_mod_error_code_excel
from pyHome.enum import gen_wea_named_enum
from pyHome.enum import gen_wea_named_enum_xml


# from timipy.util.batch_run import batch_run
from timipy import timiPath
# from timipy.attrTool import attr_generator
# from timipy.dbTool import tcaplus_generator
from timipy.dsCfgTool import gen_ds_config
from timipy.monitorTool import monitor_generator
# from timipy.tlog import parse_tmpl
# from timipy.util import Util
# from timipy.util import sync
# from timipy.xlsRes import gen_java_class
# from timipy.xlsRes import genResEnumWeAEnum
# from timipy.hotres import hotres_gen_java_class


def timeit(func):
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} cost {end - start} secs")
        return result
    return wrapper

GENERATE_JAVA_OUT = timiPath.java_src_to_path
GENERATE_PB_JAVA_TMP_OUT = timiPath.generate_path
GENERATE_TLOG_XML_OUT = os.path.join(timiPath.project_aoe_run_path, 'common/resource/xml')
RPC_DEFAULT_IMPL = os.getenv('RPC_DEFAULT_IMPL', 'true').lower() == 'true'

# Util.rm_r(GENERATE_PB_JAVA_TMP_OUT)
os.makedirs(GENERATE_PB_JAVA_TMP_OUT, exist_ok=True)

curpypath = os.path.split(os.path.realpath(__file__))[0]
sys.path.append(os.path.join(curpypath, "excel"))
import conv2pb

GENERATE_ISOLATION_SWITCH=False
GENERATE_ISOLATION_HOME=os.path.normpath(os.path.join(curpypath, "..", "..", "protocol"))

weaProj = None
logger = Util.initLogger(WeAPath.LOG_HOME)

def initWeaProj(catName, genHome, genIsolation):
  global weaProj
  if weaProj is not None:
    return
  weaProj = WeaProject(catName, genHome=genHome, genIsolation=genIsolation)
  # add project entries
  weaProj.add(entryName="keywords", workDir=".", protoHomes=["excel/basic/proto"], deps=[])
  weaProj.add(entryName="res", workDir=".", protoHomes=["excel/server/proto"], deps=["keywords"])\
    .addPkgTransfer(os.path.join("com", "tencent", "wea", "xlsRes", "keywords"), "keywords")
  weaProj.add(entryName="base_common", workDir=".", protoHomes=["protos/base_common_proto"], deps=[])
  weaProj.add(entryName="attr", workDir=".", protoHomes=["protos/cs_attr_proto"], deps=["keywords"])
  weaProj.add(entryName="common", workDir=".", protoHomes=["protos/common_proto"], deps=["res", "attr", "base_common"])
  weaProj.add(entryName="tcaplus", workDir=".", protoHomes=[WeAPath.TOOL_DB_TCAPLUS_PROTO_HOME, "protos/db_proto/proto"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="simulator4j", workDir=".", protoHomes=["protos/simulator4j_proto/proto"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="other", workDir=".", protoHomes=["protos/other_proto/proto"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="admin", workDir=".", protoHomes=["protos/admin_proto"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="analyze", workDir=".", protoHomes=["protos/analyze_proto/proto"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="hotres", workDir=".", protoHomes=["protos//hotres_proto/proto"], deps=["common", "base_common"])
  weaProj.add(entryName="cs", workDir=".", protoHomes=["protos/cs_proto/proto"], deps=["res", "attr", "common", "base_common"])
  weaProj.add(entryName="sr", workDir=".", protoHomes=["protos/sr_proto/proto"], deps=["res", "attr", "common", "base_common"])
  weaProj.add(entryName="ss", workDir=".", protoHomes=[timiPath.tmp_ss_proto_dir], deps=["res", "attr", "common", "tcaplus", "base_common"])
  weaProj.add(entryName="mod", workDir=".", protoHomes=[timiPath.tmp_mod_proto_dir], deps=["ss", "res", "attr", "common", "tcaplus", "base_common"])
  weaProj.add(entryName="api", workDir=".", protoHomes=[timiPath.tmp_api_proto_dir], deps=["ss", "res", "attr", "common", "tcaplus", "base_common"])
  weaProj.add(entryName="g6", workDir=".", protoHomes=["protos/g6_irpc"], deps=["res", "common", "attr", "base_common"])
  weaProj.add(entryName="grpc", workDir=".", protoHomes=["protos/grpc_proto/proto"], deps=["res", "common", "base_common"])
  # check project legal
  weaProj.check()

"""
目前这些函数已经移动到weaProj中

def runBat(batFile, *args):
  # run the bat files on Windows platform
  if platform.system() != "Windows":
    batFile = batFile.replace('.bat', '.sh')
  fullpath = os.path.join(timiPath.common_path, batFile)
  d = os.path.dirname(fullpath)
  n = os.path.basename(fullpath)
  if not Util.suppress_useless_warnings():
    logger.warning("runBat batFile=%s dir=%s f=%s", batFile, d, n)
    p = Popen([fullpath, *args], cwd=d)
    stdout, stderr = p.communicate()
    rc = p.returncode
    if rc != 0:
      raise RuntimeError('runBat Error, file: {}'.format(batFile))
  else:
    p = Popen([fullpath, *args], cwd=d, stdout=PIPE, stderr=PIPE, text=True)
    _, stderr = p.communicate()
    rc = p.returncode
    if rc != 0:
      raise RuntimeError(f'runBat Error, file: {batFile}, error: {stderr.strip()}')

def gatherProto(protoHome, workDir):
    if os.path.isabs(protoHome):
      protoHome = os.path.relpath(protoHome, os.getcwd())
    if os.path.isabs(workDir):
      workDir = os.path.relpath(workDir, os.getcwd())
    
    indices = {}
    for r, _, files in os.walk(protoHome):
      if r.endswith("google") or r.endswith(os.path.join("google", "protobuf")):
        continue
      indices[r] = files
    return indices
    
def compileProto(projName, protoHomes, incPaths, workDir, **kwargs):
  if not GENERATE_ISOLATION_SWITCH:
    dst_path = kwargs.get("dstPath")
    if dst_path is None:
      raise Exception(f'need appointed dstPath')
  else:
    dst_path = os.path.join(GENERATE_ISOLATION_HOME, "src", "main", "java")
    # if os.path.exists(dst_path):
    #   shutil.rmtree(dst_path)
    os.makedirs(dst_path, exist_ok=True)
  dst_path = os.path.normpath(dst_path)
  
  allProto = []
  for protoHome in protoHomes:
    indices = gatherProto(protoHome, workDir)
    for p, files in indices.items():
      if p not in incPaths:
        incPaths.append(p) 
      if len(files) > 0:
        allProto.append(os.path.join(p, "*.proto"))
  
  cmd = 'protoc3.exe'
  if "plugin" in kwargs:
    cmd += f' --plugin={kwargs.get("plugin")} --{projName}-java_out={dst_path}'
  else:
    cmd += f' --java_out={dst_path}'
  cmd += f' {" ".join(map(lambda inc:"--proto_path="+inc, incPaths))} {" ".join(allProto)}'
  # print(f'compile project:{projName} -- "{cmd}"')
  run_cmd(cmd, working_dir=workDir)
  
"""

def convCRLF2LF(real_path):
  if platform.system().lower() != 'windows':
    return
  with open(real_path, 'rb') as in_file:
    in_data = in_file.read()
    out_data = in_data.replace(b'\r\n', b'\n')

  with open(real_path, 'wb') as out_file:
    out_file.write(out_data)

def scanAndConvCRLF2LF(fullPath):
  if platform.system().lower() != 'windows':
    return
  all = os.walk(fullPath)
  for path, dir_list, file_list in all:
    for file_name in file_list:
      real_path = os.path.join(path, file_name)
      # print('conv file:', real_path)
      convCRLF2LF(real_path)


def syncWithCRLFConv(srcPath, destPath, ignoreExisting = False):
  scanAndConvCRLF2LF(srcPath)
  sync.sync(srcPath, destPath, ignoreExisting)


def copyTreeWithCRLFConv(srcPath, patterns, destPath, addSVN=False, checkDiff=False, onlyInsert=False):
  scanAndConvCRLF2LF(srcPath)
  Util.copytrees(srcPath, patterns, destPath, addSVN, checkDiff, onlyInsert)


def copyFilesWithCRLFConv(srcPath, destPath, addSVN=False, checkDiff=False, onlyInsert=False):
  scanAndConvCRLF2LF(srcPath)
  Util.copyfiles(srcPath, destPath, addSVN, checkDiff, onlyInsert)
  
# generate attr files and copy

def runServerAttr(genJavaPath, genProtoPath, isall):
  AttrGenerator.generateAttr(packageName="com.tencent.wea",
                              loadDir=os.path.join(timiPath.common_path, "attr"),
                              dumpJavaPath=genJavaPath,
                              dumpProtoPath=genProtoPath,
                              syncProtoBase=True)
  scanAndConvCRLF2LF(genProtoPath)
  # attr有独立目录，可以直接同步
  syncWithCRLFConv(os.path.join(genProtoPath, "cs_attr_proto"),
            os.path.join(timiPath.common_path, "protos/cs_attr_proto/"))
  # syncWithCRLFConv(os.path.join(genProtoPath, "ss_attr_proto"),
  #           os.path.join(timiPath.common_path, "protos/ss_attr_proto/"))

  if not isall:
    syncWithCRLFConv(
        os.path.join(os.path.join(genJavaPath, timiPath.generate_package), "attr"),
        os.path.join(GENERATE_JAVA_OUT, "attr"))
    # runCmd(
    #   'protoc3.exe --java_out=' + genJavaPath + ' --proto_path=excel/server/proto --proto_path=protos/common_proto --proto_path=protos/ss_attr_proto protos/ss_attr_proto/*.proto',
    #   '.')
    # compileProto(projName="attr"
    #        , dstPath=genJavaPath
    #        , protoHomes=["protos/cs_attr_proto"]
    #        , incPaths=["excel/server/proto"]
    #        , workDir=".")
    run_cmd(**weaProj.getProtoCompile("attr", dstPath=genJavaPath))
    # runCmd(
    #   'protoc3.exe --java_out=' + genJavaPath + ' --proto_path=excel/server/proto --proto_path=protos/common_proto --proto_path=protos/cs_attr_proto protos/cs_attr_proto/*.proto',
    #   '.')
    copyTreeWithCRLFConv(os.path.join(genJavaPath, "com/tencent/wea/protocol"),
                    "*.java",
                    os.path.join(GENERATE_JAVA_OUT, "protocol"),
                    addSVN=False, checkDiff=True)

# generate db files and copy

def runServerTcaplus(genpath):
  TcaplusGenerator.main(gameName="tcaplus", loadDir="protos/db_proto/proto"
                          , dumpDir=genpath
                          , protoPaths=[
                                        WeAPath.TOOL_DB_TCAPLUS_PROTO_HOME,
                                        "excel/basic/proto", 
                                        "excel/server/proto", 
                                        "protos/common_proto", 
                                        "protos/cs_attr_proto",
                                        "protos/base_common_proto"])
  # compileProto(projName="tcaplus"
  #               , dstPath=genpath
  #               , protoHomes=["protos/db_proto/proto"]
  #               , incPaths=["excel/server/proto", "protos/common_proto", "protos/cs_attr_proto"]
  #               , workDir=".")
  run_cmd(**weaProj.getProtoCompile("tcaplus", dstPath=genpath))
  syncWithCRLFConv(
    os.path.join(os.path.join(genpath, timiPath.generate_package), "tcaplus"),
    os.path.join(GENERATE_JAVA_OUT, "tcaplus"))
  shutil.copyfile(os.path.join(genpath, "tcaplus_db.desc")
                  , os.path.join(GENERATE_JAVA_OUT, "tcaplus", "tcaplus_db.desc"))

def runServerSimulator4j(genpath):
  os.makedirs(genpath, exist_ok=True)
  # compileProto(projName="simulator4j"
  #         , dstPath=genpath
  #         , protoHomes=["protos/simulator4j_proto/proto"]
  #         , incPaths=["excel/server/proto", "protos/common_proto", "protos/cs_attr_proto"]
  #         , workDir=".")
  run_cmd(**weaProj.getProtoCompile("simulator4j", dstPath=genpath))
  # runCmd(
  #   'protoc3.exe --java_out=' + genpath + ' --proto_path=protos/common_proto --proto_path=excel/server/proto --proto_path=protos/cs_attr_proto --proto_path=protos/simulator4j_proto/proto protos/simulator4j_proto/proto/*.proto',
  #   '.')
  copyTreeWithCRLFConv(genpath, "*.java",
                 os.path.join(timiPath.projects_path,
                              "simulator4j/src/main/java/"),
                 addSVN=False, checkDiff=True)

def runServerRes(genpath):
  os.makedirs(genpath, exist_ok=True)
  conv2pb.tryCompileProtos(os.path.join(timiPath.common_path, "excel"), is_domestic=True)
  scanAndConvCRLF2LF(os.path.join(timiPath.common_path, 'excel/server/proto'))
  
  # compileProto(projName="res"
  #         , dstPath=genpath
  #         , protoHomes=["excel/server/proto"]
  #         , incPaths=[], workDir=".")
  run_cmd(**weaProj.getProtoCompile("keywords", dstPath=genpath))
  run_cmd(**weaProj.getProtoCompile("res", dstPath=genpath))
  transfers = weaProj.getPkgTransfer("res")
  if len(transfers) > 0:
    for cmd in transfers:
      sync.sync(**cmd)
  # runCmd(
  #   'protoc3.exe --java_out=' + genpath + ' --proto_path=excel/server/proto excel/server/proto/*.proto',
  #   '.')
  fileList = [genpath + "/com/tencent/wea/xlsRes/ResTestCaseOuterClass.java",
              genpath + "/com/tencent/wea/xlsRes/BuildingResOuterClass.java"]
  for filePath in fileList:
    try:
      if os.path.exists(filePath):
        os.remove(filePath)
        # print "remove {}".format(filePath)
    except:
      pass
  syncWithCRLFConv(os.path.join(genpath, "com/tencent/wea/xlsRes"),
            os.path.join(GENERATE_JAVA_OUT, "xlsRes"))
  # gen_java_class.main()
  ResClassGenerator.generate(modName="main"
                              , loadDir=os.path.join(timiPath.common_path, "excel")
                              , dumpDir=os.path.join(timiPath.common_path, "..", "src", "main", "java"))

def runAnalyzeProtocol(gen_path):
  os.makedirs(gen_path, exist_ok=True)
  run_cmd(**weaProj.getProtoCompile("analyze", dstPath=gen_path))
  syncWithCRLFConv(os.path.join(gen_path, "com/tencent/wea/analyze"),
                   os.path.join(GENERATE_JAVA_OUT, "analyzepb"))

  genAnalyzePbHelper(os.path.join(gen_path, "analyzepb"))


def genAnalyzePbHelper(outputpath):
  analyze_proto_generator.genAnalyzeProto(timiPath.protos_path,
                                          outputpath,
                                          rpc_default_impl=RPC_DEFAULT_IMPL)
  outterpath = os.path.join(outputpath, "..")
  copyFilesWithCRLFConv(os.path.join(outputpath, '*.java'),
                        os.path.join(outterpath, 'com/tencent/wea/analyzepb'),
                        addSVN=False, checkDiff=False)
  analyzepb_path = os.path.join(GENERATE_JAVA_OUT,
                                "analyzepb")

  copyTreeWithCRLFConv(os.path.join(outterpath, "com/tencent/wea/analyzepb/"),
                       '*.java',
                       analyzepb_path,
                       addSVN=False, checkDiff=True)


def runHotResProtocol(gen_path):
    os.makedirs(gen_path, exist_ok=True)
    # compileProto(projName="hotres"
    #         , dstPath=gen_path
    #         , protoHomes=["protos/hotres_proto/proto"]
    #         , incPaths=["protos/common_proto"]
    #         , workDir=".")
    run_cmd(**weaProj.getProtoCompile("hotres", dstPath=gen_path))
    # runCmd(
    #   'protoc3.exe --java_out=' + gen_path + ' --proto_path=protos/common_proto --proto_path=protos/hotres_proto/proto protos/hotres_proto/proto/*.proto',
    #   '.')
    syncWithCRLFConv(os.path.join(gen_path, "com/tencent/wea/hotRes"),
                     os.path.join(GENERATE_JAVA_OUT, "hotRes"))
    HotResClassGenerator.generate(modName="main"
                                  , loadDir=os.path.join(timiPath.common_path, "protos", "hotres_proto", "proto")
                                  , dumpDir=os.path.join(timiPath.common_path, "..", "src", "main", "java"))


def runServerDsCfg():
  dsa_cfg_path = os.path.join(timiPath.common_path,
                              "../../../run/dsa/cfg/dsa_conf/")
  dsc_cfg_path = os.path.join(timiPath.common_path, "../../../run/dsc/cfg/")
  gen_ds_config.genDsaConfig('all', 'all', dsa_cfg_path, dsc_cfg_path, 'main')


def removeUnnecessaryFile():
  fileList = [os.path.join(timiPath.common_path,
                           "protos/ss_proto/generated/service/LockService.java"),
              os.path.join(timiPath.common_path,
                           "protos/ss_proto/generated/service/MapService.java")]
  for filePath in fileList:
    try:
      if os.path.exists(filePath):
        os.remove(filePath)
        # print "remove {}".format(filePath)
    except:
      pass


def runServerProtocol(genpath, isall):
  # 这里放一些只有主common会执行的代码生成算了 先不移动到gen_protocol了
  # 不过用到generator已经移动到tools的pyHome
  batch_run([
    (runAnalyzeProtocol, (genpath,), {}),
  ], initializer=initWeaProj, initargs=("protocol", GENERATE_ISOLATION_HOME, GENERATE_ISOLATION_SWITCH))

  # 下面是和mod共用的代码生成
  gen_args = GenProtocolPathArgs(None,
                                 timiPath.common_path,
                                 timiPath.project_aoe_run_path,
                                 timiPath.wea_path,
                                 genpath,
                                 timiPath.tmp_ss_proto_dir,
                                 timiPath.tmp_api_proto_dir,
                                 os.path.join(timiPath.common_path, "svr_tools/ThirdParty"),
                                 tmp_mod_proto_path = timiPath.tmp_mod_proto_dir,
                                 ss_services_extends_setting_path=timiPath.ss_services_extends_setting_path)

  protocol_generate_isolation_switch = GENERATE_ISOLATION_SWITCH
  protocol_generate_isolation_home = GENERATE_ISOLATION_HOME
  if protocol_generate_isolation_switch:
    gen_args.set_generate_isolation_switch_home(protocol_generate_isolation_home)

  if weaProj is None:
      initWeaProj("protocol", GENERATE_ISOLATION_HOME, GENERATE_ISOLATION_SWITCH)

  gen_protocol.runServerProtocol(weaProj, gen_args)


def runServerTlog(genpath):
  Util.symlinkDir(os.path.join(WeAPath.TOOL_TLOG_HOME, "xml"), os.path.join(timiPath.protos_path, "tlog_proto"))
  TLogGenerator.generate(loadDir=os.path.join(timiPath.protos_path, "tlog_proto")
                          , dumpDir=genpath
                          , packageName = "com.tencent.wea"
                          , simpleMeta = ["OnlineCnt", "SafeReportFlow","MidasAccountFlow",
                                          "ClearBestRecoder", "UgcGoodsChangeStatusFlow",
                                          "UgcBuyGoodsPublishFlow", "UGCShopInfoTable",
                                          "MapBasicInfoTable","MapSkyboxInfoTable",
                                          "MapActorInfoTable","GroupActorInfoTable", "UGCAchievemenInfoTable",
                                          "LOGIDAILABRECMD8001","LOGIDAILABRECMD8001DEV",
                                          "LOGIDAILABRECMD8011","LOGIDAILABRECMD8011DEV",
                                          "MidasPayFlow", "SecAntiDataFlow", "CreditScoreFeedback",
                                          "SecVerifyFlow", "EventPointFlow", "RichMonopolyActivityRankFlow"]
                          , enableDS = True
                          , compatMode = True)
  syncWithCRLFConv(
    os.path.join(os.path.join(genpath, timiPath.generate_package), "tlog/flow"),
    os.path.join(GENERATE_JAVA_OUT, "tlog/flow"))
  syncWithCRLFConv(
    os.path.join(os.path.join(genpath, timiPath.generate_package), "tlog/letsgods"),
    os.path.join(GENERATE_JAVA_OUT, "tlog/letsgods"))
  # copyTreeWithCRLFConv(genpath, '*.java', GENERATE_JAVA_OUT + '/com/tencent/wea/tlog/flow',
  #                addSVN=True, checkDiff=True)
  # Util.removeDeletedMsg(GENERATE_JAVA_OUT + '/com/tencent/wea/tlog/flow', 'protos/tlog_proto/generated', [])
  copyFilesWithCRLFConv(os.path.join(timiPath.protos_path, "tlog_proto/*.xml"),
                 GENERATE_TLOG_XML_OUT, addSVN=False,
                 checkDiff=True)


def rsyncAllJavaProtocol():
    generate_java_path = os.path.join(
        os.path.join(GENERATE_PB_JAVA_TMP_OUT, "all/java"),
        timiPath.generate_package)
    syncWithCRLFConv(os.path.join(generate_java_path, "protocol"),
                     os.path.join(GENERATE_JAVA_OUT, "protocol"))

    syncWithCRLFConv(os.path.join(generate_java_path, "attr"),
                     os.path.join(GENERATE_JAVA_OUT, "attr"))

    # 拷贝mod的协议java
    need_copy_mod_name = set()
    mod_scan_generate_java_path = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "all", "java")
    for root, dirs, files in os.walk(mod_scan_generate_java_path):
        for dir_name in dirs:
            relative_path = os.path.relpath(os.path.join(root, dir_name), mod_scan_generate_java_path).replace('\\', '/')
            if not relative_path.startswith("com/tencent/wea/"):
                continue
            if "/protocol/" not in relative_path:
                continue

            parts = relative_path.split('/')
            if len(parts) < 5:
                continue
            if parts[4] != "protocol":
                continue
            mod_name = parts[3]
            need_copy_mod_name.add(mod_name)

    for mod_name in need_copy_mod_name:
        syncWithCRLFConv(os.path.join(mod_scan_generate_java_path, "com", "tencent", "wea", mod_name, "protocol"),
                         os.path.join(GENERATE_JAVA_OUT, f"{mod_name}/protocol"))
        print(f"copy {mod_name} protocol")


    syncWithCRLFConv(os.path.join(generate_java_path, "rpc"),
                     os.path.join(GENERATE_JAVA_OUT, "rpc"))
    syncWithCRLFConv(os.path.join(generate_java_path, "attr"),
                     os.path.join(GENERATE_JAVA_OUT, "attr"))


def runUpdateErrCode():
    common_excel_folder_path = os.path.join(timiPath.common_path, "excel")
    server_text_xls_path = os.path.join(timiPath.common_path, "excel", "xls", "W_文本表_提示_Main.xlsm")

    # NKErrorCode文件所在目录
    error_code_dir = os.path.join(timiPath.wea_path, "common/src/main/java/com/tencent/nk/util")
    errcode_java_cls_paths = []
    pattern = re.compile(r'^NKErrorCode\d*\.java$')

    if os.path.exists(error_code_dir):
        for filename in os.listdir(error_code_dir):
            if pattern.match(filename):
                filepath = os.path.join(error_code_dir, filename)
                errcode_java_cls_paths.append(filepath)

    if len(errcode_java_cls_paths) == 0:
        raise Exception(f"found no matched NKErrorCode\d*.java file: {error_code_dir}")
    else:
        print(f"error code java files found: {list(os.path.basename(f) for f in errcode_java_cls_paths)}")

    gen_mod_error_code_excel.generate(None, common_excel_folder_path, server_text_xls_path,
                                      errcode_java_cls_paths)


def runGenWeAEnum():
    wea_named_enum_xml_folder_dir = os.path.join(timiPath.common_path, 'protos/wea_named_enum')
    gen_wea_named_enum.generate(None, wea_named_enum_xml_folder_dir, timiPath.wea_path)


def runGenWeAEnumXml():
    gen_wea_named_enum_xml.generate_xml(timiPath.common_path)


def runMonitorDashboardJson():
  print("开始生成监控中心的dashboard json")
  monitor_file_path = os.path.abspath(os.path.join(timiPath.timiutil_path,
                                                   "src/main/java/com/tencent/timiutil/monitor/MonitorId.java"))
  base_dashboard_json_path = os.path.abspath(GENERATE_PB_JAVA_TMP_OUT)
  monitor_generator.generate(monitor_file_path, base_dashboard_json_path)


# 注意生成路径，每个模块尽量用独立目录，且目录里都是生成代码，这样rsync时就可以将不需要的文件删除
def runServerAll():
  # all mode 生成代码到 tmpGenerateCode all 路径之下
  
  weaProj.reset()

  # gen java path
  generate_java_path = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "all/java")
  # gen proto path
  generate_proto_path = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "all/proto")
  # 先清理生成目录
  Util.rm_r(generate_java_path)
  Util.rm_r(generate_proto_path)

  os.makedirs(generate_java_path, exist_ok=True)
  os.makedirs(generate_proto_path, exist_ok=True)

  # 先更新错误码, runServerRes会执行导出 并行有风险
  runUpdateErrCode()

  # win下weaProj作为全局变量 各个batRun执行时不会共享, 因此每次执行batch_run都需要初始化
  batch_run([
    (runServerAttr, (generate_java_path, generate_proto_path, True), {}),
    (runServerTcaplus, (generate_java_path,), {}),
    (runServerRes, (generate_java_path,), {}),
    (runServerProtocol, (generate_java_path, True), {}),
    (runServerTlog, (generate_java_path,), {}),
    (runMonitorDashboardJson, (), {}),
    (runHotResProtocol, (generate_java_path, ), {}),
    (runGenWeAEnum, (), {}),
  ], initializer=initWeaProj, initargs=("protocol", GENERATE_ISOLATION_HOME, GENERATE_ISOLATION_SWITCH))
  rsyncAllJavaProtocol()

def runServerSep(args):
  mode = args.mode

  # 先清理目录，再生成
  if mode == 'attr':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/attr/java")
    protopath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/attr/proto")
    Util.rm_r(javapath)
    Util.rm_r(protopath)
    runServerAttr(javapath, protopath, False)
  elif mode == 'tcaplus':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/tcaplus")
    Util.rm_r(javapath)
    runServerTcaplus(javapath)
  elif mode == 'res':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/xlsRes/java")
    Util.rm_r(javapath)
    runServerRes(javapath)
    ##runServerDsCfg()
  elif mode == 'protocol':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/protocol")
    Util.rm_r(javapath)
    runServerProtocol(javapath, False)
  elif mode == 'tlog':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/tlog")
    Util.rm_r(javapath)
    runServerTlog(javapath)
  elif mode == 'updateErr':
    # javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/updateErr")
    runUpdateErrCode()
  elif mode == 'simulator4j':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/simulator4j")
    Util.rm_r(javapath)
    runServerSimulator4j(javapath)
  elif mode == 'monitor':
    runMonitorDashboardJson()
  elif mode == 'other':
    # javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/other")
    # Util.rm_r(javapath)
    # runOtherProtocol(javapath)
    print("please run protocol instead")
  elif mode == 'hotres':
    javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/hotRes/java")
    Util.rm_r(javapath)
    runHotResProtocol(javapath)
  elif mode == 'adminpb':
    # javapath = os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/adminpb")
    # Util.rm_r(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/adminpb"))
    # runAdminProtocol(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/adminpb"))
    print("please run protocol instead")
  elif mode == 'wea_enum':
      runGenWeAEnum()
  elif mode == 'wea_enum_xml':
      runGenWeAEnumXml()
  else:
      print('unknown cmd')


# svn_adder.add2SVN(os.path.abspath(GENERATE_JAVA_OUT), True)

def main(args):
  initWeaProj("protocol", genHome=GENERATE_ISOLATION_HOME, genIsolation=args.isolation)
  if args.mode == 'all':
    runServerAll()
  else:
    runServerSep(args)

if __name__ == "__main__":
  argparse = argparse.ArgumentParser(prog='config_patch', description='config patch tool')
  argparse.add_argument('mode', type=str, default='all', help='the target mode')
  argparse.add_argument('--isolation', action='store_true', help='whether isolation?')
  args = argparse.parse_args()
  print("got args", args)
  GENERATE_ISOLATION_SWITCH = args.isolation
  start_time = time.time()
  main(args)
  # runServerSep("protocol")
  end_time = time.time()
  print("generate code cost {} secs".format(end_time - start_time))
  # runServerProtocol(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/protocol"), False)
  # runServerAttr(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/attr"), False)
  # runServerDb(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/db"), False)
  # runServerRes(os.path.join(GENERATE_PB_JAVA_TMP_OUT, "notall/xlsRes"), False)
