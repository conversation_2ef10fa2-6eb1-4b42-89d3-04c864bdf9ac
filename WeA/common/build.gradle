import org.gradle.internal.logging.text.StyledTextOutputFactory

import static org.gradle.internal.logging.text.StyledTextOutput.Style

idea {
    module {
        sourceDirs += file("${projectDir}/common/excel/proto")
        sourceDirs += file("${projectDir}/common/protos/cs_attr_proto")
        sourceDirs += file("${projectDir}/common/protos/ss_attr_proto")
        sourceDirs += file("${projectDir}/common/protos/ss_proto/proto")
        sourceDirs += file("${projectDir}/common/protos/cs_proto")
        sourceDirs += file("${projectDir}/common/protos/db_proto/proto")
        sourceDirs += file("${projectDir}/common/protos/common_proto")
        sourceDirs += file("${projectDir}/common/protos/base_common_proto")
    }
}

dependencies {
    api files(fileTree(dir: 'lib', include: '*.jar').sort())
    api files("$projectDir/../../run/svr_tools/ThirdParty/arthas-bin/import-core/import-core.jar")
    api project(':timiutil')
    compileOnly project(':protocol')
    api('com.esotericsoftware:kryo:5.3.0')
    api('io.github.kostaskougios:cloning:1.10.3')
    api 'com.google.guava:guava:31.1-jre'
    api 'net.engio:mbassador:1.3.2'
    api 'joda-time:joda-time:2.10.14'
    api 'commons-io:commons-io:2.6'
    api 'com.google.code.gson:gson:2.9.0'
//    api 'org.python:jython:2.7.1b3'
//    api 'org.openjdk.jmh:jmh-core:1.23'
//    api 'org.openjdk.jmh:jmh-generator-annprocess:1.23'
    api 'org.codehaus.groovy:groovy-all:3.0.12'
    api 'org.apache-extras.beanshell:bsh:2.0b6'
    api 'com.alibaba:druid:1.2.11'
    api 'org.dom4j:dom4j:2.1.3'
    api 'org.mariuszgromada.math:MathParser.org-mXparser:5.0.6'
    api 'io.netty:netty-all:4.1.77.Final'
    api 'io.protostuff:protostuff-core:1.5.9'
    api 'io.protostuff:protostuff-runtime:1.5.9'
    api('io.lettuce:lettuce-core:5.1.0.RELEASE') {
        // transitive = false
        // exclude group: 'httpclient', module: 'httpclient'
        exclude group: 'io.netty'
    }
    api 'com.google.protobuf:protobuf-java:3.20.1'

    api files('../libs/log4j-api-2.16.0.jar')
    api('org.apache.logging.log4j:log4j-core:2.16.0') {
        exclude group: 'org.apache.logging.log4j', module: 'log4j-api'
    }

    api('org.slf4j:slf4j-api:1.7.32') {
//        force(true)
//        version {
//            strictly("1.7.32")
//        }
    }
    api 'org.apache.logging.log4j:log4j-slf4j-impl:2.16.0'

    api 'com.lmax:disruptor:3.4.4'
    api 'org.objenesis:objenesis:3.2'
    api files('../libs/service-api-3.40.0SP10.jar')
    api files('../libs/commons-validator-1.7.jar')
    api files('../libs/annotations.jar')
    api files('../libs/zstd-jni-1.4.5-5.jar')
    api files('../libs/l5sys.jar')
    api "org.apache.httpcomponents:httpclient:4.5.13"
    api("org.apache.httpcomponents:httpasyncclient:4.1.4") {
        exclude group: 'org.apache.httpcomponents', module: 'httpclient'
    }
    api 'org.jboss.cache:jbosscache-core:3.2.7.GA'
    api files(fileTree(dir: getRootDir().toString() + '/libs', include: '*.jar').sort())
    api 'io.dropwizard.metrics:metrics-core:4.1.5'
    // api 'com.tencent.teg.monitor:java-sdk:0.9.5-SNAPSHOT'
    // compile 'com.github.luben:zstd-jni:1.4.5-5'
    api 'io.micrometer:micrometer-core:1.6.1'
    api ('io.jaegertracing:jaeger-client:1.3.1'){
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    api 'org.projectlombok:lombok:1.18.12'
    api 'org.locationtech.jts:jts-core:1.18.0'
//    compile 'com.tencent.rainbow:java-client:2.0.8-SNAPSHOT'
    /*临时导入的rabinbow SDK依赖  -----begin*/
    api 'com.google.inject:guice:4.0'
    api 'com.fasterxml.jackson.core:jackson-core:2.11.1'
    api 'com.fasterxml.jackson.core:jackson-databind:2.11.1'
    api 'com.fasterxml.jackson.module:jackson-module-parameter-names:2.11.1'
    api 'com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.11.1'
    api 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.1'
    /*临时导入的rabinbow SDK依赖  -----end*/
    api('com.google.firebase:firebase-admin:8.2.0') {
        // transitive = false
        // exclude group: 'httpclient', module: 'httpclient'
        exclude group: 'io.grpc'
    }
    api 'org.iq80.leveldb:leveldb:0.12'
    api group: 'com.github.oshi', name: 'oshi-core', version: '3.4.3'
    api group: 'com.github.oshi', name: 'oshi-json', version: '3.13.4'
    api 'com.kotcrab.annotation:callsuper:0.1.3'
    api ('com.qcloud:cos_api:5.6.147') {
        exclude group: 'com.tencentcloudapi', module: 'tencentcloud-sdk-java-common'
    }
    api 'com.qcloud:cos-sts_api:3.1.0'

    api 'io.opentelemetry:opentelemetry-sdk:1.24.0'
    api('io.opentelemetry:opentelemetry-exporter-otlp:1.24.0')
    api('io.opentelemetry:opentelemetry-exporter-otlp-common:1.24.0')
    api('io.opentelemetry:opentelemetry-exporter-logging-otlp:1.24.0')
    api('io.opentelemetry:opentelemetry-exporter-logging-otlp:1.24.0')
    api('com.squareup.okhttp3:okhttp:4.10.0')
    api('com.squareup.okhttp3:logging-interceptor:4.10.0')

//    implementation 'io.netty:netty-handler-proxy:4.1.77.Final'
    implementation 'com.google.guava:guava:30.0-android'
    implementation 'com.google.errorprone:error_prone_annotations:2.4.0'
    implementation 'io.perfmark:perfmark-api:0.23.0'
    implementation 'org.codehaus.mojo:animal-sniffer-annotations:1.19'
    implementation 'com.google.code.findbugs:jsr305:3.0.2'
    implementation 'org.apache.commons:commons-pool2:2.4.3'
    implementation 'org.apache.httpcomponents:httpmime:4.5.12'
    runtimeOnly 'io.grpc:grpc-netty-shaded:1.54.1'
    implementation 'io.grpc:grpc-protobuf:1.54.1'
    implementation 'io.grpc:grpc-stub:1.54.1'
    compileOnly 'org.apache.tomcat:annotations-api:6.0.53' // necessary for Java 9+
//    implementation('org.apache.maven.plugins:maven-compiler-plugin:3.8.1')
//    annotationProcessor 'com.kotcrab.annotation.callsuper.CallSuperProcessor:0.1.3'
    testImplementation 'junit:junit:4.11'
    testImplementation 'org.slf4j:slf4j-simple:1.6.1'


//    implementation 'org.eclipse.jetty:jetty-server:9.1.1.v20140108'
//    implementation 'org.eclipse.jetty:jetty-servlet:9.1.1.v20140108'

    implementation ('org.eclipse.jetty:jetty-server:11.0.7') {
        exclude group: 'org.slf4j', module: 'slf4j-api'
    }
    implementation ('org.eclipse.jetty:jetty-servlet:11.0.7') {
        exclude group: 'org.slf4j', module: 'slf4j-api'
    }

    implementation 'com.tencent.rainbow:rainbow-sdk-java:3.4.0'
    implementation 'org.elasticsearch:elasticsearch:7.10.2'
    implementation 'org.elasticsearch.client:elasticsearch-rest-high-level-client:7.10.2'
    implementation 'org.elasticsearch.client:elasticsearch-rest-client:7.10.2'
    implementation 'org.elasticsearch:elasticsearch-x-content:7.10.2'
    implementation 'org.apache.httpcomponents:httpclient:4.5.13'

    implementation 'io.github.tpnsPush:xinge:********'

    testImplementation project(':protocol')
}

def os = System.getProperty("os.name").toLowerCase()

def mockJdk = "Jku" //"Jku" //"Konafiber"
def buildJdk = "Konafiber"

if (System.getenv('buildJdk') != null) {
    buildJdk = System.getenv("buildJdk")
    if (buildJdk == "Jku") {
        mockJdk = "Konafiber"
    } else {
        mockJdk = "Jku"
    }
}

def out = services.get(StyledTextOutputFactory).create("timi")

out.withStyle(Style.Info).println("current buildJdk=" + buildJdk + " mockJdk=" + mockJdk)
out.withStyle(Style.Info).println("java.home=" + System.properties['java.home'])
sourceSets {
    main {
        java {
            srcDir 'src/generated/main/java'
            srcDir 'src/mock' + mockJdk + '/main/java'
        }
    }
}

build.dependsOn(genJars)
test.dependsOn(genJars)

shadowJar {
    project.timiutilJars.each {
        exclude(it.getName())
    }
}
task buildJni {
    doFirst {
        println("build jni")
        if (!os.contains("windows")) {
            exec {
                commandLine "./buildnative.sh"
            }
        }
    }
}
shadowJar.finalizedBy(buildJni)

// 编译sdk
if (project.hasProperty('build_sdk')) {
    shadowJar {
        // 排除一个类
        exclude 'com/tencent/wea/xiaowoutil.class'
        // 排除包
        exclude 'com/tencent/wea/xiaowo'
    }
}

// 发布sdk
if (project.hasProperty('publish_sdk')) {
    apply plugin: 'maven-publish'

    // maven的包名
    group = 'com.tencent.wea'
    publishing {
        publications {
            mavenJava(MavenPublication) {
                artifact "build/libs/wea-${project.name}-${project.version}.jar"
                // 发布下so
                fileTree(dir: 'build/libs', includes: ['*.so']).each { File soFile ->
                    artifact(source: soFile, extension: 'so', classifier: soFile.name) {
                        builtBy tasks.named('buildJni')
                    }
                }
            }
        }

        repositories {
            maven (gradle.ext.weaSdkRepoConfig)
        }
    }
}

task apiJavadoc(type: Javadoc) {
    classpath = sourceSets.main.compileClasspath
    classpath += sourceSets.main.output
    source = fileTree('src/main/java/com/tencent/api/')
    destinationDir = file("$buildDir/docs/common-api")

    title = "common-api-doc"
    options.encoding = 'UTF-8'
    options.addStringOption('Xdoclint:none', '-quiet')
    options.memberLevel = JavadocMemberLevel.PUBLIC
    options.author = true
    options.version = true
    options.use = true
}
