package com.tencent.ugc;

import com.tencent.resourceloader.resclass.UgcCosMapping;
import com.tencent.wea.protocol.common.ApplyReason;
import com.tencent.wea.xlsRes.ResUgcMgr.T_UgcCosMapping;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONObject;

import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import com.tencent.cache.Cache;
import com.tencent.cache.CacheErrorCode;
import com.tencent.cache.CacheUtil;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.resourceloader.resclass.UGCEditorMapTemplate;
import com.tencent.resourceloader.resclass.UgcBucketConfig;
import com.tencent.resourceloader.resclass.UgcCommonConfig;
import com.tencent.resourceloader.resclass.UgcCosPathConfig;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.ugc.UgcPathManager.CoverPath;
import com.tencent.ugc.UgcPathManager.CustomPath;
import com.tencent.ugc.UgcPathManager.MapDataPath;
import com.tencent.ugc.data.UgcReportData;
import com.tencent.ugc.data.UgcReportData.CoverParamData;
import com.tencent.util.JsonUtil;
import com.tencent.util.VersionUtil;
import com.tencent.wea.attr.XiaowoUgcMapMetaInfo;
import com.tencent.wea.protocol.AttrDressItemInfo.proto_DressItemInfo;
import com.tencent.wea.protocol.SsUgcplatsvr.ReportMapLoading;
import com.tencent.wea.protocol.SsUgcplatsvr.ReportVersionMapInstruction;
import com.tencent.wea.protocol.SsUgcplatsvr.VideoCoverInfoReport;
import com.tencent.wea.protocol.SsUgcsvr;
import com.tencent.wea.protocol.SsUgcsvr.RpcMapUgcBaseInfoRes.Builder;
import com.tencent.wea.protocol.SsUgcsvr.UgcBaseInfo;
import com.tencent.wea.protocol.common.EditorItemInfo;
import com.tencent.wea.protocol.common.LobbyUgcMapCreatorInfo;
import com.tencent.wea.protocol.common.LobbyUgcMapLoadingInfo;
import com.tencent.wea.protocol.common.MapCoverInfo;
import com.tencent.wea.protocol.common.MapCoverVideoInfo;
import com.tencent.wea.protocol.common.MapCoverVideoInfoSaved;
import com.tencent.wea.protocol.common.MapLoadingInfo;
import com.tencent.wea.protocol.common.MapLoadingInfo.MapLoadingData;
import com.tencent.wea.protocol.common.MdType;
import com.tencent.wea.protocol.common.PlayerDressItemInfo;
import com.tencent.wea.protocol.common.RoomJoinType;
import com.tencent.wea.protocol.common.UgcAchievementIndex;
import com.tencent.wea.protocol.common.UgcAchievementInfo;
import com.tencent.wea.protocol.common.UgcAchievementMap;
import com.tencent.wea.protocol.common.UgcExtraInfo;
import com.tencent.wea.protocol.common.UgcGoodsInfo;
import com.tencent.wea.protocol.common.UgcInstanceType;
import com.tencent.wea.protocol.common.UgcMapExtraConfigDescription;
import com.tencent.wea.protocol.common.UgcMapExtraConfigDescriptionItem;
import com.tencent.wea.protocol.common.UgcMapExtraConfigIndex;
import com.tencent.wea.protocol.common.UgcMapExtraConfigIndexMap;
import com.tencent.wea.protocol.common.UgcMapExtraConfigIndexType;
import com.tencent.wea.protocol.common.UgcMapMetaInfo;
import com.tencent.wea.protocol.common.UgcMapModelType;
import com.tencent.wea.protocol.common.UgcMapScreenType;
import com.tencent.wea.protocol.common.UgcRecommendType;
import com.tencent.wea.protocol.common.UgcResType;
import com.tencent.wea.protocol.common.UgcResTypeGroup;
import com.tencent.wea.protocol.common.UgcSceneBriefInfo;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDb.UgcPublish;
import com.tencent.wea.xlsRes.ResUGCEditor;
import com.tencent.wea.xlsRes.ResUGCEditor.Item_UGCEditorMapTemplate;
import com.tencent.wea.xlsRes.ResUgcMgr;
import com.tencent.wea.xlsRes.keywords.FuncType;
import com.tencent.wea.xlsRes.keywords.MidJoinSourceType;
import com.tencent.wea.xlsRes.keywords.RoomMidJoinType;
import com.tencent.wea.xlsRes.keywords.SavePosition;
import com.tencent.wea.xlsRes.keywords.UGCMapType;
import com.tencent.wea.xlsRes.keywords.UgcCosPathType;
import com.tencent.wea.xlsRes.keywords.UgcMapEvaluationStatusType;
import com.tencent.wea.xlsRes.keywords.UgcMapExtraConfigIndexStatusType;

public class CommonUtil {

    public static final String ASTC_TO_PNG_PARAM = "imageMogr2/format/png";
    public static final String LevelData = "LevelData{}.pbin";
    public static final String LevelDataByZip = "LevelData{}.dat";
    public static final String MapDescription = "MapDescription{}.des";
    public static final String MapRes = "MapRes{}.dat";
    public static final String CoverPng = "Cover.png";
    public static final String EditorData = "EditorData.pbin";
    public static final String ConstUgcKey = "UpdatePublishConfig";
    private static final Logger LOGGER = LogManager.getLogger(CommonUtil.class);
    public static long ongDaySec = 24 * 60 * 60;
    public static long ongDayMill = 24 * 60 * 60 * 1000;

    public static String getRegion(String bucket) {
        String default_region = PropertyFileReader.getItem("cos_default_region", "ap-nanjing");
        return PropertyFileReader.getItem(bucket + "-reg", default_region);
    }

    public static String getGroupTakeOffKey() {
        return "GroupTakeOffKey";
    }

    public static String getClientBucket(String svrBucket) {
        String clientBucket = svrBucket.split("-")[0];
        if (!clientBucket.isEmpty()) {
            return clientBucket;
        }
        return UgcBucketConfig.getInstance().getClientBucket(svrBucket);
    }

    public static String getOperateKey(long ugcId, int opType) {
        return new StringBuilder().append(ugcId).append("_").append(opType).toString();
    }

    // 设置组件社区启用与否
    public static boolean setGroupCommunityOpen(boolean open) {
        String value = open ? "1" : "0";
        Cache.CacheResult<String> res = CacheUtil.UgcCommunitySwitch.setCacheString("group", value);
        if (res.errCode == CacheErrorCode.OK) {
            LOGGER.debug("set redis {} succ", open);
            return true;
        } else {
            LOGGER.error("set reids fail ret={}", res.val);
            return false;
        }
    }

    public static boolean checkTimeExpire(int saveType, long expireTime) {
        long curTime = Framework.currentTimeMillis();
        if (saveType == SavePosition.RecycleStation_VALUE) {
            if (expireTime != 0 && curTime >= expireTime) {
                return true;
            }
        }
        return false;
    }
    /**
     * 获取发布的数据url地址
     *
     * @param mdType
     * @param mapId
     * @param instance
     * @param version
     * @return
     */
    public static String getPubLevelDataUrl(int mdType, long mapId, int instance, String version, int ugcResType) {
        String path = getCosPath(instance, ugcResType, mdType, UgcCosPathType.UCPT_PUB, 0L, mapId);
        if (path.isEmpty()) {
            return path;
        }
        if (StringUtils.isNotBlank(version)) {
            path = path + "?versionId=" + version;
        }
        return path;
    }

    /**
     * 获取草稿箱的数据url地址
     *
     * @param mdType
     * @param mapId
     * @param instance
     * @param version
     * @return
     */
    public static String getUgcLevelDataUrl(int mdType, long mapId, int instance, String version, int ugcResType) {
        String path = getCosPath(instance, ugcResType, mdType, UgcCosPathType.UCPT_UGC, 0L, mapId);
        if (path.isEmpty()) {
            return path;
        }
        if (StringUtils.isNotBlank(version)) {
            path = path + "?versionId=" + version;
        }
        return path;
    }

    public static NKErrorCode checkMd5(List<UgcMapMetaInfo> mdList, List<MapCoverInfo> coverList){
        String md5Cover = "";
        for (MapCoverInfo coverInfo : coverList) {
            if (coverInfo.getIndex() == 1 && coverInfo.getInfo().getMsgType() == MdType.CoverASTC_VALUE) {
                md5Cover = coverInfo.getInfo().getMsg();
                break;
            }
        }

        String md5 = "";
        for (UgcMapMetaInfo metaInfo : mdList) {
            if (metaInfo.getMsgType() == MdType.CoverASTC_VALUE) {
                md5 = metaInfo.getMsg();
                break;
            }
        }

        if(!md5Cover.equals(md5)) {
            LOGGER.error("cover md5 not equals mdList md5，md5Cover：{},md5:{}",md5Cover,md5);
            return NKErrorCode.IdipMd5CheckError;
        }
        return NKErrorCode.OK;
    }

    public static String getPubLevelFileName(int mdType) {
        if (mdType == MdType.LevelDataByZip_VALUE) {
            return LevelDataByZip;
        } else {
            return LevelData;
        }
    }

    public static String getCosPath(int mapType, int ugcResType, int mdType,
                                    UgcCosPathType pathType, long creatorId, long ugcId) {
        int realMapType = mapType;
        if (ugcResType == UgcResType.EURT_Group_VALUE && mapType == UgcInstanceType.ResInstance_VALUE) {
            realMapType = UgcInstanceType.GroupInstance_VALUE;
        }
        ResUgcMgr.T_UgcCosPathConfig cfg = UgcCosPathConfig.getInstance().getCosConf(
                realMapType, mdType, pathType);
        if (cfg == null) {
            LOGGER.error("mapType:{}, mdType:{} pathType:{} not found in UgcCosPathConfig",
                    realMapType, mdType, pathType.getNumber());
            return "";
        }
        FuncType funcType = cfg.getFuncType();
        String formatStr = cfg.getCosPath();

        FucTypeFormatParam param = new FucTypeFormatParam();
        param.creatorId = creatorId;
        param.ugcId = ugcId;
        param.resType = ugcResType;
        return formatByFuncType(funcType, formatStr, param);
    }

    public static String formatByFuncType(FuncType funcType, String format, FucTypeFormatParam param) {
        String retStr = "";
        if (format.isEmpty()) {
            LOGGER.error("format str empty");
            return retStr;
        }
        switch (funcType) {
            case Uid_UgcIdFunc: {
                retStr = NKStringFormater.format(format, param.uid, param.ugcId);
                break;
            }
            case UgcId_Func: {
                retStr = NKStringFormater.format(format, param.ugcId);
                break;
            }
            case Uid_Func: {
                retStr = NKStringFormater.format(format, param.uid);
                break;
            }
            case Uid_DestIdFunc: {
                retStr = NKStringFormater.format(format, param.uid, param.destId);
                break;
            }
            case DestIdFunc: {
                retStr = NKStringFormater.format(format, param.destId);
                break;
            }
            case Uid_DestIdDestIdFunc: {
                retStr = NKStringFormater.format(format, param.uid, param.destId, param.destId);
                break;
            }
            case CreatorId_Func: {
                retStr = NKStringFormater.format(format, param.creatorId);
                break;
            }
            case Env_World_CreatorId_UgcIdFunc: {
                retStr = NKStringFormater.format(format, param.env, param.worldId, param.creatorId, param.ugcId);
                break;
            }
            case Env_WorldFunc: {
                retStr = NKStringFormater.format(format, param.env, param.worldId);
                break;
            }
            case Env_World_CreatorIdFunc: {
                retStr = NKStringFormater.format(format, param.env, param.worldId, param.creatorId);
                break;
            }
            case CreatorId_UgcIdFunc: {
                retStr = NKStringFormater.format(format, param.creatorId, param.ugcId);
                break;
            }
            case CreatorId_UgcId_UgcIdFunc: {
                retStr = NKStringFormater.format(format, param.creatorId, param.ugcId, param.ugcId);
                break;
            }
            case UgcId_UgcIdFunc: {
                retStr = NKStringFormater.format(format, param.ugcId, param.ugcId);
                break;
            }
            case CreatorId_ResType_UgcId_UgcIdFunc: {
                retStr = NKStringFormater.format(format, param.creatorId, param.resType, param.ugcId, param.ugcId);
                break;
            }
            case ResType_UgcId_UgcIdFunc: {
                retStr = NKStringFormater.format(format, param.resType, param.ugcId, param.ugcId);
                break;
            }
            case Ugc_NameFunc: {
                retStr = NKStringFormater.format(format, param.name);
                break;
            }
            case UgcId_DestIdFunc: {
                retStr = NKStringFormater.format(format, param.ugcId, param.destId);
                break;
            }
            case CreatorId_DestIdFunc: {
                retStr = NKStringFormater.format(format, param.creatorId, param.destId);
                break;
            }
            default: {
                LOGGER.error("funcType:{} not support", funcType);
                break;
            }
        }
        return retStr;
    }


    /**
     * 获取 /ugc 目录下 封面 cos 路径
     *
     * @param mapType
     * @param resType
     * @param creatorId
     * @param mapId
     * @return
     */
    public static String getCosUgcCoverPath(int mapType, int resType, long creatorId, long mapId,
                                            boolean isResPubCosPath) {
        MdType mdType = null;
        if (mapType == UgcInstanceType.GroupInstance_VALUE || mapType == UgcInstanceType.ResInstance_VALUE) {
            mdType = MdType.GroupPng;
        } else {
            mdType = MdType.CoverASTC;
        }
        if (isResPubCosPath) {
            return getCosPath(mapType, resType, mdType.getNumber(), UgcCosPathType.UCPT_PUB_RES_PRIVATE,
                    creatorId, mapId);
        } else {
            return getCosPath(mapType, resType, mdType.getNumber(), UgcCosPathType.UCPT_UGC,
                    creatorId, mapId);
        }
    }


    /**
     * 获取 /mulcover 目录下 pbin cos 路径
     */
    public static String getMulCoverPath(String bucket, long mapId, int mapType, String version,String name) {
        ResUgcMgr.T_UgcCosPathConfig cfg = UgcCosPathConfig.getInstance().getCosConf(
                mapType, MdType.CoverASTC.getNumber(), UgcCosPathType.UCPT_PUB_MUL_COVER);
        if (cfg == null) {
            LOGGER.error("mapType:{}, mdType:{} pathType:{} not found in UgcCosPathConfig",
                    mapType, MdType.CoverASTC.getNumber(), UgcCosPathType.UCPT_PUB_MUL_COVER.getNumber());
            return "";
        }
        FuncType funcType = cfg.getFuncType();
        String formatStr = cfg.getCosPath();

        FucTypeFormatParam param = new FucTypeFormatParam();
        param.creatorId = 0;
        param.ugcId = mapId;
        param.name = name;
        String path = formatByFuncType(funcType, formatStr, param);

        if (path.isEmpty()) {
            return path;
        }

        path = path + "?" + ASTC_TO_PNG_PARAM;

        if (StringUtils.isNotBlank(version)) {
            path += "&versionId=" + version;
        }
        return bucket + "/" + path;
    }

    /**
     * 获取 /ugc 目录下 pbin cos 路径
     *
     * @param mapType
     * @param resType
     * @param creatorId
     * @param mapId
     * @return
     */
    public static String getCosUgcPbinPath(int mapType, int resType, long creatorId, long mapId) {
        MdType mdType = null;
        if (mapType == UgcInstanceType.GroupInstance_VALUE || mapType == UgcInstanceType.ResInstance_VALUE) {
            mdType = MdType.ResourceZip;
        } else {
            mdType = MdType.LevelDataByZip;
        }
        return getCosPath(mapType, resType, mdType.getNumber(), UgcCosPathType.UCPT_UGC,
                creatorId, mapId);
    }

    public static MdType getResPubCoverType(List<UgcMapMetaInfo> mapMetaInfoList, int resType) {
        // png 优先
        for (UgcMapMetaInfo metaInfo : mapMetaInfoList) {
            if (metaInfo.getMsgType() == MdType.GroupPng_VALUE) {
                return MdType.GroupPng;
            }
        }
        for (UgcMapMetaInfo metaInfo : mapMetaInfoList) {
            if (metaInfo.getMsgType() == MdType.GroupASTC_VALUE) {
                return MdType.GroupASTC;
            }
        }

        if (resType == UgcResType.EURT_CommonTexture_VALUE || resType == UgcResType.EURT_AchiCustomIcon_VALUE) {
            return MdType.GroupASTC;
        } else {
            return MdType.GroupPng;
        }
    }

    // 获取 /pub 目录下 cover cos 路径
    public static String getCosPubCoverPath(int mapType, int resType, long mapId, boolean isResPrivate,
                                            MdType mdType) {
        if (!isResPrivate) {
            return getCosPath(mapType, resType, mdType.getNumber(), UgcCosPathType.UCPT_PUB,
                    0L, mapId);
        } else {
            return getCosPath(mapType, resType, mdType.getNumber(), UgcCosPathType.UCPT_PUB_RES_PRIVATE,
                    0L, mapId);
        }
    }

    // 获取 /pub 目录下 pbin cos 路径
    public static String getCosPubPbinPath(int mapType, int resType, long mapId) {
        MdType mdType = null;
        if (mapType == UgcInstanceType.GroupInstance_VALUE || mapType == UgcInstanceType.ResInstance_VALUE) {
            mdType = MdType.ResourceZip;
        } else {
            mdType = MdType.LevelDataByZip;
        }
        return getCosPath(mapType, resType, mdType.getNumber(), UgcCosPathType.UCPT_PUB,
                0L, mapId);
    }


   /**
     * Convert COS URL to CDN URL
     * @param cosUrl COS source URL (e.g. "bucket-125xxx.cos.ap-shanghai.myqcloud.com/path/to/file")
     * @return CDN URL or empty string if conversion fails
     */
    public static String getCdnUrl(String bucket, String cosKey) {
        if (cosKey == null || cosKey.isEmpty()) {
            return "";
        }
        // Get CDN host configuration
        ResUgcMgr.T_UgcCommonConfig config = UgcCommonConfig.getInstance().get(bucket);
        if (config == null || config.getHost().isEmpty()) {
            return "";
        }

        // Construct CDN URL
        StringBuilder url = new StringBuilder();
        url.append("https://").append(config.getHost());
        url.append("/").append(cosKey);

        return url.toString();
    }


    // 获取 /ugc 目录下 cover cdn 地址
    // 注意不带版本号和其它参数
    public static String getCdnUgcCoverUrl(String bucket, long creatorId, long mapId, int mapType,
                                           int resType, boolean isResPubCosPath) {
        StringBuilder url = new StringBuilder();
        String configBucket = getClientBucket(bucket);
        ResUgcMgr.T_UgcCommonConfig config = UgcCommonConfig.getInstance()
                .get(configBucket);
        if (config == null || config.getHost().isEmpty()) {
            return url.toString();
        }
        url.append("https://").append(config.getHost());
        url.append(getCosUgcCoverPath(mapType, resType, creatorId, mapId, isResPubCosPath));
        return url.toString();
    }

    public static String getPngCoverCdnUrl(String bucket, long creatorId, long mapId, int mapType,
                                           int resType, String version, boolean isResPubCosPath) {
        String url = getCdnUgcCoverUrl(bucket, creatorId, mapId, mapType, resType, isResPubCosPath);
        if (url.isEmpty()) {
            return url;
        }
        if (StringUtils.isNotBlank(version)) {
            url = url + "?versionId=" + version;
        }
        return url;
    }

    public static String getAstcCoverCdnUrl(String bucket, long creatorId, long mapId, int mapType,
                                            int resType, String version, boolean isResPubCosPath) {
        String url = getCdnUgcCoverUrl(bucket, creatorId, mapId, mapType, resType, isResPubCosPath);
        if (url.isEmpty()) {
            return url;
        }
        url += "?" + ASTC_TO_PNG_PARAM;
        if (StringUtils.isNotBlank(version)) {
            url += "&versionId=" + version;
        }
        return url;
    }

    public static String getPubCoverUrl(String bucket, long mapId, int mapType, String version,
                                        int resType, boolean isResPrivate, List<UgcMapMetaInfo> mapMetaInfoList) {
        MdType mdType = MdType.CoverASTC;
        if (mapType == UgcInstanceType.GroupInstance_VALUE || mapType == UgcInstanceType.ResInstance_VALUE) {
            mdType = getResPubCoverType(mapMetaInfoList, resType);
        }

        StringBuilder path = new StringBuilder(getCosPubCoverPath(mapType, resType, mapId, isResPrivate, mdType));
        if (path.length() == 0) {
            return path.toString();
        }
        List<String> paramList = new ArrayList<>();
        if (mdType == MdType.CoverASTC || mdType == MdType.GroupASTC) {
            paramList.add(ASTC_TO_PNG_PARAM);
        }
        if (StringUtils.isNotBlank(version)) {
            paramList.add("versionId=" + version);
        }
        boolean isFirstParam = true;
        for (String param : paramList) {
            if (isFirstParam) {
                isFirstParam = false;
                path.append("?");
            } else {
                path.append("&");
            }
            path.append(param);
        }
        return bucket + "/" + path;
    }
    // 判断组件社区是否启用
    public static boolean getGroupCommunityOpen() {
        boolean isOpen = false;
        Cache.CacheResult<String> res = CacheUtil.UgcCommunitySwitch.getCacheString("group");
        if (res.errCode == CacheErrorCode.OK) {
            // redis 有值就取值 没值就放通(没有在初始化的时候设置redis，因此无值认为是开放)
            if (res.val != null) {
                isOpen = res.val.equals("1");
                LOGGER.debug("value {}", res.val);
            } else {
                isOpen = true;
                LOGGER.debug("no value");
            }
        } else {
            LOGGER.error("get from redis fail, ret={}", res.errCode);
            isOpen = false;
        }
        LOGGER.debug("get from cache, isOpen={}", isOpen);
        return isOpen;
    }

    public static boolean isHasCosData(List<UgcMapMetaInfo> metaInfos) {
        for (UgcMapMetaInfo info : metaInfos) {
            if (info.getMsgType() == MdType.LevelData_VALUE ||
                    info.getMsgType() == MdType.LayerData_VALUE ||
                    info.getMsgType() == MdType.GroupData_VALUE ||
                    info.getMsgType() == MdType.LayerDataByZip_VALUE ||
                    info.getMsgType() == MdType.LevelDataByZip_VALUE ||
                    info.getMsgType() == MdType.ResourceZip_VALUE ||
                    info.getMsgType() == MdType.ResourceResZip_VALUE ||
                    info.getMsgType() == MdType.ResourceModelResZip_VALUE) {
                return true;
            }
        }
        return false;
    }

    public static XiaowoUgcMapMetaInfo UgcMapMetaInfo2XiaowoUgcMetaInfo(UgcMapMetaInfo src, int index) {
        XiaowoUgcMapMetaInfo dst = new XiaowoUgcMapMetaInfo();
        dst.setIndex(index);
        dst.setSize(src.getSize());
        dst.setMsg(src.getMsg());
        dst.setMsgType(src.getMsgType());
        dst.setProcessType(src.getProcessType());
        dst.setVersion(src.getVersion());
        dst.setIsCoverCheckPass(src.getIsCoverCheckPass());
        dst.setPreMsg(src.getPreMsg());
        dst.setLayerId(src.getLayerId());
        return dst;
    }

    /**
     * 此函数功能如下
     */
    public static String getMetaInfoUrl(long ugcId,List<UgcMapMetaInfo> mapMetaInfoList,int msgType) {
        UgcMapMetaInfo metaInfo = null;
        for (UgcMapMetaInfo info : mapMetaInfoList) {
            if (info.getMsgType() == msgType) {
                metaInfo = info;
                break;
            }
        }
        if(metaInfo != null){
            return MapDataPath.getPubLevelDataPath(ugcId, metaInfo, UgcInstanceType.CommonInstance);
        }

        return "";
    }

    /**
     * 此函数功能如下
     * 1 获取发布的mdinfo
     * 2 优先级判定，LevelData < UgcLevelZip
     * 逐步废弃
     */
    public static UgcMapMetaInfo getPublishMetaInfo(List<UgcMapMetaInfo> mapMetaInfoList) {
        UgcMapMetaInfo metaInfo = null;
        for (UgcMapMetaInfo info : mapMetaInfoList) {
            if (info.getMsgType() == MdType.LevelData_VALUE) {
                metaInfo = info;
            } else if (info.getMsgType() == MdType.LevelDataByZip_VALUE) {
                metaInfo = info;
                break;
            } else if (info.getMsgType() == MdType.GroupData_VALUE) {
                metaInfo = info;
                break;
            } else if (info.getMsgType() == MdType.ResourceZip_VALUE) {
                metaInfo = info;
                break;
            }
        }
        return metaInfo;
    }

    /**
     * 此函数功能是获取第一个关卡元信息,也就是主关卡
     */
    public static UgcMapMetaInfo getPublishMetaInfoOfFirst(List<UgcMapMetaInfo> mapMetaInfoList) {
        List<UgcMapMetaInfo> publishMetaInfoList = getPublishMetaInfoList(mapMetaInfoList);
        if (publishMetaInfoList == null) {
            if (mapMetaInfoList.isEmpty()) {
                LOGGER.error("getPublishMetaInfoOfFirst error mapMetaInfoList=empty");
            } else {
                LOGGER.error("getPublishMetaInfoOfFirst error UgcMapMetaInfo={}", mapMetaInfoList.get(0));
            }
            return null;
        }

        if (publishMetaInfoList.isEmpty()) {
            LOGGER.error("getPublishMetaInfoOfFirst error publishMetaInfoList=empty");
            return null;
        }

        return publishMetaInfoList.stream().filter(v -> (v.getLayerId() == 1 || v.getLayerId() == 0)).findFirst()
                .orElse(null);
    }

    /**
     * 获取发布目录的文件名字
     *
     * @param mapId
     * @param ugcMapMetaInfo
     * @return
     */
    public static String getPubLevelFileName(long mapId, UgcMapMetaInfo ugcMapMetaInfo) {
        String fileNameFormat = CommonUtil.LevelData;
        if (ugcMapMetaInfo == null) {
            LOGGER.error("getPubLevelFileName error ugcMapMetaInfo == null mapId={}", mapId);
            return NKStringFormater.format(fileNameFormat, "");
        }

        if (ugcMapMetaInfo.getMsgType() == MdType.LevelDataByZip_VALUE) {
            fileNameFormat = LevelDataByZip;
        }

        if (ugcMapMetaInfo.getMsgType() == MdType.MapDescription_VALUE) {
            fileNameFormat = MapDescription;
        }

        if (ugcMapMetaInfo.getMsgType() == MdType.MapResByZip_VALUE) {
            fileNameFormat = MapRes;
        }

        if (ugcMapMetaInfo.getLayerId() == 0 || ugcMapMetaInfo.getLayerId() == 1) {
            return NKStringFormater.format(fileNameFormat, "");
        }

        return NKStringFormater.format(fileNameFormat, "_" + ugcMapMetaInfo.getLayerId());
    }

    /**
     * 获取发布信息是否加密
     *
     * @param mapMetaInfoList
     * @return
     */
    public static boolean checkPublishMetaInfoIsSecret(List<UgcMapMetaInfo> mapMetaInfoList) {
        List<UgcMapMetaInfo> publishMetaInfoList = getPublishMetaInfoList(mapMetaInfoList);
        return (!publishMetaInfoList.isEmpty()) && StringUtils.isNotBlank(publishMetaInfoList.get(0).getPreMsg());
    }

    /**
     * 此函数功能如下
     * 获取地图描述文件和地图res文件
     */
    public static List<UgcMapMetaInfo> getPublishDescInfoList(List<UgcMapMetaInfo> mapMetaInfoList) {
        List<UgcMapMetaInfo> retList = new ArrayList<>();
        for (UgcMapMetaInfo info : mapMetaInfoList) {
         if (info.getMsgType() == MdType.MapDescription_VALUE) {
                retList.add(info);
             }else if (info.getMsgType() == MdType.MapResByZip_VALUE) {
                retList.add(info);
            }
        }
        return retList;
    }

    /**
     * 此函数功能如下
     * 1 获取发布的mdinfo
     * 2 优先级判定，LevelData < UgcLevelZip
     */
    public static List<UgcMapMetaInfo> getPublishMetaInfoList(List<UgcMapMetaInfo> mapMetaInfoList) {
        UgcMapMetaInfo levelDataMetaInfo = null;
        List<UgcMapMetaInfo> retList = new ArrayList<>();
        for (UgcMapMetaInfo info : mapMetaInfoList) {
            if (info.getMsgType() == MdType.LevelData_VALUE) {
                levelDataMetaInfo = info;
            } else if (info.getMsgType() == MdType.LevelDataByZip_VALUE) {
                retList.add(info);
            } else if (info.getMsgType() == MdType.GroupData_VALUE) {
                retList.add(info);
                break;
            } else if (info.getMsgType() == MdType.ResourceZip_VALUE) {
                retList.add(info);
                break;
            }
        }

        if (retList.isEmpty() && levelDataMetaInfo != null) {
            retList.add(levelDataMetaInfo);
        }
        return retList;
    }

    public static UgcMapMetaInfo getLayerDataInfo(List<UgcMapMetaInfo> mapMetaInfoList) {
        UgcMapMetaInfo metaInfo = null;
        for (UgcMapMetaInfo info : mapMetaInfoList) {
            if (info.getMsgType() == MdType.LayerData_VALUE) {
                metaInfo = info;
            } else if (info.getMsgType() == MdType.LayerDataByZip_VALUE) {
                metaInfo = info;
                break;
            }
        }

        return metaInfo;
    }

    /**
     * @param ugcBaseInfo 接口返回的ugc loading基本信息
     * @return proto_UgcMapLoadingInfo
     */
    public static LobbyUgcMapLoadingInfo getLobbyUgcMapLoadingInfo(UgcBaseInfo ugcBaseInfo) {
        return getLobbyUgcMapLoadingInfo(ugcBaseInfo.getCreatorsList(), ugcBaseInfo.getLoadingTemplateId());
    }

    public static LobbyUgcMapLoadingInfo getLobbyUgcMapLoadingInfo(List<EditorItemInfo> creatorsList, int loadingTemplateId) {
        LobbyUgcMapLoadingInfo.Builder ugcMapLoadingInfo = LobbyUgcMapLoadingInfo.newBuilder();
        for (EditorItemInfo editorItemInfo : creatorsList) {
            LobbyUgcMapCreatorInfo.Builder ugcMapCreatorInfo = LobbyUgcMapCreatorInfo.newBuilder();
            ugcMapCreatorInfo.setCreatorId(editorItemInfo.getCreatorId())
                    .setEditorType(editorItemInfo.getType().getNumber())
                    .setAvatar(editorItemInfo.getAvatar())
                    .setNickName(editorItemInfo.getNickName());
            for (PlayerDressItemInfo dressItemInfo : editorItemInfo.getDressItemInfosList()) {
                proto_DressItemInfo.Builder ugcDressItemInfo = proto_DressItemInfo.newBuilder();
                ugcDressItemInfo.setItemId(dressItemInfo.getItemId())
                        .setDressUpType(dressItemInfo.getDressUpType())
                        .setItemUUID(dressItemInfo.getItemUUID());
                ugcMapCreatorInfo.addDressItemInfos(ugcDressItemInfo);
            }
            ugcMapLoadingInfo.addCreatorInfo(ugcMapCreatorInfo);
        }
        ugcMapLoadingInfo.setLoadingTemplateId(loadingTemplateId);
        return ugcMapLoadingInfo.build();
    }

    // 地图兼容版本判断代码
    public static boolean ugcVersionCompare(String clientVersion, String mapClientVersion) {
        String[] currentVer = clientVersion.split("\\.");
        String[] targetVer = mapClientVersion.split("\\.");
        if (currentVer.length != targetVer.length) {
            return false;
        }
        // 顺序判断，只要有1位大就兼容，只要有1位小就不兼容
        for (int i = 0; i < currentVer.length; ++i) {
            int curVer = Integer.parseInt(currentVer[i]);
            int tarVer = Integer.parseInt(targetVer[i]);
            if (curVer == tarVer) {
                continue;
            }
            return curVer > tarVer;
        }
        return true;
    }

    public static boolean ugcVersionCheckWithSwitchIsNeed(long ugcId) {
        boolean ugcVersionCheckSwitch = PropertyFileReader.getRealTimeBooleanItem("ugcVersionCheckSwitch", true);
        if (!ugcVersionCheckSwitch) {
            // 这里使用realtime开关做个控制
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("skip version check switch, ugcId:{}", ugcId);
            }
            return false;
        }
        // 检查是否是内网或者的custom或者压测
        if (!ServerEngine.getInstance().isBusiness()
                && !ServerEngine.getInstance().isPreBusiness()
                && !ServerEngine.getInstance().isIdcReviewTest()) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("skip version check in this env, ugcId:{}", ugcId);
            }
            return false;
        }
        return true;
    }

    // 游玩时对地图进行版本检查 这里会有一个服务检查开关
    public static boolean ugcVersionCheckWithSwitch(long ugcId, long clientVersion64, String mapClientVersion) {
        if (!ugcVersionCheckWithSwitchIsNeed(ugcId)) {
            // 如果不需要检查则直接跳过
            return true;
        }
        String clientVersion = VersionUtil.decodeClientVersion(clientVersion64);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("ugcId:{} clientVersion:{} mapClientVersion:{}", ugcId, clientVersion, mapClientVersion);
        }
        return ugcVersionCompare(clientVersion, mapClientVersion);
    }

    // 是否禁止玩家游玩多场景地图
    public static boolean ugcMultiLevelPlayEnableCheck(long ugcId, Collection<UgcMapMetaInfo> metaInfos) {
        // 是否允许游玩多场景
        if (PropertyFileReader.getRealTimeBooleanItem("ugc_multi_level_play_enable", true)) {
            return true;
        }

        for (UgcMapMetaInfo metaInfo : metaInfos) {
            // 多场景的话他的layerid必大于1
            if (metaInfo.getLayerId() > 1) {
                LOGGER.info("ugcId:{} metaInfos:{}", ugcId, metaInfos);
                return false;
            }
        }
        return true;
    }

    // 是否禁止玩家进行发布或者二次更新
    public static boolean ugcMultiLevelPublishEnableCheck(long ugcId, Collection<UgcMapMetaInfo> metaInfos) {
        // 是否允许发布多场景
        if (PropertyFileReader.getRealTimeBooleanItem("ugc_multi_level_publish_enable", true)) {
            return true;
        }
        for (UgcMapMetaInfo metaInfo : metaInfos) {
            // 多场景的话他的layerid必大于1
            if (metaInfo.getLayerId() > 1) {
                LOGGER.info("ugcId:{} metaInfos:{}", ugcId, metaInfos);
                return false;
            }
        }
        return true;
    }

    public static boolean ugcVersionCompare(long clientVersion64, String mapClientVersion) {
        String clientVersion = VersionUtil.decodeClientVersion(clientVersion64);
        return ugcVersionCompare(clientVersion, mapClientVersion);
    }

    public static UgcReportData.CoverResultData getCoverInfo(TcaplusDb.UgcPublish dbMapInfo){
        UgcReportData.CoverResultData data = new UgcReportData.CoverResultData();
        int instance = dbMapInfo.getUgcInstanceType();
        List<MapCoverInfo> infoList = dbMapInfo.getCover().getInfoList();
        List<MapCoverInfo> tempList = infoList.stream().filter(s -> s.getInfo().getMsgType() == MdType.CoverASTC_VALUE)
                .collect(Collectors.toList());
        for (int i = 0; i < tempList.size(); i++) {
            MapCoverInfo info = tempList.get(i);
            if (info.getIndex() == 1) {
                data.mainIndex = i;
            }
            String coverUrl = CoverPath.getMulCoverPath(dbMapInfo.getBucket(), dbMapInfo.getUgcId(), instance,
                    info.getInfo().getVersion(), info.getCoverId());
            data.coverList.add(coverUrl);
        }
        return data;
    }

    public static UgcReportData.CoverResultData getCoverInfo(TcaplusDb.UgcPublish dbMapInfo,
            TcaplusDb.UgcMultiVersionTable versionTable) {
        UgcReportData.CoverResultData data = new UgcReportData.CoverResultData();
        int instance = versionTable.getUgcInstanceType();
        List<MapCoverInfo> infoList = versionTable.getCoverList().getInfoList();
        List<MapCoverInfo> tempList = infoList.stream().filter(s -> s.getInfo().getMsgType() == MdType.CoverASTC_VALUE)
                .collect(Collectors.toList());
        for (int i = 0; i < tempList.size(); i++) {
            MapCoverInfo info = tempList.get(i);
            if (info.getIndex() == 1) {
                data.mainIndex = i;
            }
            String coverUrl = CoverPath.getMulCoverPath(dbMapInfo.getBucket(), versionTable.getUgcId(), instance,
                    info.getInfo().getVersion(), info.getCoverId());
            data.coverList.add(coverUrl);
        }
        return data;
    }

    public static MapCoverVideoInfo getMapCoverVideoInfo(MapCoverVideoInfoSaved saved) {
        if (!PropertyFileReader.getRealTimeBooleanItem("ugc_video_cover_switch", true)) {
            // 如果开关没开，就返回默认的
            return MapCoverVideoInfo.getDefaultInstance();
        }
        // 检查视屏信息
        if (!saved.getCoverVideoMgr().getInUsed()) {
            return MapCoverVideoInfo.getDefaultInstance();
        }
        return saved.getCoverVideoMgr();
    }

    public static String getVideoCoverPathStr(int mapType, long creatorId, long ugcId, String bucket,
            MapCoverVideoInfoSaved saved) {
        return CoverPath.getVideoCoverPath(mapType, creatorId, ugcId, bucket, getMapCoverVideoInfo(saved));
    }

    public static VideoCoverInfoReport getVideoCoverPath(int mapType, long creatorId, long ugcId, String bucket,
            MapCoverVideoInfoSaved saved) {
        String path = getVideoCoverPathStr(mapType, creatorId, ugcId, bucket, saved);
        if (path.isEmpty()) {
            return VideoCoverInfoReport.getDefaultInstance();
        }
        VideoCoverInfoReport.Builder builder = VideoCoverInfoReport.newBuilder();
        builder.setVideoUrl(path);
        return builder.build();
    }

    // 这里只会有正常地图有说明
    public static ReportVersionMapInstruction getMapInstruction(String bucket,
            UgcMapExtraConfigDescription extraConfig) {
        if (extraConfig == null) {
            return null;
        }
        ReportVersionMapInstruction.Builder mapInstruction = ReportVersionMapInstruction.newBuilder();
        mapInstruction.setText(extraConfig.getText());
        for (MapCoverInfo coverInfo : extraConfig.getPictureList()) {
            if (!(coverInfo.getInfo().getMsgType() == MdType.CoverASTC_VALUE
                    || coverInfo.getInfo().getMsgType() == MdType.CoverASTCLarge_VALUE)) {
                continue;
            }
            String picUrl = CoverPath.getMulCoverPath(bucket, 0, UgcInstanceType.CommonInstance_VALUE,
                    coverInfo.getInfo().getVersion(), coverInfo.getCoverId());
            mapInstruction.addPicList(picUrl);
        }
        for (UgcMapExtraConfigDescriptionItem item : extraConfig.getItemsList()) {
            try {
                String itemStr = JsonFormat.printer().preservingProtoFieldNames().omittingInsignificantWhitespace()
                        .print(item);
                mapInstruction.addTextList(itemStr);
            } catch (Exception e) {
                LOGGER.error("item:{}", item, e);
            }
        }
        return mapInstruction.build();
    }

    // 构建ReportMapLoading
    public static Map<Long, ReportMapLoading> genReportMapLoading(String bucket, long creatorId,
            MapLoadingInfo mapLoadingInfo) {
        if (mapLoadingInfo == null) {
            return new HashMap<>();
        }
        Map<Long, ReportMapLoading> mapLoadingMap = new HashMap<>();
        for (MapLoadingData mapLoadingData : mapLoadingInfo.getLoadingListList()) {
            if (!mapLoadingData.hasInfo()) {
                continue;
            }
            ReportMapLoading.Builder reportMapLoadingBuilder = ReportMapLoading.newBuilder();
            reportMapLoadingBuilder.setUseOfficial(mapLoadingData.getUseOfficial());
            if (mapLoadingData.getInfo().getMsgType() == MdType.CoverASTC_VALUE
                    || mapLoadingData.getInfo().getMsgType() == MdType.CoverASTCLarge_VALUE) {
                String picUrl = CustomPath.getCustomLoadingPath(bucket, UgcInstanceType.CommonInstance_VALUE, creatorId,
                        mapLoadingData.getLoadingId(), mapLoadingData.getInfo().getVersion());
                reportMapLoadingBuilder.setPicUrl(picUrl);
            }

            mapLoadingMap.put(mapLoadingData.getInfo().getLayerId(), reportMapLoadingBuilder.build());
        }
        return mapLoadingMap;
    }

    // 获取主场景的loading
    public static ReportMapLoading getMainReportMapLoading(Map<Long, ReportMapLoading> mapLoadingMap) {
        var layerZero = mapLoadingMap.get(0L);
        if (layerZero != null) {
            return layerZero;
        }
        var layerOne = mapLoadingMap.get(1L);
        if (layerOne != null) {
            return layerOne;
        }
        return null;
    }

    /**
     * 多版本信息
     *
     * @param dbMapInfo
     * @param versionArray
     */
    public static void getPublishVersionArray(TcaplusDb.UgcPublish dbMapInfo, JSONArray versionArray,
            ReportVersionMapInstruction mapInstruction) {
        String pubCover = dbMapInfo.getBucket() + CommonUtil.getPubCover(dbMapInfo);
        int mainIndex = 0;
        List<String> coverList = new ArrayList<>();
        int instance = dbMapInfo.getUgcInstanceType();
        UgcInstanceType ugcInstanceType = UgcInstanceType.forNumber(dbMapInfo.getUgcInstanceType());
        List<MapCoverInfo> infoList = dbMapInfo.getCover().getInfoList();
        List<MapCoverInfo> tempList = new ArrayList<>();
        for (int i = 0; i < infoList.size(); i++) {
            MapCoverInfo info = infoList.get(i);
            if (info.getInfo().getMsgType() == MdType.CoverASTC_VALUE) {
                tempList.add(info);
            }
        }

        for (int i = 0; i < tempList.size(); i++) {
            MapCoverInfo info = tempList.get(i);
            if (info.getIndex() == 1) {
                mainIndex = i;
            }
            String coverUrl = CommonUtil.getMulCoverPath(dbMapInfo.getBucket(), dbMapInfo.getUgcId(), instance,
                    info.getInfo().getVersion(), info.getCoverId());
            coverList.add(coverUrl);
        }
        JSONObject versionInfo = new JSONObject();
        versionInfo.put("version", dbMapInfo.getMdList().getVersionId());
        versionInfo.put("publish_at", dbMapInfo.getPublishTime());
        versionInfo.put("update_at", dbMapInfo.getUpdateTime());
        versionInfo.put("status", dbMapInfo.getReviewStatus());
        versionInfo.put("template_id", dbMapInfo.getTemplateId());
        versionInfo.put("update_duration", dbMapInfo.getEditorSec());
        versionInfo.put("cover", pubCover);
        versionInfo.put("cover_list", coverList);
        versionInfo.put("main_cover_index", mainIndex);
        versionInfo.put("map_group_id_list", dbMapInfo.getResIdList().getArrayLongList());
        UgcMapMetaInfo metaInfo = CommonUtil.getPublishMetaInfoOfFirst(dbMapInfo.getMdList().getInfoList());
        String ugcLevelDataUrl = MapDataPath.getPubLevelDataPath(dbMapInfo.getUgcId(), metaInfo, ugcInstanceType);
        if (metaInfo != null) {
            versionInfo.put("map_data_md5", metaInfo.getPreMsg());
        }
        versionInfo.put("map_data_url", dbMapInfo.getBucket() + ugcLevelDataUrl);
        versionInfo.put("layer_id", 1);
        String descUrl = CommonUtil.getMetaInfoUrl(dbMapInfo.getUgcId(),dbMapInfo.getMdList().getInfoList(),MdType.MapDescription_VALUE);
        String mapLevelResUrl = CommonUtil.getMetaInfoUrl(dbMapInfo.getUgcId(),dbMapInfo.getMdList().getInfoList(),MdType.MapResByZip_VALUE);
        if (StringUtils.isNotBlank(descUrl)) {
            versionInfo.put("map_description", dbMapInfo.getBucket() + descUrl);
        }
        if (StringUtils.isNotBlank(mapLevelResUrl)) {
            versionInfo.put("map_level_res", dbMapInfo.getBucket() + mapLevelResUrl);
        }
        JSONArray layerArray = new JSONArray();
        List<UgcMapMetaInfo> pubList = CommonUtil.getPublishMetaInfoList(dbMapInfo.getMdList().getInfoList());
        Map<Long, ReportMapLoading> mapLoadingMap = CommonUtil.genReportMapLoading(dbMapInfo.getBucket(),
                dbMapInfo.getCreatorId(), dbMapInfo.getMapLoading());
        for (UgcMapMetaInfo info : pubList) {
            String url = MapDataPath.getPubLevelDataPath(dbMapInfo.getUgcId(), info, ugcInstanceType);
            JSONObject layerInfo = new JSONObject();
            layerInfo.put("layer_id", info.getLayerId());
            layerInfo.put("map_data_url", dbMapInfo.getBucket() + url);
            layerInfo.put("map_data_md5", info.getPreMsg());
            ReportMapLoading reportMapLoading = mapLoadingMap.get(info.getLayerId());
            if (reportMapLoading != null) {
                JSONObject mapLoading = new JSONObject();
                mapLoading.put("pic_url", reportMapLoading.getPicUrl());
                mapLoading.put("use_official", reportMapLoading.getUseOfficial());
                layerInfo.put("map_loading", mapLoading);
            }
            layerArray.put(layerInfo);
        }
        versionInfo.put("layer_info_list", layerArray);
        if (mapInstruction != null) {
            JSONObject instruction = new JSONObject();
            instruction.put("pic_list", mapInstruction.getPicListList());
            instruction.put("text", mapInstruction.getText());
            instruction.put("text_list", mapInstruction.getTextListList());
            versionInfo.put("map_instruction", instruction);
        }
        ReportMapLoading reportMapLoading = CommonUtil.getMainReportMapLoading(mapLoadingMap);
        if (reportMapLoading != null) {
            JSONObject mapLoading = new JSONObject();
            mapLoading.put("pic_url", reportMapLoading.getPicUrl());
            mapLoading.put("use_official", reportMapLoading.getUseOfficial());
            versionInfo.put("map_loading", mapLoading);
        }

        versionArray.put(versionInfo);
    }

    /**
     * 获取发布地图封面
     *
     * @param dbMapInfo
     * @return
     */
    public static String getPubCover(UgcPublish dbMapInfo) {
        CoverParamData paramData = new CoverParamData();
        paramData.ugcId = dbMapInfo.getUgcId();
        paramData.resType = dbMapInfo.getUgcResType();
        paramData.instanceType = UgcInstanceType.forNumber(dbMapInfo.getUgcInstanceType());
        paramData.isResPrivate = dbMapInfo.getIsResPrivate() == 1;
        paramData.mapMetaInfoList = dbMapInfo.getMdList().getInfoList();
        return CoverPath.getPubCoverPath(paramData);
    }

    /**
     * 获取发布地图封面
     *
     * @param dbMapInfo
     * @return
     */
    public static String getPubCover(UgcPublish dbMapInfo, TcaplusDb.UgcMultiVersionTable versionTable) {
        CoverParamData paramData = new CoverParamData();
        paramData.ugcId = versionTable.getUgcId();
        paramData.resType = dbMapInfo.getUgcResType();
        paramData.instanceType = UgcInstanceType.forNumber(versionTable.getUgcInstanceType());
        paramData.isResPrivate = dbMapInfo.getIsResPrivate() == 1;
        paramData.mapMetaInfoList = versionTable.getMdList().getInfoList();
        return CoverPath.getPubCoverPath(paramData);
    }

    /**
     * 获取发布地图的缩略图url
     *
     * @param dbMapInfo
     * @return
     */
    public static String getMapCoverUrl(TcaplusDb.UgcPublish dbMapInfo) {
        UgcMapMetaInfo astcMetaInfo = CommonUtil.getPublishMetaInfoOfFirst(dbMapInfo.getMdList().getInfoList());
        String astcVersion = astcMetaInfo == null ? "" : astcMetaInfo.getVersion();
        String cover = CommonUtil.getPubCoverUrl(dbMapInfo.getBucket(), dbMapInfo.getUgcId(),
                UgcInstanceType.forNumber(dbMapInfo.getUgcInstanceType()).getNumber(), astcVersion,
                dbMapInfo.getUgcResType(), dbMapInfo.getIsResPrivate() == 1, dbMapInfo.getMdList().getInfoList());
        return cover;
    }

    /**
     * 获取发布地图levelData url:  bucket + path
     * @param version
     * @param dbMapInfo
     * @return
     */
    public static String getMapDataUrlForManager(String version , TcaplusDb.UgcPublish dbMapInfo){
        String pubLevelUrl = CommonUtil.getMapDataUrlForManager(dbMapInfo.getMdList().getInfoList(), dbMapInfo.getBucket(),
                dbMapInfo.getUgcId(), dbMapInfo.getUgcInstanceType(), version, dbMapInfo.getUgcResType(),
                dbMapInfo.getIsResPubCosPath(), dbMapInfo.getIsResPrivate());
        return pubLevelUrl;
    }

    /**
     * 获取 管理端需要的 map_data_url: bucket + path
     */
    public static String getMapDataUrlForManager(List<UgcMapMetaInfo> mapMetaInfoList, String bucket,
                                                 long mapId, int instance, String version, int ugcResType, int isResPubCosPath,
                                                 int isResPrivate) {
        // 资源需要多个路径，特殊处理
        if (instance == UgcInstanceType.GroupInstance_VALUE || instance == UgcInstanceType.ResInstance_VALUE) {
            List<UgcMapMetaInfo> metaInfos = getResourcePublishMetaInfo(mapMetaInfoList);
            List<String> pathList = new ArrayList<>();

            for (UgcMapMetaInfo metaInfo : metaInfos) {
                UgcCosPathType pathType;
                // isResPrivate字段表示是否是私有资源, 私有资源等同于草稿
                if (isResPrivate == 0) {
                    pathType = UgcCosPathType.UCPT_PUB;
                } else {
                    // isResPubCosPath字段表示私有资源是否在pub路径
                    pathType = isResPubCosPath == 1 ? UgcCosPathType.UCPT_PUB_RES_PRIVATE : UgcCosPathType.UCPT_PUB;
                }

                String tmpPath = getCosPath(instance, ugcResType, metaInfo.getMsgType(), pathType, 0L, mapId);
                if (tmpPath.isEmpty()) {
                    continue;
                }

                if (StringUtils.isNotBlank(metaInfo.getVersion())) {
                    tmpPath = tmpPath + "?versionId=" + metaInfo.getVersion();
                }

                pathList.add(bucket + tmpPath);
            }

            return StringUtils.join(pathList, ";");
        }

        int mdType = CommonUtil.getPublishMdType(mapMetaInfoList);
        String path = getCosPath(instance, ugcResType, mdType, UgcCosPathType.UCPT_PUB, 0L, mapId);
        if (path.isEmpty()) {
            return path;
        }
        if (StringUtils.isNotBlank(version)) {
            path = path + "?versionId=" + version;
        }
        return bucket + path;
    }

    public static List<UgcMapMetaInfo> getResourcePublishMetaInfo(List<UgcMapMetaInfo> mapMetaInfoList) {
        List<UgcMapMetaInfo> metaInfos = new ArrayList<>();
        for (UgcMapMetaInfo info : mapMetaInfoList) {
            if (info.getMsgType() == MdType.GroupData_VALUE) {
                metaInfos.add(info);
            } else if (info.getMsgType() == MdType.ResourceZip_VALUE) {
                metaInfos.add(info);
            } else if (info.getMsgType() == MdType.ResourceResZip_VALUE) {
                metaInfos.add(info);
            }
        }
        return metaInfos;
    }


    /**
     * 获取发布的元数据类型
     *
     * @param mapMetaInfoList
     * @return
     */
    public static int getPublishMdType(List<UgcMapMetaInfo> mapMetaInfoList) {
        UgcMapMetaInfo publishMetaInfo = getPublishMetaInfo(mapMetaInfoList);
        return publishMetaInfo == null ? MdType.LevelData_VALUE : publishMetaInfo.getMsgType();
    }



    /**
     * 判断是否是模组类的资源， 策划需求：需区分模组类的资源以及其它资源。
     * @param resType UgcResType
     * @return
     */
    public static boolean isResTypeGroup(UgcResType resType) {
        if (resType == UgcResType.EURT_Group_Common) {
            return true;
        }
        UgcResTypeGroup enumResGroup = UgcResTypeGroup.forNumber(resType.getNumber());
        return enumResGroup != null;
    }

    public static boolean isResTypeGroup(int resType) {
        UgcResType ugcResType = UgcResType.forNumber(resType);
        if (ugcResType == null) {
            return false;
        }
        return isResTypeGroup(ugcResType);
    }

    //场景去重
    public static List<UgcMapMetaInfo> layerRemoveRepeat(List<UgcMapMetaInfo> addList, List<UgcMapMetaInfo> mapList){
        Map<Integer, Map<Long,UgcMapMetaInfo>> mdMap = new ConcurrentHashMap<>(); // 关卡信息
        for (UgcMapMetaInfo metaInfo : mapList) {
            mdMap.computeIfAbsent(metaInfo.getMsgType(), k -> new HashMap<>())
                    .put(metaInfo.getLayerId(), metaInfo);
        }
        for (UgcMapMetaInfo metaInfo : addList) {
            mdMap.computeIfAbsent(metaInfo.getMsgType(), k -> new HashMap<>())
                    .put(metaInfo.getLayerId(), metaInfo);
        }

        List<UgcMapMetaInfo> mdList = new ArrayList<>();
        for (Map<Long, UgcMapMetaInfo> value : mdMap.values()) {
            mdList.addAll(value.values());
        }
        return  mdList;
    }

    /**
     * 去重
     * @param idList
     * @return
     */
    public static Set removeRepeat(List<Long> idList){
        Set<Long> resList = new HashSet<>();
        for (Long id : idList) {
            resList.add(id);
        }
        return resList;
    }

    public static UgcRecommendType switchType(UgcMapScreenType type) {
        switch (type.getNumber()) {
            case UgcMapScreenType.Latest_VALUE:
                return UgcRecommendType.EdenRecommendNew;
            case UgcMapScreenType.Popularity_VALUE:
                return UgcRecommendType.EdenHot;
            case UgcMapScreenType.collect_VALUE:
                return UgcRecommendType.EdenCollect;
            case UgcMapScreenType.UgcMapScreenTopic_VALUE:
                return UgcRecommendType.EdenTopic;
            case UgcMapScreenType.UgcMapScreenQuicksort_VALUE:
                return UgcRecommendType.Eden7;
            case UgcMapScreenType.UgcMapScreenSimilar_VALUE:
                return UgcRecommendType.Eden8;
            case UgcMapScreenType.UgcMapScreenGiveLike_VALUE:
                return UgcRecommendType.EdenGiveLike;
            case UgcMapScreenType.UgcMapScreenOfficialGroup_VALUE:
                return UgcRecommendType.EdenOfficialGroup;
            case UgcMapScreenType.UgcMapScreenGroupRecommand_VALUE:
                return UgcRecommendType.EdenGroupRecommendTotal;
            case UgcMapScreenType.UgcSubPlayerMaps_VALUE:
                return UgcRecommendType.EdenSubscribePlayerMaps;
            case UgcMapScreenType.UgcBattleSettlementRecommend_VALUE:
                return UgcRecommendType.EdenUgcBattleSettlementRecommend;
            case UgcMapScreenType.UgcResRefSort_VALUE:
                return UgcRecommendType.EdenUgcResRefSort;
            case UgcMapScreenType.LastSeasonHot_VALUE:
                return UgcRecommendType.EdenLastSeasonHot;
            case UgcMapScreenType.AllTimeHot_VALUE:
                return UgcRecommendType.EdenAllTimeHot;
            case UgcMapScreenType.AllTimeHotNew_VALUE:
                return UgcRecommendType.EdenAllTimeHotNew;
            case UgcMapScreenType.RecommendTheme_VALUE:
                return UgcRecommendType.EdenRecommendTheme;
            case UgcMapScreenType.WeeklyHot_VALUE:
                return UgcRecommendType.EdenWeeklyHot;
            case UgcMapScreenType.MonthlyHot_VALUE:
                return UgcRecommendType.EdenMonthlyHot;
            case UgcMapScreenType.RecommendThemeNew_VALUE:
                return UgcRecommendType.EdenRecommendThemeNew;
            case UgcMapScreenType.UgcMapHottest_VALUE:
                return UgcRecommendType.EdenUgcMapHottest;
            case UgcMapScreenType.UgcMapMostCollected_VALUE:
                return UgcRecommendType.EdenUgcMapMostCollected;
            default:
                return UgcRecommendType.EdenRecommendTotal;
        }
    }

    public static List<TcaplusDb.UgcMultiVersionTable> getUgcMultiVersion(long ugcId) {
        TcaplusDb.UgcMultiVersionTable.Builder multiVersion = TcaplusDb.UgcMultiVersionTable.newBuilder();
        multiVersion.setUgcId(ugcId);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetByPartKeyReq(multiVersion).setOffset(0).setLimit(3).send();
        if (!rsp.isOKIgnoreRecordNotExist()) {
            LOGGER.error("getUgcMultiVersion rsp:{}", rsp.getResult().getValue());
            return null;
        }
        List<TcaplusDb.UgcMultiVersionTable> versionList = new ArrayList<>();
        for (TcaplusManager.TcaplusRecordGroup rspData : rsp.getRspDataList()) {
            for (TcaplusManager.TcaplusRecordData<?> rData : rspData.getRecordList()) {
                TcaplusDb.UgcMultiVersionTable dbMap = (TcaplusDb.UgcMultiVersionTable) rData.msg;
                versionList.add(dbMap);
            }
        }
        return versionList;
    }

    public static TcaplusDb.UgcMultiVersionTable getUgcMultiVersion(long ugcId, long version) {
        TcaplusDb.UgcMultiVersionTable.Builder multiVersion = TcaplusDb.UgcMultiVersionTable.newBuilder();
        multiVersion.setUgcId(ugcId);
        multiVersion.setMulVersion(version);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetReq(multiVersion).send();
        if (!rsp.isOKIgnoreRecordNotExist()) {
            LOGGER.error("getUgcMultiVersion rsp:{}", rsp.getResult().getValue());
            return null;
        }
        if (rsp.hasFirstRecord()) {
            TcaplusDb.UgcMultiVersionTable dbMap = (TcaplusDb.UgcMultiVersionTable) rsp.firstRecordData().msg;
            return dbMap;
        }
        return null;
    }

    public static TcaplusDb.UgcMapExtraConfigTable getMapExtraConfigTable(UgcMapExtraConfigIndex index) {
        return getMapExtraConfigTable(index.getUniqueId(), index.getCfgType(), index.getCfgId(), index.getVerId());
    }

    public static TcaplusDb.UgcMapExtraConfigTable getMapExtraConfigTable(long uniqueId,
            int cfgType, int cfgId, int verId) {
        TcaplusDb.UgcMapExtraConfigTable.Builder ugcMapExtraConfigTable = TcaplusDb.UgcMapExtraConfigTable.newBuilder();
        ugcMapExtraConfigTable.setUniqueId(uniqueId).setCfgType(cfgType).setCfgId(cfgId).setVerId(verId);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetReq(ugcMapExtraConfigTable).send();
        if (!rsp.isOKIgnoreRecordNotExist()) {
            LOGGER.error("getMapExtraConfigTable rsp:{}", rsp.getResult().getValue());
            return null;
        }
        if (rsp.hasFirstRecord()) {
            TcaplusDb.UgcMapExtraConfigTable dbMap = (TcaplusDb.UgcMapExtraConfigTable) rsp.firstRecordData().msg;
            return dbMap;
        }
        return null;
    }

    public static void setUgcPublish(UgcPublish.Builder map, TcaplusDb.UgcMultiVersionTable versionTable) {
        map.getDescInfoBuilder().setIsOpenHealth(versionTable.getDescInfo().getIsOpenHealth());  //是否打开生命值
        map.getDescInfoBuilder().setIsEditable(versionTable.getDescInfo().getIsEditable());      //是否可编程
        map.setTags(versionTable.getTags());    //标签
        map.setDesc(versionTable.getDesc());    //描述
        map.setPointsNumber(versionTable.getPointsNumber());       //出生点人数
        map.setClientVersion(versionTable.getClientVersion());    //客户端版本号
        map.setBuyGoodsStatus(versionTable.getBuyGoodsStatus());  // ugc地图中星钻道具是否可以购买状态
        map.getDescInfoBuilder().setIsOpenSave(versionTable.getDescInfo().getIsOpenSave());  // 是否开启存档
        map.getDescInfoBuilder().setDataStoreSaveType(
                versionTable.getDescInfo().getDataStoreSaveType());   // 数据存储类型 ENUM DataStoreSaveType
        map.getDescInfoBuilder().setIsCampsOpen(versionTable.getDescInfo().getIsCampsOpen());         //阵营是否开启
        map.getDescInfoBuilder().setIsDanMuUnable(versionTable.getDescInfo().getIsDanMuUnable());   // 是否允许发弹幕
        map.getDescInfoBuilder().setIsAllowMidJoin(versionTable.getDescInfo().getIsAllowMidJoin());    // 是否允许中途加入
        map.getDescInfoBuilder().setModelType(versionTable.getDescInfo().getModelType());  //当前地图模式
        map.setCover(versionTable.getCoverList());   //缩略图
        map.setTemplateId(versionTable.getTemplateId());     //地图模板
        map.setEditorSec(versionTable.getEditorSec());  //编辑时长
        map.setResIdList(versionTable.getResIdList());    //引用资产
        map.setMdList(versionTable.getMdList());
        map.setReviewStatus(versionTable.getStatus());
        map.setRankList(versionTable.getRankList());        // 排行榜
        map.getDescInfoBuilder().setIsContainDiyArms(versionTable.getDescInfo().getIsContainDiyArms());
        map.setOccupancyValue(versionTable.getOccupancyValue());
        map.setPropertyScore(versionTable.getPropertyScore());
        map.setActorCount(versionTable.getActorCount());
        map.setIsAdvert(versionTable.getIsAdvert());
        map.setAdvertStatus(versionTable.getAdvertStatus());
        map.setPublishGoodsStatus(versionTable.getPublishGoodsStatus());
        // 仅针对家园场景, 才关心二次发布时引用的模组数据
        if (map.getUgcInstanceType() == UgcInstanceType.HomeInstance_VALUE) {
            map.setUgcGroupIdList(versionTable.getUgcGroupIdList());
        } else if (map.getUgcInstanceType() == UgcInstanceType.CommonInstance_VALUE) {
            map.setUgcGroupIdList(versionTable.getUgcGroupIdList());
        }

        if (!versionTable.getDescInfo().getCampsList().isEmpty()) {
            map.getDescInfoBuilder().clearCamps().addAllCamps(versionTable.getDescInfo().getCampsList());   //阵容信息
        }

        // 第一次发布有数据，第二次发布不携带数据是否可以认为是非法操作
        if (!versionTable.getDescInfo().getSpawnPointInfosList().isEmpty()) {
            map.getDescInfoBuilder().clearSpawnPointInfos()
                    .addAllSpawnPointInfos(versionTable.getDescInfo().getSpawnPointInfosList());    //出生点数据
        }
        if (!versionTable.getExtraInfo().equals(UgcExtraInfo.getDefaultInstance())) {
            map.setExtraInfo(versionTable.getExtraInfo());     // 额外信息
        }

        map.getDescInfoBuilder().setPlayRuleOMD(
                versionTable.getDescInfo().getPlayRuleOMD());   // 兽人类型 PlayRuleOMD
        map.setExtraConfigIndexMap(versionTable.getExtraConfigIndexMap());
        map.setMapLoading(versionTable.getMapLoading());
        map.setLobbyCover(versionTable.getLobbyCover());
    }

    public static boolean canUgcBlobDataSave() {
        if (!PropertyFileReader.getRealTimeBooleanItem("ugc_db_saved_switch", false)) {
            return false;
        }
        if (!ServerEngine.getInstance().isIdcTestOnly()/* && !ServerEngine.getInstance().isDev() */) {
            return false;
        }
        // 是否还有其他条件
        return true;
    }

    public static boolean canUgcAppPlayerInfoSync() {
        // 检查开关
        if (!PropertyFileReader.getRealTimeBooleanItem("ugc_app_player_info_sync", true)) {
            return false;
        }
        // 直接过滤掉press环境
        if (ServerEngine.getInstance().isPress()) {
            return false;
        }
        return true;
    }

    public static UgcAchievementMap convertUgcAchievementList(List<UgcAchievementIndex> achievementIndexList, int type) {
        UgcAchievementMap.Builder achievementMap = UgcAchievementMap.newBuilder();
        for (UgcAchievementIndex index : achievementIndexList) {
            UgcAchievementInfo.Builder info = UgcAchievementInfo.newBuilder();
            info.setUgcId(index.getUgcId());
            info.setVerId(index.getVerId());
            info.setType(type);
            achievementMap.putAchCfgMap(index.getAchId(), info.build());
        }
        return achievementMap.build();
    }

    public static UgcAchievementMap convertUgcAchievementList(List<UgcAchievementIndex> achievementIndexList) {
        UgcAchievementMap.Builder achievementMap = UgcAchievementMap.newBuilder();
        for (UgcAchievementIndex index : achievementIndexList) {
            UgcAchievementInfo.Builder info = UgcAchievementInfo.newBuilder();
            info.setUgcId(index.getUgcId());
            info.setVerId(index.getVerId());
            info.setType(index.getType());
            achievementMap.putAchCfgMap(index.getAchId(), info.build());
        }
        return achievementMap.build();
    }

    public static List<UgcAchievementIndex> convertUgcAchievementMap(UgcAchievementMap achievementMap) {
        List<UgcAchievementIndex> achievementIndexList = new ArrayList<>();
        for (Entry<Integer, UgcAchievementInfo> entry : achievementMap.getAchCfgMapMap().entrySet()) {
            UgcAchievementIndex.Builder index = UgcAchievementIndex.newBuilder();
            index.setAchId(entry.getKey());
            index.setUgcId(entry.getValue().getUgcId());
            index.setVerId(entry.getValue().getVerId());
            index.setType(entry.getValue().getType());
            achievementIndexList.add(index.build());
        }
        return achievementIndexList;
    }

    public static String getExtraConfigMapInstruction(UgcMapExtraConfigDescription extraConfigDescription) {
        StringBuilder content = new StringBuilder();
        for (UgcMapExtraConfigDescriptionItem item : extraConfigDescription.getItemsList()) {
            if (item.getType() == 5) {  // 忽略图片
                continue;
            }
            content.append(item.getContent()).append("#$%");
        }
        return content.toString();
    }

    public static UgcMapExtraConfigIndexMap convertExtraConfigIndexList(
            List<UgcMapExtraConfigIndex> extraConfigIndexList,
            UgcMapExtraConfigIndexStatusType saveType,
            Map<UgcMapExtraConfigIndexKey, UgcMapExtraConfigIndex> mapExtraConfigIndexMap) {
        Map<Integer, Map<Integer, UgcMapExtraConfigIndex>> mapMap = new HashMap<>();
        for (UgcMapExtraConfigIndex extraConfigIndex : extraConfigIndexList) {
            Map<Integer, UgcMapExtraConfigIndex> cfgIdMap =
                    mapMap.computeIfAbsent(extraConfigIndex.getCfgType(), key -> new HashMap<>());
            UgcMapExtraConfigIndex.Builder index =
                    UgcMapExtraConfigIndex.newBuilder().mergeFrom(extraConfigIndex);
            if (saveType != null) {
                index.setSaveType(saveType.getNumber());
            } else if (mapExtraConfigIndexMap != null) {
                UgcMapExtraConfigIndex tmpIndex =
                        mapExtraConfigIndexMap.get(new UgcMapExtraConfigIndexKey(extraConfigIndex));
                if (tmpIndex != null) {
                    index.setSaveType(tmpIndex.getSaveType());
                }
            }
            cfgIdMap.put(extraConfigIndex.getCfgId(), index.build());
        }
        UgcMapExtraConfigIndexMap.Builder extraConfigIndexMap = UgcMapExtraConfigIndexMap.newBuilder();
        for (Entry<Integer, Map<Integer, UgcMapExtraConfigIndex>> entry : mapMap.entrySet()) {
            UgcMapExtraConfigIndexType.Builder extraConfigIndexTypeMap = UgcMapExtraConfigIndexType.newBuilder();
            extraConfigIndexTypeMap.putAllCfgIdMap(entry.getValue());
            extraConfigIndexMap.putIndexTypeMap(entry.getKey(), extraConfigIndexTypeMap.build());
        }
        return extraConfigIndexMap.build();
    }

    private static Map<UgcMapExtraConfigIndexKey, UgcMapExtraConfigIndex> genUgcMapExtraConfigIndexMap(
            UgcMapExtraConfigIndexMap indexMap, boolean exceptBan) {
        Map<UgcMapExtraConfigIndexKey, UgcMapExtraConfigIndex> extraConfigIndexMap = new HashMap<>();
        for (UgcMapExtraConfigIndexType indexType : indexMap.getIndexTypeMapMap().values()) {
            for (UgcMapExtraConfigIndex index : indexType.getCfgIdMapMap().values()) {
                // 是否被封禁了
                if (exceptBan) {
                    Boolean isBan = indexType.getCfgIdBanMapMap().get(index.getCfgId());
                    if (isBan != null && isBan) {
                        continue;
                    }
                }
                extraConfigIndexMap.put(new UgcMapExtraConfigIndexKey(index), index);
            }
        }
        return extraConfigIndexMap;
    }

    public static UgcMapExtraConfigIndexMap repairExtraConfigIndexList(UgcMapExtraConfigIndexMap db,
            List<UgcMapExtraConfigIndex> input) {
        Map<UgcMapExtraConfigIndexKey, UgcMapExtraConfigIndex> mapExtraConfigIndexMap =
                genUgcMapExtraConfigIndexMap(db, true);
        return convertExtraConfigIndexList(input, null, mapExtraConfigIndexMap);
    }

    public static UgcMapExtraConfigIndexMap convertExtraConfigIndexList(
            List<UgcMapExtraConfigIndex> extraConfigIndexList) {
        return convertExtraConfigIndexList(extraConfigIndexList, null, null);
    }

    public static List<UgcMapExtraConfigIndex> convertExtraConfigIndexMap(
            UgcMapExtraConfigIndexMap extraConfigIndexMap) {
        return convertExtraConfigIndexMap(extraConfigIndexMap, true);
    }

    public static List<UgcMapExtraConfigIndex> convertExtraConfigIndexMap(
            UgcMapExtraConfigIndexMap extraConfigIndexMap, boolean exceptBan) {
        List<UgcMapExtraConfigIndex> extraConfigIndexList = new ArrayList<>();
        for (UgcMapExtraConfigIndexType indexType : extraConfigIndexMap.getIndexTypeMapMap().values()) {
            for (UgcMapExtraConfigIndex index : indexType.getCfgIdMapMap().values()) {
                // 是否被封禁了
                if (exceptBan) {
                    Boolean isBan = indexType.getCfgIdBanMapMap().get(index.getCfgId());
                    if (isBan != null && isBan) {
                        continue;
                    }
                }
                UgcMapExtraConfigIndex.Builder info = UgcMapExtraConfigIndex.newBuilder();
                info.mergeFrom(index);
                extraConfigIndexList.add(info.build());
            }
        }
        return extraConfigIndexList;
    }

    public static UgcMapExtraConfigIndex getUgcMapExtraConfigIndex(UgcMapExtraConfigIndexMap extraConfigIndexMap,
            int cfgType, int cfgId) {
        if (extraConfigIndexMap == null) {
            return null;
        }
        List<UgcMapExtraConfigIndex> extraConfigIndexListWithBan = convertExtraConfigIndexMap(extraConfigIndexMap);
        UgcMapExtraConfigIndexMap extraConfigIndexMapWithBan = convertExtraConfigIndexList(extraConfigIndexListWithBan);
        UgcMapExtraConfigIndexType extraConfigIndexType = extraConfigIndexMapWithBan.getIndexTypeMapMap().get(cfgType);
        if (extraConfigIndexType == null) {
            return null;
        }
        UgcMapExtraConfigIndex extraConfigIndex = extraConfigIndexType.getCfgIdMapMap().get(cfgId);
        if (extraConfigIndex == null) {
            return null;
        }
        Boolean isBan = extraConfigIndexType.getCfgIdBanMapMap().get(cfgId);
        if (isBan != null && isBan) {
            return null;
        }
        return UgcMapExtraConfigIndex.newBuilder().mergeFrom(extraConfigIndex).build();
    }

    /**
     * 打包dsa json数据
     *
     * @param data
     * @return
     */
    public static String packDsaDownloadJson(SsUgcsvr.RpcMapUgcBaseInfoRes.Builder data) {
        JSONObject dsaJson = new JSONObject();

        try {
            long ugcId = data.getInfo().getUgcId();
            JSONObject commonJson = new JSONObject();
            commonJson.put("ugc_id", String.valueOf(ugcId));
            commonJson.put("region", data.getRegion());
            commonJson.put("bucket", data.getBucket());
            commonJson.put("secret_id", data.getKeyInfo().getId());
            commonJson.put("secret_key", data.getKeyInfo().getKey());
            commonJson.put("aes_key", data.getMapKey());
            commonJson.put("iv", data.getMapKey());
            commonJson.put("token", "");
            commonJson.put("additional", String.valueOf(data.getInfo().getInstanceType()));

            dsaJson.put("common", commonJson);

            JSONObject layerMap = new JSONObject();
            // fill levelData info.
            getPubLevelDataMetaJson(data, ugcId, layerMap);
            // fill extend info.
            getMapExtendMetaJson(data, ugcId, layerMap);

            dsaJson.put("layer", layerMap);
        } catch (Exception e) {
            LOGGER.error("packDsaDownloadJson error e=", e);
        }
        String jsonData = dsaJson.toString();
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("packDsaDownloadJson json data={}", jsonData);
        }
        return jsonData;
    }

    private static void getPubLevelDataMetaJson(SsUgcsvr.RpcMapUgcBaseInfoRes.Builder data, long ugcId,
            JSONObject layerMap) {
        List<UgcMapMetaInfo> descInfoList = getPublishMetaInfoList(data.getInfo().getMdList().getInfoList());
        for (UgcMapMetaInfo metaInfo : descInfoList) {
            String layerIdStr = String.valueOf(metaInfo.getLayerId());
            if (layerMap.has(layerIdStr)) {
                LOGGER.error("getPubLevelDataMetaJson error layerId={}", layerIdStr);
                continue;
            }

            JSONObject layerObject = createJsonObject(data, ugcId, metaInfo);
            JSONObject jsonObject = createJsonObject(data, ugcId, metaInfo);

            JSONArray jsonArray = new JSONArray();
            jsonArray.put(jsonObject);
            layerObject.put("datas", jsonArray);
            layerMap.put(layerIdStr, layerObject);
        }
    }

    @NotNull
    private static JSONObject createJsonObject(Builder data, long ugcId, UgcMapMetaInfo metaInfo) {
        JSONObject layerObject = new JSONObject();
        // 兼容老数据
        if (metaInfo.getLayerId() == 0 || metaInfo.getLayerId() == 1) {
            layerObject.put("file_name", data.getFileName());
            layerObject.put("key", data.getPubPath());
        } else {
            layerObject.put("file_name", CommonUtil.getPubLevelFileName(ugcId, metaInfo));
            UgcInstanceType instanceType = UgcInstanceType.forNumber(data.getInfo().getInstanceType());
            String pubLevelDataUrl = "";
            if (data.getInfo().getIsMultiTest()) {
                pubLevelDataUrl = MapDataPath.getMultiPlayPathWithoutVerID(ugcId, metaInfo,
                        instanceType);
            } else {
                pubLevelDataUrl = MapDataPath.getPubLevelDataPathWithoutVersionID(ugcId, metaInfo,
                        instanceType);
            }
            layerObject.put("key", pubLevelDataUrl);
        }

        layerObject.put("ugc_md5", metaInfo.getMsg());
        layerObject.put("pre_md5", metaInfo.getPreMsg());
        layerObject.put("version", metaInfo.getVersion() == null ? "latest" : metaInfo.getVersion());
        return layerObject;
    }

    private static void getMapExtendMetaJson(SsUgcsvr.RpcMapUgcBaseInfoRes.Builder data, long ugcId,
            JSONObject layerMap) {
        List<UgcMapMetaInfo> descInfoList = getPublishDescInfoList(data.getInfo().getMdList().getInfoList());
        for (UgcMapMetaInfo metaInfo : descInfoList) {
            String layerIdStr = String.valueOf(metaInfo.getLayerId());
            if (!layerMap.has(layerIdStr)) {
                LOGGER.error("getMapExtendMetaJson layerIdStr error layerIdStr={}", layerIdStr);
                continue;
            }
            JSONObject layerObject = layerMap.getJSONObject(layerIdStr);
            if (!layerObject.has("datas")) {
                LOGGER.error("getMapExtendMetaJson datas error layerIdStr={}", layerIdStr);
                continue;
            }
            JSONArray datas = layerObject.getJSONArray("datas");

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("file_name", CommonUtil.getPubLevelFileName(ugcId, metaInfo));
            UgcInstanceType instanceType = UgcInstanceType.forNumber(data.getInfo().getInstanceType());
            String pubLevelDataUrl = "";
            if (data.getInfo().getIsMultiTest()) {
                pubLevelDataUrl = MapDataPath.getMultiPlayPathWithoutVerID(ugcId, metaInfo,
                        instanceType);
            } else {
                pubLevelDataUrl = MapDataPath.getPubLevelDataPathWithoutVersionID(ugcId, metaInfo,
                        instanceType);
            }
            jsonObject.put("key", pubLevelDataUrl);
            jsonObject.put("ugc_md5", metaInfo.getMsg());
            jsonObject.put("pre_md5", metaInfo.getPreMsg());
            jsonObject.put("version", metaInfo.getVersion() == null ? "latest" : metaInfo.getVersion());

            datas.put(jsonObject);
        }
    }

    // 从地图的模板id中获取地图类型
    public static UGCMapType getUGCMapTypeFromTemplateId(int templateId) {
        ResUGCEditor.Item_UGCEditorMapTemplate template = UGCEditorMapTemplate.getInstance().get(templateId);
        int type = 0, openTag = 0;
        if (template != null) {
            type = template.getType().getNumber();
            openTag = template.getOpenTag();
        }
        LOGGER.debug("templateId:{} type:{} openTag:{}", templateId, type, openTag);
        return UGCMapType.forNumber(openTag);
    }

    // 检查地图是否可以评估
    public static boolean checkMapCanNotInitiateEvaluation(int templateId) {
        UGCMapType ugcMapType = getUGCMapTypeFromTemplateId(templateId);
        if (ugcMapType != UGCMapType.UGCLobby) {
            LOGGER.debug("templateId:{} can not evaluation", templateId);
            return true;
        }
        return false;
    }

    // 检查地图是否可以二次发布
    public static boolean checkMapCanUpdateWithEvaluationStatus(int templateId,
            UgcMapEvaluationStatusType evaluationStatusType) {
        if (checkMapCanNotInitiateEvaluation(templateId)) {
            // 不是评审类型 可以二次发布
            return true;
        }
        if (evaluationStatusType == UgcMapEvaluationStatusType.UMRST_SelfTest
                || evaluationStatusType == UgcMapEvaluationStatusType.UMRST_Unknown) {
            // 如果在自测也可以二次发布
            return true;
        }
        return false;
    }

    public static int getUgcMapType(int templateId) {
        UGCMapType ugcMapType = getUGCMapTypeFromTemplateId(templateId);
        if (ugcMapType == null) {
            return 0;
        }
        return ugcMapType.getNumber();
    }

    /**
     * 判断是否是竞速模式地图
     * @param modelType 地图类型
     * @param templateId 地图模板id
     * @return true 是竞速模式地图，false 非竞速模式地图
     */
    public static boolean isSpeedModeUgc(int modelType, int templateId) {

        // 统一模板地图竞速模式判断
        if (modelType == UgcMapModelType.UgcMap_Speed_VALUE) {
            return true;
        }

        ResUGCEditor.Item_UGCEditorMapTemplate template = UGCEditorMapTemplate.getInstance().get(templateId);
        if (template != null && template.getType() == UGCMapType.Topspeed) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否是OMD模式地图
     * @param templateId 地图模板id
     * @return true 是OMD模式地图，false 非OMD模式地图
     */
    public static boolean isOmdModeUgc(int templateId) {

        ResUGCEditor.Item_UGCEditorMapTemplate template = UGCEditorMapTemplate.getInstance().get(templateId);
        if (template != null && template.getType() == UGCMapType.OMDGame) {
            return true;
        }

        return false;
    }

    /**
     *  模组资产reason 映射
     * @param reason
     * @param type
     * @return
     */
    public static int getRealReason(int reason, UgcInstanceType type)  {
        if (reason == ApplyReason.AR_GROUP_OBJECT_VALUE || reason == ApplyReason.AR_GROUP_PUBLISH_OBJECT_VALUE) {
            List<T_UgcCosMapping> mappings = UgcCosMapping.getInstance().getArrayList();
            for (T_UgcCosMapping mapping : mappings) {
                if (mapping.getReason() == reason && mapping.getInstance() == type.getNumber()) {
                    return mapping.getAimReason();
                }
            }
        }
        return reason;
    }

    /**
     * 打印ugc版本下通关数据日志
     * @param ugcId 地图id
     * @param loginType 登录类型
     * @param isUgcApp 是否是ugcApp秦秋
     * @param data 版本下通关数据
     */
    public static void printVersionPassDataLog(long ugcId, int loginType, boolean isUgcApp, Message.Builder data) {

        if (!LOGGER.isDebugEnabled()) {
            return;
        }

        try {
            LOGGER.debug("version pass data update, ugcId:{}, loginType:{}, isUgcApp:{}, data:{}",
                    ugcId, loginType, isUgcApp, data != null ? JsonUtil.toJsonStringNoNewline(data) : "");
        } catch (Exception ex) {
            LOGGER.error("printVersionPassDataLog catch exception, ugcId:{}, ", ugcId, ex);
        }
    }

    public static UgcGoodsInfo convertGoodsInfo(TcaplusDb.UgcDbGoodsInfo dbGoodsInfo) {
        UgcGoodsInfo.Builder goodsInfo = UgcGoodsInfo.newBuilder();
        goodsInfo.setGoodsId(dbGoodsInfo.getGoodsId());
        goodsInfo.setUintPrice(dbGoodsInfo.getUintPrice());
        for (TcaplusDb.UgcDbItemInfo itemInfo : dbGoodsInfo.getItemInfoList()) {
            goodsInfo.putItems(itemInfo.getItemId(), itemInfo.getItemNum());
        }
        return goodsInfo.build();
    }

    public static List<UgcGoodsInfo> convertGoodsInfoList(List<TcaplusDb.UgcDbGoodsInfo> dbGoodsList) {
        List<UgcGoodsInfo> goodsList = new ArrayList<>();
        for (TcaplusDb.UgcDbGoodsInfo dbGoods : dbGoodsList) {
            goodsList.add(CommonUtil.convertGoodsInfo(dbGoods));
        }
        return goodsList;
    }

    public static boolean enableLocalServerTest() {
        return PropertyFileReader.getRealTimeBooleanItem("local_server_test", false);
    }

    // 将加入原因转化为中途加入的类型
    public static int convertJoinReasonToMidJoinSourceType(long matchId, boolean isMidJoin, int joinReason) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("matchId:{} isMidJoin:{} joinReason:{}", matchId, isMidJoin, joinReason);
        }
        if (!isMidJoin) {
            return MidJoinSourceType.MJST_Unknown_VALUE;
        }
        if (matchId != 0) {
            return MidJoinSourceType.MJST_MatchPullMidJoin_VALUE;
        }
        if (joinReason == RoomJoinType.RJT_AcceptInvitation_VALUE
                || joinReason == RoomJoinType.RJT_JoinApplyApprovedJumpToBattle_VALUE) {
            return MidJoinSourceType.MJST_RoomInviteMidJoin_VALUE;
        }
        return MidJoinSourceType.MJST_RoomJoinMidJoin_VALUE;
    }

    public static int repairRoomMidJoinType(boolean wentMidJoin, int roomMidJoinType) {
        if (roomMidJoinType == RoomMidJoinType.RMJT_Unknown_VALUE && wentMidJoin) {
            return RoomMidJoinType.RMJT_WentMidJoin_VALUE;
        }
        return roomMidJoinType;
    }

    /**
     * 是否多场景
     * @param templateId
     * @return
     */
    public static boolean isUgcMulScene(int templateId) {
        Item_UGCEditorMapTemplate template = UGCEditorMapTemplate.getInstance().get(templateId);
        return template.getIsMultiLevel();
    }

    public static UgcSceneBriefInfo.Builder genUgcSceneBriefInfo(UgcBaseInfo info) {
        UgcSceneBriefInfo.Builder ugcBriefInfoInfoBuilder = UgcSceneBriefInfo.newBuilder();
        ugcBriefInfoInfoBuilder.setUgcId(info.getUgcId());
        ugcBriefInfoInfoBuilder.addAllLayerInfo(info.getLayerInfoList());
        ugcBriefInfoInfoBuilder.setLevelLoadType(info.getPublishInputParam().getLevelLoadType());
        return ugcBriefInfoInfoBuilder;
    }

    public static class FucTypeFormatParam {

        public long ugcId = 0L;
        public long uid = 0L;
        public long creatorId = 0L;
        public int resType = 0;
        public long destId = 0L;
        public String env = "";
        public int worldId = 0;
        public String name = "";
    }

    public static class UgcMapExtraConfigIndexKey {

        private final long uniqueId;
        private final int cfgType;
        private final int cfgId;
        private final int verId;

        public UgcMapExtraConfigIndexKey(UgcMapExtraConfigIndex index) {
            this.uniqueId = index.getUniqueId();
            this.cfgType = index.getCfgType();
            this.cfgId = index.getCfgId();
            this.verId = index.getVerId();
        }

        @Override
        public int hashCode() {
            return (int) this.uniqueId;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            UgcMapExtraConfigIndexKey objCompareKey = (UgcMapExtraConfigIndexKey) obj;
            return uniqueId == objCompareKey.uniqueId
                    && cfgType == objCompareKey.cfgType
                    && cfgId == objCompareKey.cfgId
                    && verId == objCompareKey.verId;
        }
    }

    public static class UgcMapInstructionsFlowInfo {

        public String illustrateContent = "";
        public String illustratePicUrl = "";

        public UgcMapInstructionsFlowInfo(String bucket, UgcMapExtraConfigDescription extraConfigDescription) {
            StringBuilder tmpIllustratePicUrl = new StringBuilder();
            for (MapCoverInfo coverInfo : extraConfigDescription.getPictureList()) {
                if (!(coverInfo.getInfo().getMsgType() == MdType.CoverASTC_VALUE
                        || coverInfo.getInfo().getMsgType() == MdType.CoverASTCLarge_VALUE)) {
                    continue;
                }
                String picUrl = CoverPath.getMulCoverPath(bucket, 0, UgcInstanceType.CommonInstance_VALUE,
                        coverInfo.getInfo().getVersion(), coverInfo.getCoverId());
                tmpIllustratePicUrl.append(picUrl).append(";");
            }
            StringBuilder tmpIllustrateContent = new StringBuilder();
            for (UgcMapExtraConfigDescriptionItem item : extraConfigDescription.getItemsList()) {
                try {
                    String itemStr = JsonFormat.printer().preservingProtoFieldNames().omittingInsignificantWhitespace()
                            .print(item);
                    tmpIllustrateContent.append("[").append(itemStr).append("]");
                } catch (Exception e) {
                    LOGGER.error("item:{}", item, e);
                }
            }
            illustrateContent = tmpIllustrateContent.toString();
            illustratePicUrl = tmpIllustratePicUrl.toString();
        }
    }
}
