package com.tencent.resourceloader.resclass;

import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.wea.xlsRes.ResMatchOptimizeTeamsAfterMatch;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.resourceloader.ResHolder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * P_匹配后调整阵营队伍 HOK调整队伍启用系数
 */
public class AfterMatchOptimizeTeamsEvaluationData extends ResTable<ResMatchOptimizeTeamsAfterMatch.AfterMatchOptimizeTeamsEvaluationInfo> {

    private static final Logger LOGGER = LogManager.getLogger(AfterMatchOptimizeTeamsEvaluationData.class);

    Map<Integer, ResMatchOptimizeTeamsAfterMatch.AfterMatchOptimizeTeamsEvaluationInfo> internalDataMap;
    private final Map<Integer, ResMatchOptimizeTeamsAfterMatch.AfterMatchOptimizeTeamsEvaluationInfo> roomInfoIdToEvaluationInfoMap
            = new HashMap<>();

    public AfterMatchOptimizeTeamsEvaluationData() {
        internalDataMap = dataMap;
        generateKeyMetaData(ResMatchOptimizeTeamsAfterMatch.AfterMatchOptimizeTeamsEvaluationInfo.newBuilder());
    }

    public static AfterMatchOptimizeTeamsEvaluationData getInstance() {
        return (AfterMatchOptimizeTeamsEvaluationData) ResLoader.getResHolder().getResTableInstance("AfterMatchOptimizeTeamsEvaluationData");
    }

    public static AfterMatchOptimizeTeamsEvaluationData getInLoadingInstance(ResHolder resHolder) {
        return (AfterMatchOptimizeTeamsEvaluationData) resHolder.allResMap.get("AfterMatchOptimizeTeamsEvaluationData");
    }

    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {
        roomInfoIdToEvaluationInfoMap.clear();

        for (ResMatchOptimizeTeamsAfterMatch.AfterMatchOptimizeTeamsEvaluationInfo row : internalDataMap.values()) {
            for (int roomInfoId : row.getRoomInfoIdsList()) {
                roomInfoIdToEvaluationInfoMap.put(roomInfoId, row);
            }
        }
    }

    @Override
    public Class getMessageClass() {
        return ResMatchOptimizeTeamsAfterMatch.AfterMatchOptimizeTeamsEvaluationInfo.class;
    }

    /**
     * 根据房间id获取配置
     * @param roomInfoId 房间id
     * @return 匹配后调整队伍评估配置
     */
    public Optional<ResMatchOptimizeTeamsAfterMatch.AfterMatchOptimizeTeamsEvaluationInfo> get(int roomInfoId)
    {
        return Optional.ofNullable(roomInfoIdToEvaluationInfoMap.get(roomInfoId));
    }
}
