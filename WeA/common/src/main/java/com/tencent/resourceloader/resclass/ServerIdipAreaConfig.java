
package com.tencent.resourceloader.resclass;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.tool.ServerInfoTool;
import com.tencent.wea.xlsRes.ResServerIdipArea;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.resourceloader.ResHolder;

import java.util.HashSet;
import java.util.Map.Entry;

import com.tencent.wea.xlsRes.keywords.TconndApiAccount;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class ServerIdipAreaConfig extends ResTable<ResServerIdipArea.ServerIdipAreaConfig> {

    private static final Logger LOGGER = LogManager.getLogger(ServerIdipAreaConfig.class);

    private static final String KEY_SPLICING_CHAR = "-";
    private static final String IDIP_AREA_ENV_TEST = "test";
    private static final String IDIP_AREA_ENV_FORMAL = "formal";

    Map<String, ResServerIdipArea.ServerIdipAreaConfig> internalDataMap;

    // idip环境路由信息map, idip正式环境area->环境路由信息
    private ConcurrentHashMap<Integer, NKPair<String, String>> idipEnvRouteInfoMap = new ConcurrentHashMap<>();
    private Set<Integer> formalIdipAreaSet = new HashSet<>();

    public ServerIdipAreaConfig() {
        internalDataMap = dataMap;
        generateKeyMetaData(ResServerIdipArea.ServerIdipAreaConfig.newBuilder());
    }

    public static ServerIdipAreaConfig getInstance() {
        return (ServerIdipAreaConfig) ResLoader.getResHolder().getResTableInstance("ServerIdipAreaConfig");
    }

    public static ServerIdipAreaConfig getInLoadingInstance(ResHolder resHolder) {
        return (ServerIdipAreaConfig) resHolder.allResMap.get("ServerIdipAreaConfig");
    }

    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {
        idipEnvRouteInfoMap.clear();
        formalIdipAreaSet.clear();

        for (ResServerIdipArea.ServerIdipAreaConfig config : internalDataMap.values()) {
            if (ServerInfoTool.isDomesticEnv() && !config.getIsOversea() && config.getFormalIdipArea() > 0 &&
                    config.getUrl() != null && !config.getUrl().isEmpty() &&
                    config.getEnvNamespace() != null && !config.getEnvNamespace().isEmpty()) {
                idipEnvRouteInfoMap.put(config.getFormalIdipArea(), new NKPair<>(config.getUrl(), config.getEnvNamespace()));
            }
        }
        LOGGER.debug("idipEnvRouteInfoMap size:{}", idipEnvRouteInfoMap.size());

        String envFlag = PropertyFileReader.getItem("env_flag", "");
        int worldId = Framework.getInstance().getWorldId();
        StringBuffer stringBuffer = new StringBuffer().append(worldId).append(KEY_SPLICING_CHAR).append(envFlag);
        ResServerIdipArea.ServerIdipAreaConfig config = get(stringBuffer.toString());
        if (config != null) {
            formalIdipAreaSet.add(config.getWxFormalIdipArea());
            formalIdipAreaSet.add(config.getQqFormalIdipArea());
            formalIdipAreaSet.add(config.getSteamFormalIdipArea());
        }
    }

    @Override
    public Class getMessageClass() {
        return ResServerIdipArea.ServerIdipAreaConfig.class;
    }

    public ResServerIdipArea.ServerIdipAreaConfig get(String key) {
        return Optional.ofNullable(internalDataMap.get(key))
                .orElse(null);
    }

    /**
     * 获取idip平台正式环境idip area
     * @param worldId 环境worldId
     * @param envFlag 环境标识
     * @param accountType 账号类型
     * @return idip平台正式环境idip area
     */
    public Integer getFormalIdipArea(Integer worldId, String envFlag, int accountType) {

        if (envFlag == null || StringUtils.isBlank(envFlag) || StringUtils.equals("dev", envFlag)) {
            return 0;
        }

        StringBuffer stringBuffer = new StringBuffer().append(worldId).append(KEY_SPLICING_CHAR).append(envFlag);
        ResServerIdipArea.ServerIdipAreaConfig config = get(stringBuffer.toString());
        if (config == null) {
            return 0;
        }

        // 海外环境直接取当前环境idip平台正式环境的idip area返回; 国内环境需要基于用户账号类型来返回不同的idip area
        if (ServerInfoTool.isOverseaEnv()) {
            return config.getFormalIdipArea();
        } else {
            if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_WX_VALUE) {
                return config.getWxFormalIdipArea();
            } else if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_QQ_VALUE) {
                return config.getQqFormalIdipArea();
            } else if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_STEAM_VALUE) {
                return config.getSteamFormalIdipArea();
            } else {
                return 0;
            }
        }
    }

    /**
     * 获取idip平台测试环境idip area
     * @param worldId 环境worldId
     * @param envFlag 环境标识
     * @return idip平台测试环境idip area
     */
    public Integer getTestIdipArea(Integer worldId, String envFlag) {
        if (envFlag == null || StringUtils.isBlank(envFlag) || StringUtils.equals("dev", envFlag)) {
            return 0;
        }

        StringBuffer stringBuffer = new StringBuffer().append(worldId).append(KEY_SPLICING_CHAR).append(envFlag);
        ResServerIdipArea.ServerIdipAreaConfig config = get(stringBuffer.toString());
        if (config == null) {
            return 0;
        }

        return config.getTestIdipArea();
    }

    /**
     * 获取idip area环境
     * @param envFlag 环境标识
     * @return idip area环境
     */
    public String getIdipAreaEnv(String envFlag) {

        if (StringUtils.isBlank(envFlag) || StringUtils.equals("dev", envFlag)) {
            return "";
        }

        if (StringUtils.equals("idc_release", envFlag) || StringUtils.equals("idc_pre", envFlag) ||
                StringUtils.equals("idc_oversea_release", envFlag)) {
            return IDIP_AREA_ENV_FORMAL;
        } else {
            return IDIP_AREA_ENV_TEST;
        }
    }

    /**
     * 基于partition(即对应环境的idip正式环境的idip area)获取对应环境的路由信息
     * @param partition idip正式环境的idip area
     * @return 路由信息
     */
    public NKPair<String, String> getIdipEnvRouteInfo(int partition) {

        if (idipEnvRouteInfoMap == null || idipEnvRouteInfoMap.isEmpty()) {
            return null;
        }

        return idipEnvRouteInfoMap.getOrDefault(partition, null);
    }

    /**
     * 获取idip正式环境idip area集合
     * @return idip正式环境idip area集合
     */
    public Set<Integer> getFormalIdipAreaSet() {
        return formalIdipAreaSet;
    }

    /**
     * 获取idip服务路由信息
     *
     * @return
     */
    public ConcurrentHashMap<Integer, NKPair<String, String>> getIdipEnvRouteInfoMap() {
        return idipEnvRouteInfoMap;
    }

    public NKPair<Integer, String> getIdipAreaByEnvName(String envName) {

        for (Entry<String, ResServerIdipArea.ServerIdipAreaConfig> entry : internalDataMap.entrySet()) {
            if (entry.getValue().getEnvName().equals(envName)) {
                return new NKPair<>(entry.getValue().getWorldId(), entry.getValue().getEnvFlag());
            }
        }

        return new NKPair<>(0, "");
    }
}

