package com.tencent.wea.midas;

import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import com.tencent.cl5.PolarisUtil;
import com.tencent.coHttp.CoHttpClient;
import com.tencent.coHttp.CoHttpClientBuilder;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.resourceloader.resclass.MidasPageDooActivityConfData;
import com.tencent.resourceloader.resclass.MidasReleaseZoneConf;
import com.tencent.resourceloader.resclass.MidasSandboxZoneConf;
import com.tencent.resourceloader.resclass.UgcBuyGoodsCreatorListConfig;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.tconnd.Session;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.pay.OpensnsException;
import com.tencent.timiutil.pay.SnsSigCheck;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.timiutil.tool.ServerInfoTool;
import com.tencent.wea.other.BalanceQueryRes;
import com.tencent.wea.other.BuyGoodsReq;
import com.tencent.wea.other.BuyGoodsRes;
import com.tencent.wea.other.ClosePayReq;
import com.tencent.wea.other.ClosePayRes;
import com.tencent.wea.other.GameCoinPresentRes;
import com.tencent.wea.other.GoodsPayReq;
import com.tencent.wea.other.GoodsPayRes;
import com.tencent.wea.other.JoinModelInfo;
import com.tencent.wea.other.MidasAddServerRequest;
import com.tencent.wea.other.MidasAddServerRes;
import com.tencent.wea.other.MidasCallBackServerInfo;
import com.tencent.wea.other.MidasCreateGoodsRequest;
import com.tencent.wea.other.MidasCreateGoodsRes;
import com.tencent.wea.other.MidasCreatePartnerRequest;
import com.tencent.wea.other.MidasCreatePartnerRes;
import com.tencent.wea.other.MidasCreateZoneRequest;
import com.tencent.wea.other.MidasCreateZoneRes;
import com.tencent.wea.other.MidasEnvFlag;
import com.tencent.wea.other.MidasEnvironmentType;
import com.tencent.wea.other.MidasGoodsInfo;
import com.tencent.wea.other.MidasJoinModel;
import com.tencent.wea.other.MidasPartnerMode;
import com.tencent.wea.other.MidasPublishGoodsRequest;
import com.tencent.wea.other.MidasPublishGoodsRes;
import com.tencent.wea.other.MidasPublishZoneRequest;
import com.tencent.wea.other.MidasPublishZoneRes;
import com.tencent.wea.other.MidasQueryGoodsRequest;
import com.tencent.wea.other.MidasQueryGoodsRes;
import com.tencent.wea.other.MidasQueryServerRequest;
import com.tencent.wea.other.MidasQueryServerRes;
import com.tencent.wea.other.MidasQueryZoneRequest;
import com.tencent.wea.other.MidasQueryZoneRes;
import com.tencent.wea.other.MidasResOfferInfo;
import com.tencent.wea.other.MidasUpdateZoneRequest;
import com.tencent.wea.other.MidasUpdateZoneRes;
import com.tencent.wea.other.MidasZoneInfo;
import com.tencent.wea.other.ProductInfo;
import com.tencent.wea.other.UnifiedPresentReq;
import com.tencent.wea.other.UnifiedQueryReq;
import com.tencent.wea.protocol.CsPlayer.RefreshPlayerPayInfoNtf;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.common.DeliverGoodsMetaData;
import com.tencent.wea.protocol.common.MidasZoneAndServerData;
import com.tencent.wea.protocol.common.MidasZoneAndServerDetail;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerMapLabelScore;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerUgcBuyPartnerTable;
import com.tencent.wea.tcaplus.TcaplusDbWrapper;
import com.tencent.wea.tlog.TlogFlowMgr;
import com.tencent.wea.xlsRes.ResMidas;
import com.tencent.wea.xlsRes.ResUgcMgr;
import com.tencent.wea.xlsRes.keywords.AREA_TYPE;
import com.tencent.wea.xlsRes.keywords.PlatformID;
import com.tencent.wea.xlsRes.keywords.Programmer;
import com.tencent.wea.xlsRes.keywords.TconndApiAccount;
import com.tencent.wechatrobot.WechatLog;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * @program: letsgo_server
 * @description: 米大师接口管理
 * @author: digoldzhang
 * @create: 2023-03-06 14:27
 **/

public class MidasManager {

    public static final String OFFER_ID = PropertyFileReader.getItem("midas_offer_id", "");
    public static final String SECRET_KEY = PropertyFileReader.getItem("midas_secret_key", "");
    public static final String PROVIDE_OFFER_ID = PropertyFileReader.getItem("midas_provide_offer_id", "");

    private static final Logger LOGGER = LogManager.getLogger(MidasManager.class);
    private static final BasicResponseHandler BASIC_RESPONSE_HANDLER = new BasicResponseHandler();
    private static final String BUY_GOODS_ACTION = "buyGiftPackage";
    private static final String BALANCE_QUERY_ACTION = "acct_balance";
    private static final String PRESENT_ACTION = "acct";
    private static final String CLOSE_PAY_ACTION = "cancelOrder";

    private static final String BUY_GOODS_PATH = "/v3/r/mpay/unified_buy_goods";
    private static final String UNIFIED_QUERY_PATH = "/v3/r/mpay/unified_query";
    private static final String PAY_PATH = "/v3/r/mpay/mobile_save_goods";
    private static final String COIN_PAY_PATH = "/v3/r/mpay/in_game_coin_pay";
    private static final String UNIFIED_PRESENT_PATH = "/v3/r/mpay/unified_present";
    private static final String CREATE_GOODS_PATH = "/open_api/product/create_goods";
    private static final String PUBLISH_GOODS_PATH = "/open_api/product/publish_products";
    private static final String QUERY_GOODS_PATH = "/open_api/product/query_goods";
    private static final String CREATE_PARTNER_PATH = "/open_api/partner/create_partner";
    private static final String ADD_SERVER_PATH = "/open_api/zone/create_server";
    private static final String QUERY_SERVER_PATH = "/open_api/zone/query_server";
    private static final String CREATE_ZONE_PATH = "/open_api/zone/create_zone";
    private static final String PUBLISH_ZONE_PATH = "/open_api/zone/publish_zone";
    private static final String UPDATE_ZONE_PATH = "/open_api/zone/update_zone";
    private static final String QUERY_ZONE_PATH = "/open_api/zone/query_zone";
    private static final String CLOSE_PAY_PATH = "/v3/r/mpay/close_pay";
    private static final String BUY_GOODS_URL = PropertyFileReader.getItem("midas_buy_goods_url", "");
    private static final String PAY_URL = PropertyFileReader.getItem("midas_pay_url", "");
    private static final String COIN_PAY_URL = PropertyFileReader.getItem("midas_coin_pay_url", "");
    private static final String UNIFIED_QUERY_URL = PropertyFileReader.getItem("midas_unified_query_url", "");
    private static final String UNIFIED_PRESENT_URL = PropertyFileReader.getItem("midas_unified_present_url", "");
    private static final String RES_ID = "RES-PBQ8L8E2GGNN";
    private CoHttpClient client = null;

    public MidasManager() {
        int concurrencyLevel = 2000;
        client = CoHttpClientBuilder.
                create(4). // use 4 io threads
                        setMaxConnPerRoute(concurrencyLevel).
                setMaxConnTotal(concurrencyLevel).build();
    }

    public static MidasManager getInstance() {
        return InstanceHolder.instance;
    }

    public static String toJsonString(Message.Builder msg) throws InvalidProtocolBufferException {
        return JsonFormat.printer().preservingProtoFieldNames().print(msg);
    }

    public static int getPlatIdByClientPlatform(String clientPlatform) {
        if ("iap".equals(clientPlatform)) {
            return 0;
        }
        if ("android".equals(clientPlatform)) {
            return 1;
        }
        return 2;
    }

    public static AREA_TYPE getAreaTypeBySessionType(String sessionType) {
        if ("wechat".equals(sessionType)) {
            return AREA_TYPE.WX;
        }
        if ("qq".equals(sessionType)) {
            return AREA_TYPE.QQ;
        }
        return AREA_TYPE.UNKNOWN;
    }

    public static int getPayZoneId(TconndApiAccount accountType) {
        // qq wx
        boolean merged = ServerInfoTool.mergedQQAndWX();
        if (merged && accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_QQ) {
            return PropertyFileReader.getRealTimeIntItem("midas_qq_pay_zone_id", Framework.getInstance().getWorldId());
        } else if (merged && accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_WX) {
            return PropertyFileReader.getRealTimeIntItem("midas_wx_pay_zone_id", Framework.getInstance().getWorldId());
        } else {
            return PropertyFileReader.getRealTimeIntItem("midas_zoneId", Framework.getInstance().getWorldId());
        }
    }

    public static String getOfferId(boolean isUgc, boolean isPC) {
        if (isUgc) {
            return PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********");
        }
        if (isPC) {
            return PropertyFileReader.getRealTimeItem("pc_offer_id", "**********");
        }
        return PropertyFileReader.getRealTimeItem("midas_offer_id", OFFER_ID);
    }

    public static String getSecretKeyFromOfferId(String offerId) {
        if (offerId.equals(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))) {
            return PropertyFileReader.getRealTimeItem("midas_ugc_buy_secret_key", "");
        }
        if (offerId.equals(PropertyFileReader.getRealTimeItem("pc_offer_id", "**********"))) {
            return PropertyFileReader.getRealTimeItem("midas_pc_secret_key", "");
        }
        return SECRET_KEY;
    }

    public static String getSecretKey(boolean isUgc, boolean isPC) {
        if (isUgc) {
            return PropertyFileReader.getRealTimeItem("midas_ugc_buy_secret_key", "");
        }
        if (isPC) {
            return PropertyFileReader.getRealTimeItem("midas_pc_secret_key", "");
        }
        return SECRET_KEY;
    }

    public static String getProvideOfferId() {
        return PropertyFileReader.getRealTimeItem("midas_provide_offer_id", PROVIDE_OFFER_ID);
    }

    public static String getResId() {
        return PropertyFileReader.getRealTimeItem("midas_res_id", RES_ID);
    }

    public static ProductInfo.Builder createUgcProductInfo(ProductData productData, TconndApiAccount accountType) {

        String provideOfferId = productData.creatorOfferId;
        if (provideOfferId == null || provideOfferId.isEmpty()) {
            if (!PropertyFileReader.getRealTimeBooleanItem("midas_merchant_manager_test", false)) {
                ResUgcMgr.UgcBuyGoodsCreatorListConfig config = UgcBuyGoodsCreatorListConfig.getInstance()
                        .get(productData.creatorId);
                if (config == null) {
                    LOGGER.error("UgcBuyGoodsCreatorListConfig not exit: {}", productData.creatorId);
                    NKErrorCode.MidasPayFailed.throwError("UgcBuyGoodsCreatorListConfig not exit: {}",
                            productData.creatorId);
                }
                provideOfferId = config.getOfferId();
            } else {
                LOGGER.error("createUgcProductInfo fail, creatorOfferId is null: {}", productData.creatorId);
                NKErrorCode.MidasPayFailed.throwError("creatorOfferId is null: {}",
                        productData.creatorId);
            }
        }
        ProductInfo.Builder productInfo = ProductInfo.newBuilder();
        productInfo.setProductId(productData.productId)
                .setProvideOfferId(provideOfferId)
                .setQuantity(productData.num)
                .setZoneid(String.valueOf(getPayZoneId(accountType)))
                .setSellingPrice((int) productData.price * 10);
        return productInfo;
    }

    public static ProductInfo.Builder createProductInfo(ProductData productData, ResMidas.MidasConf conf,
            TconndApiAccount accountType, MonitorId monitorId) {
        ProductInfo.Builder productInfo = ProductInfo.newBuilder();
        productInfo.setProductId(productData.productId)
                .setProvideOfferId(getProvideOfferId())
                .setQuantity(productData.num)
                .setZoneid(String.valueOf(getPayZoneId(accountType)))
                .setSellingPrice((int) conf.getMidasPrice() * 10);
//                    .setRoleid(String.valueOf(session.getUid()));
        if (productData.activityPrice > 0) {
            productInfo.setActivitySellingPrice((int) productData.activityPrice * 10);
            // 活动价浮动不能超过百分之40
            if (Math.abs(productInfo.getSellingPrice() - productInfo.getActivitySellingPrice())
                    > (int) (productInfo.getSellingPrice() * 0.4)) {
                LOGGER.error("activity price over limit:{}, {}-{}", productInfo.getProductId(),
                        productInfo.getSellingPrice(), productInfo.getActivitySellingPrice());
                Monitor.getInstance().add.fail(monitorId, 1, new String[]{"MidasActivityPriceOverLimit"});
                NKErrorCode.MidasActivityPriceOverLimit.throwError("activity price over limit:{}, {}-{}",
                        productInfo.getProductId(), productInfo.getSellingPrice(),
                        productInfo.getActivitySellingPrice());
            }
        }
        if (PropertyFileReader.getRealTimeIntItem("midas_env", 0) == 0) {
            for (int id : conf.getActivityIdList()) {
                ResMidas.MidasPageDooActivityConf activityConf = MidasPageDooActivityConfData.getInstance().get(id);
                if (activityConf == null) {
                    LOGGER.error("activityConf not exit: {}", id);
                    Monitor.getInstance().add.fail(monitorId, 1, new String[]{"MidasPageDooActivityConfNotFound"});
                    NKErrorCode.MidasPayFailed.throwError("activityConf not exit: {}", id);
                }
                MidasJoinModel.Builder joinModel = MidasJoinModel.newBuilder();
                joinModel.addList(
                        JoinModelInfo.newBuilder()
                                .setActivityId(activityConf.getMidasActivityId())
                                .setModelId(activityConf.getActivityModelId())
                                .setModelType(activityConf.getMidasActivityType()));
                productInfo.setJoinModel(joinModel);
            }
        } else {
            for (int id : conf.getActivityIdList()) {
                ResMidas.MidasPageDooActivityConf activityConf = MidasPageDooActivityConfData.getInstance().get(id);
                if (activityConf == null) {
                    LOGGER.error("activityConf not exit: {}", id);
                    Monitor.getInstance().add.fail(monitorId, 1, new String[]{"MidasPageDooActivityConfNotFound"});
                    NKErrorCode.MidasPayFailed.throwError("activityConf not exit: {}", id);
                }
                MidasJoinModel.Builder joinModel = MidasJoinModel.newBuilder();
                joinModel.addList(
                        JoinModelInfo.newBuilder()
                                .setActivityId(activityConf.getMidasSandBoxActivityId())
                                .setModelId(activityConf.getSandBoxActivityModelId())
                                .setModelType(activityConf.getMidasActivityType()));
                productInfo.setJoinModel(joinModel);
            }
        }

        return productInfo;
    }

    private void fromJson(String jsonStr, Message.Builder builder) throws InvalidProtocolBufferException {
        JsonFormat.parser().ignoringUnknownFields().merge(jsonStr, builder);
    }

    /**
     * @Description: 余额查询
     * @Author: digoldzhang
     * @Date: 2023/3/7
     */
    public BalanceQueryRes.Builder balanceQuery(Session session) throws IOException, OpensnsException {
        UnifiedQueryReq.Builder req = UnifiedQueryReq.newBuilder();
        req.setAction(BALANCE_QUERY_ACTION)
                .setOfferId(getOfferId(false, session.getClientInfo().getIsPCLoginPlat()))
                .setOpenid(session.getOpenid())
                .setPf(session.getPayPf())
                .setPfkey("pfKey")
                .setZoneid(String.valueOf(getPayZoneId(session.getAccountType())))
                .setResOfferId(getProvideOfferId())
                .setResourceid(getResId())
                .setSessionId("itopid")
                .setSessionType("itop")
                .setOpenkey(session.getPayToken());
        if (session.getPayToken().startsWith("GAMEMATRIX")) {
//            req.setOpenid(session.getGameMatrixExtraData().getOpenId());
            req.setOpenkey(session.getGameMatrixExtraData().getOpenKey());
//            if (session.getAccountType() == TconndApiAccount.TCONND_ITOP_CHANNEL_WX) {
//                req.setSessionId("hy_gameid")
//                        .setSessionType("wc_actoken");
//            } else {
//                req.setSessionId("openid")
//                        .setSessionType("kp_accesstoken");
//            }
        }
        BalanceQueryRes.Builder res = BalanceQueryRes.newBuilder();
        String secretKey = getSecretKey(false, session.getClientInfo().getIsPCLoginPlat());
        doPostEncode(PropertyFileReader.getRealTimeItem("midas_unified_query_url", UNIFIED_QUERY_URL),
                UNIFIED_QUERY_PATH, req, res, secretKey);
        if (res.getRet() != 0) {
            NKErrorCode.MidasGetBalanceFailed
                    .throwError("midas balanceQuery fail, uid:{}, openid:{},ret:{}, err_code:{} , msg:{}",
                            session.getUid(), session.getOpenid(), res.getRet(), res.getErrCode(), res.getMsg());
        }
        return res;
    }

    /**
     * @Description: 下单
     * @Author: digoldzhang
     * @Date: 2023/3/6
     */
    public BuyGoodsRes.Builder buyGoods(Session session, int platId, List<ProductInfo> productInfoList,
            boolean isDirectBuy, String outTradeNo, DeliverGoodsMetaData metaData, boolean isValuablePriority,
            boolean isUgcBuy) throws IOException, OpensnsException {

        BuyGoodsReq.Builder req = BuyGoodsReq.newBuilder();
        req.setAction(BUY_GOODS_ACTION)
                .setOfferId(getOfferId(isUgcBuy, session.getClientInfo().getIsPCLoginPlat()))
                .setOpenid(session.getOpenid())
                .setOpenkey(session.getPayToken())
                .setSessionId("itopid")
                .setSessionType("itop")
                .setPf(session.getPayPf())
                .setPfkey("pfKey")
                .setCurrencyType("CNY")
//                .setTs(String.valueOf(DateUtils.currentTimeSec()))
//                .setRoleid(String.valueOf(session.getUid()))
                .setMetadata(metaData);
        req.addAllProductList(productInfoList);
        if (isDirectBuy && platId == PlatformID.IOS_VALUE && !session.getClientInfo().getIsPCLoginPlat()) {
            req.setPayChannel("iap").setIapQuantity(req.getProductListCount());
        }
        if (!isDirectBuy) {
            req.setResOfferId(getProvideOfferId())
                    .setZoneid(String.valueOf(getPayZoneId(session.getAccountType())))
                    .setResourceid(getResId())
                    .setInGameCoinPay(1)
                    .setOutTradeNo(outTradeNo);
            if (isValuablePriority) {
                // 有价货币优先
                req.setInGameCoinPayMethod("valuable_present");
            }
        }else if (PropertyFileReader.getRealTimeBooleanItem("midas_out_trade_num_switch", true)) {
            req.setOutTradeNo(outTradeNo);
        }
        if (session.getPayToken().startsWith("GAMEMATRIX")) {
//            req.setOpenid(session.getGameMatrixExtraData().getOpenId());
            req.setOpenkey(session.getGameMatrixExtraData().getOpenKey());
//            if (session.getAccountType() == TconndApiAccount.TCONND_ITOP_CHANNEL_WX) {
//                req.setSessionId("hy_gameid")
//                        .setSessionType("wc_actoken");
//            } else {
//                req.setSessionId("openid")
//                        .setSessionType("kp_accesstoken");
//            }
        }
        BuyGoodsRes.Builder res = BuyGoodsRes.newBuilder();
        String secretKey = getSecretKey(isUgcBuy, session.getClientInfo().getIsPCLoginPlat());
        doPostEncode(PropertyFileReader.getRealTimeItem("midas_buy_goods_url", BUY_GOODS_URL), BUY_GOODS_PATH, req,
                res, secretKey);
        if (res.getRet() == 1018) {
//            session.sendNtfMsg(MsgTypes.MSG_TYPE_REFRESHPLAYERPAYINFONTF, RefreshPlayerPayInfoNtf.newBuilder());
            NKErrorCode.MidasLoginInvalidError.throwError("midas buyGoods fail, uid:{}, openid:{},outTradeNo:{},res:{}",
                    session.getUid(), session.getOpenid(), outTradeNo, res.toString());
        } else if (res.getRet() != 0) {
            if (res.getRet() == 3017) {
                StringBuilder str = new StringBuilder();
                productInfoList.forEach(info -> {
                    str.append(info.getProductId()).append(";");
                });
                WechatLog.debugPanicWithNoticer(Programmer.digoldzhang, "midas productId:{} not config or publish! ",
                        str.toString());
            }
            NKErrorCode.MidasBuyGoodsFailed.throwError("midas buyGoods fail, uid:{}, openid:{}, outTradeNo:{},res:{}",
                    session.getUid(), session.getOpenid(), outTradeNo, res.toString());
        }
        return res.setOfferId(req.getOfferId());
    }

    /**
     * @Description: 支付，游戏币支付由后台调用支付接口，直购则由客户端调用
     * @Author: digoldzhang
     * @Date: 2023/3/7
     */
    @Deprecated
    public GoodsPayRes.Builder pay(Session session, String urlParams) throws IOException, OpensnsException {
        GoodsPayReq.Builder req = GoodsPayReq.newBuilder();
        req.setOfferId(OFFER_ID)
                .setOpenid(session.getOpenid())
                .setOpenkey(session.getPayToken())
                .setSessionId("itopid")
                .setSessionType("itop")
                .setPayChannel("game_coin")
                .setUrlParams(urlParams)
                .setProvideExtend("");
        GoodsPayRes.Builder res = GoodsPayRes.newBuilder();
        doPostEncode(PropertyFileReader.getRealTimeItem("midas_pay_url", PAY_URL), PAY_PATH, req, res, SECRET_KEY);
        if (res.getRet() == 1018) {
            NKErrorCode.MidasLoginInvalidError.throwError("midas pay fail, uid:{}, openid:{},res:{}",
                    session.getUid(), session.getOpenid(), res.toString());
            session.sendNtfMsg(MsgTypes.MSG_TYPE_REFRESHPLAYERPAYINFONTF, RefreshPlayerPayInfoNtf.newBuilder());
        } else if (res.getRet() != 0) {
            NKErrorCode.MidasPayFailed.throwError("midas pay fail, uid:{}, openid:{},res:{}",
                    session.getUid(), session.getOpenid(), res.toString());
        }
        return res;
    }

    /**
     * @Description: 支付，游戏币支付由后台调用支付接口，直购则由客户端调用
     * @Author: digoldzhang
     * @Date: 2023/3/7
     */
    public GoodsPayRes.Builder coinPay(Session session, String urlParams, boolean isUgcBuy)
            throws IOException, OpensnsException {
        GoodsPayReq.Builder req = GoodsPayReq.newBuilder();
        req.setOfferId(getOfferId(isUgcBuy, session.getClientInfo().getIsPCLoginPlat()))
                .setOpenid(session.getOpenid())
                .setOpenkey(session.getPayToken())
                .setSessionId("itopid")
                .setSessionType("itop")
                .setPayChannel("game_coin")
                .setUrlParams(urlParams);
        if (session.getPayToken().startsWith("GAMEMATRIX")) {
//            req.setOpenid(session.getGameMatrixExtraData().getOpenId());
            req.setOpenkey(session.getGameMatrixExtraData().getOpenKey());
//            if (session.getAccountType() == TconndApiAccount.TCONND_ITOP_CHANNEL_WX) {
//                req.setSessionId("hy_gameid")
//                        .setSessionType("wc_actoken");
//            } else {
//                req.setSessionId("openid")
//                        .setSessionType("kp_accesstoken");
//            }
        }
        GoodsPayRes.Builder res = GoodsPayRes.newBuilder();
        String secretKey = getSecretKey(isUgcBuy, session.getClientInfo().getIsPCLoginPlat());
        doPostEncode(PropertyFileReader.getRealTimeItem("midas_coin_pay_url", COIN_PAY_URL), COIN_PAY_PATH, req, res,
                secretKey);
        if (res.getRet() == 1018) {
            NKErrorCode.MidasLoginInvalidError.throwError("midas pay fail, uid:{}, openid:{},res:{}",
                    session.getUid(), session.getOpenid(), res.toString());
        } else if (res.getRet() != 0) {
            LOGGER.error("retry coin pay {} url:{}", session.getOpenid(), urlParams);
            res = GoodsPayRes.newBuilder();
            doPostEncode(PropertyFileReader.getRealTimeItem("midas_coin_pay_url", COIN_PAY_URL), COIN_PAY_PATH, req,
                    res, secretKey);
        }
        return res;
    }

    public static String getPresentBillNo(long uid, int tradeNo) {
        return Long.toHexString(uid) + "_" + Integer.toHexString(DateUtils.currUnixSec())
                + "_" + Integer.toHexString(tradeNo);
    }

    /**
     * @Description: 赠送
     * @Author: digoldzhang
     * @Date: 2023/3/7
     */
    public GameCoinPresentRes.Builder present(Session session, int amount, String billNo)
            throws IOException, OpensnsException {
        UnifiedPresentReq.Builder req = UnifiedPresentReq.newBuilder();
        req.setAction(PRESENT_ACTION)
                .setOfferId(getOfferId(false, session.getClientInfo().getIsPCLoginPlat()))
                .setOpenid(session.getOpenid())
                .setOpenkey(session.getPayToken())
                .setSessionId("itopid")
                .setSessionType("itop")
                .setPf(session.getPayPf()).setPfkey("pfKey")
                .setBillno(billNo)
                .setAmount(amount)
                .setResOfferId(getProvideOfferId())
                .setResourceid(getResId())
                .setZoneid(String.valueOf(getPayZoneId(session.getAccountType())));
        if (session.getPayToken().startsWith("GAMEMATRIX")) {
//            req.setOpenid(session.getGameMatrixExtraData().getOpenId());
            req.setOpenkey(session.getGameMatrixExtraData().getOpenKey());
//            if (session.getAccountType() == TconndApiAccount.TCONND_ITOP_CHANNEL_WX) {
//                req.setSessionId("hy_gameid")
//                        .setSessionType("wc_actoken");
//            } else {
//                req.setSessionId("openid")
//                        .setSessionType("kp_accesstoken");
//            }
        }
        GameCoinPresentRes.Builder res = GameCoinPresentRes.newBuilder();
        String secretKey = getSecretKey(false, session.getClientInfo().getIsPCLoginPlat());
        boolean needRetry = false;
        try {
            doPostEncode(PropertyFileReader.getRealTimeItem("midas_unified_present_url", UNIFIED_PRESENT_URL),
                    UNIFIED_PRESENT_PATH, req, res, secretKey);
            if (res.getRet() != 0 && res.getRet() != 1018) {
                LOGGER.error("Present failed need retry  {},{},ret:{},msg:{}", session.getOpenid(), billNo,
                        res.getRet(), res.getMsg());
                needRetry = true;
            }
        } catch (Exception e) {
            LOGGER.error("Present failed need retry {},{},e{}", session.getOpenid(), billNo, e.getMessage());
            needRetry = true;
        }
        if (needRetry) {
            LOGGER.error("retry present, reason:{},{},{}", session.getOpenid(), amount, billNo);
            res = GameCoinPresentRes.newBuilder();
            doPostEncode(PropertyFileReader.getRealTimeItem("midas_unified_present_url", UNIFIED_PRESENT_URL),
                    UNIFIED_PRESENT_PATH, req, res, secretKey);
        }
        return res;
    }

    /**
     * @Description: 关闭订单
     * @Author: digoldzhang
     */
    public ClosePayRes.Builder closePay(String openId, String outTradeNo, String offerId)
            throws IOException, OpensnsException {
        ClosePayReq.Builder req = ClosePayReq.newBuilder();
        req.setAction(CLOSE_PAY_ACTION)
                .setOfferId(offerId)
                .setOutTradeNo(outTradeNo)
                .setTs(String.valueOf(Framework.currentTimeSec()));
        ClosePayRes.Builder res = ClosePayRes.newBuilder();
        String secretKey = getSecretKeyFromOfferId(offerId);
        doPostEncode(PropertyFileReader.getRealTimeItem("midas_close_pay_url", "64264129:1769472"),
                CLOSE_PAY_PATH, req, res, secretKey);
        if (res.getRet() != 0) {
            LOGGER.error("closePay failed, {},outTradeNo:{},res:{}", openId, outTradeNo, res);
        }
        return res;
    }


    public MidasGoodsInfo.Builder genMidasGoodsInfo(String productId, long diamondPrice, String name,
            String creatorOfferId) {
        MidasGoodsInfo.Builder res = MidasGoodsInfo.newBuilder();
        res.setSubResOfferId(creatorOfferId)
                .setCouponPrice(diamondPrice)
                .setDefaultPrice(diamondPrice * 10)
                .setIapPrice(diamondPrice * 10)
                .setIapProductType("consumable")
                .setIapProductId("letsgo." + productId)
                .setProductId(productId)
                .setName(name)
                .setIsQqUin(1)
                .setIsNewCommid(4)
                .setResourceType("1");
        LOGGER.debug("genMidasGoodsInfo,{}", res.toString());
        return res;
    }

    public void createGoods(List<MidasGoodsInfo.Builder> productInfos, String creatorOfferId, long creatorId)
            throws OpensnsException, IOException {
        LOGGER.debug("createGoods, {},{}", creatorOfferId, productInfos.size());
        if (!PropertyFileReader.getRealTimeBooleanItem("midas_create_goods_switch", true)) {
            NKErrorCode.MidasCreateGoodsFailed
                    .throwError("midas create not open, creatorOfferId:{}", creatorOfferId);
        }
        if (productInfos.isEmpty()) {
            LOGGER.warn("productInfos is empty, creatorOfferId:{}, creatorId:{}", creatorOfferId, creatorId);
        }
        String requestId = "Create" + creatorOfferId + Framework.currentTimeMillis();
        MidasCreateGoodsRequest.Builder req = MidasCreateGoodsRequest.newBuilder();
        if (PropertyFileReader.getRealTimeBooleanItem("midas_merchant_manager_test", false)) {
            // 开发调试阶段临时参数
            req.setOfferId("1450030590")
                    .setResOfferId("800000031")
                    .setRequestId(requestId)
                    .setResourceType("1");
        } else {
            req.setOfferId(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))
//                    .setResOfferId(PROVIDE_OFFER_ID)
                    .setRequestId(requestId)
                    .setResourceType("1")
                    .setSubResOfferId(creatorOfferId);
        }
        JSONArray jsonArray = new JSONArray();
        for (MidasGoodsInfo.Builder info : productInfos) {
            jsonArray.put(new JSONObject(toJsonString(info)));
        }
        req.setGoodsList(jsonArray.toString());
        MidasCreateGoodsRes.Builder res = MidasCreateGoodsRes.newBuilder();
        String secretKey = PropertyFileReader.getRealTimeItem("midas_merchant_manager_secret_key", "");
        doPost(PropertyFileReader.getRealTimeItem("midas_merchant_manager_url", "192002625:329448"),
                CREATE_GOODS_PATH, req, res, secretKey);
        if (!"SUCCESS".equals(res.getCode())) {
            LOGGER.error("midas create fail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
            NKErrorCode.MidasCreateGoodsFailed
                    .throwError("midas create fail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
        } else {
            // 流水
            StringBuilder ss = new StringBuilder();
            productInfos.forEach(info -> {
                ss.append(info.getProductId()).append(";");
            });
            TlogFlowMgr.sendUgcBuyGoodsPublishFlow(creatorId, 0, ss.toString(), MidasEnvFlag.sandbox, creatorOfferId);
        }
    }

    public void publishGoods(List<String> productIdList, String creatorOfferId, long creatorId)
            throws OpensnsException, IOException {
        LOGGER.debug("publishGoods, {},{}", creatorOfferId, productIdList.size());
        if (!PropertyFileReader.getRealTimeBooleanItem("midas_publish_goods_switch", true)) {
            NKErrorCode.MidasPublishGoodsFailed
                    .throwError("midas publish not open, creatorOfferId:{}", creatorOfferId);
        }
        if (productIdList.isEmpty()) {
            LOGGER.warn("productInfos is empty, creatorOfferId:{}, creatorId:{}", creatorOfferId, creatorId);
        }
        String requestId = "Publish" + creatorOfferId + Framework.currentTimeMillis();
        MidasPublishGoodsRequest.Builder req = MidasPublishGoodsRequest.newBuilder();
        if (PropertyFileReader.getRealTimeBooleanItem("midas_merchant_manager_test", false)) {
            // 开发调试阶段临时参数
            req.setOfferId("1450030590")
                    .setResOfferId("800000031")
                    .setRequestId(requestId);
        } else {
            req.setOfferId(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))
//                    .setResOfferId(PROVIDE_OFFER_ID)
                    .setRequestId(requestId)
                    .setSubResOfferId(creatorOfferId);
        }

        JSONArray jsonArray = new JSONArray(productIdList);
        req.setProductIdList(jsonArray.toString());
        if (PropertyFileReader.getRealTimeIntItem("midas_env", 0) == 0) {
            req.setEnvFlag(MidasEnvFlag.actual);
        } else {
            req.setEnvFlag(MidasEnvFlag.sandbox);
        }
        MidasPublishGoodsRes.Builder res = MidasPublishGoodsRes.newBuilder();
        String secretKey = PropertyFileReader.getRealTimeItem("midas_merchant_manager_secret_key", "");
        doPost(PropertyFileReader.getRealTimeItem("midas_merchant_manager_url", "192002625:329448"),
                PUBLISH_GOODS_PATH, req, res, secretKey);
        if (!"SUCCESS".equals(res.getCode())) {
            LOGGER.error("midas publish fail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
            NKErrorCode.MidasPublishGoodsFailed
                    .throwError("midas publish fail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
        } else {
            StringBuilder ss = new StringBuilder();
            productIdList.forEach(id -> {
                ss.append(id).append(";");
            });
            TlogFlowMgr.sendUgcBuyGoodsPublishFlow(creatorId, 1, ss.toString(), req.getEnvFlag(), creatorOfferId);
        }
    }


    public MidasQueryGoodsRes.Builder queryAllGoods(String creatorOfferId, MidasEnvironmentType envType,
            long current, long pageSize) throws OpensnsException, IOException {
        return queryGoods(null, creatorOfferId, envType, current, pageSize);
    }

    public MidasQueryGoodsRes.Builder queryAllGoods(String creatorOfferId, long current, long pageSize)
            throws OpensnsException, IOException {
        MidasEnvironmentType envType = MidasEnvironmentType.sandboxType;
        if (PropertyFileReader.getRealTimeIntItem("midas_env", 0) == 0) {
            envType = MidasEnvironmentType.actualType;
        }
        return queryGoods(null, creatorOfferId, envType, current, pageSize);
    }

    public MidasQueryGoodsRes.Builder queryGoods(List<String> productIdList, String creatorOfferId,
            MidasEnvironmentType envType) throws OpensnsException, IOException {
        return queryGoods(productIdList, creatorOfferId, envType, 0, 0);
    }

    public MidasQueryGoodsRes.Builder queryGoodsIsPublish(List<String> productIdList, String creatorOfferId)
            throws OpensnsException, IOException {
        MidasEnvironmentType envType = MidasEnvironmentType.sandboxType;
        if (PropertyFileReader.getRealTimeIntItem("midas_env", 0) == 0) {
            envType = MidasEnvironmentType.actualType;
        }
        return queryGoods(productIdList, creatorOfferId, envType, 0, 0);
    }

    public MidasQueryGoodsRes.Builder queryGoods(List<String> productIdList, String creatorOfferId,
            MidasEnvironmentType envType, long current, long pageSize) throws OpensnsException, IOException {
        LOGGER.debug("queryGoods, {}", creatorOfferId);
        if (!PropertyFileReader.getRealTimeBooleanItem("midas_query_goods_switch", true)) {
            NKErrorCode.MidasQueryGoodsFailed
                    .throwError("midas query not open, creatorOfferId:{}", creatorOfferId);
        }
        String requestId = "Query" + creatorOfferId + Framework.currentTimeMillis();
        MidasQueryGoodsRequest.Builder req = MidasQueryGoodsRequest.newBuilder();
        if (PropertyFileReader.getRealTimeBooleanItem("midas_merchant_manager_test", false)) {
            // 开发调试阶段临时参数
            req.setOfferId("1450030590")
                    .setResOfferId("800000031")
                    .setEnvType(envType)
                    .setRequestId(requestId)
                    .setResourceType("1");
        } else {
            req.setOfferId(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))
//                    .setResOfferId(PROVIDE_OFFER_ID)
                    .setEnvType(envType)
                    .setRequestId(requestId)
                    .setResourceType("1")
                    .setSubResOfferId(creatorOfferId);
        }
        if (productIdList != null && !productIdList.isEmpty()) {
            JSONArray jsonArray = new JSONArray(productIdList);
            req.setProductIdList(jsonArray.toString());
        }
        if (current > 0) {
            req.setCurrent(current);
        }
        if (pageSize > 0) {
            req.setPageSize(pageSize);
        }
        MidasQueryGoodsRes.Builder res = MidasQueryGoodsRes.newBuilder();
        String secretKey = PropertyFileReader.getRealTimeItem("midas_merchant_manager_secret_key", "");
        doPost(PropertyFileReader.getRealTimeItem("midas_merchant_manager_url", "192002625:329448"),
                QUERY_GOODS_PATH, req, res, secretKey);
        if (!"SUCCESS".equals(res.getCode())) {
            LOGGER.error("midas query fail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
        }
        return res;
    }

    /**
     * 获取Ugc内购创作者商户信息
     *
     * @param isApply true:如果未入驻，则申请入驻
     */
    public PlayerUgcBuyPartnerTable getUgcBuyPartner(long creatorId, boolean isApply)
            throws OpensnsException, IOException, NKCheckedException {
        PlayerUgcBuyPartnerTable.Builder dbReq = PlayerUgcBuyPartnerTable.newBuilder().setCreatorId(creatorId);
        TcaplusManager.TcaplusRsp dbRsp = TcaplusUtil.newGetReq(dbReq).send();
        if (!dbRsp.isOKIgnoreRecordNotExist()) {
            LOGGER.error("db get PlayerUgcBuyPartnerTable fail, creatorId:{},{}",
                    creatorId, dbRsp.getResult());
            NKErrorCode.DBOpFailed.throwError("db get PlayerUgcBuyPartnerTable fail, creatorId:{},{}",
                    creatorId, dbRsp.getResult());
        }
        if (dbRsp.hasFirstRecord()) {
            return (TcaplusDb.PlayerUgcBuyPartnerTable) dbRsp.firstRecordData().msg;
        }
        if (isApply) {
            MidasEnvFlag envFlag = PropertyFileReader.getRealTimeIntItem("midas_env", 0) == 0 ? MidasEnvFlag.actual
                    : MidasEnvFlag.sandbox;
            return applyUgcBuyPartner(creatorId, envFlag).build();
        } else {
            return null;
        }
    }

    public PlayerUgcBuyPartnerTable.Builder applyUgcBuyPartner(long creatorId, MidasEnvFlag envFlag)
            throws OpensnsException, IOException {
        Monitor.getInstance().add.total(MonitorId.attr_midas_apply_ugc_partner_num, 1);
        LOGGER.info("applyUgcBuyPartner, creatorId:{}, envFlag:{}", creatorId, envFlag);
        if (creatorId == 0) {
            LOGGER.error("creatorId is 0,creatorId:{}", creatorId);
            Monitor.getInstance().add.fail(MonitorId.attr_midas_apply_ugc_partner_num, 1,
                    new String[]{"CreatorIdError"});
            NKErrorCode.MidasApplyPartnerFailed.throwError("creatorId is 0,creatorId:{}", creatorId);
        }
        // 尝试创建商户
        MidasResOfferInfo partnerInfo = null;
        try {
            MidasCreatePartnerRes.Builder createPartnerRes = createPartner(creatorId);
            partnerInfo = createPartnerRes.getPartnerResOfferInfo();
            TlogFlowMgr.sendUgcPlayerCreatePartnerFlow(creatorId, envFlag, partnerInfo.getResOfferId(),
                    partnerInfo.getResOfferName(), partnerInfo.getResOfferLogo());
        } catch (Exception e) {
            LOGGER.error("createPartner fail,creatorId:{}", creatorId);
            Monitor.getInstance().add.fail(MonitorId.attr_midas_apply_ugc_partner_num, 1,
                    new String[]{"CreatePartnerFail"});
            NKErrorCode.MidasApplyPartnerFailed.throwError("createPartner fail,creatorId:{}", creatorId);
        }
        // 尝试创建分区和回调
        int qqZoneId = getPayZoneId(TconndApiAccount.TCONND_ITOP_CHANNEL_QQ);
        int wxZoneId = getPayZoneId(TconndApiAccount.TCONND_ITOP_CHANNEL_WX);
        MidasZoneAndServerData.Builder zoneData = MidasZoneAndServerData.newBuilder();
        // 创建qq分区
        try {
            MidasZoneAndServerDetail.Builder qqZoneDetail = createAndPublishZone(creatorId, envFlag, partnerInfo,
                    qqZoneId);
            zoneData.putDetail(qqZoneDetail.getZoneId(), qqZoneDetail.build());
        } catch (Exception e) {
            LOGGER.error("createAndPublishZone fail,creatorId:{}, zoneId:{}", creatorId, qqZoneId);
            Monitor.getInstance().add.fail(MonitorId.attr_midas_apply_ugc_partner_num, 1,
                    new String[]{"CreateAndPublishZoneFail"});
            NKErrorCode.MidasApplyPartnerFailed.throwError("createAndPublishZone fail,creatorId:{}, zoneId:{}",
                    creatorId, qqZoneId);
        }

        // 创建wx分区
        if (qqZoneId != wxZoneId) {
            try {
                MidasZoneAndServerDetail.Builder wxZoneDetail = createAndPublishZone(creatorId, envFlag, partnerInfo,
                        wxZoneId);
                zoneData.putDetail(wxZoneDetail.getZoneId(), wxZoneDetail.build());
            } catch (Exception e) {
                LOGGER.error("createAndPublishZone fail,creatorId:{}, zoneId:{}", creatorId, wxZoneId);
                Monitor.getInstance().add.fail(MonitorId.attr_midas_apply_ugc_partner_num, 1,
                        new String[]{"CreateAndPublishZoneFail"});
                NKErrorCode.MidasApplyPartnerFailed.throwError("createAndPublishZone fail,creatorId:{}, zoneId:{}",
                        creatorId, wxZoneId);
            }
        }
        // 更新db信息
        PlayerUgcBuyPartnerTable.Builder dbReq = PlayerUgcBuyPartnerTable.newBuilder();
        dbReq.setCreatorId(creatorId)
                .setResOfferId(partnerInfo.getResOfferId())
                .setResOfferName(partnerInfo.getResOfferName())
                .setResOfferLogo(partnerInfo.getResOfferLogo())
                .setSandboxTdeaSecretId(partnerInfo.getSandboxTdeaSecretId())
                .setSandboxTdeaSecretKey(partnerInfo.getSandboxTdeaSecretKey())
                .setTdeaSecretId(partnerInfo.getTdeaSecretId())
                .setTdeaSecretKey(partnerInfo.getTdeaSecretKey())
                .setSandboxSecretKey(partnerInfo.getSandboxSecretKey())
                .setSecretKey(partnerInfo.getSecretKey())
                .setZoneAndSvr(zoneData);
        TcaplusManager.TcaplusRsp dbRsp = TcaplusUtil.newReplaceReq(dbReq).send();
        if (!dbRsp.isOK()) {
            LOGGER.error("db insert PlayerUgcBuyPartnerTable fail, creatorId:{},{}",
                    creatorId, dbRsp.getResult());
            Monitor.getInstance().add.fail(MonitorId.attr_midas_apply_ugc_partner_num, 1,
                    new String[]{"UpdateDbFail"});
            NKErrorCode.MidasApplyPartnerFailed.throwError("db insert PlayerUgcBuyPartnerTable fail, creatorId:{},{}",
                    creatorId, dbRsp.getResult());
        }
        return dbReq;
    }

    private MidasZoneAndServerDetail.Builder createAndPublishZone(long creatorId, MidasEnvFlag envFlag,
            MidasResOfferInfo partnerInfo, int zoneId) throws OpensnsException, IOException {
        MidasZoneAndServerDetail.Builder res = MidasZoneAndServerDetail.newBuilder();
        ResMidas.MidasZoneConf zoneConf;
        if (envFlag == MidasEnvFlag.sandbox) {
            zoneConf = MidasSandboxZoneConf.getInstance().get(zoneId);
        } else {
            zoneConf = MidasReleaseZoneConf.getInstance().get(zoneId);
        }
        if (zoneConf == null) {
            LOGGER.error("MidasZoneConf not exist, zoneId:{}", zoneId);
            NKErrorCode.MidasApplyPartnerFailed.throwError("MidasZoneConf not exist, zoneId:{}", zoneId);
        }
        // 回调创建了没
        String svrId = "1";
        boolean needCreateSvr = true;
        MidasQueryServerRes.Builder queryServerRes = MidasManager.getInstance()
                .queryServer(creatorId, partnerInfo.getResOfferId(), envFlag);
        for (MidasCallBackServerInfo cbInfo : queryServerRes.getServerListList()) {
            if (cbInfo.getServerName().equals(zoneConf.getServerName()) && cbInfo.getUrl()
                    .equals(zoneConf.getServerUrl())) {
                svrId = cbInfo.getServerId();
                needCreateSvr = false;
                break;
            }
        }
        // 创建回调地址
        if (needCreateSvr) {
            MidasCallBackServerInfo.Builder serverInfo = MidasCallBackServerInfo.newBuilder();
            serverInfo.setServerName(zoneConf.getServerName()).setUrl(zoneConf.getServerUrl());
            MidasManager.getInstance().addServer(creatorId, partnerInfo.getResOfferId(), serverInfo, envFlag);
            // 创建后需要重新查询
            queryServerRes = MidasManager.getInstance().queryServer(creatorId, partnerInfo.getResOfferId(), envFlag);
            for (MidasCallBackServerInfo cbInfo : queryServerRes.getServerListList()) {
                if (cbInfo.getServerName().equals(zoneConf.getServerName()) && cbInfo.getUrl()
                        .equals(zoneConf.getServerUrl())) {
                    svrId = cbInfo.getServerId();
                    break;
                }
            }
            TlogFlowMgr.sendUgcPlayerAddServerFlow(creatorId, envFlag, partnerInfo.getResOfferId(),
                    zoneConf.getServerName(), zoneConf.getServerUrl(), svrId);
        }

        // 创建发布android分区
        boolean needCreateZone = true;
        MidasQueryZoneRes.Builder queryZoneRes = MidasManager.getInstance()
                .queryZone(creatorId, partnerInfo.getResOfferId(), envFlag, "android", 0, 100);
        for (MidasZoneInfo zoneInfo : queryZoneRes.getZoneListList()) {
            if (zoneInfo.getZoneId().equals(String.valueOf(zoneConf.getZoneId()))) {
                needCreateZone = false;
                break;
            }
        }
        MidasZoneInfo.Builder androidZoneInfo = MidasManager.getInstance()
                .genZoneInfo(partnerInfo.getResOfferId(), String.valueOf(zoneConf.getZoneId()), zoneConf.getZoneName(),
                        "android", envFlag, svrId);
        if (needCreateZone) {
            MidasManager.getInstance().createZone(creatorId, partnerInfo.getResOfferId(), androidZoneInfo);
        } else {
            MidasManager.getInstance().updateZone(creatorId, partnerInfo.getResOfferId(), androidZoneInfo);
        }
        MidasManager.getInstance().publishZone(creatorId, partnerInfo.getResOfferId(), envFlag, "android");
        TlogFlowMgr.sendUgcPlayerAddZoneFlow(creatorId, envFlag, partnerInfo.getResOfferId(),
                androidZoneInfo.getZoneId(), androidZoneInfo.getZoneName(), "android", svrId);
        // 创建发布iap分区
        needCreateZone = true;
        queryZoneRes = MidasManager.getInstance()
                .queryZone(creatorId, partnerInfo.getResOfferId(), envFlag, "iap", 0, 100);
        for (MidasZoneInfo zoneInfo : queryZoneRes.getZoneListList()) {
            if (zoneInfo.getZoneId().equals(String.valueOf(zoneConf.getZoneId()))) {
                needCreateZone = false;
                break;
            }
        }
        MidasZoneInfo.Builder iapZoneInfo = MidasManager.getInstance()
                .genZoneInfo(partnerInfo.getResOfferId(), String.valueOf(zoneConf.getZoneId()), zoneConf.getZoneName(),
                        "iap", envFlag, svrId);
        if (needCreateZone) {
            MidasManager.getInstance().createZone(creatorId, partnerInfo.getResOfferId(), iapZoneInfo);
        } else {
            MidasManager.getInstance().updateZone(creatorId, partnerInfo.getResOfferId(), iapZoneInfo);
        }
        MidasManager.getInstance().publishZone(creatorId, partnerInfo.getResOfferId(), envFlag, "iap");
        TlogFlowMgr.sendUgcPlayerAddZoneFlow(creatorId, envFlag, partnerInfo.getResOfferId(),
                iapZoneInfo.getZoneId(), iapZoneInfo.getZoneName(), "iap", svrId);
        res.setZoneId(String.valueOf(zoneConf.getZoneId()))
                .setZoneName(zoneConf.getZoneName())
                .setEnvFlag(envFlag.getNumber())
                .setStatus(1)
                .setServerId(svrId)
                .setServerName(zoneConf.getServerName())
                .setUrl(zoneConf.getServerUrl());
        return res;
    }

    public MidasCreatePartnerRes.Builder createPartner(long creatorId) throws OpensnsException, IOException {
        LOGGER.debug("createPartner,{}", creatorId);
        if (creatorId == 0) {
            LOGGER.warn("creatorId error, creatorId:{}", creatorId);
        }
        String requestId = "CreatePartner" + creatorId + Framework.currentTimeMillis();
        String outRegistrationId = String.valueOf(creatorId);
        MidasCreatePartnerRequest.Builder req = MidasCreatePartnerRequest.newBuilder();
        req.setOfferId(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))
                .setResOfferId(getProvideOfferId())
                .setPartnerResOfferName(String.valueOf(creatorId))
                .setPartnerMode(MidasPartnerMode.TrafficPlatform)
                .setOutRegistrationId(outRegistrationId)
                .setRequestId(requestId);
        MidasCreatePartnerRes.Builder res = MidasCreatePartnerRes.newBuilder();
        String secretKey = PropertyFileReader.getRealTimeItem("midas_merchant_manager_secret_key", "");
        doPost(PropertyFileReader.getRealTimeItem("midas_merchant_manager_url", "192002625:329448"),
                CREATE_PARTNER_PATH, req, res, secretKey);
        if (!"SUCCESS".equals(res.getCode())) {
            LOGGER.error("midas createPartner fail, creatorId:{},res:{}", creatorId, res.toString());
            NKErrorCode.MidasApiReqFailed.throwError("midas createPartner fail, creatorOfferId:{},res:{}", creatorId,
                    res.toString());
        }
        return res;
    }

    public MidasAddServerRes.Builder addServer(long creatorId, String creatorOfferId,
            MidasCallBackServerInfo.Builder serverInfo, MidasEnvFlag envFlag) throws OpensnsException, IOException {
        LOGGER.debug("addServer,{}", creatorId);
        if (creatorId == 0) {
            LOGGER.warn("creatorId error, creatorId:{}", creatorId);
        }
        String requestId = "AddServer" + creatorId + Framework.currentTimeMillis();
        MidasAddServerRequest.Builder req = MidasAddServerRequest.newBuilder();
        req.setOfferId(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))
//                    .setResOfferId(PROVIDE_OFFER_ID)
                .setRequestId(requestId)
                .setSubResOfferId(creatorOfferId)
                .setServerInfo(new JSONObject(toJsonString(serverInfo)).toString())
                .setEnvFlag(envFlag);
        MidasAddServerRes.Builder res = MidasAddServerRes.newBuilder();
        String secretKey = PropertyFileReader.getRealTimeItem("midas_merchant_manager_secret_key", "");
        doPost(PropertyFileReader.getRealTimeItem("midas_merchant_manager_url", "192002625:329448"),
                ADD_SERVER_PATH, req, res, secretKey);
        if (!"SUCCESS".equals(res.getCode())) {
            LOGGER.error("midas add server fail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
            NKErrorCode.MidasApiReqFailed.throwError("midas add server fail, creatorOfferId:{},res:{}", creatorOfferId,
                    res.toString());
        }
        return res;
    }

    public MidasQueryServerRes.Builder queryServer(long creatorId, String creatorOfferId, MidasEnvFlag envFlag)
            throws OpensnsException, IOException {
        LOGGER.debug("queryServer,{}", creatorId);
        if (creatorId == 0) {
            LOGGER.warn("creatorId error, creatorId:{}", creatorId);
        }
        String requestId = "QueryServer" + creatorId + Framework.currentTimeMillis();
        MidasQueryServerRequest.Builder req = MidasQueryServerRequest.newBuilder();
        req.setOfferId(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))
//                    .setResOfferId(PROVIDE_OFFER_ID)
                .setRequestId(requestId)
                .setSubResOfferId(creatorOfferId)
                .setEnvFlag(envFlag);
        MidasQueryServerRes.Builder res = MidasQueryServerRes.newBuilder();
        String secretKey = PropertyFileReader.getRealTimeItem("midas_merchant_manager_secret_key", "");
        doPost(PropertyFileReader.getRealTimeItem("midas_merchant_manager_url", "192002625:329448"),
                QUERY_SERVER_PATH, req, res, secretKey);
        if (!"SUCCESS".equals(res.getCode())) {
            LOGGER.error("midas queryServer fail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
            NKErrorCode.MidasApiReqFailed.throwError("midas queryServer fail, creatorOfferId:{},res:{}", creatorOfferId,
                    res.toString());
        }
        return res;
    }

    public MidasUpdateZoneRes.Builder updateZone(long creatorId, String creatorOfferId, MidasZoneInfo.Builder zoneInfo)
            throws OpensnsException, IOException {
        LOGGER.debug("addServer,{}", creatorId);
        if (creatorId == 0) {
            LOGGER.warn("creatorId error, creatorId:{}", creatorId);
        }
        String requestId = "UpdateZone" + creatorId + Framework.currentTimeMillis();
        MidasUpdateZoneRequest.Builder req = MidasUpdateZoneRequest.newBuilder();
        req.setOfferId(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))
//                    .setResOfferId(PROVIDE_OFFER_ID)
                .setRequestId(requestId)
                .setSubResOfferId(creatorOfferId)
                .setZoneInfo(new JSONObject(toJsonString(zoneInfo)).toString());
        MidasUpdateZoneRes.Builder res = MidasUpdateZoneRes.newBuilder();
        String secretKey = PropertyFileReader.getRealTimeItem("midas_merchant_manager_secret_key", "");
        doPost(PropertyFileReader.getRealTimeItem("midas_merchant_manager_url", "192002625:329448"),
                UPDATE_ZONE_PATH, req, res, secretKey);
        if (!"SUCCESS".equals(res.getCode())) {
            LOGGER.error("midas updateZone fail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
            NKErrorCode.MidasApiReqFailed.throwError("midas updateZone fail, creatorOfferId:{},res:{}", creatorOfferId,
                    res.toString());
        }
        return res;
    }

    public MidasZoneInfo.Builder genZoneInfo(String creatorOfferId, String zoneId, String zoneName,
            String clientVersion, MidasEnvFlag envFlag, String serverId) {
        MidasZoneInfo.Builder zoneInfo = MidasZoneInfo.newBuilder();
        zoneInfo.setSubResOfferId(creatorOfferId)
                .setZoneId(zoneId)
                .setZoneName(zoneName)
                .setClientVersion(clientVersion)
                .setEnvFlag(envFlag)
                .setServerIdList(serverId);
        return zoneInfo;
    }

    public MidasCreateZoneRes.Builder createZone(long creatorId, String creatorOfferId, MidasZoneInfo.Builder zoneInfo)
            throws OpensnsException, IOException {
        LOGGER.debug("createZone,{}", creatorId);
        if (creatorId == 0) {
            LOGGER.warn("creatorId error, creatorId:{}", creatorId);
        }
        String requestId = "CreateZone" + creatorId + Framework.currentTimeMillis();
        MidasCreateZoneRequest.Builder req = MidasCreateZoneRequest.newBuilder();
        req.setOfferId(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))
//                    .setResOfferId(PROVIDE_OFFER_ID)
                .setRequestId(requestId)
                .setSubResOfferId(creatorOfferId)
                .setZoneInfo(new JSONObject(toJsonString(zoneInfo)).toString());
        MidasCreateZoneRes.Builder res = MidasCreateZoneRes.newBuilder();
        String secretKey = PropertyFileReader.getRealTimeItem("midas_merchant_manager_secret_key", "");
        doPost(PropertyFileReader.getRealTimeItem("midas_merchant_manager_url", "192002625:329448"),
                CREATE_ZONE_PATH, req, res, secretKey);
        if (!"SUCCESS".equals(res.getCode())) {
            LOGGER.error("midas createZonefail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
            NKErrorCode.MidasApiReqFailed.throwError("midas createZone fail, creatorOfferId:{},res:{}", creatorOfferId,
                    res.toString());
        }
        return res;
    }

    public MidasPublishZoneRes.Builder publishZone(long creatorId, String creatorOfferId, MidasEnvFlag envFlag,
            String clientVersion)
            throws OpensnsException, IOException {
        LOGGER.debug("publishZone,{}", creatorId);
        if (creatorId == 0) {
            LOGGER.warn("creatorId error, creatorId:{}", creatorId);
        }
        String requestId = "publishZone" + creatorId + Framework.currentTimeMillis();
        MidasPublishZoneRequest.Builder req = MidasPublishZoneRequest.newBuilder();
        req.setOfferId(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))
//                    .setResOfferId(PROVIDE_OFFER_ID)
                .setRequestId(requestId)
                .setSubResOfferId(creatorOfferId)
                .setEnvFlag(envFlag)
                .setClientVersion(clientVersion);
        MidasPublishZoneRes.Builder res = MidasPublishZoneRes.newBuilder();
        String secretKey = PropertyFileReader.getRealTimeItem("midas_merchant_manager_secret_key", "");
        doPost(PropertyFileReader.getRealTimeItem("midas_merchant_manager_url", "192002625:329448"),
                PUBLISH_ZONE_PATH, req, res, secretKey);
        if (!"SUCCESS".equals(res.getCode())) {
            LOGGER.error("midas publishZone fail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
            NKErrorCode.MidasApiReqFailed.throwError("midas publishZone fail, creatorOfferId:{},res:{}", creatorOfferId,
                    res.toString());
        }
        return res;
    }

    public MidasQueryZoneRes.Builder queryZone(long creatorId, String creatorOfferId, MidasEnvFlag envFlag,
            String clientVersion, long current, long pageSize)
            throws OpensnsException, IOException {
        LOGGER.debug("publishZone,{}", creatorId);
        if (creatorId == 0) {
            LOGGER.warn("creatorId error, creatorId:{}", creatorId);
        }
        String requestId = "publishZone" + creatorId + Framework.currentTimeMillis();
        MidasQueryZoneRequest.Builder req = MidasQueryZoneRequest.newBuilder();
        req.setOfferId(PropertyFileReader.getRealTimeItem("midas_ugc_buy_offer_id", "**********"))
//                    .setResOfferId(PROVIDE_OFFER_ID)
                .setRequestId(requestId)
                .setSubResOfferId(creatorOfferId)
                .setEnvFlag(envFlag)
                .setClientVersion(clientVersion)
                .setCurrent(current)
                .setPageSize(pageSize);
        MidasQueryZoneRes.Builder res = MidasQueryZoneRes.newBuilder();
        String secretKey = PropertyFileReader.getRealTimeItem("midas_merchant_manager_secret_key", "");
        doPost(PropertyFileReader.getRealTimeItem("midas_merchant_manager_url", "192002625:329448"),
                QUERY_ZONE_PATH, req, res, secretKey);
        if (!"SUCCESS".equals(res.getCode())) {
            LOGGER.error("midas queryZone fail, creatorOfferId:{},res:{}", creatorOfferId, res.toString());
            NKErrorCode.MidasApiReqFailed.throwError("midas queryZone fail, creatorOfferId:{},res:{}", creatorOfferId,
                    res.toString());
        }
        return res;
    }


    public void doPost(String host, String path, Message.Builder req, Message.Builder res, String secretKey)
            throws IOException, OpensnsException {
        LOGGER.debug("host:{}, path : {}, req : {} ", host, path, req);
        try {
            String reqJsonStrNotSig = toJsonString(req);
            JSONObject jsonObject = new JSONObject(reqJsonStrNotSig);
            Map<String, String> params = new HashMap<>();
            jsonObject.keySet().forEach(key -> {
                String value = jsonObject.optString(key);
                params.put(key, value);
            });
            LOGGER.debug("SECRET_KEY:{}, makeSig:{}", secretKey, params.toString());
            String sig = SnsSigCheck.makeSig("post", path, params, secretKey + "&");
            jsonObject.put("sig", sig);
            String reqJsonStr = jsonObject.toString();
            String url = getUrl(host, path);
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setEntity(new StringEntity(reqJsonStr, StandardCharsets.UTF_8));
            LOGGER.debug("http request body : {}, {}", httpPost.toString(), reqJsonStr);
            String responseStr = client.execute(httpPost, BASIC_RESPONSE_HANDLER);
            LOGGER.debug("reponse string from http : {}", responseStr);
            fromJson(responseStr, res);
            LOGGER.debug("http response body : {}", res);
        } catch (Exception e) {
            LOGGER.error("doPost fail", e);
            Monitor.getInstance().add.fail(MonitorId.attr_midas_http_req, 1);
            throw e;
        } finally {
            Monitor.getInstance().add.total(MonitorId.attr_midas_http_req, 1);
        }
    }

    public void doPostEncode(String host, String path, Message.Builder req, Message.Builder res, String secretKey)
            throws IOException, OpensnsException {
        try {
            LOGGER.debug("host:{}, path : {}, req : {} ", host, path, req);
            String reqJsonStrNotSig = toJsonString(req);
            JSONObject jsonObject = new JSONObject(reqJsonStrNotSig);
            Map<String, String> params = new HashMap<>();
            StringBuilder reqStr = new StringBuilder();
            jsonObject.keySet().forEach(key -> {
                String value = jsonObject.optString(key);
                params.put(key, value);
                try {
                    String encodeStr = SnsSigCheck.encodeUrl(value);
                    reqStr.append(key).append("=").append(encodeStr).append("&");
                } catch (OpensnsException e) {
                    LOGGER.error("encodeUrl fail, key{}, value:{}", key, value);
                }
            });
            LOGGER.debug("SECRET_KEY:{}, makeSig:{}", secretKey, params.toString());
            String sig = SnsSigCheck.makeSig("post", path, params, secretKey + "&");
            reqStr.append("sig").append("=").append(SnsSigCheck.encodeUrl(sig));
            String url = getUrl(host, path);
            HttpPost httpPost = new HttpPost(url);
//            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            httpPost.setEntity(new StringEntity(reqStr.toString()));
            LOGGER.debug("http request body : {},  {}", httpPost.toString(), reqStr.toString());
            String responseStr = client.execute(httpPost, BASIC_RESPONSE_HANDLER);
            LOGGER.debug("reponse string from http : {}", responseStr);
            fromJson(responseStr, res);
            LOGGER.debug("http response body : {}", res);
        } catch (Exception e) {
            LOGGER.error("doPost fail", e);
            Monitor.getInstance().add.fail(MonitorId.attr_midas_http_req, 1);
            throw e;
        } finally {
            Monitor.getInstance().add.total(MonitorId.attr_midas_http_req, 1);
        }
    }

    public String getUrl(String host, String path) {

        NKPair<String, Integer> instance = PolarisUtil.discover(host, "Production");
        if (instance == null) {
            NKErrorCode.CurrencyPlatformError.throwError("Polaris discover fail");
        }
        LOGGER.info("selected instance is {}:{}", instance.getKey(), instance.getValue());
        return "http://" + instance.getKey() + ":" + instance.getValue() + path;
    }

    public String reqSessionKeyByWxCode(String wxCode, boolean isQQMiniGame) throws NKCheckedException {
        String adress = PropertyFileReader.getRealTimeItem("wxMiniGame_code2Session_url",
                "https://api.weixin.qq.com/sns/jscode2session");
        String appId = PropertyFileReader.getRealTimeItem("wxMiniGame_appid", "wxb9f8793c2d5521e6");
        String secret = PropertyFileReader.getRealTimeItem("wxMiniGame_secret", "a116ad9b5470bc94ad814f4a4df258fb");
        if (isQQMiniGame) {
            adress = PropertyFileReader.getRealTimeItem("qqMiniGame_code2Session_url",
                    "https://api.q.qq.com/sns/jscode2session");
            appId = PropertyFileReader.getRealTimeItem("qqMiniGame_appid", "1112288843");
            secret = PropertyFileReader.getRealTimeItem("qqMiniGame_secret", "0RPu7IxL82cHf1fa");
        }
        StringBuilder sBuffer = new StringBuilder(adress);
        sBuffer.append("?")
                .append("appid=").append(appId)
                .append("&secret=").append(secret)
                .append("&js_code=").append(wxCode)
                .append("&grant_type=").append("authorization_code");
        String url = sBuffer.toString();
        LOGGER.debug(url);
        JSONObject result = null;
        try {
            HttpPost httpPost = new HttpPost(url);
            String response = client.execute(httpPost, BASIC_RESPONSE_HANDLER);
            result = new JSONObject(response);
            if (result.has("errcode") && 0 != result.getInt("errcode")) {
                throw new NKCheckedException(
                        NKStringFormater.format("request failed: request={} response={}",
                                url, StringUtils.replace(response, "\"", "")));
            }
            LOGGER.debug("request ok: request={} response={}", url, response);
        } catch (Exception e) {
            LOGGER.warn("request failed: request={}   e:{}", url, e.getMessage());
            throw new NKCheckedException(NKStringFormater.format("request failed: request={}  e:{}",
                    url, e.getMessage()));
        }
        return result.getString("session_key");
    }

    public String getWxMiniGamePaySignData(ProductInfo productInfo, String outTradeNo,
            DeliverGoodsMetaData metaData, int platId) throws InvalidProtocolBufferException {
        String metaDataStr = toJsonString(metaData.toBuilder());
        JSONObject signData = new JSONObject();
        signData.put("mode", "goods");
        signData.put("offerId", OFFER_ID);
        signData.put("buyQuantity", 1);
        signData.put("env", PropertyFileReader.getRealTimeIntItem("midas_wxMiniGame_env", 0));
        signData.put("currencyType", "CNY");
        signData.put("platform", platId == PlatformID.IOS_VALUE ? "iap" : "android");
        signData.put("zoneId", productInfo.getZoneid());
        signData.put("productId", productInfo.getProductId());
        signData.put("goodsPrice", productInfo.getSellingPrice());
        signData.put("outTradeNo", outTradeNo);
        signData.put("attach", metaDataStr);
        if (productInfo.getJoinModel().getListCount() > 0) {
            JSONArray activityList = new JSONArray();
            productInfo.getJoinModel().getListList().forEach(info -> {
                JSONObject data = new JSONObject();
                data.put("activity_id", info.getActivityId());
                data.put("model_id", info.getModelId());
                data.put("model_type", info.getModelType());
                activityList.put(data);
            });
            JSONObject extraData = new JSONObject();
            extraData.put("activity_list", activityList);
            signData.put("extraInfo", extraData);
        }

        return signData.toString();
    }

    public String genWxMiniGamePaySig(String signData) {
        String secretKey = PropertyFileReader.getItem("applet_appKey", "");
        return HMAC_SHAx_HEX(secretKey, "requestMidasPaymentGameItem&" + signData);
    }

    public String genMiniGameSignature(String sessionKey, String signData) {
        if (sessionKey.isEmpty()) {
            LOGGER.error("seesionKey is empty");
            return "";
        }
        return HMAC_SHAx_HEX(sessionKey, signData);
    }

    public String getQQMiniGamePaySignData(ProductInfo productInfo, String outTradeNo,
            DeliverGoodsMetaData metaData, int platId) throws InvalidProtocolBufferException {
        String metaDataStr = JsonFormat.printer().print(metaData.toBuilder());
        metaDataStr = new JSONObject(metaDataStr).toString();
        JSONObject signData = new JSONObject();
        signData.put("mode", "goods");
        signData.put("buy_quantity", 1);
        signData.put("env", PropertyFileReader.getRealTimeIntItem("midas_env", 0));
        signData.put("currency_type", "CNY");
        signData.put("platform", platId == PlatformID.IOS_VALUE ? "iap" : "android");
        signData.put("zone_id", productInfo.getZoneid());
        signData.put("product_id", productInfo.getProductId());
        signData.put("goods_price", productInfo.getSellingPrice());
        signData.put("game_bill_no", outTradeNo);
        signData.put("attach", metaDataStr);
        if (productInfo.getJoinModel().getListCount() > 0) {
            JSONArray activityList = new JSONArray();
            productInfo.getJoinModel().getListList().forEach(info -> {
                JSONObject data = new JSONObject();
                data.put("activity_id", info.getActivityId());
                data.put("model_id", info.getModelId());
                data.put("model_type", info.getModelType());
                activityList.put(data);
            });
            JSONObject extraData = new JSONObject();
            extraData.put("activity_list", activityList);
            signData.put("extra_info", extraData);
        }

        return signData.toString();
    }

    public String genQQMiniGamePaySig(String signData) {
        String secretKey = PropertyFileReader.getItem("qq_applet_appKey", "");
        return HMAC_SHAx_HEX(secretKey, "requestMidasPaymentGameItem&" + signData);
    }


    // 计算给定字符串的HMAC-SHA256哈希值，并将结果转换为十六进制表示
    private static String HMAC_SHAx_HEX(String secretKey, String signingString) {
        LOGGER.debug("key:{}, signData:{}", secretKey, signingString);
        try {
            Mac hasher = Mac.getInstance("HmacSHA256");
            hasher.init(new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));

            byte[] hash = hasher.doFinal(signingString.getBytes(StandardCharsets.UTF_8));

            return DatatypeConverter.printHexBinary(hash).toLowerCase();

        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("UisSignature HMAC_SHAx_HEX meets NoSuchAlgorithmException {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        } catch (InvalidKeyException e) {
            LOGGER.error("UisSignature HMAC_SHAx_HEX meets InvalidKeyException {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    public static class ProductData {

        public final String productId;
        public final int num;
        public long price;  // 单位：角(1个星钻)
        public long creatorId;
        public String creatorOfferId;

        public long activityPrice;  // 活动价格，最终会以这个价格进行扣款。单位：角(1个星钻)

        public ProductData(String productId, int num, long activityPrice) {
            this.productId = productId;
            this.num = num;
            this.activityPrice = activityPrice;
        }

        public ProductData(String productId, int num) {
            this.productId = productId;
            this.num = num;
        }

        public ProductData(String productId, int num, long price, long creatorId, String creatorOfferId) {
            this.productId = productId;
            this.num = num;
            this.price = price;
            this.creatorId = creatorId;
            this.creatorOfferId = creatorOfferId;
        }

        @Override
        public String toString() {
            return "ProductData{" +
                    "productId='" + productId + '\'' +
                    ", num=" + num +
                    ", price=" + price +
                    ", creatorId=" + creatorId +
                    ", creatorOfferId='" + creatorOfferId + '\'' +
                    ", activityPrice=" + activityPrice +
                    '}';
        }
    }


    private static class InstanceHolder {

        public static MidasManager instance = new MidasManager();
    }
}
