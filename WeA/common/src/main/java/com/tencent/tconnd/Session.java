package com.tencent.tconnd;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.protobuf.Message;
import com.tencent.gameplay.GamePlay;
import com.tencent.gameplay.GamePlayHolder;
import com.tencent.log.NKMDC;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.resourceloader.resclass.ServerTextConfData;
import com.tencent.resourceloader.resclass.ServerTextPatchConfData;
import com.tencent.timiutil.coroutine.CoroutineConfig;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.NKStopWatch;
import com.tencent.timiutil.time.TxStopWatch;
import com.tencent.wea.protocol.CsConfig;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsPlayer;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.common.GamePlayInfo;
import com.tencent.wea.protocol.common.KVEntry;
import com.tencent.wea.protocol.common.PlayerGameMatrixExtraData;
import com.tencent.wea.xlsRes.ResText;
import com.tencent.wea.xlsRes.keywords.DeviceLevel;
import com.tencent.wea.xlsRes.keywords.PlatformID;
import com.tencent.wea.xlsRes.keywords.PlayerLoginPlat;
import com.tencent.wea.xlsRes.keywords.PlayerStateType;
import com.tencent.wea.xlsRes.keywords.TconndApiAccount;
import com.tencent.wea.xlsRes.keywords.TconndApiClientType;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.nio.ByteBuffer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;

public class Session {

    private static final Logger LOGGER = LogManager.getLogger(Session.class);
    private static final Logger sessionLogger = LogManager.getLogger("Session");

    private final int sessionId;
    private final int connidx;
    private final long createTime;
    TxStopWatch sessionStopWatch;
    private String openid;
    private volatile long uid = 0;
    private String ipAddr;
    private List<String> ipLocation = new ArrayList<>();
    private ConcurrentHashMap<String, Object> attachments = new ConcurrentHashMap<String, Object>();
    private TconndApiAccount accountType;
    private TconndApiClientType clientType;
    // 支付信息
    private String payToken;
    private String payPf;
    private String payPfKey;
    private String accessToken;
    private volatile long lastMaasTs = 0;
    private boolean maasNtf = false;
    private int srcServerId;
    private String curHandleMsgName;
    private volatile boolean logVerbose;
    private LinkedList<LogicMsg> pendingMessages = new LinkedList<>(); // 待处理的消息
    private ClientInfo clientInfo = new ClientInfo();
    private AtomicLong lock = new AtomicLong(0);
    private long deleteCheckTime = 0;
    private boolean handlingMsg = false; // 是否正在处理消息
    private volatile State state = State.STATE_CONNECTED;
    private volatile Stage stage = Stage.CONNECTED;
    private boolean isKickNtfed = false;
    private String DSVersion;

    private String jqCallInfo = "";
    private PlayerGameMatrixExtraData gameMatrixExtraData;
    private String deeplink = "";

    public Session(int sessionId, int connidx) {
        this.connidx = connidx;
        this.sessionId = sessionId;
        createTime = Framework.currentTimeMillis();
        lastMaasTs = createTime;
    }

    public boolean tryLock() {
        if (lock.compareAndSet(0, /*Thread.currentCarrierThread().getId()*/
                CoroutineConfig.jdkutil.getCurrentThread().getId()) ||
                lock.compareAndSet(
                        CoroutineConfig.jdkutil.getCurrentThread().getId()/*Thread.currentCarrierThread().getId()*/,
                        CoroutineConfig.jdkutil.getCurrentThread().getId()/*Thread.currentCarrierThread().getId()*/)) {
            return true;
        }
        return false;
    }

    public void unlock() {
        if (lock.compareAndSet(0, 0) ||
                lock.compareAndSet(
                        CoroutineConfig.jdkutil.getCurrentThread().getId()/*Thread.currentCarrierThread().getId()*/,
                        0)) {
            return;
        }
    }

    public long getDeleteCheckTime() {
        return deleteCheckTime;
    }

    public void setDeleteCheckTime(long ms) {
        deleteCheckTime = ms;
    }

    public void createStopWatch() {
        sessionStopWatch = NKStopWatch.SW_SessionStopwatch.getStopWatch(NKStringFormater.format("{},{}", openid, uid));
    }

    public TxStopWatch getSessionStopWatch() {
        return sessionStopWatch;
    }

    public void markStopWatch(String mark) {
        if (sessionStopWatch != null) {
            sessionStopWatch.mark(mark);
        }
    }

    public void destoryStopWatch() {
        if (sessionStopWatch == null) {
            return;
        }
        try {
            sessionStopWatch.dump(500);
        } finally {
            sessionStopWatch = null;
        }
    }

    public ClientInfo getClientInfo() {
        return clientInfo;
    }

    public void initClientInfo(CsPlayer.Login_C2S_Msg reqMsg) {
        clientInfo.channel = reqMsg.getChannel();
        clientInfo.clientVersion = reqMsg.getClientVersion();
        clientInfo.idfa = reqMsg.getIdfa();
        clientInfo.imei = reqMsg.getImei();
        clientInfo.telecomOper = reqMsg.getTelecomOper();
        clientInfo.systemHardware = reqMsg.getSystemHardware();
        clientInfo.netWork = reqMsg.getNetWork();
        clientInfo.deviceId = reqMsg.getDeviceId();
        clientInfo.resVersion = reqMsg.getResVersion();
        clientInfo.memory = reqMsg.getDeviceMemory();
        //clientInfo.recommendZoneIds = reqMsg.getRecommendZoneList();
        clientInfo.systemSoftware = reqMsg.getSystemSoftware();
        clientInfo.platId = reqMsg.getPlatId();
        clientInfo.deviceLevel = reqMsg.getDeviceLevel();
        clientInfo.userAgent = reqMsg.getUserAgent();
        clientInfo.caid = reqMsg.getCaid();
        clientInfo.oaid = reqMsg.getOaid();
        clientInfo.cloudGameType = reqMsg.getCloudGameType();
        if(reqMsg.getIsGameMatrix() == 1){
            clientInfo.cloudGamePlat = reqMsg.getGameMatrixExtraData().getCloudGamePlat();
        }
        clientInfo.loginPlat = reqMsg.getLoginPlat();
        clientInfo.wxminigame_clue_token = reqMsg.getWxgameClueToken();
        clientInfo.wxminigame_req_id = reqMsg.getWxgameReqId();
        clientInfo.wxminigame_click_id = reqMsg.getWxgameClickId();
        clientInfo.wxminigame_callback = reqMsg.getWxgameCallback();
        clientInfo.wxminigame_minigame_channel = reqMsg.getWxgameMinigameChannel();
        clientInfo.wxminigame_wx_openid = reqMsg.getWxgameWxOpenid();
        clientInfo.wxminigame_unionid = reqMsg.getWxgameUnionid();
        clientInfo.isSimulate = reqMsg.getIsSimulate();
        clientInfo.adultType = reqMsg.getAdultType();
        clientInfo.isRealName = reqMsg.getIsRealName();
        clientInfo.qqNumber = reqMsg.getQqNumber();
        clientInfo.newCaid = reqMsg.getIosCaid();
        clientInfo.isVa = reqMsg.getIsVa();
        clientInfo.gpu = reqMsg.getGpu();
        clientInfo.cpu = reqMsg.getCpu();
        clientInfo.clientDeviceType = reqMsg.getClientDeviceType();
        clientInfo.devicePlatform = reqMsg.getDevicePlatform();
        clientInfo.matrixDeviceInfo = genMatrixDeviceInfo(reqMsg.getGameMatrixExtraData());
        clientInfo.wxSceneId = reqMsg.getWxSceneId();
        clientInfo.wxgame_mpAppId = reqMsg.getWxgameMpAppId();

        // fill game play info
        fillGamePlayInfo(uid, reqMsg.getPlayLabelList(), reqMsg.getPlayInfoList());
    }

    private String genMatrixDeviceInfo(PlayerGameMatrixExtraData extraData) {
        if (extraData == null) {
            return "";
        }
        return "{\"system\":\"" + extraData.getCloudGameSystem() + "\", " +
                "\"platform\":" + extraData.getCloudGamePlat() + ", " +
                "\"brand\":\"" + extraData.getCloudGameBand() + "\", " +
                "\"model\":\"" + extraData.getCloudGameModel() + "\"}";
    }

    public void fillGamePlayInfo(long id, List<KVEntry> playLabelList, List<GamePlayInfo> playInfoList){
        boolean devLabelEnabled = ServerEngine.getInstance().isTestEnv() || ServerEngine.getInstance().isPressTest();
        String DevicePlatformName = clientInfo.getDevicePlatformName(playLabelList);
        clientInfo.updateGamePlayLabels(getUid(), Map.of("platform", DevicePlatformName));
        clientInfo.updateGamePlayLabels(getUid(), playLabelList
                .stream()
                .filter(kv -> (devLabelEnabled
                        || !kv.getKey().equalsIgnoreCase("platform")))
                .collect(Collectors.toMap(KVEntry::getKey, KVEntry::getValue)));
        clientInfo.updateGamePlayInfos(getUid(), playInfoList);

        if (LOGGER.isDebugEnabled()){
            LOGGER.debug("fillGamePlayInfo id[{}] devLabelEnabled[{}] DevicePlatformName[{}] ",
                    id, devLabelEnabled, DevicePlatformName);
        }
    }

    public void initClinetInfo(int sessionId, CsConfig.GetPlayPufferInfo_C2S_Msg reqMsg){
        clientInfo.cloudGameType = reqMsg.getCloudGameType();
        clientInfo.isSimulate = reqMsg.getIsSimulate();
        clientInfo.loginPlat = reqMsg.getLoginPlat();
        clientInfo.platId = reqMsg.getPlatId();
        List<KVEntry> playLabelList = reqMsg.getPlayLabelList();

        // fill game play info
        boolean devLabelEnabled = ServerEngine.getInstance().isTestEnv();
        clientInfo.updateGamePlayLabels(sessionId, Map.of("platform", clientInfo.getDevicePlatformName(playLabelList)));
        clientInfo.updateGamePlayLabels(sessionId, reqMsg.getPlayLabelList()
                .stream()
                .filter(kv -> (devLabelEnabled
                        || !kv.getKey().equalsIgnoreCase("platform")))
                .collect(Collectors.toMap(KVEntry::getKey, KVEntry::getValue)));

        clientInfo.updateGamePlayInfos(sessionId, reqMsg.getPlayInfoList());
    }

    public int getConnidx() {
        return connidx;
    }

    public String getCurHandleMsgName() {
        return curHandleMsgName;
    }

    public void setCurHandleMsgName(String curHandleMsgName) {
        this.curHandleMsgName = curHandleMsgName;
    }

    public int getSrcServerId() {
        return srcServerId;
    }

    public void setSrcServerId(int srcServerId) {
        this.srcServerId = srcServerId;
    }

    public boolean getMaasNtf() {
        return maasNtf;
    }

    public void setMaasNtf(boolean maasNtf) {
        this.maasNtf = maasNtf;
    }

    public long getLastMaasTs() {
        return lastMaasTs;
    }

    public void setLastMaasTs(long lastMaasTs) {
        this.lastMaasTs = lastMaasTs;
    }

    public int getSessionId() {
        return sessionId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
        NKMDC.put(NKMDC.UID, String.valueOf(getUid()));
    }

    public String getIpAddr() {
        return ipAddr;
    }

    public List<String> getIpLocation() {
        return ipLocation;
    }

    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr;
    }

    public void setIpLocation(List<String> ipLocation) {
        List<String> locations = Lists.newArrayList();
        if (ipLocation == null) {
            this.ipLocation = locations;
            return;
        }

        for (var location : ipLocation) {
            if ("未知".equals(location)) {
                LOGGER.debug("unknown location and skip, uid:{} location:{}", uid, location);
                continue;
            }

            locations.add(location);
        }

        this.ipLocation = locations;
        LOGGER.debug("set ip location, uid:{} ip:{} location:{}", uid, ipAddr, ipLocation);
    }

    /**
     * 发送PB消息
     *
     * @param header
     * @param body
     * @param bodyBytes: 如果非null, 则包含已经序列化过的body
     */
    public void sendMsg(CsHead.CSHeader.Builder header, Message.Builder body, byte[] bodyBytes, int deltaMS) {
        sendMsg(header, body, bodyBytes != null ? ByteBuffer.wrap(bodyBytes) : null, deltaMS);
    }

    /**
     * 发送PB消息
     *
     * @param header
     * @param body
     * @param bodyBuffer: 如果非null, 则包含已经序列化过的body
     */
    public void sendMsg(CsHead.CSHeader.Builder header, Message.Builder body, ByteBuffer bodyBuffer, int deltaMS) {
        if (!checkState(State.STATE_DISCONNECTED)) {
            Monitor.getInstance().add.total(MonitorId.attr_s2c_and_ntf_msg, 1, new String[]{
                    MsgTypes.getMsgName(header.getType()),
                    getStage().name()
            });
            TconndManager.getInstance().sendMsgOrBuffer(this, header, body, bodyBuffer, deltaMS);
        } else {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("can not send msg: disconnected {} {}", this, header.getType());
            }
        }
    }

    public void sendMsg(CsHead.CSHeader.Builder header, Message.Builder body, int deltaMS) {
        sendMsg(header, body, (ByteBuffer)null, deltaMS);
    }

    /**
     * 发送PB消息
     * NOTE: seqId 通过ThreadLocal存储
     *
     * @param type
     * @param body
     */
    public void sendMsg(int type, int seqId, Message.Builder body) {
        sendMsg(CsHead.CSHeader.newBuilder().setSeqId(seqId).setType(type)
                .setServerTs(Framework.currentTimeMillis()), body, (ByteBuffer)null, 0);
    }

    /**
     * 发送PB消息
     * NOTE: seqId 通过ThreadLocal存储
     *
     * @param type
     * @param body
     */
    public void sendMsg(int type, int seqId, Message.Builder body, int deltaMS) {
        sendMsg(CsHead.CSHeader.newBuilder().setSeqId(seqId).setType(type)
                .setServerTs(Framework.currentTimeMillis()), body, (ByteBuffer)null, deltaMS);
    }

    /**
     * 发送ntf(push)消息
     * NOTE: seq id为0
     *
     * @param type
     * @param body
     */
    public void sendNtfMsg(int type, Message.Builder body) {
        sendMsg(CsHead.CSHeader.newBuilder().setSeqId(0).setType(type)
                .setServerTs(Framework.currentTimeMillis()), body, (ByteBuffer)null, 0);
    }

    /**
     * 如果bodyBytes非空,发送已经序列化好的数据
     * NOTE: seq id为0
     *
     * @param type
     * @param body
     */
    public void sendNtfMsg(int type, Message.Builder body, byte[] bodyBytes) {
        sendMsg(CsHead.CSHeader.newBuilder().setSeqId(0).setType(type).
                setServerTs(Framework.currentTimeMillis()), body, bodyBytes, 0);
    }

    
    /**
     * 发送已经序列化好的数据, 存储在ByteBuff
     * NOTE: seq id为0
     *
     * @param type
     * @param body
     */
    public void sendNtfMsg(int type, ByteBuffer bodyBuff) {
        sendMsg(CsHead.CSHeader.newBuilder().setSeqId(0).setType(type).
                setServerTs(Framework.currentTimeMillis()), null, bodyBuff, 0);
    }

//    public void sendMsgWithReqHeader(CsHead.CSHeader reqHeader, int type, Message.Builder body, int deltaMS) {
//        sendMsg(CsHead.CSHeader.newBuilder().setSeqId(getCurMsgSeqId()).setType(type).setClientTs(reqHeader.getClientTs()).setServerTs(Framework.currentTimeMillis()), body, deltaMS);
//    }

    // 判断是否要使用补丁配置中的错误文本
    public static boolean needUsePatchText(int type, int errorCode, String msg) {
        if (type == -1) {
            return false;
        }

        boolean enable = PropertyFileReader.getRealTimeBooleanItem("enable_errormsg_use_patch_text", true);
        if (!enable) {
            return false;
        }

        // errorCode不在补丁配置里，则不替换
        ResText.ClientServerTextConf textConf = ServerTextPatchConfData.getPatchConf(errorCode);
        if (textConf == null) {
            return false;
        }

        String patchText = ServerTextPatchConfData.getPatchText(errorCode);
        if (Strings.isNullOrEmpty(patchText)) {
            return false;
        }

        // 如果普通文本里配了rpcType，则不替换，避免影响原来的跟客户端的特殊rpc处理
        int orgRpcType = ServerTextConfData.getRPCType(errorCode);
        if (orgRpcType > 0) {
            return false;
        }

        return true;
    }

    // 没有做补丁文本配置处理的接口。外部调用建议用 sendErrorMsg 接口
    public void innerSendErrorMsg(int type, int seqId, int errorCode, String errorMsg, int rpcType) {
        if (type == -1) {
            return;
        }
        CsHead.CSHeader.Builder header = CsHead.CSHeader.newBuilder()
                .setSeqId(seqId)
                .setType(type)
                .setServerTs(Framework.currentTimeMillis());

        if (!Strings.isNullOrEmpty(errorMsg)) {
            header.setErrorMsg(errorMsg);
        }
        header.setErrorCode(errorCode);
        header.setRpcType(rpcType);
        sendMsg(header, null, 0);
    }

    public void sendErrorMsg(int type, NKErrorCode code) {
        int errorCode = code.getValue();
        String errorMsg = "";
        if (needUsePatchText(type, errorCode, errorMsg)) {
            errorMsg = ServerTextPatchConfData.getPatchText(errorCode);
            errorCode = ServerTextPatchConfData.getHookedErrorCode(errorCode);  // 拿到补丁文本后把errorCode替换为PlaceHold*
        }
        innerSendErrorMsg(type, 0, errorCode, errorMsg, ServerTextConfData.getRPCType(code.getValue()));
    }

    public void sendErrorMsg(int type, NKErrorCode code, String errorMsg) {
        int errorCode = code.getValue();
        if (needUsePatchText(type, errorCode, errorMsg)) {
            errorMsg = ServerTextPatchConfData.getPatchText(errorCode);
            errorCode = ServerTextPatchConfData.getHookedErrorCode(errorCode);  // 拿到补丁文本后把errorCode替换为PlaceHold*
        }
        innerSendErrorMsg(type, 0, errorCode, errorMsg, ServerTextConfData.getRPCType(code.getValue()));
    }

    /**
     * 发送ErrorCode给客户端
     *
     * @param type
     * @param errorCode 不允许非空
     * @param errorMsg 允许为空
     */
    public void sendErrorMsg(int type, int seqId, int errorCode, String errorMsg) {
        if (needUsePatchText(type, errorCode, errorMsg)) {
            errorMsg = ServerTextPatchConfData.getPatchText(errorCode);
            errorCode = ServerTextPatchConfData.getHookedErrorCode(errorCode);  // 拿到补丁文本后把errorCode替换为PlaceHold*
        }
        innerSendErrorMsg(type, seqId, errorCode, errorMsg, ServerTextConfData.getRPCType(errorCode));
    }

    private boolean kickoffCheckPass() {
        if (!checkState(State.STATE_LOGIN)) {
            return false;
        }
        if (isKickNtfed) {
            return false;
        }
        isKickNtfed = true;
        return true;
    }

    public void kickOffNtf(NKErrorCode code, String message) {
        if (!kickoffCheckPass()) {
            return;
        }

        sendErrorMsg(MsgTypes.MSG_TYPE_KICKPLAYERNTF, code, message);

        // 信息直接填充包头里面，方便客户端处理
//        CsPlayer.KickPlayerNtf.Builder ntf = CsPlayer.KickPlayerNtf.newBuilder();
//        ntf.setReason(message);
//        ntf.setSid(getSessionId());
//        ntf.setErrorCode(code.getValue());
//        sendNtfMsg(MsgTypes.MSG_TYPE_KICKPLAYERNTF, ntf);
    }

    public void kickOffNtf(CsPlayer.KickPlayerNtf.Builder builder) {
        if (!kickoffCheckPass()) {
            return;
        }
        builder.setSid(getSessionId());
        sendNtfMsg(MsgTypes.MSG_TYPE_KICKPLAYERNTF, builder);
    }

    public void kickOffNtf(NKErrorCode code) {
        kickOffNtf(code, code.name());
    }

    public void setDisconnect() {
        LOGGER.info("session freed at {} {} {} {}", getOpenid(), Framework.currentTimeMillis(), sessionId, connidx);
        setState(State.STATE_DISCONNECTED);
    }

    public Object getProperty(String key) {
        return attachments.get(key);
    }

    public void setProperty(String key, Object value) {
        attachments.put(key, value);
    }

    public void removeProperty(String key) {
        attachments.remove(key);
    }

    public boolean containsProperty(String key) {
        return attachments.containsKey(key);
    }

    /**
     * 是否链接状态，该接口兼容老的代码
     *
     * @return
     */
    public boolean isConnected() {
        return state == State.STATE_CONNECTED || state == State.STATE_LOGIN;
    }

    public boolean isLogin() {
        return this.state == State.STATE_LOGIN;
    }

    public boolean checkState(State state) {
        return this.state == state;
    }

    public State getState() {
        return state;
    }

    public void setState(State state) {
        if (ServerEngine.logErrorNotPressTest()) {
            sessionLogger.warn("setState sid:{} uid:{} {} -> {}", sessionId, uid, this.state, state);
        }
        this.state = state;
    }

    public Stage getStage() {
        return this.stage;
    }

    public void setStage(Stage val) {
        if (val.getValue() < this.stage.getValue()) {
            LOGGER.error("sid:{} uid:{} set stage unexpected from:{} to:{}", sessionId, uid, this.stage, val);
        }
        this.stage = val;
        LOGGER.debug("sid:{} uid:{} set stage from:{} to:{}", sessionId, uid, this.stage, val);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Session[");
        sb.append("sid:").append(sessionId);
        sb.append(" connidx:").append(connidx);
        sb.append(" createTime:").append(createTime);
        sb.append(" openid:'").append(openid).append('\'');
        sb.append(" uid:").append(uid);
        sb.append(" ipAddr:'").append(ipAddr).append('\'');
        sb.append(" state:").append(state);
        sb.append(']');
        return sb.toString();
    }

    public TconndApiAccount getAccountType() {
        return Objects.requireNonNull(accountType);
    }

    public void setAccountType(int accountType) {
        this.accountType = TconndApiAccount.forNumber(accountType);
        if (this.accountType == null) {
            this.accountType = TconndApiAccount.TCONND_ITOP_CHANNEL_Unknown;
        }
    }

    public TconndApiClientType getClientType() {
        return clientType;
    }

    public void setClientType(int clientType) {
        this.clientType = TconndApiClientType.forNumber(clientType);
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getPayPf() {
        return payPf;
    }

    public void setPayPf(String payPf) {
        this.payPf = payPf;
    }

    public String getPayPfKey() {
        return payPfKey;
    }

    public void setPayPfKey(String payPfKey) {
        this.payPfKey = payPfKey;
    }

    public String getJqCallInfo() {
        return jqCallInfo;
    }

    public void setJqCallInfo(String jqCallInfo) {
        this.jqCallInfo = jqCallInfo;
    }

    public PlayerGameMatrixExtraData getGameMatrixExtraData() {
        return gameMatrixExtraData;
    }

    public void setGameMatrixExtraData(PlayerGameMatrixExtraData gameMatrixExtraData) {
        this.gameMatrixExtraData = gameMatrixExtraData;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }

    public String getDeeplink() {
        return deeplink;
    }


    public boolean isH5GameMatrix() {
        return getPayToken().startsWith("GAMEMATRIX");
    }

    public boolean isH5GameMatrixTokenValid() {
        PlayerGameMatrixExtraData extraData = getGameMatrixExtraData();
        if (extraData == null || extraData.getOpenKey().isEmpty()) {
            return false;
        }
        return true;
    }

    public boolean isLogVerbose() {
        return logVerbose;
    }

    public void setLogVerbose(boolean logVerbose) {
        this.logVerbose = logVerbose;
    }

    public SessionLogMDC setLogMDC() {
        Map<String, String> contextMap = null;
        boolean flag = logVerbose;
        if (flag) {
            contextMap = MDC.getCopyOfContextMap();
            //MDC.clear();
            MDC.put("wl", "1"); // 用这个控制是否显示
            MDC.put("openid", openid);
            MDC.put("sid", String.valueOf(sessionId));
            MDC.put("connid", String.valueOf(connidx));
        }
        return new SessionLogMDC(contextMap, flag);
    }

    public void restoreLogMDC(SessionLogMDC sessionLogMDC) {
        if (sessionLogMDC.logVerbose) {
            //MDC.clear();
            if (sessionLogMDC.contextMap != null) {
                //MDC.setContextMap(sessionLogMDC.contextMap);
                for (Map.Entry<String, String> entry : sessionLogMDC.contextMap.entrySet()) {
                    MDC.put(entry.getKey(), entry.getValue());
                }
            }
        }
    }

    public LinkedList<LogicMsg> getPendingMessages() {
        return pendingMessages;
    }

    public boolean isHandlingMsg() {
        return handlingMsg;
    }

    public void setHandlingMsg(boolean handlingMsg) {
        this.handlingMsg = handlingMsg;
    }

    public boolean pfCheck(String pf) {
        if (pf == null || pf.isEmpty()) {
            return false;
        }
        if (pf.equals(getPayPf())) {
            return true;
        }
        if (Session.getPlatIdFromPf(pf) == clientInfo.platId) {
            return true;
        }
        return false;
    }

    /**
     * 验证支付平台与客户端平台ID一致性
     *
     * @return true表示平台ID匹配，false表示不匹配
     */
    public boolean checkPfWithPlatId() {
        if (payPf == null) {
            return false;
        }
        return Session.getPlatIdFromPf(payPf) == clientInfo.platId;
    }

    // 返回>=0为正常platID
    public static int getPlatIdFromPf(String pf) {
        //pf的组成为: 唤起平台_账号体系-注册渠道-操作系统-安装渠道-账号体系-appid-openid。
        // 例如：qq_qq-1001-iap-1001-qq-1106977030-1352068295328743236
        if (!pf.isEmpty()) {
            String[] pfs = pf.split("-");
            if (pfs.length >= 3) {
                if ("iap".equals(pfs[2])) {
                    return 0;
                } else {
                    return 1;
                }
            }
        }
        return -1;
    }

    public enum State {
        STATE_CONNECTED, // 连接且完成鉴权
        STATE_LOGIN, // 完成login command
        STATE_DISCONNECTED // session断开连接
    }

    public enum Stage {
        NA(0),                          // 直接指定值，避免引用未初始化的 counter
        CONNECTED(1),                      // 1

        // 登录阶段
        LOGIN_LOAD(100),
        LOGIN_BIND(110),
        LOGIN_AFTER(120),
        LOGIN_FINISH(130), 

        // 登录成功后
        GAMING(200);

        private final int value;

        Stage(int val) {
            this.value = val;
        }

        public int getValue() {
            return value;
        }
    }

    public enum CloudGameType {
        NONE(0),            // 不是
        PIONEER(1),         // 先锋云游戏
        WECHAT_MINI_GAME(2),// 微信小游戏
        QQ_MINI_GAME(3);    // qq小游戏

        private final int code;

        CloudGameType(int code) {
            this.code = code;
        }

        int getValue() {
            return code;
        }
    }

    //流水相关
    public static class ClientInfo {

        public String clientVersion = "";
        public String systemHardware = "";
        public String systemSoftware = "";
        public String telecomOper = "";
        public String netWork = "";
        public int channel;//渠道号
        public String deviceId = "";//imei或idfa的hash值，可以保证每个客户端唯一
        public String resVersion = "";
        public String secReportData = "";
        public int memory;
        public int platId;
        public List<Integer> recommendZoneIds;
        public DeviceLevel deviceLevel;
        public String imei = "";
        public String idfa = "";
        public String oaid = "";
        public String caid = ""; // old caid
        private int currentMatchType = 0;
        private PlayerStateType playerStateType = PlayerStateType.PST_Offline;
        private String userAgent = "";  //用户浏览器代理信息
        private int cloudGameType = 0;  // 0不是, 1为先锋云游戏, 2为微信小游戏  3为qq小游戏
        private int cloudGamePlat = -1;  // // 0 苹果, 1 安卓, 2 PC， 非云游：-1
        private boolean inXiaowo = false;
        private boolean inFarm = false;
        private boolean inHouse = false;
        private boolean inRich = false;
        private boolean inCook = false;
        private String wxminigame_clue_token = "";
        private String wxminigame_req_id = "";
        private String wxminigame_click_id = "";
        private String wxminigame_callback = "";
        private String wxminigame_minigame_channel = "";
        private String wxminigame_wx_openid = "";
        private String wxminigame_unionid = "";
        private boolean isSimulate = false;
        private int adultType = 0;      // 成年状态，0-未知 1-未成年 2- 已成年
        private int isRealName = 0; //(实名认证状态，0-未实名认证1-已实名认证)。
        private String qqNumber = "";  //qq号
        private String gpu = ""; //客户端gpu型号
        private String cpu = ""; //客户端cpu型号
        GamePlayHolder gamePlayHolder = new GamePlayHolder(GamePlay.getInstance());

        private String newCaid = ""; // new caid
        private int isVa = 0;

        private PlayerLoginPlat loginPlat;

        private int devicePlatform = 0;
        private int clientDeviceType = 0; // 客户端设备类型

        private String matrixDeviceInfo;
        private String wxSceneId;
        private String wxgame_mpAppId;

        public void setInXiaowo(boolean inXiaowo) {
            this.inXiaowo = inXiaowo;
        }

        public void setInFarm(boolean in) {
            this.inFarm = in;
        }

        public void setInHouse(boolean in) {
            this.inHouse = in;
        }
        public void setInCook(boolean in) {
            this.inCook = in;
        }

        public void setInRich(boolean in) {
            this.inRich = in;
        }
        public boolean getInXiaowo() {
            return this.inXiaowo;
        }
        public boolean getInFarm() {
            return this.inFarm;
        }
        public boolean getInRich() {
            return this.inRich;
        }
        public boolean getInCook() {
            return this.inCook;
        }
        public boolean getInHouse() {
            return this.inHouse;
        }
        public boolean getIsSimulate() {
            return isSimulate;
        }

        public void setCurrentMatchType(int matchType) {
            currentMatchType = matchType;
        }

        public int getCurrentMatchType() {
            return currentMatchType;
        }

        public void setPlayerStateType(PlayerStateType stateType) {
            playerStateType = stateType;
        }

        public PlayerStateType getPlayerStateType() {
            return playerStateType;
        }

        public PlayerLoginPlat getLoginPlat() {
            return loginPlat;
        }

        public int getClientDeviceType() {
            return clientDeviceType;
        }
        public  boolean isHarmonyPlat(){
            return loginPlat == PlayerLoginPlat.PLP_Harmony;
        }
        public int getDevicePlatform() {
            return devicePlatform;
        }

        public String getWxSceneId() {
            return wxSceneId;
        }

        public String getWxgame_mpAppId() {
            return wxgame_mpAppId;
        }

        public String getDevicePlatformName(List<KVEntry> playLabelList) {
            boolean isDebug = LOGGER.isDebugEnabled();
            String platformName = "null";
            do {
                // 25.03.07 判断是否是PC云游
                if (getIsPCCloudGame(playLabelList)) {
                    platformName = PlatformID.PC.getValueDescriptor().getName();
                    if (isDebug) { LOGGER.debug("getIsPCCloudGame true"); }
                    break;
                }

                if (getIsGameMatrix() == 1 || getIsSimulate()) {
                    platformName = PlatformID.Android.getValueDescriptor().getName();
                    if (isDebug) { LOGGER.debug("IsGameMatrix || IsSimulate true"); }
                    break;
                }

                if (getIsPC()) {
                    platformName = PlatformID.PC.getValueDescriptor().getName();
                    if (isDebug) { LOGGER.debug("getIsPC true"); }
                    break;
                }

                try {
                    PlatformID platVal = PlatformID.valueOf(this.platId);
                    if (platVal != null) {
                        platformName = platVal.getValueDescriptor().getName();
                        if (isDebug) { LOGGER.debug("platVal != null"); }
                    }
                } catch (IllegalArgumentException e) {
                    LOGGER.error("Invalid platId[{}]" + this.platId);
                }
            } while (false);

            if (isDebug) { LOGGER.debug("getDevicePlatformName ok! platformName[{}]", platformName); }
            return platformName;
        }

        public String getRealDeviceId() {
            return platId == PlatformID.IOS_VALUE ? idfa : imei;
        }

        public String getUserAgent() {
            return userAgent;
        }

        public int getIsGameMatrix() {
            return cloudGameType != 0 ? 1 : 0;
        }

        public boolean isWxMiniGame() {
            return cloudGameType == 2;
        }

        public boolean isQQMiniGame() {
            return cloudGameType == 3;
        }

        public boolean isMiniGame() {
            return isWxMiniGame() || isQQMiniGame();
        }


        public String getWxminigameClueToken() {
            return wxminigame_clue_token;
        }

        public String getWxminigameReqId() {
            return wxminigame_req_id;
        }

        public String getWxminigameClickId() {
            return wxminigame_click_id;
        }

        public String getWxminigameCallback() {
            return wxminigame_callback;
        }

        public String getWxminigameMinigameChannel() {
            return wxminigame_minigame_channel;
        }

        public String getWxminigameWxOpenid() {
            return wxminigame_wx_openid;
        }

        public String getWxminigameUnionid() {
            return wxminigame_unionid;
        }

        public int getAdultType() {
            return adultType;
        }

        public int getCloudGameType() {
            return cloudGameType;
        }

        public int getCloudGamePlat() {
            return cloudGamePlat;
        }

        public boolean isAppCloudGamePlat() {
            return cloudGamePlat == 0 || cloudGamePlat == 1;
        }

        public String getNewCaid() {
            return newCaid;
        }

        public int getIsVa() {
            return isVa;
        }

        public int getIsRealName() {
            return isRealName;
        }

        public String getQqNumber() {
            return qqNumber;
        }

        public String getGpu() {
            return gpu;
        }

        public String getCpu() {
            return cpu;
        }

        public String getMatrixDeviceInfo() {
            return matrixDeviceInfo;
        }

        public GamePlayHolder getGamePlay() {
            return this.gamePlayHolder;
        }

        // 是否PC包，PLP_PC用的一定是PC包，老客户端可能PC没有传devicePlatform
        public boolean getIsPC() {
            return loginPlat == PlayerLoginPlat.PLP_PC || devicePlatform == 3;
        }

        public boolean isSteam() {
            return loginPlat == PlayerLoginPlat.PLP_PCSteam;
        }


        // 是否是云游
        public boolean getIsCloudGame() {
            // 需求是只要不为0,都看做是云游
            return cloudGameType != CloudGameType.NONE.getValue();
        }

        // 是否满足PC判断条件(仅用于PC云游判断)
        private boolean isCloudGamePC(List<KVEntry> playLabelList) {
            for (KVEntry entry : playLabelList) {
                if ("platform".equals(entry.getKey()) && "PC".equals(entry.getValue())) {
                    return true;
                }
            }

            return false;
        }

        /*
        * 判断是否是PC云游 25.03.07
        * 1. cloudGameType判断是否为云游，cloudGameType不为0，即为云游
        * 2. 在第一个条件成立后，再通过playLabel判断是否为pc版本，
        * playLable是一个列表，找到key为platform的item，如果value为字符串PC，即为云游pc，下发pc版本puffer信息
        */
        public boolean getIsPCCloudGame(List<KVEntry> playLabelList) {
            if (!getIsCloudGame()) {
                // 非云游,那一定不是PC云游
                return false;
            }

            return isCloudGamePC(playLabelList);
        }

        // 是否客户端允许在PC平台
        public boolean getIsPCLoginPlat() {
            return loginPlat == PlayerLoginPlat.PLP_PC;
        }

        public boolean getIsWxVa() {
            return loginPlat == PlayerLoginPlat.PLP_WeChatVA;
        }

        public void updateGamePlayLabels(long uid, Map<String, String> labels) {
            this.gamePlayHolder.updateLabels(uid, labels);
        }

        public boolean updateGamePlayInfos(long uid, List<GamePlayInfo> playInfos) {
            return this.gamePlayHolder.updateInfos(uid, playInfos);
        }
    }

    static class SessionLogMDC {

        private final Map<String, String> contextMap;
        private final boolean logVerbose;

        public SessionLogMDC(Map<String, String> contextMap, boolean logVerbose) {
            this.contextMap = contextMap;
            this.logVerbose = logVerbose;
        }
    }
}
