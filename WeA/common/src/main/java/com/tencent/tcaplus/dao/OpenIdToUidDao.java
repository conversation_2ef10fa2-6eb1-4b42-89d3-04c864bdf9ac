package com.tencent.tcaplus.dao;

import com.tencent.cache.Cache;
import com.tencent.cache.CacheUtil;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.tcaplus.TcaplusErrorCode;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.wea.protocol.common.AccountState;
import com.tencent.wea.protocol.common.AccountTransferState;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDb.OpenIdToUid;
import com.tencent.wea.xlsRes.keywords.PlatformID;
import com.tencent.wea.xlsRes.keywords.TconndApiAccount;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nullable;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class OpenIdToUidDao {

    private static final Logger LOGGER = LogManager.getLogger(OpenIdToUidDao.class);

    public static Optional<OpenIdToUid> getDataByOpenIdAndPlatId(String openId, int platId) {
        OpenIdToUid.Builder otb = OpenIdToUid.newBuilder()
                .setOpenid(openId).setPlatId(platId);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetReq(otb).send();
        if (rsp.isOK()) {
            return Optional.of((OpenIdToUid) rsp.firstRecordData().msg);
        }
        return Optional.empty();
    }

    @Nullable
    public static TcaplusDb.OpenIdToUid getTcaplusOpenIdToUid(String openId, int platId) {
        Optional<OpenIdToUid> newOpenIdToUid = getDataByOpenIdAndPlatId(openId, platId);
        if (newOpenIdToUid.isPresent()) {
            return newOpenIdToUid.get();
        } else {
            return null;
        }
    }

    public static long getUidByOpenIdAndPlatId(String openId, int platId) {
        Optional<OpenIdToUid> newOpenIdToUid = getDataByOpenIdAndPlatId(openId, platId);
        return newOpenIdToUid.map(OpenIdToUid::getUid).orElse(0L);
    }

    public static OpenIdToUid.Builder getPlayerInfoFromOpenId(String openId, int platId) {
        OpenIdToUid.Builder dbOpenIdToUid = OpenIdToUid.newBuilder();
        dbOpenIdToUid.setOpenid(openId);
        dbOpenIdToUid.setPlatId(platId);

        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetReq(dbOpenIdToUid).send();
        if (rsp.isOK()) {
            return (TcaplusDb.OpenIdToUid.Builder) rsp.firstRecordData().msg.toBuilder();
        }
        return null;
    }

    public static long getUidByOpenIdAndPlatIdByCheck(String openId, int platId) {
        Optional<OpenIdToUid> newOpenIdToUid = getDataByOpenIdAndPlatId(openId, platId);
        if (newOpenIdToUid.isPresent()) {
            if (newOpenIdToUid.get().getIsRegisterFini() == 0) {
                LOGGER.error("player {}|{} register not fini", openId, platId);
            } else if (newOpenIdToUid.get().getDeleted() != AccountState.ACCOUNT_STATE_CANCEL_DELETE_VALUE) {
                return newOpenIdToUid.get().getUid();
            }
        }
        return 0L;
    }

    /**
     * 获取openIdToUid数据(仅限idipsvr使用, 会检查账号是否处于注册流程中/是否处于注销状态)
     * @param openId openId
     * @param platId platId
     * @return OpenIdToUid数据
     */
    public static OpenIdToUid getOpenIdToUidForIdipByCheck(String openId, int platId) {

        Optional<OpenIdToUid> newOpenIdToUid = getDataByOpenIdAndPlatId(openId, platId);
        if (!newOpenIdToUid.isPresent()) {
            return null;
        }

        // 账号处于注册流程中, 返回null
        if (newOpenIdToUid.get().getIsRegisterFini() == 0) {
            LOGGER.error("player register not fini, openId:{}, platId:{}", openId, platId);
            return null;
        }

        // 账号处于注销状态中, 返回null
        if (newOpenIdToUid.get().getDeleted() == AccountState.ACCOUNT_STATE_CANCEL_DELETE_VALUE) {
            return null;
        }

        return newOpenIdToUid.get();
    }

    public static List<OpenIdToUidInfo> getUidByOpenId(String openId) {
       
        List<OpenIdToUidInfo> list = new ArrayList<>();
        if(openId.equals("876543212345678L")) { //预热的虚拟玩家不写数据库，这里就特别处理下
            list.add(new OpenIdToUidInfo("876543212345678L", 2, 876543212345678L,
                TconndApiAccount.TCONND_ITOP_CHANNEL_QQ_VALUE, 0, 0, 0));
            return list;
        }
        TcaplusDb.OpenIdToUid.Builder record = TcaplusDb.OpenIdToUid.newBuilder();
        record.setOpenid(openId);
        TcaplusManager.TcaplusRsp queryRsp = TcaplusUtil.newGetByPartKeyReq(record).send();
        if (queryRsp.isOKIgnoreRecordNotExist()) {
            for (TcaplusManager.TcaplusRecordGroup dataGroup : queryRsp.getRspDataList()) {
                for (TcaplusManager.TcaplusRecordData<?> rData : dataGroup.getRecordList()) {
                    TcaplusDb.OpenIdToUid data = (TcaplusDb.OpenIdToUid) rData.msg;
                    list.add(new OpenIdToUidInfo(data.getOpenid(), data.getPlatId(), data.getUid(),
                            data.getAccountType(), data.getDeleted(), data.getTransferStatus(), data.getTransferInterval()));
                }
            }
        } else {
            NKErrorCode.UnknownError
                    .throwError("OpenIdToUidDao::getUidByOpenId db error, ret:{}, {}", queryRsp.getResult(), record);
        }
        return list;
    }

    public static void updateLoginTime(String openid, int platId, long uid) {
        // 更新登录时间
        OpenIdToUid.Builder otb = OpenIdToUid.newBuilder()
                .setOpenid(openid)
                .setPlatId(platId)
                .setUid(uid)
                .setZoneid(Framework.getInstance().getZoneId())
                .setLoginTime(Framework.currentTimeMillis());
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newUpdateReq(otb).send();
        if (!rsp.isOK()) {
            LOGGER.error("updateLoginTime fail, result: {}", rsp.getResult());
        }
    }

    public static boolean insertOpenidUid(String openid, int platId, long uid, int accountType,
            boolean isRegisterFini, long creatorId) {
        OpenIdToUid.Builder otb = OpenIdToUid.newBuilder()
                .setOpenid(openid)
                .setPlatId(platId)
                .setUid(uid)
                .setCreateTime(Framework.currentTimeMillis())
                .setZoneid(Framework.getInstance().getZoneId())
                .setIsRegisterFini(isRegisterFini ? 1 : 0)
                .setAccountType(accountType)
                .setCreatorId(creatorId);

        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newInsertReq(otb).send();
        if (!rsp.isOK()) {
            LOGGER.error("insertOpenidUid fail:{},{},{},{}", openid, platId, uid, creatorId);
        }
        return rsp.isOK();
    }

    public static boolean updateOpenIdUidMapRegisterFini(String openid, int platId, long uid) {
        OpenIdToUid.Builder otb = OpenIdToUid.newBuilder()
                .setOpenid(openid)
                .setPlatId(platId)
                .setUid(uid)
                .setZoneid(Framework.getInstance().getZoneId())
                .setIsRegisterFini(1);

        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newUpdateReq(otb).send();

        return rsp.isOK();
    }

    private static void batchGetUid(TcaplusManager.TcaplusReq batchGetReq, Map<String, List<OpenIdToUidInfo>> result) {
        TcaplusManager.TcaplusRsp batchGetRsp = TcaplusManager.getInstance()
                .tcaplusSend(batchGetReq);
        if (batchGetRsp.isOK()) {
            for (TcaplusManager.TcaplusRecordGroup rspData : batchGetRsp.getRspDataList()) {
                for (TcaplusManager.TcaplusRecordData<?> rData : rspData.getRecordList()) {
                    OpenIdToUid data = (OpenIdToUid) rData.msg;
                    if (data.getIsRegisterFini() == 0) {
                        LOGGER.error("player {} register not fini", data.getOpenid());
                        continue;
                    }
                    OpenIdToUidInfo info = new OpenIdToUidInfo(data.getOpenid(), data.getPlatId(), data.getUid(),
                            data.getAccountType(), data.getDeleted());
                    result.computeIfAbsent(data.getOpenid(), k -> new ArrayList<>()).add(info);
                }
            }
        }
    }

    public static Map<String, List<OpenIdToUidInfo>> batchGetUidListFromOpenID(Collection<String> openIdList,
            Collection<Integer> platIdList) {
        TcaplusManager.TcaplusReq batchGetReq = null;
        Map<String, List<OpenIdToUidInfo>> result = new LinkedHashMap<>();
        for (String openid : openIdList) {
            for (int platId : platIdList) {
                OpenIdToUid.Builder otb = OpenIdToUid.newBuilder();
                otb.setOpenid(openid).setPlatId(platId);
                if (batchGetReq == null) {
                    batchGetReq = TcaplusUtil.newBatchGetReq(otb);
                } else {
                    batchGetReq.addRecord(otb);
                }
                if (batchGetReq.getRecordCount() >= 1000) {
                    batchGetUid(batchGetReq, result);
                    batchGetReq = null;
                }
            }
        }
        if (batchGetReq != null) {
            batchGetUid(batchGetReq, result);
        }

        return result;
    }

    /**
     * 批量通过openid查询uid, ios和安卓都查
     *
     * @param openIdList openid列表
     * @return {@link Map}<{@link String}, {@link Long}>
     */
    public static Map<String, List<OpenIdToUidInfo>> batchGetUidListFromOpenID(Collection<String> openIdList) {
        TcaplusManager.TcaplusReq batchGetReq = null;
        Map<String, List<OpenIdToUidInfo>> result = new LinkedHashMap<>();
        for (String openid : openIdList) {
            OpenIdToUid.Builder otb = OpenIdToUid.newBuilder();
            otb.setOpenid(openid).setPlatId(PlatformID.IOS_VALUE);
            if (batchGetReq == null) {
                batchGetReq = TcaplusUtil.newBatchGetReq(otb);
            } else {
                batchGetReq.addRecord(otb);
            }
            otb.setPlatId(PlatformID.Android_VALUE);
            batchGetReq.addRecord(otb);

            if (batchGetReq.getRecordCount() >= 1000) {
                batchGetUid(batchGetReq, result);
                batchGetReq = null;
            }
        }

        if (batchGetReq != null) {
            batchGetUid(batchGetReq, result);
        }

        return result;
    }

    public static void checkRegisterLimit(String openid, int platId) throws NKRuntimeException {
        // step1. 判断db中是否已经存在超过上限的uid角色
        OpenIdToUid.Builder otb = OpenIdToUid.newBuilder();
        otb.setOpenid(openid).setPlatId(platId);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetReq(otb).send();

        if (rsp.isOK()) {
            NKErrorCode.UidNumExceedMax.throwError("UidNumExceedMax");
        } else if (rsp.getResult() != TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST) {
            NKErrorCode.UnknownError.throwError(String.format("get db fail %d", rsp.getResult().getValue()));
        }
    }

    public static void deleteOpenidToUid(String openId, int platId, long uid) {
        OpenIdToUid.Builder delNewOpenIdToUidReq = OpenIdToUid.newBuilder().setOpenid(openId).setPlatId(platId)
                .setUid(uid);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newDeleteReq(delNewOpenIdToUidReq).send();
        if (!rsp.isOKIgnoreRecordNotExist()) {
            rsp.getResult().throwError();
        }
    }

    /**
     * 基于openId获取对应的uid和platId映射关系
     *
     * @param openId
     * @param platId
     * @return
     */
    public static NKPair<Long, Integer> getUidAndPlatIdByOpenId(String openId, int platId) {
        List<OpenIdToUid> list = getDataListByOpenId(openId);
        if (list == null) {
            return null;
        }
        for (OpenIdToUid openIdToUid : list) {
            if (openIdToUid.getPlatId() == platId) {
                NKPair<Long, Integer> data = new NKPair<>(openIdToUid.getUid(), openIdToUid.getPlatId());
                return data;
            }
        }
        return null;
    }

    /**
     * 基于openId获取OpenIdToUid表数据列表
     *
     * @param openId
     * @return
     */
    public static List<OpenIdToUid> getDataListByOpenId(String openId) {
        OpenIdToUid.Builder otb = OpenIdToUid.newBuilder().setOpenid(openId);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetByPartKeyReq(otb).send();
        if (rsp.isOKIgnoreRecordNotExist()) {
            List<OpenIdToUid> list = new ArrayList<>();
            for (TcaplusManager.TcaplusRecordGroup dataGroup : rsp.getRspDataList()) {
                for (TcaplusManager.TcaplusRecordData<?> rData : dataGroup.getRecordList()) {
                    list.add((TcaplusDb.OpenIdToUid) rData.msg);
                }
            }
            return list;
        }
        return null;
    }

    public static void updateTransferStatus(String openId, int platId, long uid, int transferStatus) {
        TcaplusDb.OpenIdToUid.Builder builder = TcaplusDb.OpenIdToUid.newBuilder()
                .setOpenid(openId)
                .setPlatId(platId)
                .setUid(uid)
                .setTransferStatus(transferStatus);

        if (transferStatus == AccountTransferState.ACCOUNT_STATE_TRANSFER_SUCCESS_VALUE) {
            builder.setTransferInterval(Framework.currentTimeMillis()); // 最终成功才记录转区时间
        }

        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newUpdateReq(builder).send();

        if (!rsp.isOK() && rsp.getResult().getValue() != TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST.getValue()) {
            LOGGER.error("updateTransferStatus fail, result: {}", rsp.getResult());
            NKErrorCode.DBOpFailed.throwError("OpenidToUid table update failed, uid:{}, result:{}",
                    uid, rsp.getResult().getValue());
        }

        // 转区状态变化主动删除 账号转区状态中 key
        LOGGER.info("redis move key AccountInTransferStatus uid:{}", uid);
        boolean isDeleted = Cache.delKeyIfValueEqualTo(CacheUtil.AccountInTransferStatus.name(), String.valueOf(uid));
        if (!isDeleted) {
            LOGGER.error("redis move AccountInTransferStatus key fail, uid:{}, openid:{}, platId:{},transferStatus:{}",
                    uid, openId, platId, transferStatus);
        }
    }

    public static class OpenIdToUidInfo {

        private final String openId;
        private final int platId;   // 0:ios，1:安卓
        private final long uid;
        private int lastSvrId;
        private int accountType;
        private int accountState;   // 账号状态, 复用db表里的deleted字段
        private int transferStatus; //（0-未转区，1-转区中，2-转区成功，3-转区失败）
        private long transferInterval; //上次转区时间

        public OpenIdToUidInfo(String openId, int platId, long uid, int accountType, int accountState) {
            this.openId = openId;
            this.platId = platId;
            this.uid = uid;
            this.accountType = accountType;
            this.accountState = accountState;
        }

        public OpenIdToUidInfo(String openId, int platId, long uid, int accountType, int accountState, int transferStatus, long transferInterval) {
            this.openId = openId;
            this.platId = platId;
            this.uid = uid;
            this.accountType = accountType;
            this.accountState = accountState;
            this.transferStatus = transferStatus;
            this.transferInterval = transferInterval;
        }

        public String getOpenId() {
            return openId;
        }

        public int getPlatId() {
            return platId;
        }

        public long getUid() {
            return uid;
        }

        public int getLastSvrId() {
            return lastSvrId;
        }

        public void setLastSvrId(int lastSvrId) {
            this.lastSvrId = lastSvrId;
        }

        public int getAccountType() {
            return accountType;
        }

        public int getAccountState() {
            return accountState;
        }

        public int getTransferStatus() {
            return transferStatus;
        }

        public long getTransferInterval() {
            return transferInterval;
        }
    }
}
