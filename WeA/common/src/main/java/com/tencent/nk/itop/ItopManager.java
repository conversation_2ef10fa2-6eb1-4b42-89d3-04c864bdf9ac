package com.tencent.nk.itop;

import com.tencent.coHttp.CoHttpClient;
import com.tencent.coHttp.CoHttpClientBuilder;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.wea.xlsRes.keywords.PlatformID;
import com.tencent.wea.xlsRes.keywords.TconndApiAccount;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2019/9/29
 */

public class ItopManager {

    private static final Logger LOGGER = LogManager.getLogger(ItopManager.class);
    private static final BasicResponseHandler BASIC_RESPONSE_HANDLER = new BasicResponseHandler();
    private CoHttpClient client = null;

    public ItopManager() {
        int concurrencyLevel = 100;
        client = CoHttpClientBuilder.
                create(2). // use 2 io threads
                setMaxConnPerRoute(concurrencyLevel).
                setMaxConnTotal(concurrencyLevel).build();
    }

    public static ItopManager getInstance() {
        return InstanceHolder.instance;
    }

    /**
     * 获得用户个人信息
     *
     * @param platId
     * @param accountType
     * @param openID
     * @param token
     * @return
     * @throws NKCheckedException
     */
    public UserInfo getProfile(int platId, int accountType, String openID, String token) throws NKCheckedException {
        JSONObject jsonObject = doProcess(platId, accountType, openID, token, "/v2/profile/userinfo",
                MonitorId.attr_msdk_userinfo_req);
        return new UserInfo(
                jsonObject.getString("user_name")
                , jsonObject.getInt("gender")
                , jsonObject.getString("picture_url"));
    }

    public JSONObject GetFriendList(int platId, int accountType, String openID, String token)
            throws NKCheckedException {
        return doProcess(platId, accountType, openID, token, "/v2/friend/friend_list",
                MonitorId.attr_msdk_friend_list_req);
    }

    public JSONObject LoginAuthentication(int platId, int accountType, String openID, String token)
            throws NKCheckedException {
        return doProcess(platId, accountType, openID, token, "/v2/auth/verify_login",
                MonitorId.attr_msdk_verify_login_req);
    }

    public JSONObject GetFreeFlowInfo(int platId, int accountType, String openID, String token)
            throws NKCheckedException {
        return doProcess(platId, accountType, openID, token, "/v2/freeflow/isfree",
                MonitorId.attr_msdk_get_free_flow_info_req);
    }

    public JSONObject SendARKShareReq(int platId, int accountType, String openID, String token, String fOpenID, String extra)
            throws NKCheckedException,UnsupportedEncodingException {
        return doProcess(platId, accountType, openID, token, fOpenID, extra, "/v2/friend/ark_share", MonitorId.attr_msdk_ark_share_req);
    }

    /**
     * 基于MSDK组件的gopenid获取平台openid
     * @param platId
     * @param accountType
     * @param openID
     * @return
     * @throws NKCheckedException
     */
    public JSONObject GetOpenidFromGOpenid(int platId, int accountType, String openID)
            throws NKCheckedException {
        return doProcess(platId, accountType, openID, null, "/v2/profile/openid2uid_notoken",
                MonitorId.attr_msdk_openid2uid_notoken_req);
    }

    /**
     * 基于MSDK组件的gopenid获取平台openid
     * @param platId
     * @param accountType
     * @param openID
     * @param token
     * @return
     * @throws NKCheckedException
     */
    public JSONObject GetOpenidFromGOpenid(int platId, int accountType, String openID, String token)
            throws NKCheckedException {
        return doProcess(platId, accountType, openID, token, "/v2/profile/openid2uid",
                MonitorId.attr_msdk_openid2uid_req);
    }

    /**
     * 基于平台openid获取MSDK组件的gopenid
     * @param platId
     * @param accountType
     * @param openID
     * @return
     * @throws NKCheckedException
     */
    public JSONObject GetGOpenidFromOpenid(int platId, int accountType, String openID) throws NKCheckedException {
        return doProcessByUid(platId, accountType, openID, "/v2/profile/uid2openid_notoken",
                MonitorId.attr_msdk_uid2openid_notoken_req);
    }

    public JSONObject SendQQAchievementReport(int platId, int accountType, String openID, String token, JSONArray body)
            throws NKCheckedException {
        return doProcess(platId, accountType, openID, token, body, "/v2/acvm/report", MonitorId.attr_msdk_report_req);
    }

    public List<BindInfo> GetBindInfo(int platId, int accountType, String openID, String token)
            throws NKCheckedException {
        // 下面这段逻辑和晓波沟通可以暂时通过开关关掉，海外用户使用，目前不需要
        boolean needGetBindInfo = PropertyFileReader.getRealTimeBooleanItem("need_get_bind_info", false);
        if (!needGetBindInfo) return new ArrayList<>();

        String bodyString = makeBodyString(openID, token);;
        JSONObject jsonObject = doProcess(platId, accountType, bodyString, "/v2/profile/get_bind_info",
                MonitorId.attr_msdk_get_bind_info_req);
        JSONArray bindInfoArray = null;
        List<BindInfo> retList = new ArrayList<>();
        if (jsonObject.has("bind_list")) {
            bindInfoArray = jsonObject.getJSONArray("bind_list");
        }
        if (bindInfoArray == null || bindInfoArray.length() == 0) {
            LOGGER.info("get_bind_info is null, platId:{}, openId:{}, accountType:{}", platId, openID, accountType);
            return retList;
        }
        for (Object ob : bindInfoArray) {
            int channelId = ((JSONObject) ob).getInt("channelid");
            String userName = ((JSONObject) ob).getString("user_name");
            String pictureUrl = ((JSONObject) ob).getString("picture_url");
            int timestamp = ((JSONObject) ob).getInt("timestamp");
            BindInfo tmp = new BindInfo(convertChannelId(channelId).getNumber(), userName, pictureUrl, timestamp);
            retList.add(tmp);
        }
        return retList;
    }

    public JSONObject UnBind(int platId, int accountType, String openID, long uid, String token)
            throws NKCheckedException {
        String bodyString = makeUnBindBodyString(openID, token, accountType, uid);
        return doProcess(platId, accountType, bodyString, "/v2/auth/unbind", MonitorId.attr_msdk_unbind_req);
    }

    private String makeUnBindBodyString(String openID, String token, int accountType, long uid) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("openid", openID);
        jsonObject.put("token", token);
        jsonObject.put("unbind_channelid", convertAccountType(accountType).getNumber());
        jsonObject.put("uid", Long.toString(uid));
        return jsonObject.toString();
    }

    private JSONObject doProcess(int platId, int accountType, String bodyString, String path, MonitorId monitorId)
            throws NKCheckedException {
        String url = makeUrl(platId, accountType, path, bodyString);
        return doHttpPost(url, bodyString, monitorId);
    }

    private JSONObject doProcess(int platId, int accountType, String openID, String token, String path,
            MonitorId monitorId)
            throws NKCheckedException {
        String bodyString = makeBodyString(openID, token);
        String url = makeUrl(platId, accountType, path, bodyString);
        return doHttpPost(url, bodyString, monitorId);
    }

    private JSONObject doProcess(int platId, int accountType, String openID, String token, JSONArray body, String path,
            MonitorId monitorId)
            throws NKCheckedException {
        String bodyString = makeBodyString(openID, token, body);
        String url = makeUrl(platId, accountType, path, bodyString);
        return doHttpPost(url, bodyString, monitorId);
    }

    private JSONObject doProcess(int platId, int accountType, String openID, String token, String fOpenID, String extra,
            String path, MonitorId monitorId)
            throws NKCheckedException,UnsupportedEncodingException {
        String bodyString = makeBodyString(openID, token, fOpenID, extra);
        String url = makeUrl(platId, accountType, path, bodyString);
        return doHttpPost(url, bodyString, monitorId);
    }

    private JSONObject doProcessByUid(int platId, int accountType, String uid, String path, MonitorId monitorId)
            throws NKCheckedException {
        String bodyString = makeBodyString(uid);
        String url = makeUrl(platId, accountType, path, bodyString);
        return doHttpPost(url, bodyString, monitorId);
    }

    private JSONObject doHttpPost(String url, String bodyString, MonitorId monitorId)
            throws NKCheckedException {
        Monitor.getInstance().add.total(MonitorId.attr_msdk_http_req, 1);
        if (monitorId != null) {
            Monitor.getInstance().add.total(monitorId, 1);
        }
        LOGGER.debug(url);
        LOGGER.debug(bodyString);
        JSONObject result = null;
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setEntity(new StringEntity(bodyString));
            String response = client.execute(httpPost, BASIC_RESPONSE_HANDLER);
            result = new JSONObject(response);
            if (!result.has("ret") || 0 != result.getInt("ret")) {
                Monitor.getInstance().add.fail(MonitorId.attr_msdk_http_req, 1);
                if (monitorId != null) {
                    Monitor.getInstance().add.fail(monitorId, 1);
                }
                if (result.has("ret") && 1270 == result.getInt("ret")) {
                    // 玩家未授权“获取好友关系”
                    LOGGER.info("api unauthorized, request={} body={} response={}", url,
                            StringUtils.replace(bodyString, "\"", ""),
                            StringUtils.replace(response, "\"", ""));
                    return result;
                }
                throw new NKCheckedException(
                        NKStringFormater.format("itop request failed: request={} body={} response={}",
                                url, StringUtils.replace(bodyString, "\"", ""),
                                StringUtils.replace(response, "\"", "")));
            }
            LOGGER.debug("itop request ok: request={} body={} response={}", url, bodyString, response);
        } catch (Exception e) {
            LOGGER.warn("itop request failed: request={} body={}  e:{}", url, bodyString, e.getMessage());
            Monitor.getInstance().add.fail(MonitorId.attr_msdk_http_req, 1);
            if (monitorId != null) {
                Monitor.getInstance().add.fail(monitorId, 1);
            }
            throw new NKCheckedException(NKStringFormater.format("itop request failed: request={} body={}  e:{}",
                    url, StringUtils.replace(bodyString, "\"", ""), e.getMessage()));
        }
        return result;
    }
    private String makeBodyString(String openID, String token) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("openid", openID);
        if (token != null) {
            jsonObject.put("token", token);
        }
        return jsonObject.toString();
    }

    private String makeBodyString(String openID, String token, JSONArray body) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("openid", openID);
        if (token != null) {
            jsonObject.put("token", token);
        }
        jsonObject.put("achievements", body);
        return jsonObject.toString();
    }

    private String makeBodyString(String uid) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("uid", uid);
        return jsonObject.toString();
    }

    private String makeBodyString(String openID, String token, String fOpenID, String extra)
            throws UnsupportedEncodingException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("openid", openID);
        jsonObject.put("token", token);
        jsonObject.put("fopenid", fOpenID);
        jsonObject.put("extra", URLEncoder.encode(extra, StandardCharsets.UTF_8));
        return jsonObject.toString();
    }

    private String makeUrl(int platId, int accountType, String path, String bodyString) {
        StringBuffer sBuffer = new StringBuffer(path);
        sBuffer.append("?")
                .append("channelid=").append(convertAccountType(accountType).getNumber())
                .append("&gameid=").append(PropertyFileReader.getRealTimeIntItem("itop_game_appid", 10001))
                .append("&os=").append(convertPlatType(platId).ordinal())
                .append("&source=").append(1)
                .append("&ts=").append(Framework.currentTimeMillis());

        StringBuffer md5TmpBuffer = new StringBuffer(sBuffer);
        md5TmpBuffer.append(bodyString).append(PropertyFileReader.getRealTimeItem("itop_sign_key", ""));
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            sBuffer.append("&sig=").append(Hex.encodeHexString(md.digest(md5TmpBuffer.toString().getBytes("utf-8"))));
        } catch (Exception e) {
            LOGGER.error("md5 failed {}", md5TmpBuffer, e);
        }

        return PropertyFileReader.getRealTimeItem("itop_service_url", "") + sBuffer;
    }

    private ITOPOSType convertPlatType(int platType) {
        switch (platType) {
            case PlatformID.IOS_VALUE:
                return ITOPOSType.ITOPOSTYPE_IOS;
            case PlatformID.Android_VALUE:
                return ITOPOSType.ITOPOSTYPE_Android;
            default:
                return ITOPOSType.ITOPOSTYPE_Windows;
        }
    }

    private ITOPChannelIDType convertAccountType(int accountType) {
        if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_GUEST_VALUE) {
            return ITOPChannelIDType.ITOPCHANNELIDTYPE_Guest;
        } else if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_QQ_VALUE) {
            return ITOPChannelIDType.ITOPCHANNELIDTYPE_MQQ;
        } else if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_WX_VALUE) {
            return ITOPChannelIDType.ITOPCHANNELIDTYPE_WeChat;
        } else if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_FACEBOOK_VALUE) {
            return ITOPChannelIDType.ITOPCHANNELIDTYPE_Facebook;
        } else if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_GAME_CENTER_VALUE) {
            return ITOPChannelIDType.ITOPCHANNELIDTYPE_GameCenter;
        } else if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_GOOGLE_VALUE) {
            return ITOPChannelIDType.ITOPCHANNELIDTYPE_GooglePlay;
        } else if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_TWITTER_VALUE) {
            return ITOPChannelIDType.ITOPCHANNELIDTYPE_Twitter;
        } else if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_APPLE_VALUE) {
            return ITOPChannelIDType.ITOPCHANNELIDTYPE_Apple;
        } else if (accountType == TconndApiAccount.TCONND_ITOP_CHANNEL_STEAM_VALUE) {
            return ITOPChannelIDType.ITOPCHANNELIDTYPE_Steam;
        } else {
            LOGGER.error("unknown accountType {}", accountType);
            return ITOPChannelIDType.ITOPCHANNELIDTYPE_Unknown;
        }
    }

    private TconndApiAccount convertChannelId(int channelId) {
        if (channelId == ITOPChannelIDType.ITOPCHANNELIDTYPE_Guest.getNumber()) {
            return TconndApiAccount.TCONND_ITOP_CHANNEL_GUEST;
        } else if (channelId == ITOPChannelIDType.ITOPCHANNELIDTYPE_MQQ.getNumber()) {
            return TconndApiAccount.TCONND_ITOP_CHANNEL_QQ;
        } else if (channelId == ITOPChannelIDType.ITOPCHANNELIDTYPE_WeChat.getNumber()) {
            return TconndApiAccount.TCONND_ITOP_CHANNEL_WX;
        } else if (channelId == ITOPChannelIDType.ITOPCHANNELIDTYPE_Facebook.getNumber()) {
            return TconndApiAccount.TCONND_ITOP_CHANNEL_FACEBOOK;
        } else if (channelId == ITOPChannelIDType.ITOPCHANNELIDTYPE_GameCenter.getNumber()) {
            return TconndApiAccount.TCONND_ITOP_CHANNEL_GAME_CENTER;
        } else if (channelId == ITOPChannelIDType.ITOPCHANNELIDTYPE_GooglePlay.getNumber()) {
            return TconndApiAccount.TCONND_ITOP_CHANNEL_GOOGLE;
        } else if (channelId == ITOPChannelIDType.ITOPCHANNELIDTYPE_Twitter.getNumber()) {
            return TconndApiAccount.TCONND_ITOP_CHANNEL_TWITTER;
        } else if (channelId == ITOPChannelIDType.ITOPCHANNELIDTYPE_Apple.getNumber()) {
            return TconndApiAccount.TCONND_ITOP_CHANNEL_APPLE;
        } else if (channelId == ITOPChannelIDType.ITOPCHANNELIDTYPE_Steam.getNumber()) {
            return TconndApiAccount.TCONND_ITOP_CHANNEL_STEAM;
        } else {
            LOGGER.error("unknown channelId {}", channelId);
            return TconndApiAccount.TCONND_ITOP_CHANNEL_Unknown;
        }
    }

    private enum ITOPOSType {
        ITOPOSTYPE_Unknown,
        ITOPOSTYPE_Android,
        ITOPOSTYPE_IOS,
        ITOPOSTYPE_WEB,
        ITOPOSTYPE_Linux,
        ITOPOSTYPE_Windows,
    }

    private enum ITOPChannelIDType {
        ITOPCHANNELIDTYPE_Unknown(0),
        ITOPCHANNELIDTYPE_WeChat(1),
        ITOPCHANNELIDTYPE_MQQ(2),
        ITOPCHANNELIDTYPE_Guest(3),
        ITOPCHANNELIDTYPE_Facebook(4),
        ITOPCHANNELIDTYPE_GameCenter(5),
        ITOPCHANNELIDTYPE_GooglePlay(6),
        ITOPCHANNELIDTYPE_SelfAccount(7),
        ITOPCHANNELIDTYPE_Twitter(9),
        ITOPCHANNELIDTYPE_Apple(15),
        ITOPCHANNELIDTYPE_Steam(21),
        ;

        private Integer id;
        ITOPChannelIDType(Integer id) {
            this.id = id;
        }

        public Integer getNumber() {
            return this.id;
        }
    }

    /**
     * 个人信息
     *
     * @ref https://docs.itop.qq.com/v5/zh-CN/Server/getuserinfo.html
     */
    public static class UserInfo {

        private final String userName;
        private final int gender;   // 0:未定义，1:男， 2:女
        private final String pictureUrl;

        public UserInfo(String userName, int gender, String pictureUrl) {
            this.userName = userName;
            this.gender = gender;
            this.pictureUrl = pictureUrl;
        }

        public String getUserName() {
            return userName;
        }

        public int getGender() {
            return gender;
        }

        public String getPictureUrl() {
            return pictureUrl;
        }
    }

    public static class BindInfo {

        public final int accountType; // 账号类型
        public final String userName; //用户名或昵称
        public final String pictureUrl; //头像URL地址
        public final int timestamp;  //与该账号绑定的Unix时间戳

        public BindInfo( int accountType, String userName, String pictureUrl, int timestamp) {
            this.accountType = accountType;
            this.userName = userName;
            this.pictureUrl = pictureUrl;
            this.timestamp = timestamp;
        }

    }

    private static class InstanceHolder {

        public static ItopManager instance = new ItopManager();
    }
}