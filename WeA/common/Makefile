WORK_DIR=${PWD}
CPP_SRC_PATH=${WORK_DIR}/src/main/cpp
TSF4G_ROOT=${WORK_DIR}/src/main/cpp/tsf4g
APOLLO_ROOT=${WORK_DIR}/src/main/cpp/apolloservice
APOLLO_VOICE_ROOT=${WORK_DIR}/src/main/cpp/apollo_voice_all
TSS_ROOT=${WORK_DIR}/src/main/cpp/tss
PROTOBUF_ROOT=${WORK_DIR}/src/main/cpp/protobuf
SO_OUTPUT_DIR=${WORK_DIR}/build/libs
JPS_ROOT=${WORK_DIR}/src/main/cpp/jps
RECAST_ROOT=${WORK_DIR}/src/main/cpp/recastnavigation_20200806
RECAST_LIB_PATH=${RECAST_ROOT}/lib/Release
RECAST_POLYREF64_ROOT=${WORK_DIR}/src/main/cpp/recastnavigation_20210125
RECAST_POLYREF64_LIB_PATH=${RECAST_POLYREF64_ROOT}/Release/lib64
RECAST_POLYREF64_INCLUDE_PATH=${RECAST_POLYREF64_ROOT}/Release/include/recastnavigation
TBUSPP_ROOT=${WORK_DIR}/src/main/cpp/tbuspp
TBUS2_ROOT=${WORK_DIR}/src/main/cpp/tbus2/api
TCONND_ROOT=${WORK_DIR}/src/main/cpp/tconnd
FASTLZ_ROOT=${WORK_DIR}/src/main/cpp/fastlz
CXX_FLAG=-Wl,--no-whole-archive -fPIC -shared -ggdb -fno-omit-frame-pointer -O2  -std=c++0x -Wall -Wno-conversion-null -Wno-unused-variable -Wno-pointer-arith -D_GLIBCXX_USE_CXX11_ABI=0

.PHONY: all clean
all:${SO_OUTPUT_DIR}/libapolloapi_jni.so ${SO_OUTPUT_DIR}/libapolloaudience_jni.so ${SO_OUTPUT_DIR}/libtmempoolapi_jni.so ${SO_OUTPUT_DIR}/libhexagonapi_jni.so ${SO_OUTPUT_DIR}/libjpsapi_jni.so ${SO_OUTPUT_DIR}/libtbusppapi_jni.so ${SO_OUTPUT_DIR}/libtbuspp2api_jni.so ${SO_OUTPUT_DIR}/libtgcpapi_jni.so
#####################################################################################################

${CPP_SRC_PATH}/com_tencent_apollo_ApolloApi.h: ${PWD}/src/main/java/com/tencent/apollo/ApolloApi.java
	javac -classpath build/classes/java/main:build/libs/*:../timiutil/build/libs/* -h ${CPP_SRC_PATH} ${PWD}/src/main/java/com/tencent/apollo/ApolloApi.java

${SO_OUTPUT_DIR}/libapolloapi_jni.so: ${CPP_SRC_PATH}/com_tencent_apollo_ApolloApi.h ${CPP_SRC_PATH}/ApolloApi.cpp ${CPP_SRC_PATH}/VoipListener.h ${CPP_SRC_PATH}/SnsListener.h ${CPP_SRC_PATH}/QQMusicListener.h ${CPP_SRC_PATH}/TrankRspListener.h ${CPP_SRC_PATH}/TopNextRspListener.h ${CPP_SRC_PATH}/LbsRspListener.h ${CPP_SRC_PATH}/FaceToFaceRspListener.h ${CPP_SRC_PATH}/PayListener.h ${CPP_SRC_PATH}/LargeRoomEventHandler.h ${CPP_SRC_PATH}/ApolloCommon.h ${CPP_SRC_PATH}/VoipListener.cpp ${CPP_SRC_PATH}/SnsListener.cpp ${CPP_SRC_PATH}/QQMusicListener.cpp ${CPP_SRC_PATH}/TrankRspListener.cpp ${CPP_SRC_PATH}/TopNextRspListener.cpp ${CPP_SRC_PATH}/LbsRspListener.cpp ${CPP_SRC_PATH}/FaceToFaceRspListener.cpp ${CPP_SRC_PATH}/PayListener.cpp ${CPP_SRC_PATH}/LargeRoomEventHandler.cpp ${CPP_SRC_PATH}/ApolloCommon.cpp
	g++ -I${JAVA_HOME}/include \
		-I${JAVA_HOME}/include/linux \
		-I${APOLLO_ROOT}/include \
		-I${APOLLO_VOICE_ROOT}/include \
		-I${TSF4G_ROOT}/include \
		-Wl,--whole-archive -L${APOLLO_ROOT}/lib -lapolloservice -lpebble_codec  -L${APOLLO_VOICE_ROOT}/lib -llarge_room_static ${TSF4G_ROOT}/lib/libtsf4g.a \
		-L${CPP_SRC_PATH}/tsf4g/lib -lreadline -lncurses -lscew -lexpat -ltdr_comm \
		${CXX_FLAG} -o ${SO_OUTPUT_DIR}/libapolloapi_jni.so \
		${CPP_SRC_PATH}/TrankRspListener.cpp ${CPP_SRC_PATH}/TopNextRspListener.cpp ${CPP_SRC_PATH}/LbsRspListener.cpp ${CPP_SRC_PATH}/FaceToFaceRspListener.cpp ${CPP_SRC_PATH}/VoipListener.cpp ${CPP_SRC_PATH}/SnsListener.cpp ${CPP_SRC_PATH}/QQMusicListener.cpp ${CPP_SRC_PATH}/PayListener.cpp ${CPP_SRC_PATH}/LargeRoomEventHandler.cpp ${CPP_SRC_PATH}/ApolloCommon.cpp \
		${CPP_SRC_PATH}/ApolloApi.cpp

#####################################################################################################
${CPP_SRC_PATH}/com_tencent_apollo_ApolloAudience.h: ${PWD}/src/main/java/com/tencent/apollo/ApolloAudience.java
	javac -classpath build/classes/java/main:build/libs/*:../timiutil/build/libs/* -h ${CPP_SRC_PATH} ${PWD}/src/main/java/com/tencent/apollo/ApolloAudience.java

${SO_OUTPUT_DIR}/libapolloaudience_jni.so: ${CPP_SRC_PATH}/com_tencent_apollo_ApolloAudience.h ${CPP_SRC_PATH}/ApolloAudience.cpp ${CPP_SRC_PATH}/ApolloCommon.h ${CPP_SRC_PATH}/ApolloCommon.cpp
	g++ -I${JAVA_HOME}/include \
		-I${JAVA_HOME}/include/linux \
		-I${APOLLO_ROOT}/include \
		-I${APOLLO_VOICE_ROOT}/include \
		-I${TSF4G_ROOT}/include \
		-Wl,--whole-archive -L${APOLLO_VOICE_ROOT}/lib -lapollo_audience ${TSF4G_ROOT}/lib/libtsf4g.a \
		-L${CPP_SRC_PATH}/tsf4g/lib -lreadline -lncurses -lscew -lexpat \
		${CXX_FLAG} -o ${SO_OUTPUT_DIR}/libapolloaudience_jni.so \
        ${CPP_SRC_PATH}/ApolloCommon.cpp \
		${CPP_SRC_PATH}/ApolloAudience.cpp

########################################################################################################################
${CPP_SRC_PATH}/com_tencent_tbuspp_TbusppApi.h: ${PWD}/src/main/java/com/tencent/tbuspp/TbusppApi.java
	javac -classpath build/classes/java/main:build/libs/*:../timiutil/build/libs/* -h ${CPP_SRC_PATH} ${PWD}/src/main/java/com/tencent/tbuspp/TbusppApi.java
	# touch ${CPP_SRC_PATH}/TbusppApi.h

${SO_OUTPUT_DIR}/libtbusppapi_jni.so: ${CPP_SRC_PATH}/com_tencent_tbuspp_TbusppApi.h ${CPP_SRC_PATH}/TbusppApi.cpp
	g++ -I${JAVA_HOME}/include \
    		-I${JAVA_HOME}/include/linux \
    		-I${TBUSPP_ROOT}/include \
    		-I${TCONND_ROOT}/include/apps \
    		-I${TCONND_ROOT}/libtconn/include/tbase \
    		-I${TCONND_ROOT}/libtconn/include/tbase/tdr_cpp_comm \
    		-Wl,--whole-archive -z muldefs \
    		${TCONND_ROOT}/lib/libtconnapi.a \
    		${TCONND_ROOT}/lib/libtconnapi_tbuspp.a \
    		${TCONND_ROOT}/libtconn/lib/tbase/libtsf4g.a \
    		${TCONND_ROOT}/libtconn/lib/tbase/libtdr.a \
    		${TCONND_ROOT}/libtconn/lib/tbase/libtdr_comm.a \
    		${TCONND_ROOT}/libtconn/lib/tbase/libtdr_xml.a \
    		-L${TCONND_ROOT}/libtconn/lib/tbase -lreadline -lncurses -lscew -lexpat \
    		-L${TBUSPP_ROOT}/lib64_release -lbase_agent_client_api \
    		${CXX_FLAG} -o ${SO_OUTPUT_DIR}/libtbusppapi_jni.so \
    		${CPP_SRC_PATH}/TbusppApi.cpp

########################################################################################################################
${CPP_SRC_PATH}/com_tencent_tbuspp_Tbuspp2Api.h: ${PWD}/src/main/java/com/tencent/tbuspp/Tbuspp2Api.java
	javac -classpath build/classes/java/main:build/libs/*:../timiutil/build/libs/* -h ${CPP_SRC_PATH} ${PWD}/src/main/java/com/tencent/tbuspp/Tbuspp2Api.java
	# touch ${CPP_SRC_PATH}/Tbuspp2Api.h

${SO_OUTPUT_DIR}/libtbuspp2api_jni.so: ${CPP_SRC_PATH}/com_tencent_tbuspp_Tbuspp2Api.h ${CPP_SRC_PATH}/Tbuspp2Api.cpp
	g++ -I${JAVA_HOME}/include \
    		-I${JAVA_HOME}/include/linux \
    		-I${TBUS2_ROOT}/inc \
    		-I${TCONND_ROOT}/include/apps \
    		-I${TCONND_ROOT}/libtconn/include/tbase \
    		-I${TCONND_ROOT}/libtconn/include/tbase/tdr_cpp_comm \
    		-Wl,--whole-archive -z muldefs \
    		${TCONND_ROOT}/libtconn/lib/tbase/libtsf4g.a \
    		${TCONND_ROOT}/libtconn/lib/tbase/libtdr.a \
    		${TCONND_ROOT}/libtconn/lib/tbase/libtdr_comm.a \
    		${TCONND_ROOT}/libtconn/lib/tbase/libtdr_xml.a \
    		-L${TCONND_ROOT}/libtconn/lib/tbase -lreadline -lncurses -lscew -lexpat \
    		-L${TBUS2_ROOT}/lib -ltbus2_api \
    		${CXX_FLAG} -o ${SO_OUTPUT_DIR}/libtbuspp2api_jni.so \
    		${CPP_SRC_PATH}/Tbuspp2Api.cpp

########################################################################################################################

${CPP_SRC_PATH}/com_tencent_tmempool_TmempoolApi.h: ${PWD}/src/main/java/com/tencent/tmempool/TmempoolApi.java
	javac -classpath build/classes/java/main:build/libs/*:../timiutil/build/libs/* -h ${CPP_SRC_PATH} ${PWD}/src/main/java/com/tencent/tmempool/TmempoolApi.java
	# touch ${CPP_SRC_PATH}/TmempoolApi.h

${SO_OUTPUT_DIR}/libtmempoolapi_jni.so: ${CPP_SRC_PATH}/com_tencent_tmempool_TmempoolApi.h ${CPP_SRC_PATH}/TmempoolApi.cpp
	g++ -I${JAVA_HOME}/include \
		-I${JAVA_HOME}/include/linux \
		-I${TSF4G_ROOT}/include \
		-Wl,--whole-archive ${TSF4G_ROOT}/lib/libtsf4g.a \
		-L${TSF4G_ROOT}/lib -lreadline -lncurses -lscew -lexpat -lrt \
		${CXX_FLAG} -o ${SO_OUTPUT_DIR}/libtmempoolapi_jni.so \
		${CPP_SRC_PATH}/TmempoolApi.cpp
########################################################################################################################
${CPP_SRC_PATH}/com_tencent_hexagon_HexagonApi.h: ${PWD}/src/main/java/com/tencent/hexagon/HexagonApi.java
	javac -classpath build/classes/java/main:build/libs/*:../timiutil/build/libs/* -h ${CPP_SRC_PATH} ${PWD}/src/main/java/com/tencent/hexagon/HexagonApi.java
	# touch ${CPP_SRC_PATH}/HexagonApi.h

${SO_OUTPUT_DIR}/libhexagonapi_jni.so: ${CPP_SRC_PATH}/com_tencent_hexagon_HexagonApi.h ${CPP_SRC_PATH}/HexagonApi.cpp
	g++ -I${JAVA_HOME}/include \
		-I${JAVA_HOME}/include/linux \
		-I${TSF4G_ROOT}/include \
		-Wl,--whole-archive ${TSF4G_ROOT}/lib/libtsf4g.a \
		-L${TSF4G_ROOT}/lib -lreadline -lncurses -lscew -lexpat -lrt \
		${CXX_FLAG} -o ${SO_OUTPUT_DIR}/libhexagonapi_jni.so \
		${CPP_SRC_PATH}/HexagonApi.cpp
########################################################################################################################
${CPP_SRC_PATH}/com_tencent_jps_JpsApi.h: ${PWD}/src/main/java/com/tencent/jps/JpsApi.java
	javac -classpath build/classes/java/main:build/libs/*:../timiutil/build/libs/* -h ${CPP_SRC_PATH} ${PWD}/src/main/java/com/tencent/jps/JpsApi.java
	# touch ${CPP_SRC_PATH}/JpsApi.h

${SO_OUTPUT_DIR}/libjpsapi_jni.so: ${CPP_SRC_PATH}/com_tencent_jps_JpsApi.h ${CPP_SRC_PATH}/JpsApi.cpp ${JPS_ROOT}/lib/libwarthog.a
	g++ -I${JAVA_HOME}/include \
		-I${JAVA_HOME}/include/linux \
		-I${TSF4G_ROOT}/include \
		-I${JPS_ROOT}/domains \
		-I${JPS_ROOT}/experimental \
		-I${JPS_ROOT}/heuristics \
		-I${JPS_ROOT}/jps \
		-I${JPS_ROOT}/search \
		-I${JPS_ROOT}/util \
		-Wl,--whole-archive ${TSF4G_ROOT}/lib/libtsf4g.a \
        -L${TSF4G_ROOT}/lib -lreadline -lncurses -lscew -lexpat -lrt \
		-Wl,--whole-archive ${JPS_ROOT}/lib/libwarthog.a \
		${CXX_FLAG} -o ${SO_OUTPUT_DIR}/libjpsapi_jni.so \
		${CPP_SRC_PATH}/JpsApi.cpp
########################################################################################################################
# ${CPP_SRC_PATH}/NavMeshApi.h: ${PWD}/src/main/java/com/tencent/navmesh/NavMeshApi.java
# 	javah -jni -classpath build/classes/java/main -o ${CPP_SRC_PATH}/NavMeshApi.h com.tencent.navmesh.NavMeshApi

# ${SO_OUTPUT_DIR}/libnavmeshapi_jni.so: ${CPP_SRC_PATH}/NavMeshApi.h ${CPP_SRC_PATH}/NavMeshApi.cpp ${FASTLZ_ROOT}/libfastlz.a ${RECAST_LIB_PATH}/libDetour.a ${RECAST_LIB_PATH}/libDetourTileCache.a
# 	g++ -I${JAVA_HOME}/include \
# 		-I${JAVA_HOME}/include/linux \
# 		-I${TSF4G_ROOT}/include \
# 		-I${FASTLZ_ROOT}/ \
# 		-I${RECAST_ROOT}/include \
# 		-Wl,--whole-archive ${TSF4G_ROOT}/lib/libtsf4g.a \
#         -L${TSF4G_ROOT}/lib -L${FASTLZ_ROOT} -lreadline -lncurses -lscew -lexpat -lrt -lfastlz\
# 		-Wl,--whole-archive ${RECAST_LIB_PATH}/libDetour.a ${RECAST_LIB_PATH}/libDetourTileCache.a\
# 		${CXX_FLAG} -o ${SO_OUTPUT_DIR}/libnavmeshapi_jni.so \
# 		${CPP_SRC_PATH}/NavMeshApi.cpp

# ${CPP_SRC_PATH}/navmeshapi_v2/NavMeshApiV2.h: ${PWD}/src/main/java/com/tencent/navmesh/NavMeshApiV2.java
# 	javah -jni -classpath build/classes/java/main -o ${CPP_SRC_PATH}/navmeshapi_v2/NavMeshApiV2.h com.tencent.navmesh.NavMeshApiV2

# ${SO_OUTPUT_DIR}/libnavmeshapi_v2_jni.so: ${CPP_SRC_PATH}/navmeshapi_v2/NavMeshApiV2.h \
# 		${CPP_SRC_PATH}/navmeshapi_v2/NavMeshApiV2.cpp \
# 		${CPP_SRC_PATH}/navmeshapi_v2/MeshInstance.cpp \
# 		${CPP_SRC_PATH}/navmeshapi_v2/MeshInstance.h \
# 		${CPP_SRC_PATH}/navmeshapi_v2/MeshQuery.cpp \
# 		${CPP_SRC_PATH}/navmeshapi_v2/MeshQuery.h \
# 		${CPP_SRC_PATH}/navmeshapi_v2/tlog_utils.cpp \
# 		${CPP_SRC_PATH}/navmeshapi_v2/tlog_utils.h \
# 		${FASTLZ_ROOT}/libfastlz.a ${RECAST_POLYREF64_LIB_PATH}/libDetour.a ${RECAST_POLYREF64_LIB_PATH}/libDetourTileCache.a
# 	g++ -I${JAVA_HOME}/include \
# 		-I${JAVA_HOME}/include/linux \
# 		-I${TSF4G_ROOT}/include \
# 		-I${FASTLZ_ROOT}/ \
# 		-I${RECAST_POLYREF64_INCLUDE_PATH} \
# 		-I${CPP_SRC_PATH}/navmeshapi_v2 \
# 		-Wl,--whole-archive ${TSF4G_ROOT}/lib/libtsf4g.a \
#         -L${TSF4G_ROOT}/lib -L${FASTLZ_ROOT} -lreadline -lncurses -lscew -lexpat -lrt -lfastlz\
# 		-Wl,--whole-archive ${RECAST_POLYREF64_LIB_PATH}/libDetour.a \
# 		-Wl,--whole-archive ${RECAST_POLYREF64_LIB_PATH}/libDetourTileCache.a \
# 		-DTLOG_WITH_MUTEX -lpthread \
# 		${CXX_FLAG} -o ${SO_OUTPUT_DIR}/libnavmeshapi_v2_jni.so \
# 		${CPP_SRC_PATH}/navmeshapi_v2/NavMeshApiV2.cpp \
# 		${CPP_SRC_PATH}/navmeshapi_v2/NavMeshApiV2.h \
# 		${CPP_SRC_PATH}/navmeshapi_v2/MeshInstance.cpp \
# 		${CPP_SRC_PATH}/navmeshapi_v2/MeshInstance.h \
# 		${CPP_SRC_PATH}/navmeshapi_v2/MeshQuery.cpp \
# 		${CPP_SRC_PATH}/navmeshapi_v2/MeshQuery.h \
# 		${CPP_SRC_PATH}/navmeshapi_v2/tlog_utils.cpp \
# 		${CPP_SRC_PATH}/navmeshapi_v2/tlog_utils.h
########################################################################################################################
${CPP_SRC_PATH}/com_tencent_tgcp_TgcpApi.h: ${PWD}/src/main/java/com/tencent/tgcp/TgcpApi.java
	javac -classpath build/classes/java/main:build/libs/*:../timiutil/build/libs/* -h ${CPP_SRC_PATH} ${PWD}/src/main/java/com/tencent/tgcp/TgcpApi.java
	# touch ${CPP_SRC_PATH}/TgcpApi.h

${SO_OUTPUT_DIR}/libtgcpapi_jni.so: ${CPP_SRC_PATH}/com_tencent_tgcp_TgcpApi.h ${CPP_SRC_PATH}/TgcpApi.cpp
	g++ -I${JAVA_HOME}/include \
    		-I${JAVA_HOME}/include/linux \
    		-I${TCONND_ROOT}/include/apps \
    		-I${TCONND_ROOT}/libtconn/include/tbase \
        -I${TCONND_ROOT}/libtconn/include/tbase/tdr_cpp_comm \
    		-Wl,--whole-archive -z muldefs \
    		${TCONND_ROOT}/lib/libtgcpapi/libtgcpapi.a \
    		${TCONND_ROOT}/libtconn/lib/tbase/libtsf4g.a \
        ${TCONND_ROOT}/libtconn/lib/tbase/libtdr.a \
        ${TCONND_ROOT}/libtconn/lib/tbase/libtdr_comm.a \
        ${TCONND_ROOT}/libtconn/lib/tbase/libtdr_xml.a \
        -L${CPP_SRC_PATH}/tsf4g/lib -lreadline -lncurses -lscew -lexpat -ltdr_comm \
    		-L${TCONND_ROOT}/lib/libtgcpapi \
    		${CXX_FLAG} -o ${SO_OUTPUT_DIR}/libtgcpapi_jni.so \
    		${CPP_SRC_PATH}/TgcpApi.cpp

########################################################################################################################
clean:
	$(RM) ${CPP_SRC_PATH}/com_tencent_apollo_ApolloApi.h
	$(RM) ${SO_OUTPUT_DIR}/libapolloapi_jni.so
	$(RM) ${CPP_SRC_PATH}/com_tencent_apollo_ApolloAudience.h
	$(RM) ${SO_OUTPUT_DIR}/libapolloaudience_jni.so
	$(RM) ${CPP_SRC_PATH}/com_tencent_tbuspp_TbusppApi.h
	$(RM) ${SO_OUTPUT_DIR}/libtbusppapi_jni.so
	$(RM) ${CPP_SRC_PATH}/com_tencent_tbuspp_Tbuspp2Api.h
	$(RM) ${SO_OUTPUT_DIR}/libtbuspp2api_jni.so
	$(RM) ${CPP_SRC_PATH}/com_tencent_tmempool_TmempoolApi.h
	$(RM) ${SO_OUTPUT_DIR}/libtmempoolapi_jni.so
	$(RM) ${CPP_SRC_PATH}/com_tencent_hexagon_HexagonApi.h
	$(RM) ${SO_OUTPUT_DIR}/libhexagonapi_jni.so
	$(RM) ${CPP_SRC_PATH}/com_tencent_jps_JpsApi.h
	$(RM) ${SO_OUTPUT_DIR}/libjpsapi_jni.so

	# $(RM) ${SO_OUTPUT_DIR}/libnavmeshapi_jni.so
	# $(RM) ${SO_OUTPUT_DIR}/libnavmeshapi_v2_jni.so

	$(RM) ${CPP_SRC_PATH}/com_tencent_tgcp_TgcpApi.h
	$(RM) ${SO_OUTPUT_DIR}/libtgcpapi_jni.so
