package com.tencent.timiutil.monitor;

import static com.tencent.timiutil.monitor.MonitorModule.Activity;
import static com.tencent.timiutil.monitor.MonitorModule.ActivitySvr;
import static com.tencent.timiutil.monitor.MonitorModule.AigcSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Ams;
import static com.tencent.timiutil.monitor.MonitorModule.Apollo;
import static com.tencent.timiutil.monitor.MonitorModule.ArenaSvr;
import static com.tencent.timiutil.monitor.MonitorModule.AsyncSvr;
import static com.tencent.timiutil.monitor.MonitorModule.AttrOp;
import static com.tencent.timiutil.monitor.MonitorModule.Battle;
import static com.tencent.timiutil.monitor.MonitorModule.BattleSettlement;
import static com.tencent.timiutil.monitor.MonitorModule.BattleSvr;
import static com.tencent.timiutil.monitor.MonitorModule.C2sReq;
import static com.tencent.timiutil.monitor.MonitorModule.CacheSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Chatsvr;
import static com.tencent.timiutil.monitor.MonitorModule.ClubSvr;
import static com.tencent.timiutil.monitor.MonitorModule.CocGameScene;
import static com.tencent.timiutil.monitor.MonitorModule.CocPlayer;
import static com.tencent.timiutil.monitor.MonitorModule.CocSvr;
import static com.tencent.timiutil.monitor.MonitorModule.ConfigSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Coroutine;
import static com.tencent.timiutil.monitor.MonitorModule.Cronjob;
import static com.tencent.timiutil.monitor.MonitorModule.CrossRouteSvr;
import static com.tencent.timiutil.monitor.MonitorModule.DanMuSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Dirsvr;
import static com.tencent.timiutil.monitor.MonitorModule.DsCommonMonitor;
import static com.tencent.timiutil.monitor.MonitorModule.DsDbSvr;
import static com.tencent.timiutil.monitor.MonitorModule.DsIdcLoad;
import static com.tencent.timiutil.monitor.MonitorModule.DsRecoverySvr;
import static com.tencent.timiutil.monitor.MonitorModule.DscAllocSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Event;
import static com.tencent.timiutil.monitor.MonitorModule.FarmSvr;
import static com.tencent.timiutil.monitor.MonitorModule.GameApi;
import static com.tencent.timiutil.monitor.MonitorModule.Gamesvr;
import static com.tencent.timiutil.monitor.MonitorModule.Gray;
import static com.tencent.timiutil.monitor.MonitorModule.HotRes;
import static com.tencent.timiutil.monitor.MonitorModule.IRpc;
import static com.tencent.timiutil.monitor.MonitorModule.IdipSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Jvm;
import static com.tencent.timiutil.monitor.MonitorModule.LBS;
import static com.tencent.timiutil.monitor.MonitorModule.LevelChooser;
import static com.tencent.timiutil.monitor.MonitorModule.Lobby;
import static com.tencent.timiutil.monitor.MonitorModule.LobbyAlloc;
import static com.tencent.timiutil.monitor.MonitorModule.Lock;
import static com.tencent.timiutil.monitor.MonitorModule.Logger;
import static com.tencent.timiutil.monitor.MonitorModule.Mail;
import static com.tencent.timiutil.monitor.MonitorModule.MailBox;
import static com.tencent.timiutil.monitor.MonitorModule.MatchAllocSvr;
import static com.tencent.timiutil.monitor.MonitorModule.MatchSvr;
import static com.tencent.timiutil.monitor.MonitorModule.MetaAiRpc;
import static com.tencent.timiutil.monitor.MonitorModule.Midas;
import static com.tencent.timiutil.monitor.MonitorModule.Module;
import static com.tencent.timiutil.monitor.MonitorModule.OutputControl;
import static com.tencent.timiutil.monitor.MonitorModule.OutputSpecialControl;
import static com.tencent.timiutil.monitor.MonitorModule.PlayMode;
import static com.tencent.timiutil.monitor.MonitorModule.Player;
import static com.tencent.timiutil.monitor.MonitorModule.Pod;
import static com.tencent.timiutil.monitor.MonitorModule.Proxy;
import static com.tencent.timiutil.monitor.MonitorModule.Pulsar;
import static com.tencent.timiutil.monitor.MonitorModule.Qualifying;
import static com.tencent.timiutil.monitor.MonitorModule.Raffle;
import static com.tencent.timiutil.monitor.MonitorModule.Rank;
import static com.tencent.timiutil.monitor.MonitorModule.Redis;
import static com.tencent.timiutil.monitor.MonitorModule.Relation;
import static com.tencent.timiutil.monitor.MonitorModule.RoomSvr;
import static com.tencent.timiutil.monitor.MonitorModule.RouteSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Rpc;
import static com.tencent.timiutil.monitor.MonitorModule.SampleRoomSvr;
import static com.tencent.timiutil.monitor.MonitorModule.SceneSvr;
import static com.tencent.timiutil.monitor.MonitorModule.SeqSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Shard;
import static com.tencent.timiutil.monitor.MonitorModule.SocialPerformance;
import static com.tencent.timiutil.monitor.MonitorModule.SpAccountSvr;
import static com.tencent.timiutil.monitor.MonitorModule.SpDbSvr;
import static com.tencent.timiutil.monitor.MonitorModule.StarPGroupSvr;
import static com.tencent.timiutil.monitor.MonitorModule.StarPGuildSvr;
import static com.tencent.timiutil.monitor.MonitorModule.StarPMailSvr;
import static com.tencent.timiutil.monitor.MonitorModule.StarPRoomSvr;
import static com.tencent.timiutil.monitor.MonitorModule.StarPSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Statistics;
import static com.tencent.timiutil.monitor.MonitorModule.StreamSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Task;
import static com.tencent.timiutil.monitor.MonitorModule.Tbus;
import static com.tencent.timiutil.monitor.MonitorModule.Tbuspp2Error;
import static com.tencent.timiutil.monitor.MonitorModule.TbusppError;
import static com.tencent.timiutil.monitor.MonitorModule.TbusppOnlineInstance;
import static com.tencent.timiutil.monitor.MonitorModule.Tcaplus;
import static com.tencent.timiutil.monitor.MonitorModule.Tconnd;
import static com.tencent.timiutil.monitor.MonitorModule.TimeRefresh;
import static com.tencent.timiutil.monitor.MonitorModule.Timer;
import static com.tencent.timiutil.monitor.MonitorModule.Tss;
import static com.tencent.timiutil.monitor.MonitorModule.TycoonSvr;
import static com.tencent.timiutil.monitor.MonitorModule.UgcAppSvr;
import static com.tencent.timiutil.monitor.MonitorModule.UgcDataStoreSvr;
import static com.tencent.timiutil.monitor.MonitorModule.UgcOMD;
import static com.tencent.timiutil.monitor.MonitorModule.UgcPlatSvr;
import static com.tencent.timiutil.monitor.MonitorModule.UgcSvr;
import static com.tencent.timiutil.monitor.MonitorModule.Uic;
import static com.tencent.timiutil.monitor.MonitorModule.XiaoWoSvr;
import static com.tencent.timiutil.monitor.MonitorModule.platform;
import static com.tencent.timiutil.monitor.MonitorModule.thread;

import com.tencent.wea.namednointenum.WeANamedNoIntEnum;
import com.tencent.wea.namednointenum.WeANamedNoIntEnumRegistry;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class MonitorId extends WeANamedNoIntEnum {
    // ############### monitor id 定义格式如下 ######################
    // monitor_id, 模块, 描述, 中文标题, 具体例子参看Chatsvr的monitorId定义
    // public static final MonitorId id = new MonitorId(module); // description @title
    public static final MonitorId attr_framework_proc = new MonitorId(Module);

    public static final MonitorId attr_localservice_run_job_proc = new MonitorId(Module);

    public static final MonitorId attr_modules_prepare_register_exception = new MonitorId(Module); // Player模块异常 注册预处理时发生异常 @模块注册预处理异常

    public static final MonitorId attr_modules_on_register_exception = new MonitorId(Module); // Player模块异常 注册时发生异常 @模块注册异常

    public static final MonitorId attr_modules_after_register_exception = new MonitorId(Module); // Player模块异常 注册后处理发生异常 @模块注册后处理异常

    public static final MonitorId attr_modules_prepare_load_exception = new MonitorId(Module); // Player模块异常 加载预处理发生异常 @模块加载预处理异常

    public static final MonitorId attr_modules_on_load_exception = new MonitorId(Module); // Player模块异常 加载发生异常 @模块加载异常

    public static final MonitorId attr_modules_after_load_exception = new MonitorId(Module); // Player模块异常 加载后处理发生异常 @模块加载后处理异常

    public static final MonitorId attr_modules_prepare_login_exception = new MonitorId(Module); // Player模块异常 登录预处理发生异常 @模块登录预处理异常

    public static final MonitorId attr_modules_on_login_exception = new MonitorId(Module); // Player模块异常 登录发生异常 @模块登录异常

    public static final MonitorId attr_modules_after_login_exception = new MonitorId(Module); // Player模块异常 登录后处理发生异常 @模块登录后处理异常

    public static final MonitorId attr_modules_login_finish_exception = new MonitorId(Module); // Player模块异常 登录后处理发生异常 @模块登录后处理异常

    public static final MonitorId attr_modules_on_midnight_exception = new MonitorId(Module); // Player模块异常 零点事件发生异常 @模块零点事件异常

    public static final MonitorId attr_modules_on_week_start_exception = new MonitorId(Module); // Player模块异常 周起始事件发生异常 @模块周起始事件异常

    public static final MonitorId attr_modules_on_logout_exception = new MonitorId(Module); // Player模块异常 登出发生异常 @模块登出事件异常

    public static final MonitorId attr_modules_on_everyhour_exception = new MonitorId(Module); // Player模块异常 整点事件发生异常 @模块整点事件异常

    public static final MonitorId attr_modules_after_refresh_abtest_exception = new MonitorId(Module); // Player模块异常 ABTest刷新后异常 @ABTest刷新后异常

    public static final MonitorId attr_modules_cache_lock_get = new MonitorId(Module); // 尝试获得分布式锁

    public static final MonitorId attr_modules_cache_lock_preempt = new MonitorId(Module);    // 分布式锁发起抢锁

    public static final MonitorId attr_modules_cache_lock_renewal = new MonitorId(Module);    // 分布式锁尝试续锁

    public static final MonitorId attr_modules_cache_lock_preempt_server_version = new MonitorId(Module); // 服务迁移结果监控
    public static final MonitorId attr_modules_tlog_queue_size = new MonitorId(Module);    // 当前Tlog存量队列数量

    public static final MonitorId attr_modules_tlog_send_req = new MonitorId(Module);      // Tlog发送数量
    public static final MonitorId attr_modules_stochastic_seq_failed = new MonitorId(Module);      // 随机序列生成失败

    public static final MonitorId attr_current_online = new MonitorId(Gamesvr);         // 实时在线人数 @实时在线人数
    public static final MonitorId attr_current_online_new = new MonitorId(Gamesvr);         // 实时在线人数(new) @实时在线人数(new)
    public static final MonitorId attr_online_harmony = new MonitorId(Gamesvr);         // 实时在线人数harmoney登录 @harmoney登实时在线人数
    public static final MonitorId attr_online_android = new MonitorId(Gamesvr);         // 实时在线人数Android登录 @android实时在线人数

    public static final MonitorId attr_online_ios = new MonitorId(Gamesvr);             // 实时在线人数IOS登录 @ios实时在线人数

    public static final MonitorId attr_current_lobby = new MonitorId(Gamesvr);          // 实时在大厅人数 @实时大厅人数

    public static final MonitorId attr_current_battle = new MonitorId(Gamesvr);         // 实时在战斗(DS)人数 @实时战斗人数
    public static final MonitorId attr_current_xiaowo = new MonitorId(Gamesvr);          // 实时在家园人数 @实时家园人数
    public static final MonitorId attr_current_farm = new MonitorId(Gamesvr);          // 实时在农场人数 @实时农场人数
    public static final MonitorId attr_current_house = new MonitorId(Gamesvr);          // 实时在小屋人数 @实时小屋人数
    public static final MonitorId attr_current_cook = new MonitorId(Gamesvr);          // 实时在餐厅人数 @实时餐厅人数

    public static final MonitorId attr_login_total = new MonitorId(Gamesvr);            // 累计登录次数 @累计登录

    public static final MonitorId attr_login_slow = new MonitorId(Gamesvr);             // 累计慢登录(登录耗时超过2s)次数 @慢登录

    public static final MonitorId attr_login_slow_500_2000 = new MonitorId(Gamesvr);             // 累计慢登录(登录耗时超过500ms - 2s)次数 @慢登录

    public static final MonitorId attr_after_login_slow = new MonitorId(Gamesvr);             // 累计afterLogin长耗时(耗时超过2s)次数 @afterLogin长耗时

    public static final MonitorId attr_after_login_slow_500_2000 = new MonitorId(Gamesvr);             // 累计afterLogin长耗时(耗时超过500ms - 2s)次数 @afterLogin长耗时

    public static final MonitorId attr_login_concurrent = new MonitorId(Gamesvr); // 并发登录 @并发登录

    public static final MonitorId attr_login_stat_total_count = new MonitorId(Gamesvr); //累计登录统计次数
    public static final MonitorId attr_login_stat_concurrent_login = new MonitorId(Gamesvr); //game级别的并发登录量
    public static final MonitorId attr_login_stat_avg_cost = new MonitorId(Gamesvr); //累计登录平均耗时

    public static final MonitorId attr_login_stat_max_cost = new MonitorId(Gamesvr); //登录最大耗时
    public static final MonitorId attr_login_stat_timeout_count = new MonitorId(Gamesvr); //登录超时统计
    public static final MonitorId attr_login_stat_step_exception_count = new MonitorId(Gamesvr); //登录步骤异常
    public static final MonitorId attr_login_pay_token_null_count = new MonitorId(Gamesvr); //登录payToken为空
    public static final MonitorId attr_login_pay_pf_null_count = new MonitorId(Gamesvr); //登录pf为空

    public static final MonitorId attr_player_dau = new MonitorId(Gamesvr);             // dau @dau

    public static final MonitorId attr_player_cache_count = new MonitorId(Gamesvr);             // 缓存的玩家数 @缓存的玩家数

    public static final MonitorId attr_player_reg_daily = new MonitorId(Gamesvr);       // 当日新注册用户 @日新增

    public static final MonitorId attr_login_concurrent_limit = new MonitorId(Gamesvr); // 累计并发登录次数 @并发登录

    public static final MonitorId attr_reg_total = new MonitorId(Gamesvr);              // 累计注册 @累计注册

    public static final MonitorId attr_kick_player = new MonitorId(Gamesvr);            // 累计踢玩家下线次数(包括同玩家并发登录) @踢线

    public static final MonitorId attr_reg_ban = new MonitorId(Gamesvr);                // 累计禁止注册 @禁止注册

    public static final MonitorId attr_threads_shut_down_timeout_curr = new MonitorId(Gamesvr); // 当前线程池退出超时 @当前线程池退出超时

    public static final MonitorId attr_threads_shut_down_timeout_total = new MonitorId(Gamesvr); // 线程池退出超时计数 @累计线程池退出超时

    public static final MonitorId attr_login_no_permission = new MonitorId(Gamesvr);    // 禁止登录期间累计登录次数(非白名单) @禁止登录

    public static final MonitorId attr_login_ip_not_allow = new MonitorId(Gamesvr);     // ip黑名单累计尝试登录次数 @禁止登录(ip)

    public static final MonitorId attr_login_city_ip_not_allow = new MonitorId(Gamesvr);     // 城市ip黑名单禁止次数 @禁止登录(ip)

    public static final MonitorId attr_login_city_ip_not_allow_first_login = new MonitorId(Gamesvr);     // 城市ip黑名单禁止次数 @省市ip禁止登录(login)

    public static final MonitorId attr_login_city_ip_not_allow_first_register = new MonitorId(Gamesvr);     // 城市ip黑名单禁止次数 @省市ip禁止登录(register)

    public static final MonitorId attr_login_ban = new MonitorId(Gamesvr); // 禁止登录 @禁止登录

   

    public static final MonitorId attr_account_cancel_req = new MonitorId(Gamesvr);   // 账号注销请求
    public static final MonitorId attr_account_cancel_process = new MonitorId(Gamesvr);   // 账号注销处理

    public static final MonitorId attr_account_transfer_req = new MonitorId(Gamesvr);   // 账号转区请求
    public static final MonitorId attr_account_transfer_process = new MonitorId(Gamesvr);   // 账号转区处理
    public static final MonitorId attr_account_transfer_rollback = new MonitorId(Gamesvr);  // 账号转区回滚
    public static final MonitorId attr_account_transfer_dispatch_wait_cost_ms = new MonitorId(Gamesvr);   // 账号转区调度等待耗时
    public static final MonitorId attr_account_transfer_process_cost_ms = new MonitorId(Gamesvr);   // 账号转区处理耗时
    public static final MonitorId attr_account_transfer_kick_player = new MonitorId(Gamesvr);   // 账号转区处理 业务模块踢除玩家
    public static final MonitorId attr_account_transfer_init_backup = new MonitorId(Gamesvr);   // 账号转区处理 业务模块踢除玩家
    public static final MonitorId attr_account_transfer_user_data = new MonitorId(Gamesvr);   // 账号转区处理 用户基础数据修改
    public static final MonitorId attr_account_transfer_relation_data = new MonitorId(Gamesvr);   // 账号转区处理 关系数据修改
    public static final MonitorId attr_account_transfer_business_data = new MonitorId(Gamesvr);   // 账号转区处理 业务模块数据修改
    public static final MonitorId attr_account_transfer_other_data = new MonitorId(Gamesvr);   // 账号转区处理 其他数据修改
    public static final MonitorId attr_account_transfer_main_data = new MonitorId(Gamesvr);   // 账号转区处理 其他数据修改

    public static final MonitorId attr_login_queue_upload_online_failed = new MonitorId(Gamesvr);

    public static final MonitorId attr_login_queue_upload_online_success = new MonitorId(Gamesvr);

    public static final MonitorId attr_login_queue_illegal_token_passed = new MonitorId(Gamesvr);

    public static final MonitorId attr_login_queue_illegal_token_auth_failed = new MonitorId(Gamesvr);

    public static final MonitorId attr_login_queue_illegal_token_expired = new MonitorId(Gamesvr);
    public static final MonitorId attr_expire_item_conf_err = new MonitorId(Gamesvr);

    public static final MonitorId attr_gamesvr_get_free_flow_req_times = new MonitorId(Gamesvr); // gamesvr 拉取免流信息次数
    public static final MonitorId attr_login_use_cache = new MonitorId(Gamesvr); // 登陆使用player缓存 @登陆使用player缓存

    public static final MonitorId attr_reg_insert_player_fail = new MonitorId(Gamesvr); // 注册插入player失败 @注册插入player失败

    public static final MonitorId attr_reg_insert_openid2uid_fail = new MonitorId(Gamesvr); // 注册插入OpenidToUid表失败 @注册插入OpenidToUid表失败
    public static final MonitorId attr_ab_test_refresh = new MonitorId(Gamesvr);            // 刷新玩家abTest分组 @刷新玩家abTest分组
    public static final MonitorId attr_box_openapi_invoke = new MonitorId(Gamesvr);            // BOXOPENAPI调用 @BOXOPENAPI调用
    public static final MonitorId attr_battle_settlement_cost_time_ms = new MonitorId(Gamesvr); // 结算消耗事件 @结算消耗事件

    public static final MonitorId attr_prefab_query = new MonitorId(Gamesvr); // 预注册信息拉取次数 @预注册信息拉取次数
    public static final MonitorId attr_prefab_check_nickname = new MonitorId(Gamesvr); // 和预注册信息查重名次数 @和预注册信息查重名次数

    public static final MonitorId attr_anti_addict_instruct_exe = new MonitorId(Gamesvr); // 防沉迷指令执行次数 @防沉迷指令执行次数

    public static final MonitorId attr_concert_new_ticket_cnt = new MonitorId(Gamesvr);       // 演唱会首次分配纪念票数量
    public static final MonitorId attr_concert_change_ticket_cnt = new MonitorId(Gamesvr);    // 演唱会修改纪念票数量
    public static final MonitorId attr_concert_ticket_id_allocate_success_cnt = new MonitorId(Gamesvr);   // 演唱会纪念票ID分配成功数量
    public static final MonitorId attr_concert_ticket_id_allocate_failed_cnt = new MonitorId(
            Gamesvr);    // 演唱会纪念票ID分配失败数量

    public static final MonitorId attr_concert_star_popularity_write_cnt = new MonitorId(
            Gamesvr);    // 演唱会明星热度数据操作redis数量

    public static final MonitorId attr_concert_star_popularity_request_write_cnt = new MonitorId(
            Gamesvr);  // 演唱会明星热度值请求修改次数

    public static final MonitorId attr_community_channel_hot_topic_write_cnt = new MonitorId(
            Gamesvr);    // 社区频道热门话题数据操作redis数量
    public static final MonitorId attr_community_channel_hot_topic_load_cnt = new MonitorId(
            Gamesvr);    // 社区频道热门话题数据操作redis数量
    public static final MonitorId attr_community_channel_hot_topic_delete_cnt = new MonitorId(
            Gamesvr);    // 社区频道热门话题数据操作redis数量
    public static final MonitorId attr_community_channel_hot_topic_thumb_up_cnt = new MonitorId(
            Gamesvr);    // 社区频道热门话题数据操作redis数量
    public static final MonitorId attr_online_earning_ad_sec_hard_violation = new MonitorId(Gamesvr);    // 网赚广告触发硬警报
    public static final MonitorId attr_online_earning_ad_sec_soft_violation = new MonitorId(Gamesvr);    // 网赚广告触发软警报
    public static final MonitorId attr_online_earning_op_record_append = new MonitorId(Gamesvr);    // 网赚操作记录写入速度

    public static final MonitorId attr_task_load_time = new MonitorId(Gamesvr);          // 任务加载耗时(ms) @任务加载耗时
    public static final MonitorId attr_task_type_count = new MonitorId(Gamesvr);        // 各类型任务数量 @类型任务数
    public static final MonitorId attr_task_module_time = new MonitorId(Gamesvr);        // 各模块加载耗时(ms) @模块加载耗时

    public static final MonitorId attr_safelock_size = new MonitorId(Lock); // safelock的当前数量 @当前锁的数量

    public static final MonitorId attr_safelock_trylockcount = new MonitorId(Lock); // safelock的乐观锁计数 @乐观锁总数

    public static final MonitorId attr_safelock_trylockfailcount = new MonitorId(Lock); // safelock的乐观锁失败计数 @乐观锁失败总数

    public static final MonitorId attr_safelock_lockcount = new MonitorId(Lock); // safelock的锁计数 @锁总数

    public static final MonitorId attr_safelock_lockfailcount = new MonitorId(Lock); // safelock的锁失败计数 @锁失败总数

    public static final MonitorId attr_safelock_unlockcount = new MonitorId(Lock); // safelock的解锁计数 @解锁总数

    public static final MonitorId attr_safelock_expirecount = new MonitorId(Lock); // safelock的锁超时计算 @锁超时总数

    public static final MonitorId attr_safelock_waitProcQueueSize = new MonitorId(Lock); // safelock的待处理队列长度 @待处理队列长度

    public static final MonitorId attr_tss_text_check_req = new MonitorId(Tss);   // tss文本检测请求 @tss文本检测请求

    public static final MonitorId attr_tss_text_check_req_scene = new MonitorId(Tss); // tss文本检测分场景请求 @tss文本检测分场景请求

    public static final MonitorId attr_tss_text_check = new MonitorId(Tss);    // tss文本检测 @tss文本检测

    public static final MonitorId attr_tss_text_check_evil = new MonitorId(Tss);   // tss恶意文本检测计数 @tss恶意文本检测总数

    public static final MonitorId attr_tss_text_check_dirty = new MonitorId(Tss); // tss脏词文本检测计数 @tss脏词文本检测总数

    public static final MonitorId attr_tss_pic_check_req = new MonitorId(Tss);    // tss图片检测请求 @tss图片检测请求

    public static final MonitorId attr_tss_pic_check_req_scene = new MonitorId(Tss);  // tss图片检测分场景请求 @tss图片检测分场景请求

    public static final MonitorId attr_tss_pic_check = new MonitorId(Tss);    // tss图片检测 @tss图片检测

    public static final MonitorId attr_player_credit_score_req = new MonitorId(Tss);  // 玩家信用分请求 @玩家信用分请求

    public static final MonitorId attr_credit_limit_info_req = new MonitorId(Tss);    // 信用限制信息请求 @信用限制信息请求

    public static final MonitorId attr_player_credit_score_check = new MonitorId(Tss);    // 玩家信用分检测 @玩家信用分检测

    public static final MonitorId attr_tss_check_nickname = new MonitorId(Tss); // tss同步脏词检测计数 @同步脏词检测总数

    public static final MonitorId attr_anti_cheat_add_user = new MonitorId(Tss);                      // tss反外挂添加用户 @tss反外挂添加用户
    public static final MonitorId attr_anti_cheat_del_user = new MonitorId(Tss);                      // tss反外挂删除用户 @tss反外挂删除用户
    public static final MonitorId attr_anti_cheat_info_report = new MonitorId(Tss);                   // tss反外挂上报信息 @tss反外挂上报信息
    public static final MonitorId attr_anti_cheat_tick_user = new MonitorId(Tss);                     // tss反外挂tick用户 @tss反外挂tick用户
    public static final MonitorId attr_anti_cheat_info_ntf_to_client = new MonitorId(Tss);            // tss反外挂通知客户端 @tss反外挂通知客户端
    public static final MonitorId attr_anti_cheat_login_queue_size = new MonitorId(Tss);              // tss反外挂登录队列长度 @tss反外挂登录队列长度
    public static final MonitorId attr_anti_cheat_logout_queue_size = new MonitorId(Tss);             // tss反外挂登出队列长度 @tss反外挂登出队列长度
    public static final MonitorId attr_anti_cheat_tick_queue_size = new MonitorId(Tss);               // tss反外挂tick队列长度 @tss反外挂tick队列长度

    public static final MonitorId attr_gamesvr_reputation_score_data_update = new MonitorId(Tss);     // 玩家信誉分数据更新 @玩家信誉分数据更新
    public static final MonitorId attr_gamesvr_reputation_score_change = new MonitorId(Tss);          // 玩家信誉分变化 @玩家信誉分变化
    public static final MonitorId attr_reputation_score_no_enough = new MonitorId(Tss);               // 玩家信誉分不足 @玩家信誉分不足
    public static final MonitorId attr_reputation_score_behavior_report = new MonitorId(Tss);         // 信誉分行为上报 @信誉分行为上报
    public static final MonitorId attr_reputation_score_battle_report = new MonitorId(Tss);           // 信誉分对局举报 @信誉分对局举报
    public static final MonitorId attr_reputation_score_send_mail = new MonitorId(Tss);               // 信誉分发送邮件 @信誉分发送邮件
    public static final MonitorId attr_reputation_score_low_value = new MonitorId(Tss);               // 信誉分分值低于阈值 @信誉分分值低于阈值

    public static final MonitorId attr_tss_check_dirty_sync = new MonitorId(Uic); // tss同步脏词检测计数 @同步脏词检测总数

    public static final MonitorId attr_uic_check = new MonitorId(Uic); // uic文本检测 @文本检测

    public static final MonitorId attr_tconnd_connect = new MonitorId(Tconnd); // tconnd链接计数 @链接数

    public static final MonitorId attr_tconnd_disconnect = new MonitorId(Tconnd); // tconnd断开链接计数 @断开链接计数

    public static final MonitorId attr_tconnd_invalid_pb_total = new MonitorId(Tconnd); // tconnd解析pb协议失败 @无效pb数

    public static final MonitorId attr_tconnd_unimplement_type_totol = new MonitorId(Tconnd); // tconnd不支持的方法 @不支持方法总数

    public static final MonitorId attr_tconnd_req_total = new MonitorId(Tconnd); // tconnd请求计数 @请求总数

    public static final MonitorId attr_tconnd_res_total = new MonitorId(Tconnd); // tconnd回包计数 @回包总数

    public static final MonitorId attr_tconnd_send_fail_total = new MonitorId(Tconnd); // tconnd回包失败计数 @回包失败总数

    public static final MonitorId attr_tconnd_pkg_gt_1m = new MonitorId(Tconnd); // tconnd回包大于1m计数 @回包大于1m

    public static final MonitorId attr_tconnd_pkg_gt_768k = new MonitorId(Tconnd); // tconnd回包大于768k @回包大于768k

    public static final MonitorId attr_tconnd_session_remove_fail = new MonitorId(Tconnd); // tconnd移除session失败计数 @移除session失败

    public static final MonitorId attr_tconnd_flow_per_min = new MonitorId(Tconnd); // 每分钟下发流量 @每分钟流量

    public static final MonitorId attr_apollo_req_send = new MonitorId(Apollo);      // ApolloApi调用 @ApolloApi调用
    public static final MonitorId attr_apollo_res_recv = new MonitorId(Apollo);      // ApolloApi响应 @ApolloApi响应

    public static final MonitorId attr_apollo_direct_buy_error = new MonitorId(Apollo);      // Apollo安卓直购失败 @安卓直购失败次数
    public static final MonitorId attr_apollo_direct_buy_success = new MonitorId(Apollo);      // Apollo安卓直购成功 @安卓直购成功次数

    public static final MonitorId attr_apollo_present_err = new MonitorId(Apollo);      // Apollo赠送失败 @赠送失败次数
    public static final MonitorId attr_apollo_present_suc = new MonitorId(Apollo);      // Apollo赠送成功 @赠送成功次数

    public static final MonitorId attr_apollo_pay_err = new MonitorId(Apollo);      // Apollo支付失败 @支付失败次数
    public static final MonitorId attr_apollo_pay_suc = new MonitorId(Apollo);      // Apollo支付成功 @支付成功次数

    public static final MonitorId attr_apollo_get_balance_err = new MonitorId(Apollo);      // Apollo余额查询失败 @余额查询失败
    public static final MonitorId attr_apollo_get_balance_suc = new MonitorId(Apollo);      // Apollo余额查询成功 @余额查询成功

    public static final MonitorId attr_apollo_report_score_trank = new MonitorId(Apollo);         // Trank上报分数 @Trank上报分数
    public static final MonitorId attr_apollo_report_score_topnext = new MonitorId(Apollo);       // TopNext上报分数 @TopNext上报分数
    public static final MonitorId attr_apollo_get_score_trank = new MonitorId(Apollo);            // Trank拉取榜单 @Trank拉取榜单
    public static final MonitorId attr_apollo_get_score_topnext = new MonitorId(Apollo);          // TopNext拉取榜单 @TopNext拉取榜单
    public static final MonitorId attr_apollo_get_score_topnext_no_sub_rank = new MonitorId(Apollo);          // TopNext拉取榜单子榜不存在 @TopNext拉取榜单子榜不存在
    public static final MonitorId attr_apollo_get_one_user_score_trank = new MonitorId(Apollo);   // Trank拉取个人分数 @Trank拉取个人分数
    public static final MonitorId attr_apollo_get_score_rank_trank = new MonitorId(Apollo);   // Trank拉取分数排名 @Trank拉取分数排名
    public static final MonitorId attr_apollo_get_one_user_score_topnext = new MonitorId(Apollo); // TopNext拉取个人分数 @TopNext拉取个人分数
    public static final MonitorId attr_apollo_get_sub_ranks_topnext = new MonitorId(Apollo); // TopNext拉取子榜列表 @TopNext拉取子榜列表
    public static final MonitorId attr_apollo_remove_score_trank = new MonitorId(Apollo);         // Trank删除榜单 @Trank删除榜单
    public static final MonitorId attr_apollo_remove_score_topnext = new MonitorId(Apollo);       // TopNext删除榜单 @TopNext删除榜单
    public static final MonitorId attr_apollo_remove_one_user_score_trank = new MonitorId(Apollo);    // Trank删除个人分数 @Trank删除个人分数
    public static final MonitorId attr_apollo_remove_one_user_score_topnext = new MonitorId(Apollo);  // TopNext删除个人分数 @TopNext删除个人分数
    public static final MonitorId attr_apollo_generate_image_trank = new MonitorId(Apollo);  // Trank生成榜单镜像 @Trank生成榜单镜像
    public static final MonitorId attr_apollo_get_image_status_trank = new MonitorId(Apollo);  // Trank获取榜单镜像信息 @Trank获取榜单镜像信息
    public static final MonitorId attr_apollo_set_image_switch_trank = new MonitorId(Apollo);  // Trank榜单镜像开关 @Trank榜单镜像开关
    public static final MonitorId attr_apollo_change_sub_topnext = new MonitorId(Apollo);             // TopNext迁移子榜 @TopNext迁移子榜
    public static final MonitorId attr_apollo_queue_size = new MonitorId(Apollo);    // Apollo队列长度 @Apollo队列长度
    public static final MonitorId attr_apollo_trank_run_le_100 = new MonitorId(Apollo);      // Trank执行时延小于100毫秒 @Trank执行时延小于100毫秒
    public static final MonitorId attr_apollo_trank_run_le_500 = new MonitorId(Apollo);      // Trank执行时延小于500毫秒 @Trank执行时延小于500毫秒
    public static final MonitorId attr_apollo_trank_run_le_1000 = new MonitorId(Apollo);      // Trank执行时延小于1000毫秒 @Trank执行时延小于1000毫秒
    public static final MonitorId attr_apollo_trank_run_le_5000 = new MonitorId(Apollo);      // Trank执行时延小于5000毫秒 @Trank执行时延小于5000毫秒
    public static final MonitorId attr_apollo_trank_run_gt_5000 = new MonitorId(Apollo);       // Trank执行时延大于5000毫秒 @Trank执行时延大于5000毫秒
    public static final MonitorId attr_apollo_topnext_run_le_100 = new MonitorId(Apollo);      // TopNext执行时延小于100毫秒 @TopNext执行时延小于100毫秒
    public static final MonitorId attr_apollo_topnext_run_le_500 = new MonitorId(Apollo);      // TopNext执行时延小于500毫秒 @TopNext执行时延小于500毫秒
    public static final MonitorId attr_apollo_topnext_run_le_1000 = new MonitorId(Apollo);      // TopNext执行时延小于1000毫秒 @TopNext执行时延小于1000毫秒
    public static final MonitorId attr_apollo_topnext_run_le_5000 = new MonitorId(Apollo);      // TopNext执行时延小于5000毫秒 @TopNext执行时延小于5000毫秒
    public static final MonitorId attr_apollo_topnext_run_gt_5000 = new MonitorId(Apollo);       // TopNext执行时延大于5000毫秒 @TopNext执行时延大于5000毫秒
    public static final MonitorId attr_apollo_lbs_run_le_100 = new MonitorId(Apollo);      // LBS执行时延小于100毫秒 @LBS执行时延小于100毫秒
    public static final MonitorId attr_apollo_lbs_run_le_500 = new MonitorId(Apollo);      // LBS执行时延小于500毫秒 @LBS执行时延小于500毫秒
    public static final MonitorId attr_apollo_lbs_run_le_1000 = new MonitorId(Apollo);      // LBS执行时延小于1000毫秒 @LBS执行时延小于1000毫秒
    public static final MonitorId attr_apollo_lbs_run_le_5000 = new MonitorId(Apollo);      // LBS执行时延小于5000毫秒 @LBS执行时延小于5000毫秒
    public static final MonitorId attr_apollo_lbs_run_gt_5000 = new MonitorId(Apollo);       // LBS执行时延大于5000毫秒 @LBS执行时延大于5000毫秒
    public static final MonitorId attr_apollo_f2f_run_le_100 = new MonitorId(Apollo);      // F2F执行时延小于100毫秒 @F2F执行时延小于100毫秒
    public static final MonitorId attr_apollo_f2f_run_le_500 = new MonitorId(Apollo);      // F2F执行时延小于500毫秒 @F2F执行时延小于500毫秒
    public static final MonitorId attr_apollo_f2f_run_le_1000 = new MonitorId(Apollo);      // F2F执行时延小于1000毫秒 @F2F执行时延小于1000毫秒
    public static final MonitorId attr_apollo_f2f_run_le_5000 = new MonitorId(Apollo);      // F2F执行时延小于5000毫秒 @F2F执行时延小于5000毫秒
    public static final MonitorId attr_apollo_f2f_run_gt_5000 = new MonitorId(Apollo);       // F2F执行时延大于5000毫秒 @F2F执行时延大于5000毫秒
    public static final MonitorId attr_apollo_trank_reach_quota_limit = new MonitorId(Apollo);  //Trank请求达到配额上限 @Trank请求达到配额上限
    public static final MonitorId attr_apollo_topnext_reach_quota_limit = new MonitorId(Apollo);  //TopNext请求达到配额上限 @TopNext请求达到配额上限
    public static final MonitorId attr_apollo_lbs_report = new MonitorId(Apollo);      // LBS上报 @LBS上报
    public static final MonitorId attr_apollo_lbs_remove = new MonitorId(Apollo);       // LBS删除 @LBS删除
    public static final MonitorId attr_apollo_lbs_search = new MonitorId(Apollo);      // LBS搜索 @LBS搜索
    public static final MonitorId attr_apollo_lbs_get_users = new MonitorId(Apollo);       // LBS查询 @LBS查询
    public static final MonitorId attr_apollo_lbs_clear = new MonitorId(Apollo);       // LBS清空 @LBS清空
    public static final MonitorId attr_apollo_f2f_search = new MonitorId(Apollo);      // F2F搜索 @F2F搜索
    public static final MonitorId attr_apollo_f2f_update = new MonitorId(Apollo);       // F2F更新 @F2F更新
    public static final MonitorId attr_apollo_f2f_remove = new MonitorId(Apollo);       // F2F删除 @F2F删除

    public static final MonitorId attr_task_run_succ = new MonitorId(Task);     // 任务执行成功 @成功总数

    public static final MonitorId attr_task_run_fail = new MonitorId(Task);     // 任务执行失败 @失败总数

    public static final MonitorId attr_task_run_gt_1sec = new MonitorId(Task);  // 任务执行大于1s @耗时>1s

    public static final MonitorId attr_task_run_gt_5sec = new MonitorId(Task);  // 任务执行大于5s @耗时>5s

    public static final MonitorId attr_task_run_gt_30sec = new MonitorId(Task); // 任务执行大于30ss @耗时>30s

    public static final MonitorId attr_task_run_gt_60sec = new MonitorId(Task); // 任务执行大于60s @耗时>60s

    public static final MonitorId attr_player_in_process_queue_gt_20 = new MonitorId(Player);

    public static final MonitorId attr_player_protocol_queue_full_count = new MonitorId(Player);// 玩家队列已满, 客户端协议或者定时器增加到队列失败

    public static final MonitorId attr_player_protocol_queue_stay_time = new MonitorId(Player); // 在玩家队列待的时间, 单位为毫秒

    public static final MonitorId attr_player_proto_latency_1 = new MonitorId(Player);         // 在玩家队列时延小于1毫秒

    public static final MonitorId attr_player_proto_latency_10 = new MonitorId(Player);         // 在玩家队列时延小于10毫秒

    public static final MonitorId attr_player_proto_latency_50 = new MonitorId(Player);         // 在玩家队列时延小于50毫秒

    public static final MonitorId attr_player_proto_latency_100 = new MonitorId(Player);        // 在玩家队列时延小于100毫秒

    public static final MonitorId attr_player_proto_latency_200 = new MonitorId(Player);        // 在玩家队列时延小于200毫秒

    public static final MonitorId attr_player_proto_latency_500 = new MonitorId(Player);        // 在玩家队列时延小于500毫秒

    public static final MonitorId attr_player_proto_latency_large_500 = new MonitorId(Player);  // 在玩家队列时延大于500毫秒

    public static final MonitorId attr_player_update_timekey = new MonitorId(Player); // 玩家数据快照 @玩家数据快照

    public static final MonitorId attr_player_backup_cnt_login = new MonitorId(Player);

    public static final MonitorId attr_player_backup_delete = new MonitorId(Player); // 历史玩家快照数据删除 @玩家快照数据删除

    public static final MonitorId attr_player_backup_avg_cnt = new MonitorId(Player); // 玩家的平均快照个数 @平均快照个数

    public static final MonitorId attr_player_add_gray_tag_cnt = new MonitorId(Player);     //添加玩家灰度tag数量
    public static final MonitorId attr_player_remove_gray_tag_cnt = new MonitorId(Player);  //移除玩家灰度ttag数量
    public static final MonitorId attr_player_client_cache_size_warn = new MonitorId(Player); //player clientCache存储预警

    public static final MonitorId attr_designer_configure_files_update_ntf_bytes = new MonitorId(Player);   // 策划配置表游戏内下发字节数 @配表数据下发字节数

    public static final MonitorId attr_full_update_even_size = new MonitorId(Player);                       // 全量存盘平均大小 @attr存盘字节数平均值

    public static final MonitorId attr_full_update_max_size = new MonitorId(Player);                        // 全量存盘最大大小 @attr存盘字节数最大值

    public static final MonitorId attr_full_update_count = new MonitorId(Player);                           // 全量存盘次数 @attr存盘次数

    public static final MonitorId attr_delta_update_even_size = new MonitorId(Player);                      // 全量存盘平均大小 @attr增量更新字节数平均值

    public static final MonitorId attr_delta_update_max_size = new MonitorId(Player);                       // 全量存盘最大大小 @attr增量更新字节数最大值

    public static final MonitorId attr_delta_update_count = new MonitorId(Player);                          // 全量存盘次数 @attr增量更新次数

    public static final MonitorId attr_player_battle_online_cnt = new MonitorId(Player); // 玩家对局online的次数 @玩家对局online的次数
    public static final MonitorId attr_player_room_online_cnt = new MonitorId(Player); // 玩家房间online的次数 @玩家房间online的次数

    public static final MonitorId attr_player_public_update_field = new MonitorId(
            Player);         // 玩家Public更新字段次数 @attrPublic更新字段次数

    public static final MonitorId attr_proxy_metadata_cache_size = new MonitorId(Proxy);        // 元数据缓存使用量 @元数据缓存大小

    public static final MonitorId attr_proxy_metadata_cache_usage = new MonitorId(Proxy);       // 元数据缓存使用率-xx% @元数据缓存使用率

    public static final MonitorId attr_proxy_metadata_cache_miss = new MonitorId(Proxy);        // 元数据缓存未命中次数 @元数据缓存未命中

    public static final MonitorId attr_proxy_metadata_alloc_count = new MonitorId(Proxy);       // 元数据分配次数 @元数据分配次数

    public static final MonitorId attr_proxy_metadata_query_count = new MonitorId(Proxy);       // 元数据总查询次数 @元数据查询次数
    
    public static final MonitorId attr_proxy_metadata_insert_or_update_count = new MonitorId(Proxy);       // 元数据更新次数 @元数据更新次数
    public static final MonitorId attr_proxy_metadata_delete_count = new MonitorId(Proxy);       // 元数据更新次数 @元数据更新次数
    
    public static final MonitorId attr_proxy_metadata_realloc_count = new MonitorId(Proxy);       // 元数据不存在情况下，重新分配 @元数据重新分配次数

    public static final MonitorId attr_proxy_relay_queue_deep = new MonitorId(Proxy);           // 转发队列深度 @转发队列的长度

    public static final MonitorId attr_proxy_relay_message_success = new MonitorId(Proxy);      // 转发成功数量 @转发成功次数

    public static final MonitorId attr_proxy_relay_message_failed = new MonitorId(Proxy);       // 转发失败数量 @转发失败次数

    public static final MonitorId proxy_relay_irpc_queue_deep = new MonitorId(Proxy);           // 转发IRPC队列深度 @转发IRPC队列的长度

    public static final MonitorId proxy_relay_irpc_message_success = new MonitorId(Proxy);      // 转发IRPC成功数量 @转发IRPC成功次数

    public static final MonitorId proxy_relay_irpc_message_failed = new MonitorId(Proxy);       // 转发IRPC失败数量 @转发IRPC失败次数

    public static final MonitorId proxy_stateful_route = new MonitorId(Proxy);                  // 有状态路由次数 @有状态路由次数

    public static final MonitorId proxy_stateful_reroute = new MonitorId(Proxy);                // 有状态路由转移次数 @有状态路由转移次数

    public static final MonitorId proxy_room_create_routing = new MonitorId(Proxy);             // 创建Room路由的次数 @创建Room路由的次数

    public static final MonitorId proxy_room_destroy_routing = new MonitorId(Proxy);            // 销毁Room路由的次数 @销毁Room路由的次数

    public static final MonitorId proxy_battle_create_routing = new MonitorId(Proxy);           // 创建Battle路由的次数 @创建Battle路由的次数

    public static final MonitorId proxy_battle_destroy_routing = new MonitorId(Proxy);          // 销毁Battle路由的次数 @销毁Battle路由的次数

    public static final MonitorId proxy_scene_create_routing = new MonitorId(Proxy);            // 创建Scene路由的次数 @创建Scene路由的次数

    public static final MonitorId proxy_scene_destroy_routing = new MonitorId(Proxy);           // 销毁Scene路由的次数 @销毁Scene路由的次数

    public static final MonitorId proxy_club_create_routing = new MonitorId(Proxy);           // 创建CLUB路由的次数 @创建CLUB路由的次数

    public static final MonitorId proxy_club_destroy_routing = new MonitorId(Proxy);          // 销毁club路由的次数 @销毁club路由的次数

    public static final MonitorId proxy_lobby_create_routing = new MonitorId(Proxy);            // 创建Lobby路由的次数 @创建Lobby路由的次数

    public static final MonitorId attr_proxy_relay_min_thread_cnt = new MonitorId(Proxy);           // 转发队列最小线程次数 @转发队列最小线程次数
    public static final MonitorId attr_proxy_relay_uid_thread_cnt = new MonitorId(Proxy);           // 转发队列按uid分线程次数 @转发队列按uid分线程次数

    // 跨区开房间服务
    public static final MonitorId attr_crossroutesvr_router_message_num = new MonitorId(CrossRouteSvr);          // 跨区转入router队列的消息数
    public static final MonitorId attr_crossroutesvr_router_message_success = new MonitorId(CrossRouteSvr);      // 跨区router转出成功数量
    public static final MonitorId attr_crossroutesvr_router_message_failed = new MonitorId(CrossRouteSvr);       // 跨区router转出失败数量

    public static final MonitorId attr_crossroutesvr_proxy_message_num = new MonitorId(CrossRouteSvr);           // 跨区转入proxy队列的消息数
    public static final MonitorId attr_crossroutesvr_proxy_message_success = new MonitorId(CrossRouteSvr);       // 跨区proxy转出成功数量
    public static final MonitorId attr_crossroutesvr_proxy_message_failed = new MonitorId(CrossRouteSvr);        // 跨区proxy转出失败数量

    public static final MonitorId attr_crossroutesvr_forward_message_num = new MonitorId(CrossRouteSvr);           // 跨区转入forward队列的消息数
    public static final MonitorId attr_crossroutesvr_forward_message_success = new MonitorId(CrossRouteSvr);       // 跨区forward转出成功数量
    public static final MonitorId attr_crossroutesvr_forward_message_failed = new MonitorId(CrossRouteSvr);        // 跨区forward转出失败数量
    //活动中心
    public static final MonitorId attr_activity_unexpected_del = new MonitorId(Activity);       // 进行中的活动被删除 @活动异常删除

    public static final MonitorId attr_activity_red_envelop_rain_ams_gift = new MonitorId(Activity);                   // 红包雨活动 ams送礼api(抽奖) @ams抽奖
    public static final MonitorId attr_activity_red_envelop_rain_ams_gift_out_of_stock = new MonitorId(Activity);      // 红包雨活动 ams送礼库存不足 @ams送礼库存不足
    public static final MonitorId attr_activity_red_envelop_rain_ams_gift_sys_error = new MonitorId(Activity);        // 红包雨活动 ams送礼系统异常 @ams送礼系统异常
    public static final MonitorId attr_activity_red_envelop_rain_ams_gift_timeout = new MonitorId(Activity);          // 红包雨活动 ams送礼超时 @ams送礼超时
    public static final MonitorId attr_activity_red_envelop_rain_ams_gift_cost_ms = new MonitorId(Activity);          // 红包雨活动 ams送礼接口耗时毫秒 @ams送礼接口耗时毫秒
    public static final MonitorId attr_activity_red_envelop_rain_ams_query = new MonitorId(Activity);                 // 红包雨活动 ams查询历史 @ams查询历史
    public static final MonitorId attr_activity_red_envelop_rain_ams_polaris_err = new MonitorId(Activity);           // 红包雨活动 ams 路由异常 @ams路由异常
    public static final MonitorId attr_activity_red_envelop_rain_open_normal_task = new MonitorId(Activity);              // 红包雨活动 开红包普通任务 @开红包普通任务
    public static final MonitorId attr_activity_red_envelop_rain_open_retry_task = new MonitorId(Activity);               // 红包雨活动 开红包重试任务 @开红包重试任务
    public static final MonitorId attr_activity_red_envelop_rain_normal_task_size = new MonitorId(Activity);               // 红包雨活动 普通任务长度 @普通任务长度
    public static final MonitorId attr_activity_red_envelop_rain_retry_task_size = new MonitorId(Activity);                // 红包雨活动 重试任务长度 @重试任务长度
    public static final MonitorId attr_activity_sync_ds_cfg = new MonitorId(Activity);                                // 红包雨活动 同步ds配置 @同步ds配置
    public static final MonitorId attr_activity_sync_ds_cfg_retry_give_up = new MonitorId(Activity);                  // 红包雨活动 同步ds配置重试失败 @同步ds配置重试失败
    public static final MonitorId attr_activity_discover_abtest_fetch_id = new MonitorId(Activity);      // 发现活动AB实验拉取玩家分组id
    public static final MonitorId attr_activity_inflate_red_packet_receive_fail = new MonitorId(Activity);            // 膨胀爆红包活动-领取失败
    public static final MonitorId attr_activity_inflate_red_packet_recharge_num = new MonitorId(Activity);            // 膨胀爆红包活动-充值次数
    public static final MonitorId attr_activity_seven_day_check_in_recharge_num = new MonitorId(Activity);            // 七日签到BP活动-充值次数

    public static final MonitorId attr_activity_squad_offline_join_update_redis_fail_after_retry = new MonitorId(Activity); // 离线加入小队更新redis多次重试后失败 @离线加入小队更新redis多次重试后失败
    public static final MonitorId attr_activity_squad_online_join_update_redis_fail_after_retry = new MonitorId(Activity); // 离线加入小队更新redis多次重试后失败 @离线加入小队更新redis多次重试后失败

    public static final MonitorId attr_activity_player_in_process_queue_gt_100 = new MonitorId(Activity);

    public static final MonitorId attr_activity_super_core_rank_refresh = new MonitorId(Activity);   // 超核排行榜刷新 @超核排行榜刷新
    public static final MonitorId attr_activity_super_core_rank_join_rank = new MonitorId(Activity);   // 超核排行榜加入排行 @超核排行榜加入排行
    public static final MonitorId attr_activity_super_core_rank_should_hidden_but_not = new MonitorId(Activity);   // 超核排行榜不应显示但是显示了 @超核排行榜不应显示但是显示了
    public static final MonitorId attr_activity_lucky_rebate_total_draw_cnt = new MonitorId(Activity); // 幸运返利活动抽奖次数 @幸运返利活动抽奖次数
    public static final MonitorId attr_activity_lucky_rebate_draw_level = new MonitorId(Activity); // 幸运返利活动抽奖奖项等级 @幸运返利活动抽奖奖项等级
    public static final MonitorId attr_activity_lucky_rebate_draw_dup_cnt = new MonitorId(Activity); // 幸运返利活动抽奖冲突次数 @幸运返利活动抽奖冲突次数
    public static final MonitorId attr_activity_lucky_rebate_sampling_cnt = new MonitorId(Activity); // 幸运返利活动采样次数 @幸运返利活动采样次数
    public static final MonitorId attr_activity_lucky_rebate_sampling_change_cnt = new MonitorId(Activity); // 幸运返利活动采样变更次数 @幸运返利活动采样变更次数
    public static final MonitorId attr_activity_lucky_rebate_settlement_cnt = new MonitorId(Activity); // 幸运返利活动结算次数 @幸运返利活动结算次数
    public static final MonitorId attr_activity_lucky_rebate_settlement_redraw_cnt = new MonitorId(Activity); // 幸运返利活动结算重随机次数 @幸运返利活动结算重随机次数

    public static final MonitorId attr_activity_summer_flash_mob_ugc_rank_reward_operate = new MonitorId(Activity);   // 快闪季活动-UGC排行榜奖励操作
    public static final MonitorId attr_activity_summer_flash_mob_ugc_rank_reward = new MonitorId(Activity);   // 快闪季活动-UGC排行榜奖励

    public static final MonitorId attr_activity_bi_error = new MonitorId(Activity);

    public static final MonitorId attr_active_thread_total = new MonitorId(thread); // 当前活跃的线程总数 @当前活跃的线程总数

    public static final MonitorId attr_blocked_thread_total = new MonitorId(thread); // 当前阻塞的线程总数 @当前阻塞的线程总数

    public static final MonitorId attr_not_blocked_thread_total = new MonitorId(thread); // 当前未阻塞的线程总数 @当前未阻塞的线程总数

    public static final MonitorId attr_thread_deadlock_detacted = new MonitorId(thread); // 死锁计数 @死锁

    public static final MonitorId attr_ThreadPinned = new MonitorId(thread); // ThreadPinned @ThreadPinned

    public static final MonitorId attr_thread_avg_cpu_max = new MonitorId(thread); // ThreadPinned @ThreadPinned

    public static final MonitorId attr_jvm_mem_used = new MonitorId(Jvm); // 内存使用量 @内存使用量（单位M）

    public static final MonitorId attr_jvm_mem_commit = new MonitorId(Jvm); // 内存提交量 @内存提交量（单位M）

    public static final MonitorId attr_jvm_mem_usage = new MonitorId(Jvm); // 内存使用率 @内存使用率

    public static final MonitorId attr_jvm_nomem_used = new MonitorId(Jvm); // 堆外内存使用量 @堆外内存使用量（单位M）

    public static final MonitorId attr_jvm_nomem_commit = new MonitorId(Jvm); // 堆外内存提交量 @堆外内存提交量（单位M）

    public static final MonitorId attr_jvm_mem_eden_used = new MonitorId(Jvm); // 新生代eden区使用量 @新生代eden区使用量

    public static final MonitorId attr_jvm_mem_eden_committed = new MonitorId(Jvm); // 新生代eden区提交量 @新生代eden区提交量

    public static final MonitorId attr_jvm_mem_survivor_used = new MonitorId(Jvm); // 新生代survivor区使用量 @新生代survivor区使用量

    public static final MonitorId attr_jvm_mem_survivor_committed = new MonitorId(Jvm); // 新生代survivor区提交量 @新生代survivor区提交量

    public static final MonitorId attr_jvm_mem_old_used = new MonitorId(Jvm); // 老年代survivor区使用量 @老年代survivor区使用量

    public static final MonitorId attr_jvm_mem_old_committed = new MonitorId(Jvm); // 老年代survivor区提交量 @老年代survivor区提交量

    public static final MonitorId attr_jvm_mem_get_usage_cost = new MonitorId(Jvm); // 内存分配耗时 @内存分配耗时

    public static final MonitorId attr_jvm_mem_young_gc_count_total = new MonitorId(Jvm); // young gc次数 @新生代gc次数

    public static final MonitorId attr_jvm_mem_mix_gc_count_total = new MonitorId(Jvm); // 混合gc次数 @混合gc次数

    public static final MonitorId attr_jvm_mem_old_gc_count_total = new MonitorId(Jvm); // 老年代gc次数 @老年代gc次数

    public static final MonitorId attr_jvm_mem_other_gc_count_total = new MonitorId(Jvm); // 其他gc次数 @其他gc次数

    public static final MonitorId attr_jvm_mem_matadata_threshold_gc_count_total = new MonitorId(Jvm); // 达到metadata设置的阈值导致的full gc次数 @full gc次数

    public static final MonitorId attr_jvm_mem_regions_old_gc_count_total = new MonitorId(Jvm); // 老年代region回收次数 @老年代region回收次数

    public static final MonitorId attr_jvm_mem_gc_cpu_time = new MonitorId(Jvm); // gc耗时 @gc耗时

    public static final MonitorId attr_jvm_gc_avg_duration_ms = new MonitorId(Jvm); // gc平均耗时 @gc平均耗时

    public static final MonitorId attr_jvm_gc_max_duration_ms = new MonitorId(Jvm); // gc最大耗时 @gc最大耗时

    public static final MonitorId attr_jvm_gc_old_duration_ms = new MonitorId(Jvm); // gc累计耗时 @gc累计耗时

    public static final MonitorId attr_jvm_gc_throughoutput = new MonitorId(Jvm); // gc吞吐量 @gc吞吐量

    public static final MonitorId attr_rss_mem = new MonitorId(Jvm); // 内存消耗 @内存消耗

    public static final MonitorId attr_virtual_mem = new MonitorId(Jvm); // 虚拟内存消耗 @虚拟内存消耗

    public static final MonitorId attr_jvm_mem_code_cache_used = new MonitorId(Jvm); // 代码缓存内存使用量 @代码缓存内存使用量

    public static final MonitorId attr_jvm_mem_code_cache_commited = new MonitorId(Jvm); // 代码缓存内存提交量 @代码缓存内存提交量

    public static final MonitorId attr_jvm_mem_metaspace_used = new MonitorId(Jvm); // 类元数据内存使用量 @类元数据内存使用量

    public static final MonitorId attr_jvm_mem_metaspace_commited = new MonitorId(Jvm); // 类元数据内存提交量 @类元数据内存提交量

    public static final MonitorId attr_jvm_mem_classspace_used = new MonitorId(Jvm); // 编译文件内存使用量 @编译文件内存使用量

    public static final MonitorId attr_jvm_mem_classspace_commited = new MonitorId(Jvm); // 编译文件内存提交量 @编译文件内存提交量

    public static final MonitorId attr_jvm_compilation_time = new MonitorId(Jvm); // VM运行时编译耗时 @VM运行时编译耗时

    public static final MonitorId attr_logger_queue_free = new MonitorId(Logger); // Logger RingBuffer RemainingCapacity
    public static final MonitorId attr_logger_queue_capacity = new MonitorId(Logger); // Logger RingBuffer BufferSize

    // rpc
    public static final MonitorId attr_rpc_client_total_count = new MonitorId(Rpc); // rpc客户端发送的请求总数 @发送请求总数

    public static final MonitorId attr_rpc_client_timeout = new MonitorId(Rpc); // rpc超时计数 @超时次数

    public static final MonitorId attr_rpc_client_failed_unknown_service = new MonitorId(Rpc); // rpc未知异常 @未知异常

    public static final MonitorId attr_rpc_client_failed_exception = new MonitorId(Rpc); // rpc失败 @失败

    public static final MonitorId attr_rpc_client_failed_nk_runtime_exception = new MonitorId(Rpc); // rpc运行时异常 @运行时异常

    public static final MonitorId attr_rpc_client_failed_relay_failed = new MonitorId(Rpc); // rpc转发失败 @转发失败

    public static final MonitorId attr_rpc_client_failed_redirect_failed = new MonitorId(Rpc); // rpc重定向失败 @重定向失败

    public static final MonitorId attr_rpc_client_req_limit = new MonitorId(Rpc); // rpc请求限制 @请求限制


    public static final MonitorId attr_rpc_client_cost_time = new MonitorId(Rpc); // 处理请求花费的时间 @处理时间

    public static final MonitorId attr_rpc_client_return_code = new MonitorId(Rpc); // 请求方拿到返回码

    public static final MonitorId attr_rpc_hash_key_is_0 = new MonitorId(Rpc); // 一致性哈希key为0 @一致性哈希key为0

    public static final MonitorId attr_rpc_server_recv_total = new MonitorId(Rpc); // rpc服务器收到的请求总数 @收到的请求总数

    public static final MonitorId attr_rpc_server_recv_req_size = new MonitorId(Rpc); // rpc服务器收到的请求包体大小 @收到的包体大小

    public static final MonitorId attr_rpc_server_recv_rsp_size = new MonitorId(Rpc); // rpc服务器返回请求响应包体大小 @返回请求的响应包体大小

    public static final MonitorId attr_rpc_server_recv_exception = new MonitorId(Rpc); // rpc异常

    public static final MonitorId attr_rpc_server_cost_time = new MonitorId(Rpc); // 处理请求花费的时间 @处理时间

    public static final MonitorId attr_rpc_server_wallcost_time = new MonitorId(Rpc); // 处理请求花费的时间 @Wall处理时间

    public static final MonitorId attr_rpc_server_return_code = new MonitorId(Rpc); // 返回码 @返回码

    public static final MonitorId attr_rpc_server_req_limit = new MonitorId(Rpc); // rpc请求限制
    
    // IRpc
    public static final MonitorId attr_irpc_client_total_count = new MonitorId(IRpc); // IRpc客户端发送的请求总数 @发送请求总数

    public static final MonitorId attr_irpc_client_timeout = new MonitorId(IRpc); // IRpc超时计数 @超时次数

    public static final MonitorId attr_irpc_server_recv_total = new MonitorId(IRpc); // IRpc服务器收到的请求总数 @收到的请求总数

    public static final MonitorId attr_irpc_server_recv_req_size = new MonitorId(IRpc); // IRpc服务器收到的请求包体大小 @收到的请求包体大小

    public static final MonitorId attr_irpc_server_recv_rsp_size = new MonitorId(IRpc); // IRpc服务器返回请求响应包体大小 @返回请求的响应包体大小

    public static final MonitorId attr_irpc_server_req_limit = new MonitorId(IRpc); // irpc 请求限制

    public static final MonitorId attr_msg_rate_limit = new MonitorId(Rpc); //CS/RPC/IRPC请求速度限制

    public static final MonitorId attr_msg_size_limit = new MonitorId(Rpc); //CS/RPC/IRPC请求包大小限制
    public static final MonitorId attr_msg_limit_exception = new MonitorId(Rpc); //限流异常监控
    public static final MonitorId attr_msg_limit_entities_count = new MonitorId(Rpc); //队列长度监控

    // Mailbox
    public static final MonitorId attr_mailbox_map_size = new MonitorId(MailBox); //mailbox数量
    public static final MonitorId attr_mailbox_max_queue_size = new MonitorId(MailBox); //mailbox最大队列长度
    public static final MonitorId attr_mailbox_avg_queue_size = new MonitorId(MailBox); //mailbox平均队列长度

    public static final MonitorId attr_mailbox_exec_total_count = new MonitorId(MailBox); //mailbox次数
    public static final MonitorId attr_mailbox_exec_total_wait_time = new MonitorId(MailBox); //mailbox耗时
    public static final MonitorId attr_mailbox_long_time_drop_count = new MonitorId(MailBox); // 丢弃长时间在队列的协议统计

    // tbuspp
    public static final MonitorId attr_tbuspp_send_req_total_count = new MonitorId(Tbus); // 放队列里 @加入发送队列计数

    public static final MonitorId attr_tbuspp_send_ok_count = new MonitorId(Tbus);  // 真正send出去 @发送计数

    public static final MonitorId attr_tbuspp_send_failed_count = new MonitorId(Tbus); // 发送失败 @发送失败

    public static final MonitorId attr_tbuspp_recv_ok_count = new MonitorId(Tbus); // 接收成功 @接收成功

    public static final MonitorId attr_tbuspp_recv_failed_count = new MonitorId(Tbus); // 接收失败 @接收失败

    public static final MonitorId attr_tbuspp_send_size_gt_1k = new MonitorId(Tbus); // 发送数据大于1k @发送数据>1k

    public static final MonitorId attr_tbuspp_send_size_gt_10k = new MonitorId(Tbus); // 发送数据大于10k @发送数据>10k

    public static final MonitorId attr_tbuspp_send_size_gt_50k = new MonitorId(Tbus); // 发送数据大于50k @发送数据>50k

    public static final MonitorId attr_tbuspp_online_instance_count = new MonitorId(TbusppOnlineInstance); // tbuspp在线实例数 @在线实例
    public static final MonitorId attr_tbuspp_online_instance_error = new MonitorId(TbusppOnlineInstance); // tbuspp路由表错误数 @路由表错误
    public static final MonitorId attr_tbuspp_online_instance_cost = new MonitorId(TbusppOnlineInstance); // tbuspp全量路由更新耗时 @路由表更新耗时

    public static final MonitorId attr_tbuspp_address_table_is_null = new MonitorId(TbusppError);        // kAddressTableIsNull = -1032	路由表是空的	持续出现 @空路由表
    public static final MonitorId attr_tbuspp_dst_server_name_not_null = new MonitorId(TbusppError);     // kDstServerNameNotExist = -1037	目的端实例不存在	持续出现 @目的端实例不存在
    public static final MonitorId attr_tbuspp_route_state_is_not_normal = new MonitorId(TbusppError);    // kRouteStateIsnotNormal = -1055	目标服务下实例都是非正常在线状态(unready、预退出) 持续出现 @目标服务下实例不可用
    public static final MonitorId attr_tbuspp_mq_queue_not_space = new MonitorId(TbusppError);           // kMQ_QUEUE_NOT_SPACE = -3008	空闲子队列不够 持续出现 @无空闲队列
    public static final MonitorId attr_tbuspp_queue_sub_queue_full = new MonitorId(TbusppError);         // kQueueSubQueueFull = -3045	子队列满了 持续出现 @子队列已满
    public static final MonitorId attr_tbuspp_global_lock_time_out_10s = new MonitorId(TbusppError);     // kGlobalLockTimeOut10s = -5032	10s没能拿到锁 出现一次 @获取锁超时
    public static final MonitorId attr_tbuspp_remote_qid_not_found_error = new MonitorId(TbusppError);   // kRemoteQidNotFoundError = -5048	远端消息包含的qid，在本地找不到.	持续出现 @未知qid
    public static final MonitorId attr_tbuspp_queue_empty = new MonitorId(TbusppError);                  // kQueueEmpty = -5053	根据qid找不到相应的instance	持续出现 @无对应实例
    public static final MonitorId attr_tbuspp_no_encrypt_aesKey = new MonitorId(TbusppError);            // kNoEncryptAesKey = -5066	加密时没有通信密钥	持续出现 @无密钥（加密）
    public static final MonitorId attr_tbuspp_no_decrypt_aesKey = new MonitorId(TbusppError);            // kNoDecryptAesKey = -5067	解密时没有通信密钥	持续出现 @无密钥（解密）
    public static final MonitorId attr_tbuspp_need_redo = new MonitorId(TbusppError);                    // kNeedRedo = -5073	添加路由拿锁超时,需要重做本次请求	出现一次 @添加路由信息时获取锁超时
    public static final MonitorId attr_tbuspp_auth_timeout = new MonitorId(TbusppError);                 // kAuthTimeout = -5075	鉴权超时	出现一次 @鉴权超时
    public static final MonitorId attr_tbuspp_agent_version_not_support = new MonitorId(TbusppError);    // kAgentVersionNotSupport = -6015	agent版本较低	出现一次 @agent版本不支持
    public static final MonitorId attr_tbuspp_instance_online = new MonitorId(TbusppError);              // kInstanceOnline = -10010	实例在线，无法覆盖	出现一次 @实例无法覆盖
    public static final MonitorId attr_tbuspp_response_timeout = new MonitorId(TbusppError);             // kResponseTimeout = -10001	响应超时	持续出现 @响应超时

    // tbuspp2
    public static final MonitorId attr_tbuspp2_send_req_total_count = new MonitorId(Tbus); // 放队列里 @加入发送队列计数

    public static final MonitorId attr_tbuspp2_send_ok_count = new MonitorId(Tbus);  // 真正send出去 @发送计数

    public static final MonitorId attr_tbuspp2_send_failed_count = new MonitorId(Tbus); // 发送失败 @发送失败

    public static final MonitorId attr_tbuspp2_recv_ok_count = new MonitorId(Tbus); // 接收成功 @接收成功

    public static final MonitorId attr_tbuspp2_recv_failed_count = new MonitorId(Tbus); // 接收失败 @接收失败

    public static final MonitorId attr_tbuspp2_send_size_gt_1k = new MonitorId(Tbus); // 发送数据大于1k @发送数据>1k

    public static final MonitorId attr_tbuspp2_send_size_gt_10k = new MonitorId(Tbus); // 发送数据大于10k @发送数据>10k

    public static final MonitorId attr_tbuspp2_send_size_gt_50k = new MonitorId(Tbus); // 发送数据大于50k @发送数据>50k

    public static final MonitorId attr_tbuspp2_generic = new MonitorId(Tbuspp2Error);                     // TBUSPP_ERR_GENERIC -1
    public static final MonitorId attr_tbuspp2_op_denied = new MonitorId(Tbuspp2Error);                   // TBUSPP_ERR_OP_DENIED -2
    public static final MonitorId attr_tbuspp2_queue_empty = new MonitorId(Tbuspp2Error);                 // TBUSPP_ERR_QUEUE_EMPTY -3
    public static final MonitorId attr_tbuspp2_queue_busy = new MonitorId(Tbuspp2Error);                  // TBUSPP_ERR_QUEUE_BUSY -4
    public static final MonitorId attr_tbuspp2_less_msg_buf = new MonitorId(Tbuspp2Error);                // TBUSPP_ERR_LESS_MSG_BUF -5
    public static final MonitorId attr_tbuspp2_unexpected = new MonitorId(Tbuspp2Error);                  // TBUSPP_ERR_UNEXPECTED -6
    public static final MonitorId attr_tbuspp2_cmd_channel_broken = new MonitorId(Tbuspp2Error);          // TBUSPP_ERR_CMD_CHANNEL_BROKEN -9
    public static final MonitorId attr_tbuspp2_wrong_arg = new MonitorId(Tbuspp2Error);                   // TBUSPP_ERR_WRONG_ARG -10
    public static final MonitorId attr_tbuspp2_not_found = new MonitorId(Tbuspp2Error);                   // TBUSPP_ERR_NOT_FOUND -11
    public static final MonitorId attr_tbuspp2_time_out = new MonitorId(Tbuspp2Error);                    // TBUSPP_ERR_TIMEOUT -13
    public static final MonitorId attr_tbuspp2_wrong_agent_version = new MonitorId(Tbuspp2Error);         // TBUSPP_ERR_WRONG_AGENT_VERSION -14
    public static final MonitorId attr_tbuspp2_wrong_gidmask = new MonitorId(Tbuspp2Error);               // TBUSPP_ERR_WRONG_GIDMASK -15
    public static final MonitorId attr_tbuspp2_shm_failed = new MonitorId(Tbuspp2Error);                  // TBUSPP_ERR_SHM_FAILED -16
    public static final MonitorId attr_tbuspp2_busid_mismatch = new MonitorId(Tbuspp2Error);              // TBUSPP_ERR_BUSID_MISMATCH -17
    public static final MonitorId attr_tbuspp2_not_impl = new MonitorId(Tbuspp2Error);                    // TBUSPP_ERR_NOT_IMPL -18
    public static final MonitorId attr_tbuspp2_route_fail = new MonitorId(Tbuspp2Error);                  // TBUSPP_ERR_ROUTE_FAIL -1200
    public static final MonitorId attr_tbuspp2_route_not_ready = new MonitorId(Tbuspp2Error);             // TBUSPP_ERR_ROUTE_NOT_READY -1201
    public static final MonitorId attr_tbuspp2_span_arg_invalid = new MonitorId(Tbuspp2Error);            // TBUSPP_ERR_SPAN_ARG_INVALID -2001

    //tcaplus
    public static final MonitorId attr_tcaplus_send_bytes = new MonitorId(Tcaplus); // tcaplus请求的字节数大小 @请求字节数大小

    public static final MonitorId attr_tcaplus_send_cnt = new MonitorId(Tcaplus); // tcaplus请求总数 @请求总数

    public static final MonitorId attr_tcaplus_recv_bytes = new MonitorId(Tcaplus); // tcaplus结果的字节数大小 @结果字节数大小

    public static final MonitorId attr_tcaplus_recv_cnt = new MonitorId(Tcaplus); // tcaplus收到结果计数 @收包计数

    public static final MonitorId attr_tcaplus_delay = new MonitorId(Tcaplus); // tcaplus延迟 @延迟

    public static final MonitorId attr_tcaplus_max_delay = new MonitorId(Tcaplus); // tcaplus最大延迟 @最大延迟

    public static final MonitorId attr_tcaplus_timeout_cnt = new MonitorId(Tcaplus); // 请求超时 @超时次数

    public static final MonitorId attr_tcaplus_not_exist_cnt = new MonitorId(Tcaplus); //数据不存在 @数据不存在

    public static final MonitorId attr_tcaplus_exist_cnt = new MonitorId(Tcaplus); // 数据存在 @数据存在

    public static final MonitorId attr_tcaplus_err_cnt = new MonitorId(Tcaplus); // 查询错误 @错误

    public static final MonitorId attr_tcaplus_coro_update_queue_update_cnt = new MonitorId(Tcaplus); // 协程队列更新次数 @次数

    public static final MonitorId attr_tcaplus_coro_update_queue_update_success_cnt = new MonitorId(Tcaplus); // 协程队列更新成功次数 @次数

    public static final MonitorId attr_tcaplus_coro_update_queue_update_failed_cnt = new MonitorId(Tcaplus); // 协程队列更新失败次数 @次数

    public static final MonitorId attr_tcaplus_async_update_queue_in_queue_cnt = new MonitorId(Tcaplus); // 异步队列进队次数 @次数
    public static final MonitorId attr_tcaplus_async_update_queue_out_queue_cnt = new MonitorId(Tcaplus); // 异步队列出队次数 @次数
    public static final MonitorId attr_tcaplus_async_update_queue_update_failed_cnt = new MonitorId(Tcaplus); // 异步队列更新失败次数 @次数
    public static final MonitorId attr_tcaplus_async_update_queue_update_success_cnt = new MonitorId(Tcaplus); // 异步队列更新成功次数 @次数
    public static final MonitorId attr_tcaplus_async_update_queue_avg_send_wait_time = new MonitorId(Tcaplus); // 异步队列发送平均等待时间 @时间
    public static final MonitorId attr_tcaplus_async_update_queue_avg_wait_in_queue_time = new MonitorId(Tcaplus); // 异步队列在队列中等待时间 @时间

    public static final MonitorId attr_tcaplus_send_queue_size = new MonitorId(Tcaplus);  // tcaplus请求队列大小
    public static final MonitorId attr_tcaplus_send_queue_limit_exceed = new MonitorId(Tcaplus); // tcaplus请求队列超过限制
    public static final MonitorId attr_tcaplus_prepare_queue_size = new MonitorId(Tcaplus);   // 发送队列大小

    public static final MonitorId attr_tcaplus_player_update_success_cnt = new MonitorId(Tcaplus); // 玩家更新成功次数

    public static final MonitorId attr_tcaplus_player_update_failed_cnt = new MonitorId(Tcaplus); // 玩家更新失败次数

    public static final MonitorId attr_poll_jobs = new MonitorId(Coroutine); // 获取的job数 @获取的job数

    public static final MonitorId attr_fini_jobs = new MonitorId(Coroutine); // 完成的job数 @完成的job数

    public static final MonitorId attr_avg_time_queue_to_run = new MonitorId(Coroutine); // job的平均等待时长 @平均等待时长

    public static final MonitorId attr_avg_time_run_to_fini = new MonitorId(Coroutine); // job的平均处理时长 @平均处理时长

    public static final MonitorId attr_queue_full_count = new MonitorId(Coroutine); // 任务队列满 @任务队列满

    public static final MonitorId attr_jobs_in_queue = new MonitorId(Coroutine); // 队列中的任务数 @队列中的任务数

    public static final MonitorId attr_coro_async_count = new MonitorId(Coroutine); // 协程异步任务数 @协程异步任务数
    public static final MonitorId attr_total_create_to_run = new MonitorId(Coroutine);      // job的总等待时间
    public static final MonitorId attr_total_cost_run_to_finish = new MonitorId(Coroutine); // job的总消耗时间
    public static final MonitorId attr_avg_time_queue_to_run_by_set = new MonitorId(Coroutine);   // job的平均等待时长 @平均等待时长 Set方式
    public static final MonitorId attr_avg_time_run_to_fini_by_set = new MonitorId(Coroutine);    // job的平均处理时长 @平均处理时长 Set方式

    public static final MonitorId CoroAsyncCount = new MonitorId(Coroutine);

   
    public static final MonitorId attr_thread_for_coro_block_times = new MonitorId(Coroutine); //线程block次数
    public static final MonitorId attr_thread_for_coro_block_duration_time = new MonitorId(Coroutine); //线程block持续时间

    public static final MonitorId attr_thread_for_coro_tick_time = new MonitorId(Coroutine); //单次tick时间过长
    public static final MonitorId attr_thread_for_coro_tick_percent = new MonitorId(Coroutine); //一段时间的tick耗时百分比

    public static final MonitorId attr_dir_warn_total_req_limit = new MonitorId(Dirsvr); //dir 请求总次数到上限
    public static final MonitorId attr_dir_warn_total_req_level_limit = new MonitorId(Dirsvr); //dir 某个等级的求次数到上限
    public static final MonitorId attr_dir_warn_ip_req_limit = new MonitorId(Dirsvr); //dir ip请求次数到上限
    public static final MonitorId attr_dir_warn_ip_req_limit_level_up = new MonitorId(Dirsvr); //dir ip请求限制等级提升
    public static final MonitorId attr_dir_warn_ip_net_limit = new MonitorId(Dirsvr); // dir ip 流量报警
    public static final MonitorId attr_dir_warn_conn_repeat_req_times = new MonitorId(Dirsvr); // dir重复次数报警
    public static final MonitorId attr_dir_get_free_flow_req_times = new MonitorId(Dirsvr); // dir 拉取免流信息次数


    // 邮件
    public static final MonitorId mail_count_gt_50 = new MonitorId(Mail);       // 玩家邮件存量超过50 @玩家邮件存量超过50

    public static final MonitorId mail_count_gt_100 = new MonitorId(Mail);      // 玩家邮件存量超过100 @玩家邮件存量超过100

    public static final MonitorId mail_count_gt_500 = new MonitorId(Mail);      // 玩家邮件存量超过500 @玩家邮件存量超过500

    public static final MonitorId mail_count_gt_1k = new MonitorId(Mail);       // 玩家邮件存量超过1k @玩家邮件存量超过1k

    public static final MonitorId mail_read_detail = new MonitorId(Mail);       // 阅读详细数量 @阅读邮件数

    public static final MonitorId mail_delete = new MonitorId(Mail);            // 删除数量 @删除邮件数

    public static final MonitorId mail_get_reward = new MonitorId(Mail);        // 领奖数量 @领取邮件数

    public static final MonitorId mail_notify_new_mail = new MonitorId(Mail);   // 新增邮件 @新增邮件数
    public static final MonitorId mail_send_mail = new MonitorId(Mail);         // 发送邮件 @发送邮件数
    public static final MonitorId mail_quick_delete = new MonitorId(Mail);      // 快速删除 @一键删除的次数
    public static final MonitorId mail_quick_get_att = new MonitorId(Mail);     // 快速领取 @一键领取的次数
    public static final MonitorId mail_load_count = new MonitorId(Mail);        // 累计加载邮件数量 @累计加载邮件数量
    public static final MonitorId mail_load_time_ms = new MonitorId(Mail);      // 累计加载邮件耗时 @累计加载邮件耗时


    //战斗
    public static final MonitorId attr_battle_finish_frame = new MonitorId(Battle); // 战斗进行的回合数

    public static final MonitorId attr_battle_count = new MonitorId(Battle); // 玩家进入对局结算数量 @玩家进入对局结算统计

    //匹配//匹配新队伍入队
    public static final MonitorId attr_matchsvr_match = new MonitorId(MatchSvr);                      //匹配结果 @匹配结果
    public static final MonitorId attr_matchsvr_match_simulator = new MonitorId(MatchSvr);            // 模拟器队伍匹配结果 @模拟器队伍匹配结果
    public static final MonitorId attr_matchsvr_matchunit_req = new MonitorId(MatchSvr);                  // 匹配单元累计总数 @匹配单元累计总数
    // public static final MonitorId attr_matchsvr_timeout_match = new MonitorId(MatchSvr);              //超时匹配(包括最大超时匹配) @匹配超时
    public static final MonitorId attr_matchsvr_maxtimeout_match = new MonitorId(MatchSvr);           //最大超时匹配 @最大匹配超时
    public static final MonitorId attr_matchsvr_match_queue = new MonitorId(MatchSvr);                //匹配队列大小 @匹配队列大小
    public static final MonitorId attr_matchsvr_match_cancel_queue = new MonitorId(MatchSvr);         //取消匹配队列大小 @取消匹配队列大小
    public static final MonitorId attr_matchsvr_match_modify_info_queue = new MonitorId(MatchSvr);    // 修改匹配信息队列大小 @修改匹配信息队列大小
    public static final MonitorId attr_matchsvr_teamsync_match_succ_queue = new MonitorId(MatchSvr);        //匹配成功同步队列大小 @匹配成功同步队列大小
    public static final MonitorId attr_matchsvr_teamsync_match_fail_queue = new MonitorId(MatchSvr);        //匹配失败同步队列大小 @匹配失败同步队列大小
    public static final MonitorId attr_matchsvr_teamsync_cancel_succ_queue = new MonitorId(MatchSvr);        //取消成功同步队列大小 @取消成功同步队列大小
    public static final MonitorId attr_matchsvr_teamsync_cancel_fail_queue = new MonitorId(MatchSvr);        //取消失败同步队列大小 @取消失败同步队列大小

    public static final MonitorId attr_matchsvr_success_cost_time_total = new MonitorId(MatchSvr);                 //匹配成功耗时 @匹配成功耗时
    public static final MonitorId attr_matchsvr_cancel_cost_time_total = new MonitorId(MatchSvr);                  //取消匹配耗时 @取消匹配耗时

    public static final MonitorId attr_matchsvr_success_time_out_total = new MonitorId(MatchSvr);                  //匹配成功超时次数 @匹配成功超时次数
    public static final MonitorId attr_matchsvr_matchunit_total = new MonitorId(MatchSvr);                  //匹配单元总数 @匹配单元总数
    public static final MonitorId attr_matchsvr_matchplayercnt_total = new MonitorId(MatchSvr);                  //匹配玩家总数 @匹配玩家总数
    public static final MonitorId attr_matchsvr_timeout_task_cnt_total = new MonitorId(MatchSvr);                 // 匹配超时任务总数 @匹配超时任务总数
    public static final MonitorId attr_matchsvr_matchavergereqload_total = new MonitorId(MatchSvr);            //匹配平均请求数 @匹配平均请求数
    public static final MonitorId attr_matchsvr_matchunit_mode = new MonitorId(MatchSvr);                  //分模式匹配单元总数 @分模式匹配单元总数
    public static final MonitorId attr_matchsvr_matchplayercnt_mode = new MonitorId(MatchSvr);                  //分模式匹配玩家总数 @分模式匹配玩家总数
    public static final MonitorId attr_matchsvr_timeout_task_cnt_mode = new MonitorId(MatchSvr);                  //分模式超时任务总数 @分模式超时任务总数
    public static final MonitorId attr_matchsvr_warmlose_abtest_fetch_id = new MonitorId(MatchSvr);      // 温暖局AB实验拉取玩家分组idb @温暖局AB实验拉取玩家分组
    public static final MonitorId attr_matchsvr_add_cancel_queue = new MonitorId(MatchSvr);               // 加入取消匹配队列 @加入取消匹配队列
    public static final MonitorId attr_matchsvr_add_modify_queue = new MonitorId(MatchSvr);               // 加入匹配信息修改队列 @加入匹配信息修改队列
    public static final MonitorId attr_matchsvr_modify_team_info_result = new MonitorId(MatchSvr);         // 修改匹配中队伍信息结果 @修改匹配中队伍信息结果
    public static final MonitorId attr_matchsvr_room_ob_start_match = new MonitorId(MatchSvr);            // roomSvr侧观测到的申请匹配 @roomSvr观测申请匹配
    public static final MonitorId attr_matchsvr_room_ob_match_result = new MonitorId(MatchSvr);           // roomSvr侧观测到的匹配结果通知 @roomSvr观测到匹配结果通知
    public static final MonitorId attr_matchsvr_create_battle = new MonitorId(MatchSvr);                  // 拉起战场 @创建战场
    public static final MonitorId attr_matchsvr_pre_selects_side_assign = new MonitorId(MatchSvr);        // 预先设置阵营的匹配方式 @预先设置阵营的匹配方式
    public static final MonitorId attr_matchsvr_pre_selects_side_assign_base_team = new MonitorId(MatchSvr);  // 预先设置阵营分配基准队伍 @预先设置阵营分配基准队伍
    public static final MonitorId attr_matchsvr_proc_design_side_assign_base_team = new MonitorId(MatchSvr);  // procDesignateSideMatchSide配基准队伍 @procDesignateSideMatchSide分配基准队伍

    public static final MonitorId attr_matchsvr_dynamicplayers_dsccpuload = new MonitorId(MatchSvr);        // 当前dsccpu负载
    public static final MonitorId attr_matchsvr_dynamicplayers_players = new MonitorId(MatchSvr);        // 不同模式当前动态真人数
    public static final MonitorId attr_matchsvr_matchsucc_players = new MonitorId(MatchSvr);             // 匹配成功的时候不同模式真人数
    public static final MonitorId attr_matchsvr_dynamicplayers_maxtimeoutfail = new MonitorId(MatchSvr);  // 最大超时因为开了动态真人匹配失败数
    public static final MonitorId attr_matchsvr_dynamicplayers_originplayers = new MonitorId(MatchSvr);        // 不同模式当前原始真人数
    public static final MonitorId attr_matchsvr_matchfillbackunit_req = new MonitorId(MatchSvr);                  // 匹配回填单元累计总数 @匹配单元累计总数
    public static final MonitorId attr_matchsvr_fillback = new MonitorId(MatchSvr);                      //回填结果 @回填结果
    public static final MonitorId attr_matchsvr_match_score_less_0 = new MonitorId(MatchSvr);             // 匹配分小于0 @匹配分小于0
    public static final MonitorId attr_matchsvr_mmr_score_less_0 = new MonitorId(MatchSvr);             // mmr小于0 @mmr小于0
    public static final MonitorId attr_matchsvr_match_ugc = new MonitorId(MatchSvr);                           //UGC匹配结果 @UGC匹配结果
    public static final MonitorId attr_matchsvr_maxtimeout_match_ugc = new MonitorId(MatchSvr);            //UGC最大超时匹配 @UGC最大匹配超时
    public static final MonitorId attr_matchsvr_team_size_match_rule_result_err = new MonitorId(MatchSvr);  // 按队伍人数匹配功能最后结果有错误 @按队伍人数匹配功能最后结果有错误

    //房间
    public static final MonitorId attr_roomsvr_room_size = new MonitorId(RoomSvr);            //房间个数 @当前房间总数
    public static final MonitorId attr_roomsvr_roommember_size = new MonitorId(RoomSvr);      //房间成员个数 @当前房间内玩家数
    public static final MonitorId attr_roomsvr_game_mode_room_size = new MonitorId(RoomSvr);            //各游戏模式的房间个数 @当前房间总数
    public static final MonitorId attr_roomsvr_game_mode_roommember_size = new MonitorId(RoomSvr);      //各游戏模式的房间成员个数 @当前房间内玩家数
    public static final MonitorId attr_roomsvr_createroom = new MonitorId(RoomSvr);      //创建房间个数 @创建房间
    public static final MonitorId attr_roomsvr_joinroom = new MonitorId(RoomSvr);            //加入房间个数 @加入房间
    public static final MonitorId attr_roomsvr_recruit_cnt = new MonitorId(RoomSvr);        //招募房间个数 @招募房间个数
    public static final MonitorId attr_roomsvr_exit_room_for_data_fix = new MonitorId(RoomSvr); //退出共存队伍/房间次数 @退出共存队伍/房间次数
    public static final MonitorId attr_roomsvr_insertroom = new MonitorId(RoomSvr);        //删除房间次数
    public static final MonitorId attr_roomsvr_loadroom = new MonitorId(RoomSvr);        //载入房间次数
    public static final MonitorId attr_roomsvr_updateroom = new MonitorId(RoomSvr);      //保存房间次数
    public static final MonitorId attr_roomsvr_deleteroom = new MonitorId(RoomSvr);      //删除房间次数
    public static final MonitorId attr_roomsvr_updateroom_fields = new MonitorId(RoomSvr);      //保存房间次数

    public static final MonitorId attr_roomsvr_team_create_cnt = new MonitorId(RoomSvr); // 队伍创建次数 @队伍创建次数
    public static final MonitorId attr_roomsvr_room_create_cnt = new MonitorId(RoomSvr); // 房间创建次数 @房间创建次数

    public static final MonitorId attr_roomsvr_async_create_chat_retry_fail = new MonitorId(RoomSvr); //房间异步创建聊天重试失败次数 @房间异步创建聊天重试失败次数
    public static final MonitorId attr_roomsvr_lbs_pin_join_fail_by_full = new MonitorId(RoomSvr); //面对面建房加入房间已满的次数 @面对面建房加入房间已满的次数
    public static final MonitorId attr_roomsvr_room_online_cost_gt_500_ms = new MonitorId(RoomSvr); //online耗时大于500ms @online耗时大于500ms
    public static final MonitorId attr_roomsvr_room_online_cost_gt_100_ms = new MonitorId(RoomSvr); //online耗时大于100ms @online耗时大于100ms
    public static final MonitorId attr_roomsvr_room_online_cost_gt_50_ms = new MonitorId(RoomSvr); //online耗时大于50ms @online耗时大于50ms
    public static final MonitorId attr_roomsvr_room_online_cost_gt_10_ms = new MonitorId(RoomSvr); //online耗时大于10ms @online耗时大于10ms
    public static final MonitorId attr_roomsvr_room_online_cost_lte_10_ms = new MonitorId(RoomSvr); //offline耗时小于等于10ms @offline耗时小于等于10ms
    public static final MonitorId attr_roomsvr_room_offline_cost_gt_500_ms = new MonitorId(RoomSvr); //offline耗时大于500ms @offline耗时大于500ms
    public static final MonitorId attr_roomsvr_room_offline_cost_gt_100_ms = new MonitorId(RoomSvr); //offline耗时大于100ms @offline耗时大于100ms
    public static final MonitorId attr_roomsvr_room_offline_cost_gt_50_ms = new MonitorId(RoomSvr); //offline耗时大于50ms @offline耗时大于50ms
    public static final MonitorId attr_roomsvr_room_offline_cost_gt_10_ms = new MonitorId(RoomSvr); //offline耗时大于10ms @offline耗时大于10ms
    public static final MonitorId attr_roomsvr_room_offline_cost_lte_10_ms = new MonitorId(RoomSvr); //offline耗时小于等于10ms @offline耗时小于等于10ms
    public static final MonitorId attr_roomsvr_player_update_member_base_info_cnt = new MonitorId(RoomSvr); //玩家同步信息到room的次数@玩家同步信息到room的次数
    public static final MonitorId attr_roomsvr_room_sync_full_info_to_client_cnt = new MonitorId(RoomSvr); // 全量同步信息到client的次数@全量同步信息到client的次数
    public static final MonitorId attr_roomsvr_room_player_heartbeat_timeout_cnt = new MonitorId(RoomSvr); //心跳超时玩家数量 @心跳超时玩家数量
    public static final MonitorId attr_roomsvr_room_co_match_cnt = new MonitorId(RoomSvr); // 再来一局单元的个数@再来一局单元的个数
    public static final MonitorId attr_roomsvr_room_co_match_start_cnt = new MonitorId(RoomSvr); // 再来一局开始匹配的次数@再来一局开始匹配的次数
    public static final MonitorId attr_roomsvr_room_co_match_result_route_fail_cnt = new MonitorId(RoomSvr); // 再来一局匹配结果通知失败的次数@再来一局开始匹配的次数

    public static final MonitorId attr_hok_team_warm_round_group = new MonitorId(RoomSvr); //hok 组队温暖局分组id
    public static final MonitorId attr_hok_team_first_round = new MonitorId(RoomSvr); //hok 组队新手第一句

    // 特殊玩法流程
    public static final MonitorId attr_play_mode_ugc_multi_round_score_match_succ_cnt = new MonitorId(PlayMode); // ugc多轮积分赛匹配成功@ugc多轮积分赛匹配成功
    public static final MonitorId attr_play_mode_ugc_multi_round_score_map_random_fail_cnt = new MonitorId(PlayMode); // ugc多轮积分赛地图随机失败@ugc多轮积分赛地图随机失败
    public static final MonitorId attr_play_mode_ugc_multi_round_score_pre_start_fail_cnt = new MonitorId(PlayMode); // ugc多轮积分赛倒计时开始失败@ugc多轮积分赛倒计时开始失败
    public static final MonitorId attr_play_mode_ugc_multi_round_score_start_fail_cnt = new MonitorId(PlayMode); // ugc多轮积分赛开始对局失败@ugc多轮积分赛开始对局失败
    public static final MonitorId attr_play_mode_ugc_multi_round_score_ready_timeout_cnt = new MonitorId(PlayMode); // ugc多轮积分赛超时倒计时次数@ugc多轮积分赛超时倒计时次数
    public static final MonitorId attr_play_mode_ugc_multi_round_score_wait_timeout_cnt = new MonitorId(PlayMode); // ugc多轮积分赛超时解散次数@ugc多轮积分赛超时解散次数
    public static final MonitorId attr_play_mode_ugc_multi_round_score_end_cnt = new MonitorId(PlayMode); // ugc多轮积分赛结束@ugc多轮积分赛结束
    public static final MonitorId attr_play_mode_ugc_multi_round_score_round_settlement_cnt = new MonitorId(PlayMode); // ugc多轮积分赛单轮结算@ugc多轮积分赛单轮结算

    //战场
    public static final MonitorId attr_battlesvr_battle_size = new MonitorId(BattleSvr);                  //房间个数 @当前战场总数
    public static final MonitorId attr_battlesvr_create_battle = new MonitorId(BattleSvr);                //创建战场个数 @创建战场
    public static final MonitorId attr_battlesvr_settlement_battle = new MonitorId(BattleSvr);            //战场结算个数 @战场结算
    public static final MonitorId attr_battlesvr_battle_membersize = new MonitorId(BattleSvr);            //战场成员个数(包括机器人) @战场玩家总数（包括机器人,退出对局的玩家）
    public static final MonitorId attr_battlesvr_battle_robotsize = new MonitorId(BattleSvr);             //战场机器人个数 @战场机器人总数
    public static final MonitorId attr_battlesvr_battle_alive_player_size = new MonitorId(BattleSvr);   //在对局中的真人数（不算机器人，放弃对局的也不算）
    public static final MonitorId attr_battlesvr_battle_matchtype_size = new MonitorId(BattleSvr);        //玩法个数 @当前玩法总数
    public static final MonitorId attr_battlesvr_battle_matchtype_membersize = new MonitorId(BattleSvr);  //玩法成员个数(包括机器人) @玩法玩家总数（包括机器人）
    public static final MonitorId attr_battlesvr_battle_matchtype_robotsize = new MonitorId(BattleSvr);   //玩法机器人个数 @玩法机器人总数
    public static final MonitorId attr_battlesvr_battle_matchtype_alive_player_size = new MonitorId(BattleSvr);   //玩法在对局中的真人数（不算机器人，放弃对局的也不算）

    public static final MonitorId attr_battlesvr_battle_create_timeout = new MonitorId(BattleSvr);        //CREATE_GAME_SESSION_TIMEOUT个数 @CREATE_GAME_SESSION_TIMEOUT总数
    public static final MonitorId attr_battlesvr_battle_create_abnormal_stop = new MonitorId(BattleSvr);  //CREATE_GAME_SESSION_ABNORMAL_STOP个数 @CREATE_GAME_SESSION_ABNORMAL_STOP总数
    public static final MonitorId attr_battlesvr_battle_create_process_force_terminated = new MonitorId(BattleSvr);  //CREATE_GAME_SESSION_DS_PROCESS_FORCE_TERMINATED个数 @CREATE_GAME_SESSION_DS_PROCESS_FORCE_TERMINATED总数
    public static final MonitorId attr_battlesvr_battle_create_fleet_force_terminated = new MonitorId(BattleSvr);  //CREATE_GAME_SESSION_FLEET_FORCE_TERMINATED个数 @CREATE_GAME_SESSION_FLEET_FORCE_TERMINATED总数
    public static final MonitorId attr_battlesvr_battle_create_game_session_too_long = new MonitorId(BattleSvr);  //CREATE_GAME_SESSION_GAME_SESSION_TOO_LONG个数 @CREATE_GAME_SESSION_GAME_SESSION_TOO_LONG总数
    public static final MonitorId attr_battlesvr_battle_create_game_session_check_pid_not_exist = new MonitorId(BattleSvr);  //CREATE_GAME_SESSION_CHECK_PID_NOT_EXIST个数 @CREATE_GAME_SESSION_CHECK_PID_NOT_EXIST总数
    public static final MonitorId attr_battlesvr_battle_end_abnormal = new MonitorId(BattleSvr);          //GAME_SESSION_ENDCODE_ABNORMAL个数 @GAME_SESSION_ENDCODE_ABNORMAL总数
    public static final MonitorId attr_battlesvr_battle_end_ds_process_force_terminated = new MonitorId(BattleSvr);          //GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED个数 @GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED总数
    public static final MonitorId attr_battlesvr_battle_end_fleet_force_terminated = new MonitorId(BattleSvr);          //GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED个数 @GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED总数
    public static final MonitorId attr_battlesvr_battle_end_game_seesion_too_long = new MonitorId(BattleSvr);          //GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG个数 @GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG总数
    public static final MonitorId attr_battlesvr_battle_end_process_ending_without_terminate_game_session = new MonitorId(BattleSvr);          //GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION个数 @GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION总数
    public static final MonitorId attr_battlesvr_battle_game_session_endcode_check_pid_not_exist = new MonitorId(BattleSvr);          //GAME_SESSION_ENDCODE_CHECK_PID_NOT_EXIST个数 @GAME_SESSION_ENDCODE_CHECK_PID_NOT_EXIST总数
    public static final MonitorId attr_battlesvr_battle_game_session_endcode_health_check_timeout = new MonitorId(BattleSvr);          //GAME_SESSION_ENDCODE_HEALTH_CHECK_TIMEOUT个数 @GAME_SESSION_ENDCODE_HEALTH_CHECK_TIMEOUT总数
    public static final MonitorId attr_battlesvr_battle_game_session_endcode_stop_for_recovery = new MonitorId(BattleSvr);          //GAME_SESSION_ENDCODE_STOP_FOR_RECOVERY个数 @GAME_SESSION_ENDCODE_STOP_FOR_RECOVERY总数
   

    public static final MonitorId attr_battlesvr_battle_mmr_score_less_0 = new MonitorId(BattleSvr);      // mmr分数统计小于0(mmr分数统计小于0)

    public static final MonitorId attr_battlesvr_current_robot_num = new MonitorId(BattleSvr);             //战场当前机器人个数 @战场机器人总数
    public static final MonitorId attr_battlesvr_matchtype_current_robot_num = new MonitorId(BattleSvr);   //玩法战场当前机器人个数 @战场机器人总数
    public static final MonitorId attr_battlesvr_leveltype_current_robot_num = new MonitorId(BattleSvr);   //关卡类型战场当前机器人个数 @战场机器人总数
    public static final MonitorId attr_battlesvr_levelid_current_robot_num = new MonitorId(BattleSvr);     //关卡Id战场当前机器人个数 @战场机器人总数
    
    public static final MonitorId attr_battlesvr_tlog_ds_flow = new MonitorId(BattleSvr);            //Battle DS Tlog日志
    public static final MonitorId attr_battlesvr_ds_force_terminate = new MonitorId(BattleSvr);            //战场强制结束次数 @战场强制结束
    public static final MonitorId attr_battlesvr_ds_migrate_terminate = new MonitorId(BattleSvr);            //战场强制结束次数 @战场强制结束
    public static final MonitorId attr_battlesvr_ds_overtime = new MonitorId(BattleSvr);            //战场超时次数 @战场超时次数

    public static final MonitorId attr_battlesvr_midJoin_num = new MonitorId(BattleSvr);            //中途加入次数结果
    public static final MonitorId attr_battlesvr_midJoin_search_num = new MonitorId(BattleSvr);            //中途加入搜索结果
    public static final MonitorId attr_battlesvr_midJoin_check_num = new MonitorId(BattleSvr);            //中途加入检查结果
    public static final MonitorId attr_battlesvr_fillback_result = new MonitorId(BattleSvr);            //中途加入回填结果
    public static final MonitorId attr_battlesvr_midJoin_avg_time = new MonitorId(BattleSvr);            //中途加入的战场的平均时间
    public static final MonitorId attr_battlesvr_midJoin_avg_players = new MonitorId(BattleSvr);            //中途加入的战场的平均人数

    public static final MonitorId attr_battlesvr_ds_ai_stat_count = new MonitorId(BattleSvr); //统计次数
    public static final MonitorId attr_battlesvr_ds_ai_connect_succ_rate = new MonitorId(BattleSvr); //连接成功率
    public static final MonitorId attr_battlesvr_ds_ai_connect_succ_count = new MonitorId(BattleSvr); //连接成功次数
    public static final MonitorId attr_battlesvr_ds_ai_connect_fail_count = new MonitorId(BattleSvr); //连接失败次数
    public static final MonitorId attr_battlesvr_ds_ai_send_pack_count = new MonitorId(BattleSvr); //发送包数量
    public static final MonitorId attr_battlesvr_ds_ai_recv_pack_count = new MonitorId(BattleSvr); //接受包数量
    public static final MonitorId attr_battlesvr_ds_ai_send_pack_bytes_KB = new MonitorId(BattleSvr); //发送总流量
    public static final MonitorId attr_battlesvr_ds_ai_recv_pack_bytes_KB = new MonitorId(BattleSvr); //接收总流量
    public static final MonitorId attr_battlesvr_ds_ai_rtt_ms_max = new MonitorId(BattleSvr); //请求往返延迟最大值
    public static final MonitorId attr_battlesvr_ds_ai_rtt_ms_avg = new MonitorId(BattleSvr); //请求往返延迟平均值
    public static final MonitorId attr_battlesvr_ds_ai_robot_num = new MonitorId(BattleSvr); //机器人数量
    
    public static final MonitorId attr_battlesvr_ds_localai_result = new MonitorId(BattleSvr);  //拉起成功或者失败
    public static final MonitorId attr_battlesvr_ds_localai_robot = new MonitorId(BattleSvr);   //AI人数
    public static final MonitorId attr_battlesvr_ds_localai_concurrency = new MonitorId(BattleSvr);   //请求并发数
    public static final MonitorId attr_battlesvr_ds_localai_requesttime = new MonitorId(BattleSvr);  //请求并发数

    public static final MonitorId attr_battlesvr_chase_new_bie_guide_count = new MonitorId(BattleSvr);  //大王新手引导次数

    public static final MonitorId attr_battlesvr_is_warm_round = new MonitorId(BattleSvr);  // 战斗是温暖局 @战斗是温暖局

    //公会
    public static final MonitorId attr_clubsvr_club_size = new MonitorId(ClubSvr);               //公会个数 @当前公会总数
    public static final MonitorId attr_clubsvr_membersize = new MonitorId(ClubSvr);               //公会成员个数 @公会玩家总数
    public static final MonitorId attr_clubsvr_active_club_size = new MonitorId(ClubSvr);               //活跃公会个数 @当前活跃公会总数
    public static final MonitorId attr_clubsvr_active_club_rank_empty = new MonitorId(ClubSvr);               //活跃公会排行榜为空 @活跃公会排行榜为空

    // gamesvr room操作
    public static final MonitorId attr_gamesvr_create_room = new MonitorId(Gamesvr);      //创建房间 @新建房间

    public static final MonitorId attr_gamesvr_join_room = new MonitorId(Gamesvr);        //加入房间 加入房间的唯一接口 @加入房间

    public static final MonitorId attr_gamesvr_leave_room = new MonitorId(Gamesvr);       //离开房间 @离开房间

    public static final MonitorId attr_gamesvr_wantojoin_room = new MonitorId(Gamesvr);   //想要加入 @求邀

    public static final MonitorId attr_gamesvr_reply_wantojoin_room = new MonitorId(Gamesvr);     //想要加入的回复,可能无回复 @求邀结果

    public static final MonitorId attr_gamesvr_kick_player_room = new MonitorId(Gamesvr);         //踢人       @踢人

    public static final MonitorId attr_gamesvr_invite_player_room = new MonitorId(Gamesvr);       //邀请玩家    @邀请玩家

    public static final MonitorId attr_gamesvr_reply_invite_player_room = new MonitorId(Gamesvr); // 被邀请玩家的回复,可能无回复 @邀请回复

    public static final MonitorId attr_gamesvr_invite_lock_room = new MonitorId(Gamesvr); // 邀请锁定 @邀请锁

    public static final MonitorId attr_gamesvr_modify_gamemode_room = new MonitorId(Gamesvr);     //更改房间类型 @更改游戏模式

    public static final MonitorId attr_gamesvr_start_match_room = new MonitorId(Gamesvr);         //开始匹配   @匹配计数

    public static final MonitorId attr_gamesvr_start_ready_room = new MonitorId(Gamesvr);         //准备,取消准备      @准备

    public static final MonitorId attr_gamesvr_join_room_fromQQ = new MonitorId(Gamesvr);         //从QQ加入   @从QQ加入房间

    public static final MonitorId attr_gamesvr_update_room = new MonitorId(Gamesvr);         //房间信息更新  @房间信息更新

    // gamesvr club操作
    public static final MonitorId attr_gamesvr_create_club = new MonitorId(Gamesvr);      //创建公会 @新建公会

    public static final MonitorId attr_gamesvr_joib_club = new MonitorId(Gamesvr);        //加入公会 @加入公会

    public static final MonitorId attr_gamesvr_exit_club = new MonitorId(Gamesvr);        //离开公会 @离开公会

    public static final MonitorId attr_gamesvr_apply_club = new MonitorId(Gamesvr);       //申请公会 @申请公会

    public static final MonitorId attr_gamesvr_dissolve_club = new MonitorId(Gamesvr);    //解散公会 @解散公会

    public static final MonitorId attr_gamesvr_setup_club = new MonitorId(Gamesvr);       //公会设置 @公会设置

    public static final MonitorId attr_gamesvr_modify_club_score = new MonitorId(Gamesvr);       //修改公会评分 @修改公会评分

    public static final MonitorId attr_gamesvr_alloc_short_uid = new MonitorId(Gamesvr);         //申请短uid   @申请短uid

    public static final MonitorId attr_gamesvr_alloc_bless_bag = new MonitorId(Gamesvr);      // 分配福袋 @分配福袋

    public static final MonitorId attr_gamesvr_update_plat_privileges = new MonitorId(Gamesvr);   // 更新平台特权 @更新平台特权

    public static final MonitorId attr_gamesvr_album_wall_like = new MonitorId(Gamesvr);          // 照片墙点赞
    public static final MonitorId attr_gamesvr_album_get_rank = new MonitorId(Gamesvr);           // 照片墙获取排行
    public static final MonitorId attr_gamesvr_album_get_pic_info = new MonitorId(Gamesvr);       // 照片墙获取照片信息
    public static final MonitorId attr_gamesvr_album_get_pic_like_num = new MonitorId(Gamesvr);   // 照片墙获取照片点赞数
    public static final MonitorId attr_gamesvr_album_get_rank_cache_miss = new MonitorId(Gamesvr);  // 照片墙获取排行缓存miss
    public static final MonitorId attr_gamesvr_album_get_pic_info_cache_miss = new MonitorId(Gamesvr);       // 照片墙获取照片信息缓存miss
    public static final MonitorId attr_gamesvr_album_get_pic_like_num_cache_miss = new MonitorId(Gamesvr);   // 照片墙获取照片点赞数缓存miss
    public static final MonitorId attr_gamesvr_update_album_info = new MonitorId(Gamesvr);        // 更新相册信息 @更新相册信息
    public static final MonitorId attr_gamesvr_album_info_store_table = new MonitorId(Gamesvr);   // 相册信息存储表 @相册信息存储表
    public static final MonitorId attr_gamesvr_album_info_migrate = new MonitorId(Gamesvr);       // 相册信息迁移 @相册信息迁移
    public static final MonitorId attr_gamesvr_update_albumLike_info = new MonitorId(Gamesvr);    // 更新相册点赞信息 @更新相册点赞信息
    public static final MonitorId attr_gamesvr_album_num_over_60 = new MonitorId(Gamesvr);        // 相册数量大于容量的60% @相册数量大于容量的60%
    public static final MonitorId attr_gamesvr_album_num_over_90 = new MonitorId(Gamesvr);        // 相册数量大于容量的90% @相册数量大于容量的90%
    public static final MonitorId attr_gamesvr_update_albumLike_info_not_load = new MonitorId(Gamesvr);    // 更新相册点赞信息时未加载完毕 @更新相册点赞信息时未加载完毕
    public static final MonitorId attr_gamesvr_update_album_ext_info = new MonitorId(Gamesvr);        // 更新相册扩展信息 @更新相册扩展信息
    public static final MonitorId attr_gamesvr_load_battle_count_form_tlog_failure = new MonitorId(Gamesvr); // 从tlog中加载对局次数 @从tlog中加载对局次数
    public static final MonitorId attr_gamesvr_load_total_loginday_form_tlog_failure = new MonitorId(Gamesvr); // 从tlog中加载对局次数 @从tlog中加载对局次数

    // 关系链
    public static final MonitorId attr_relation_add_msg_retry_count = new MonitorId(Relation);    // 消息列表并发重试次数 @消息列表并发重试次数

    public static final MonitorId attr_relation_online_friend_hot_data_update_count = new MonitorId(Relation); // 每次更新在线好友热数据数量 @每次更新在线好友热数据数量

    public static final MonitorId attr_relation_friend_online_status_update_count = new MonitorId(Relation); // 每次更新好友在线状态数量 @每次更新好友在线状态数量

    public static final MonitorId attr_relation_plat_friend_update = new MonitorId(Relation);   // 更新平台好友列表次数 @更新平台好友列表次数

    public static final MonitorId attr_relation_add_msg_limit_err = new MonitorId(Relation);   // add消息列表数量达到上限次数 @add消息列表数量达到上限次数

    public static final MonitorId attr_relation_agree_msg_limit_err = new MonitorId(Relation);   // agree消息列表数量达到上限次数 @agree消息列表数量达到上限次数

    public static final MonitorId attr_relation_deny_msg_limit_err = new MonitorId(Relation);   // deny消息列表数量达到上限次数 @deny消息列表数量达到上限次数

    public static final MonitorId attr_relation_remove_msg_limit_err = new MonitorId(Relation);   // remove消息列表数量达到上限次数 @remove消息列表数量达到上限次数

    public static final MonitorId attr_relation_friend_limit_count = new MonitorId(Relation);   // 好友数量达到上限的次数 @好友数量达到上限的次数
    public static final MonitorId attr_batch_notify_friend_data_change_info_count = new MonitorId(
            Relation);   // 批量同步X个好友数据变化
    public static final MonitorId attr_batch_notify_friend_data_change_num = new MonitorId(
            Relation);   // 批量同步好友数据变化次数
    public static final MonitorId attr_rpc_batch_notify_friend_data_change_info_count = new MonitorId(
            Relation);   // rpc批量同步X个玩家数据给好友
    public static final MonitorId attr_rpc_batch_notify_friend_data_change_num = new MonitorId(
            Relation);   // rpc批量同步数据给好友次数

    // 聊天
    public static final MonitorId attr_chatsvr_chat_group_size = new MonitorId(Chatsvr); // chatsvr聊天组缓存大小 @聊天缓存大小

    public static final MonitorId attr_chatsvr_chat_group_insert = new MonitorId(Chatsvr); // chatsvr聊天插入 @新建聊天

    public static final MonitorId attr_chatsvr_chat_group_update = new MonitorId(Chatsvr); // chatsvr聊天更新 @更新聊天

    public static final MonitorId attr_chatsvr_chat_not_exist_cnt = new MonitorId(Chatsvr); // chatsvr不存在计数 @聊天不存在

    public static final MonitorId attr_chatsvr_chat_msg_insert = new MonitorId(Chatsvr); // chatsvr消息插入 @添加消息
    public static final MonitorId attr_chatsvr_chat_msg_insert_blocked = new MonitorId(Chatsvr); // chatsvr消息插入屏蔽 @消息落db屏蔽
    public static final MonitorId attr_chatsvr_chat_msg_channel_insert_blocked = new MonitorId(Chatsvr); //按频道类型不插入数据库

    public static final MonitorId attr_chatsvr_insert_Interaction_msg_type = new MonitorId(Chatsvr); // chatsvr 消息插入离线队列的消息类型 @消息类型
    public static final MonitorId attr_chatsvr_chat_session_insert = new MonitorId(Chatsvr); // chatsvr聊天状态插入 @新建聊天状态

    public static final MonitorId attr_chatsvr_async_chat_msg_notification = new MonitorId(Chatsvr); // chatsvr异步通知gamesvr @通知gamesvr

    public static final MonitorId attr_chatsvr_join_chat_group_cnt = new MonitorId(Chatsvr); // chatsvr加入聊天计数 @加入群聊

    public static final MonitorId attr_chatsvr_join_chat_group_lock_err = new MonitorId(Chatsvr); // chatsvr加入聊天锁异常 @加入群聊的锁异常

    public static final MonitorId attr_chatsvr_quit_chat_group_cnt = new MonitorId(Chatsvr); // chatsvr退出聊天计数 @退出群聊

    public static final MonitorId attr_chatsvr_quit_chat_group_lock_err = new MonitorId(Chatsvr); // chatsvr退出聊天锁异常 @退出群聊的锁异常

    public static final MonitorId attr_chatsvr_tss_check_dirty_fail = new MonitorId(Chatsvr); // chatsvr tss脏词检测失败 @脏词检测失败

    public static final MonitorId attr_chatsvr_chat_ban = new MonitorId(Gamesvr); // gamesvr 聊天禁言 @聊天禁言

    public static final MonitorId attr_chatsvr_chat_record_insert_via_cache_fail = new MonitorId(Chatsvr); // chatsvr缓存数据同步到db失败 @缓存数据同步到db失败

    public static final MonitorId attr_chat_private_msg_red_dot_number = new MonitorId(Chatsvr); // 私聊离线红点计数 @私聊离线红点计数
    public static final MonitorId attr_chatsvr_chat_remind_notice = new MonitorId(Chatsvr); // 被提及到的通知消息 @被提及到的通知消息

    // 平台
    public static final MonitorId attr_plat_http_req = new MonitorId(platform); // 平台http请求 @平台http请求
    public static final MonitorId attr_plat_co_http_proxy_queue_deep = new MonitorId(platform);           // cohttp代理消息队列长度 @cohttp代理消息队列长度
    public static final MonitorId attr_plat_co_http_proxy_queue_full = new MonitorId(platform);           // cohttp代理消息队列满了 @cohttp代理消息队列满了
    public static final MonitorId attr_plat_co_http_proxy_proc_cnt = new MonitorId(platform);           // cohttp代理消息处理数量 @cohttp代理消息处理数量
    public static final MonitorId attr_plat_co_http_proxy_task_proc_cnt = new MonitorId(platform);           // cohttp代理消息http任务处理数量 @cohttp代理消息http任务处理数量
    public static final MonitorId attr_plat_co_http_proxy_long_block = new MonitorId(platform);           // cohttp代理消息http任务阻塞数量 @cohttp代理消息http任务阻塞数量

    public static final MonitorId attr_plat_profile_update = new MonitorId(platform); // 更新玩家平台信息次数 @更新玩家平台信息次数
    public static final MonitorId attr_msdk_http_req = new MonitorId(platform); // msdk平台http请求 @msdk平台http请求
    public static final MonitorId attr_msdk_userinfo_req = new MonitorId(platform); // msdk玩家信息请求 @msdk玩家信息请求
    public static final MonitorId attr_msdk_friend_list_req = new MonitorId(platform); // msdk好友列表请求 @msdk好友列表请求
    public static final MonitorId attr_msdk_report_req = new MonitorId(platform); // msdk成就上报请求 @msdk成就上报请求
    public static final MonitorId attr_msdk_verify_login_req = new MonitorId(platform); // msdk鉴权http请求 @msdk鉴权http请求

    public static final MonitorId attr_msdk_get_free_flow_info_req = new MonitorId(platform); // msdk免流信息http请求 @msdk免流信息http请求
    public static final MonitorId attr_msdk_ark_share_req = new MonitorId(platform); // msdk后台分享请求 @msdk后台分享请求
    public static final MonitorId attr_msdk_openid2uid_notoken_req = new MonitorId(platform); // msdk无鉴权Gopenid获取Openid请求 @msdk无鉴权Gopenid获取Openid请求
    public static final MonitorId attr_msdk_openid2uid_req = new MonitorId(platform); // msdk通过Gopenid获取Openid请求 @msdk通过Gopenid获取Openid请求
    public static final MonitorId attr_msdk_uid2openid_notoken_req = new MonitorId(platform); // msdk无鉴权Openid获取Gopenid请求 @msdk无鉴权Openid获取Gopenid请求
    public static final MonitorId attr_msdk_get_bind_info_req = new MonitorId(platform); // msdk获取绑定信息请求 @msdk获取绑定信息请求
    public static final MonitorId attr_msdk_unbind_req = new MonitorId(platform); // msdk解绑请求 @msdk解绑请求

    public static final MonitorId attr_plat_async_client_http_req = new MonitorId(platform);  // 平台侧异步HTTP客户端请求 @平台侧异步HTTP客户端请求
    public static final MonitorId attr_plat_async_client_http_rsp = new MonitorId(platform);  // 平台侧异步HTTP客户端响应 @平台侧异步HTTP客户端响应
    public static final MonitorId attr_plat_async_client_http_cancelled = new MonitorId(platform);  // 平台侧异步HTTP客户端终止 @平台侧异步HTTP客户端终止
    public static final MonitorId attr_plat_async_client_http_cost_too_long = new MonitorId(platform);  // 平台侧HTTP客户端耗时过长 @平台侧异步HTTP客户端耗时过长
    public static final MonitorId attr_plat_client_http_req = new MonitorId(platform);  // 平台侧HTTP客户端请求 @平台侧HTTP客户端请求
    public static final MonitorId attr_plat_client_http_rsp = new MonitorId(platform);  // 平台侧HTTP客户端响应 @平台侧HTTP客户端响应
    public static final MonitorId attr_plat_client_http_cancelled = new MonitorId(platform);  // 平台侧HTTP客户端终止 @平台侧HTTP客户端终止
    public static final MonitorId attr_plat_client_http_cost_too_long = new MonitorId(platform);  // 平台侧HTTP客户端耗时过长 @平台侧HTTP客户端耗时过长

    // IdipSvr
    public static final MonitorId attr_idip_http_req = new MonitorId(IdipSvr); // IdipHttp请求 @IdipHttp请求
    public static final MonitorId attr_idip_http_req_cmdid = new MonitorId(IdipSvr);  // 基于cmdid分类的IdipHttp请求 @基于cmdid分类的IdipHttp请求
    public static final MonitorId attr_idip_http_req_result = new MonitorId(IdipSvr); // 基于result分类的IdipHttp请求 @基于result分类的IdipHttp请求
    public static final MonitorId attr_idip_ugc_platform_http_req = new MonitorId(IdipSvr);   // ugc平台http请求 @ugc平台http请求
    public static final MonitorId attr_idip_datamore_platform_http_req = new MonitorId(IdipSvr);  // datamore平台http请求 @datamore平台http请求
    public static final MonitorId attr_idip_request_forward_http_req = new MonitorId(IdipSvr);    // idip请求转发请求 @idip请求转发请求
    public static final MonitorId attr_idip_request_process_handler = new MonitorId(IdipSvr);     // idip请求处理handler @idip请求处理handler

    // C2sReq
    public static final MonitorId attr_c2s_req = new MonitorId(C2sReq);          // 客户端cs累计请求 @客户端cs请求
    public static final MonitorId attr_c2s_req_cost = new MonitorId(C2sReq);          // 客户端cs请求耗时
    public static final MonitorId attr_c2s_req_limit = new MonitorId(C2sReq);         //请求限制
    public static final MonitorId attr_c2s_req_size = new MonitorId(C2sReq);          // 客户端cs请求包体大小 @客户端cs请求包体大小

    public static final MonitorId attr_c2s_req_unknown_fail = new MonitorId(C2sReq);      // 客户端cs累计请求未知错误次数 @cs累计未知错误

    public static final MonitorId attr_s2c_and_ntf_msg = new MonitorId(C2sReq);        // 服务端s2c及ntf累计发送 @服务端s2c及ntf累计发送

    public static final MonitorId attr_s2c_and_ntf_size = new MonitorId(C2sReq);        // 服务端s2c及ntf包体大小 @服务端s2c及ntf包体大小

    // Qualifying
    public static final MonitorId attr_qualifying_settlement_season_mismatch = new MonitorId(Qualifying); // 段位结算赛季不匹配 @段位结算赛季不匹配
    public static final MonitorId attr_qualifying_shocksreward = new MonitorId(Qualifying); // 冲段奖励领取 @冲段奖励领取
    public static final MonitorId attr_qualifying_show_unlock = new MonitorId(Gamesvr); //gamesvr 段位展示解锁 @段位展示解锁
    public static final MonitorId attr_qualifying_reward_unlock = new MonitorId(Gamesvr);//gamesvr 段位奖励达成解锁 @段位奖励展示解锁

    // Statistics
    public static final MonitorId attr_chat_channel_allocation = new MonitorId(Statistics); // 聊天频道分配 @聊天频道分配
    public static final MonitorId attr_chat_offline_interaction_num_lt_10 = new MonitorId(Statistics); // 离线消息条数小于10条 @离线消息条数小于10条
    public static final MonitorId attr_chat_offline_interaction_num_lt_20 = new MonitorId(Statistics); // 离线消息条数小于20条 @离线消息条数小于20条
    public static final MonitorId attr_chat_offline_interaction_num_lt_50 = new MonitorId(Statistics); // 离线消息条数小于20条 @离线消息条数小于20条
    public static final MonitorId attr_chat_offline_interaction_num_lt_100 = new MonitorId(Statistics); // 离线消息条数小于20条 @离线消息条数小于20条
    public static final MonitorId attr_chat_offline_interaction_num_gte_20 = new MonitorId(Statistics); // 离线消息条数大于等于20条 @离线消息条数大于等于20条
    public static final MonitorId attr_chat_offline_interaction_num_gte_100 = new MonitorId(Statistics); // 离线消息条数大于等于20条 @离线消息条数大于等于20条
    public static final MonitorId attr_chat_reddot_ntf_num_lte_1 = new MonitorId(Statistics); // 离线消息推送数量小于等于1
    public static final MonitorId attr_chat_reddot_ntf_num_lte_2 = new MonitorId(Statistics); // 离线消息推送数量小于等于2
    public static final MonitorId attr_chat_reddot_ntf_num_lte_5 = new MonitorId(Statistics); // 离线消息推送数量小于等于5
    public static final MonitorId attr_chat_reddot_ntf_num_lte_10 = new MonitorId(Statistics); // 离线消息推送数量小于等于10
    public static final MonitorId attr_chat_reddot_ntf_num_gt_10 = new MonitorId(Statistics); // 离线消息推送数量大于10

    public static final MonitorId attr_issue_notify_player = new MonitorId(Statistics);  // 全服广播通知玩家 @全服广播通知玩家
    public static final MonitorId attr_issue_notify_num = new MonitorId(Statistics);  // 全服广播通知数量 @全服广播通知数量
    public static final MonitorId attr_register_tag_limit = new MonitorId(Statistics); // 标签玩家注册限制统计 @标签玩家注册限制统计
    public static final MonitorId attr_gamesvr_msg_list_map_size = new MonitorId(Statistics); // gamesvr全局消息缓存map大小 @gamesvr全局消息缓存map大小
    public static final MonitorId attr_gamesvr_msg_list_update_interval = new MonitorId(Statistics); // gamesvr频道消息更新间隔 @gamesvr频道消息更新间隔
    public static final MonitorId attr_gamesvr_msg_list_remove_interval_size = new MonitorId(Statistics); // gamesvr频道消息清理区间大小 @gamesvr频道消息清理区间大小
    public static final MonitorId attr_guid_allocator_update_bak_cnt = new MonitorId(Statistics); // guid预分配次数 @guid预分配次数
    public static final MonitorId attr_gamesvr_fill_play_by_recruit_fetched_from_remote = new MonitorId(Statistics); //gamesvr从远程直接填充招募信息 @gamesvr从远程直接填充招募信息
    public static final MonitorId attr_gamesvr_fill_playgroup_by_recruit_fetched_from_remote = new MonitorId(Statistics); //gamesvr从远程直接填充招募信息 @gamesvr从远程直接填充招募信息
    // scene
    public static final MonitorId attr_scenesvr_scene_size = new MonitorId(SceneSvr);         //场景个数 @当前场景总数
    public static final MonitorId attr_scenesvr_create_scene = new MonitorId(SceneSvr);      //创建场景个数 @创建场景
    public static final MonitorId attr_scenesvr_scene_membersize = new MonitorId(SceneSvr);      //场景内玩家数量(不包括机器人) @场景玩家总数（不包括机器人）
    public static final MonitorId attr_scenesvr_gameplay_end_mapentity_size = new MonitorId(SceneSvr);     //场景玩法一轮结束时物件数量 @场景玩法结束时物件数量
    public static final MonitorId attr_scenesvr_mapentity_create = new MonitorId(SceneSvr);     //场景玩法新增物件 @场景玩法新增物件
    public static final MonitorId attr_scenesvr_mapentity_destroy = new MonitorId(SceneSvr);     //场景玩法销毁物件 @场景玩法销毁物件
    public static final MonitorId attr_scenesvr_frame_move_count_gt_10 = new MonitorId(SceneSvr);     //单帧移动次数超过10 @单帧移动次数超过10
    public static final MonitorId attr_scenesvr_frame_action_count_gt_10 = new MonitorId(SceneSvr);     //单帧Action次数超过10 @单帧Action次数超过10
    public static final MonitorId attr_scenesvr_lobby_size = new MonitorId(SceneSvr);         //大厅个数 @当前大厅总数
    public static final MonitorId attr_scenesvr_round_size = new MonitorId(SceneSvr);         //对局个数 @当前对局总数
    public static final MonitorId attr_scenesvr_lobby_membersize = new MonitorId(SceneSvr);      //大厅内玩家数量(不包括机器人) @大厅玩家总数（不包括机器人）
    public static final MonitorId attr_scenesvr_round_membersize = new MonitorId(SceneSvr);      //对局内玩家数量(不包括机器人) @对局玩家总数（不包括机器人）

    // rank
    public static final MonitorId attr_rank_backend_remove_obsolete = new MonitorId(Rank);       //排行榜过期榜单清榜个数 @排行榜过期榜单清榜个数
    public static final MonitorId attr_rank_backend_remove_all = new MonitorId(Rank);       //排行榜榜单清榜个数 @排行榜榜单清榜个数
    public static final MonitorId attr_rank_backend_remove_gm = new MonitorId(Rank);       //排行榜榜单GM清榜个数 @排行榜榜单GM清榜个数
    public static final MonitorId attr_rank_cache_kv_empty = new MonitorId(Rank);           //排行榜缓存键值为空 @排行榜缓存键值为空
    public static final MonitorId attr_rank_cache_kv_expired = new MonitorId(Rank);         //排行榜缓存键值过期 @排行榜缓存键值过期
    public static final MonitorId attr_rank_cache_kv_expired_reuse = new MonitorId(Rank);         //排行榜缓存键值过期使用过期值 @排行榜缓存键值过期使用过期值
    public static final MonitorId attr_rank_cache_kv_retry = new MonitorId(Rank);           //排行榜缓存键值查找重试 @排行榜缓存键值查找重试
    public static final MonitorId attr_rank_cache_kv_ok = new MonitorId(Rank);         //排行榜缓存键值已找到 @排行榜缓存键值已找到
    public static final MonitorId attr_rank_refreshed = new MonitorId(Rank);         //排行榜榜单缓存更新 @排行榜榜单缓存更新
    public static final MonitorId attr_rank_fetched_from_ranksvr = new MonitorId(Rank);  //排行榜榜单从Ranksvr获取 @排行榜榜单从Ranksvr获取
    public static final MonitorId attr_rank_fetched_from_redis = new MonitorId(Rank);    //排行榜榜单从Redis获取 @排行榜榜单从Redis获取
    public static final MonitorId attr_rank_fetched_from_local = new MonitorId(Rank);    //排行榜榜单从本地缓存获取 @排行榜榜单从本地缓存获取
    public static final MonitorId attr_rank_self_not_found = new MonitorId(Rank);  //排行榜自身排名未找到 @排行榜自身排名未找到
    public static final MonitorId attr_rank_send_tlog_snapshot = new MonitorId(Rank); //排行榜发送Tlog快照 @排行榜发送Tlog快照
    public static final MonitorId attr_rank_get_size = new MonitorId(Rank); //排行榜获取榜单大小 @排行榜获取榜单大小
    public static final MonitorId attr_rank_size = new MonitorId(Rank); //排行榜榜单大小 @排行榜榜单大小
    public static final MonitorId attr_rank_resize = new MonitorId(Rank); //排行榜调整榜单大小 @排行榜调整榜单大小
    public static final MonitorId attr_rank_end_of_list_count = new MonitorId(Rank); //排行榜榜单末尾缓存数量 @排行榜榜单末尾缓存数量
    public static final MonitorId attr_rank_upload_intercepted = new MonitorId(Rank); //排行榜更新请求被拦截 @排行榜更新请求被拦截
    public static final MonitorId attr_rank_friend_ranker = new MonitorId(Rank); //好友排行榜请求量 @好友排行榜请求量
    public static final MonitorId attr_rank_global_ranker = new MonitorId(Rank); //全服排行榜请求量 @全服排行榜请求量
    public static final MonitorId attr_rank_geo_ranker = new MonitorId(Rank); //地区排行榜请求量 @地区排行榜请求量
    public static final MonitorId attr_rank_ugc_ranker = new MonitorId(Rank); //UGC排行榜请求量 @UGC排行榜请求量
    public static final MonitorId attr_rank_fetch_ugc_plat_common = new MonitorId(Rank);    //请求UGC平台数据 @请求UGC平台数据
    public static final MonitorId attr_rank_fetch_player_public_score = new MonitorId(Rank); //请求玩家公开分数 @请求玩家公开分数
    public static final MonitorId attr_rank_fetch_zset_score = new MonitorId(Rank); //请求玩家ZSet分数 @请求玩家ZSet分数
    public static final MonitorId attr_rank_known_lbs_count = new MonitorId(Rank);  //榜单已知LBS子榜数量 @榜单已知LBS子榜数量
    public static final MonitorId attr_rank_fetch_cs_too_frequent = new MonitorId(Rank); //拉榜单CS协议过多 @拉榜单CS协议过多
    public static final MonitorId attr_rank_geo_settlement_unready = new MonitorId(Rank); //地理排行榜结算未就绪 @地理排行榜结算未就绪
    public static final MonitorId attr_rank_geo_reset = new MonitorId(Rank); //地理排行榜重置 @地理排行榜重置
    public static final MonitorId attr_rank_geo_code_invalid = new MonitorId(Rank); //地理排行榜编码错误 @地理排行榜编码错误
    public static final MonitorId attr_rank_info_fetch_exceed_limit = new MonitorId(Rank); //排行榜信息拉取过多 @排行榜信息拉取过多
    public static final MonitorId attr_rank_coordination_retry_exceed_limit = new MonitorId(Rank); //榜单处理重试超出次数 @榜单处理重试超出次数
    public static final MonitorId attr_rank_coordination_leader_init = new MonitorId(Rank); //榜单处理任务初始化 @榜单处理任务初始化
    public static final MonitorId attr_rank_coordination_follower_response = new MonitorId(Rank); //榜单处理响应 @榜单处理响应
    public static final MonitorId attr_rank_plat_season_not_found = new MonitorId(Rank); //榜单平台侧赛季ID未找到 @榜单平台侧赛季ID未找到
    public static final MonitorId attr_rank_plat_report_score = new MonitorId(Rank); //榜单平台侧分数更新 @榜单平台侧分数更新
    public static final MonitorId attr_rank_plat_report_score_batch = new MonitorId(Rank); //榜单平台侧分数批量更新 @榜单平台侧分数批量更新
    public static final MonitorId attr_rank_plat_report_score_batch_size = new MonitorId(Rank); //榜单平台侧分数批量更新大小 @榜单平台侧分数批量更新大小
    public static final MonitorId attr_rank_plat_fetch_score = new MonitorId(Rank); //榜单平台侧拉取 @榜单平台侧拉取
    public static final MonitorId attr_rank_plat_fetch_score_by_score = new MonitorId(Rank); //榜单平台侧通过分数拉取 @榜单平台侧通过分数拉取
    public static final MonitorId attr_rank_plat_remove_score = new MonitorId(Rank); //榜单平台侧清榜 @榜单平台侧清榜
    public static final MonitorId attr_rank_plat_get_one_user_score = new MonitorId(Rank); //榜单平台侧查询 @榜单平台侧查询
    public static final MonitorId attr_rank_plat_remove_one_user_score = new MonitorId(Rank); //榜单平台侧删除 @榜单平台侧删除
    public static final MonitorId attr_rank_plat_move_one_user_score = new MonitorId(Rank); //榜单平台侧分数迁移 @榜单平台侧分数迁移
    // LBS
    public static final MonitorId attr_lbs_report = new MonitorId(LBS); //LBS上报 @LBS上报
    public static final MonitorId attr_lbs_remove = new MonitorId(LBS); //LBS删除 @LBS删除
    public static final MonitorId attr_lbs_search = new MonitorId(LBS); //LBS搜索 @LBS搜索

    // 灰度
    public static final MonitorId attr_ugc_apply_key_info = new MonitorId(UgcSvr);  //申请key info
    public static final MonitorId attr_ugc_apply_key_limit_exceeded = new MonitorId(UgcSvr);  //申请key 已经达到了上限
    public static final MonitorId attr_ugc_apply_bucket_info = new MonitorId(Gamesvr);  //申请bucket
    public static final MonitorId attr_ugc_publish_map = new MonitorId(Gamesvr);  //地图发布
    public static final MonitorId attr_ugc_download_secret = new MonitorId(UgcSvr);  //下载

    public static final MonitorId attr_ugc_servlet = new MonitorId(UgcSvr);  //平台调用游戏的http请求接口
    public static final MonitorId attr_ugc_servlet_flow_control = new MonitorId(UgcSvr);  //平台调用游戏的http请求接口,触发限流
    public static final MonitorId attr_ugc_Apply_Bucket = new MonitorId(UgcSvr);  //申请bucket
    public static final MonitorId attr_ugc_Apply_Common_Bucket = new MonitorId(UgcSvr);  //申请bucket
    public static final MonitorId attr_ugc_logic = new MonitorId(UgcSvr);  //平台调用游戏的http逻辑接口
    public static final MonitorId attr_ugc_flow_control = new MonitorId(UgcSvr);  //ugcplatsvr如果达到限流进行上报

    public static final MonitorId attr_ugc_omd_sel_statistics = new MonitorId(UgcOMD);  //ugc兽人选择次数统计
    public static final MonitorId attr_ugc_omd_sel_change_statistics = new MonitorId(UgcOMD);  //ugc兽人大厅选择地图 访问次数统计

    public static final MonitorId attr_ugc_omd_enter_statistics = new MonitorId(UgcOMD);  //ugc兽人选择次数统计
    public static final MonitorId attr_ugc_omd_enter_change_statistics = new MonitorId(UgcOMD);  //ugc兽人大厅选择地图 访问次数统计

    public static final MonitorId attr_ugc_omd_result_statistics = new MonitorId(UgcOMD);  //ugc兽人大厅结算次数统计

    public static final MonitorId attr_ugc_publish_list_map = new MonitorId(Gamesvr);  //地图发布列表
    public static final MonitorId attr_ugc_check_md5 = new MonitorId(Gamesvr);  //地图发布校验
    public static final MonitorId attr_ugc_operate_map = new MonitorId(Gamesvr);  //点赞 订阅
    public static final MonitorId attr_ugc_screen_map = new MonitorId(Gamesvr);  //筛选
    public static final MonitorId attr_ugc_search_map = new MonitorId(Gamesvr);  //搜索
    public static final MonitorId attr_ugc_take_off_map = new MonitorId(Gamesvr);  //下架
    public static final MonitorId attr_ugc_take_off_map_apply = new MonitorId(Gamesvr);  //下架申请
    public static final MonitorId attr_ugc_publish_details = new MonitorId(UgcSvr);  //界面详情
    public static final MonitorId attr_ugc_op_sub_map = new MonitorId(Gamesvr);  //订阅置顶
    public static final MonitorId attr_ugc_best_record_map = new MonitorId(Gamesvr);  //最佳纪录
    public static final MonitorId attr_ugc_c2s_plat_req = new MonitorId(C2sReq); // 客户端cs转发到平台的累计请求 @客户端cs转发到平台请求
    public static final MonitorId attr_ugc_map_base_info = new MonitorId(Gamesvr);  //地图信息
    public static final MonitorId attr_ugc_rpc_apply_key_info = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_get_user_op_map = new MonitorId(Gamesvr); //获取操作过的地图
    public static final MonitorId attr_ugc_start_battle_map = new MonitorId(Gamesvr);  //游玩地图
    public static final MonitorId attr_ugc_public_play_map = new MonitorId(Gamesvr);  //地图游玩量
    public static final MonitorId attr_ugc_search_instance = new MonitorId(UgcSvr);  //搜索地图
    public static final MonitorId attr_ugc_topic_detail = new MonitorId(UgcSvr);  //话题详情
    public static final MonitorId attr_ugc_search_suggestion = new MonitorId(UgcSvr);  //搜索下拉推荐
    public static final MonitorId attr_ugc_instance_version_info = new MonitorId(UgcSvr);  //获取地图版本号

    public static final MonitorId attr_ugc_rpc_save_level_info = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_enter_map = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_copy_entity = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_create_entity = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_delete_entity = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_modify_publish = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_modify_name = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_entity_list = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_grpc_discover = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_grpc_request = new MonitorId(Rpc);
    public static final MonitorId attr_ugc_grpc_request_receive = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_grpc_request_response = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_grpc_client_error = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_grpc_client_notfound = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_grpc_client_cost = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_update_publish_meta_map = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_active_publish_goods = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_check_goods_valid = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_action = new MonitorId(UgcSvr);
    public static final MonitorId attr_async_action = new MonitorId(UgcSvr);
    public static final MonitorId attr_analyze_action = new MonitorId(UgcSvr);
    public static final MonitorId attr_http_virtual_action = new MonitorId(UgcSvr);
    public static final MonitorId attr_http_action = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_publish_reset_version_pass_data = new MonitorId(UgcSvr);   // 地图发后改重置版本下通关数据
    public static final MonitorId attr_ugc_update_version_pass_data = new MonitorId(UgcSvr);      // 地图更新版本下通关数据
    public static final MonitorId attr_ugc_send_mail = new MonitorId(UgcSvr);   // ugc发送邮件
    public static final MonitorId attr_ugc_operate_publish_co_creator = new MonitorId(UgcSvr);   // ugc发布态草稿共创权限操作
    public static final MonitorId attr_ugc_co_create_multi_edit_operate = new MonitorId(UgcSvr);  // ugc共创多人编辑操作
    public static final MonitorId attr_ugc_co_create_edit_mode = new MonitorId(UgcSvr);           // ugc共创编辑模式
    public static final MonitorId attr_ugc_co_create_editor_operate = new MonitorId(UgcSvr);      // ugc共创编辑者存储操作
    public static final MonitorId attr_ugc_co_create_multi_edit_occupy_operate = new MonitorId(UgcSvr);   // ugc共创多人编辑占用存储操作
    public static final MonitorId attr_ugc_cs_forward = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_cs_forward_bytes = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_get_publish_map = new MonitorId(UgcSvr);

    public static final MonitorId attr_ugcplat_report_statistics = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_servlet_statistics = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_servlet_batchget_uid = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_servlet_batchget_creatorId = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_report = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_servlet_count_limit = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_servlet_creator_info = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_http_call_cost = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_http_call_times = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_load_remote_config = new MonitorId(UgcPlatSvr);

    public static final MonitorId attr_ugcplat_platroominfo_update_redis = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_platroominfo_delete_redis = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_platroominfo_topn_redis = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_platroominfo_topn_with_tag_redis = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_platroominfo_update_es = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_platroominfo_delete_es = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_platroominfo_topn_es = new MonitorId(UgcPlatSvr);
    public static final MonitorId attr_ugcplat_platroominfo_topn_with_tag_es = new MonitorId(UgcPlatSvr);

    public static final MonitorId attr_ugc_aigc_change_color = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_aigc_gen_image = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_cocreate_create_entity = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_cocreate_apply_layer = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_accredit_layer = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_editor_layer = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_editor_layer_exit = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_player_info = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_aigc_gen_image_tss = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_cocreate_editor = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_remove_map = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_rpc_layer_apply_result = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_cocreate_editor_heart = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_cocreate_editor_ding = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_search_map_front = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_aigc_gen_module = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_map_group_list = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_multiple_save_meta = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_need_down_res = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_aigc_gen_magic_pic = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_home_page_recommend_cost = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_home_page_recommend_cost_sum = new MonitorId(UgcSvr);

    public static final MonitorId attr_redis_break_count = new MonitorId(Redis);//redis 熔断次数 支持俩个参数和0个参数
    public static final MonitorId attr_redis_access_count = new MonitorId(Redis);//redis 访问次数 支持俩个参数和0个参数
    public static final MonitorId attr_redis_req_info = new MonitorId(Redis);//redis请求信息 支持俩个参数和0个参数
    public static final MonitorId attr_redis_conn_info = new MonitorId(Redis);//redis连接信息 支持俩个参数和0个参数
    public static final MonitorId attr_redis_cache_info = new MonitorId(Redis);//redis hit次数
    public static final MonitorId attr_redis_cache_total_info = new MonitorId(Redis);//redis total次数 访问次数总数

    public static final MonitorId attr_analyze_data_info = new MonitorId(Redis);//数据命中率
    public static final MonitorId attr_analyze_miss_data_info = new MonitorId(Redis);//数据miss命中率
    public static final MonitorId attr_analyze_total_info = new MonitorId(Redis);//数据访问总数
    public static final MonitorId attr_local_analyze_total_info = new MonitorId(Redis);//本地数据请求总量
    public static final MonitorId attr_local_analyze_miss_info = new MonitorId(Redis);//本地请求失败量
    public static final MonitorId attr_local_analyze_hit_info = new MonitorId(Redis);//本地请求命中

    public static final MonitorId attr_http_hit_info = new MonitorId(Redis);//http hit 数据命中率
    public static final MonitorId attr_http_error_info = new MonitorId(Redis);//http error数据命中率
    public static final MonitorId attr_http_total_info = new MonitorId(Redis);//http total 数据访问总数

    public static final MonitorId attr_ugc_serial_job_queue_warning = new MonitorId(UgcSvr);  //ugc串行任务队列长度告警 @ugc串行任务队列长度告警
    public static final MonitorId attr_ugc_serial_job_queue_full = new MonitorId(UgcSvr);     //ugc串行任务队列长度错误 @ugc串行任务队列长度错误
    public static final MonitorId attr_ugc_set_redis_len_statistics = new MonitorId(UgcSvr); //set redis长度统计

    public static final MonitorId attr_analyze_data_len_statistics = new MonitorId(UgcSvr); //统计analyze长度
    public static final MonitorId attr_route_relay_queue_deep = new MonitorId(RouteSvr);  // routesvr转发队列深度
    public static final MonitorId attr_route_relay_rpc_total = new MonitorId(RouteSvr);   // routerSvr转发的协议数量 @转发的协议数量
    public static final MonitorId attr_ugc_table_single_batch_get_count_statistics = new MonitorId(UgcSvr); //ugc表批量查询单次数量统计
    public static final MonitorId attr_ugc_table_batch_get_count_statistics = new MonitorId(UgcSvr); //ugc表批量查询次数统计 废弃
    public static final MonitorId attr_ugc_table_batch_get_count_add_statistics = new MonitorId(UgcSvr); //ugc表批量查询次数统计
    public static final MonitorId attr_ugc_friend_get_cache_statistics = new MonitorId(UgcSvr); //ugc局内好友命中缓存统计

    public static final MonitorId attr_ugc_group_create = new MonitorId(UgcSvr);         // 组合创建
    public static final MonitorId attr_ugc_group_publish = new MonitorId(UgcSvr);        // 组合发布
    public static final MonitorId attr_ugc_group_delete = new MonitorId(UgcSvr);         // 组合删除
    public static final MonitorId attr_ugc_group_takeoff = new MonitorId(UgcSvr);        // 组合下架
    public static final MonitorId attr_ugc_group_list = new MonitorId(UgcSvr);           // 组合拉取列表
    public static final MonitorId attr_ugc_group_screen = new MonitorId(UgcSvr);         // 组合拉取社区
    public static final MonitorId attr_ugc_group_search = new MonitorId(UgcSvr);         // 组合搜索
    public static final MonitorId attr_ugc_group_official = new MonitorId(UgcSvr);       // 拉取官方作品
    public static final MonitorId attr_ugc_res_homepage_recommend = new MonitorId(UgcSvr);  // 首页推荐
    public static final MonitorId attr_ugc_res_homepage_set = new MonitorId(UgcSvr);        // 首页推荐拉取更多集合
    public static final MonitorId attr_ugc_res_bag_add = new MonitorId(UgcSvr);             // 资源背包添加
    public static final MonitorId attr_ugc_res_bag_delete = new MonitorId(UgcSvr);          // 资源背包删除
    public static final MonitorId attr_ugc_res_bag_search = new MonitorId(UgcSvr);          // 资源背包搜索
    public static final MonitorId attr_ugc_res_bag_get = new MonitorId(UgcSvr);             // 资源背包拉取
    public static final MonitorId attr_ugc_res_community_search = new MonitorId(UgcSvr);    // 资源社区搜索
    public static final MonitorId attr_ugc_res_private_adapt = new MonitorId(UgcSvr);       // 资源适配
    public static final MonitorId attr_ugc_res_list = new MonitorId(UgcSvr);           // 资源拉取列表
    public static final MonitorId attr_ugc_get_save_record = new MonitorId(UgcSvr);    // 拉去保存记录信息
    public static final MonitorId attr_ugc_res_classification_create = new MonitorId(UgcSvr);  // ugc资产分类-创建操作
    public static final MonitorId attr_ugc_res_classification_publish = new MonitorId(UgcSvr);  // ugc资产分类-发布操作

    public static final MonitorId attr_ugc_fetch_top_rank = new MonitorId(UgcSvr);       // 资源适配
    public static final MonitorId attr_ugc_check_rank_id_valid = new MonitorId(UgcSvr);       // 检查rankid有效
    public static final MonitorId attr_ugc_rpc_batch_update_rank = new MonitorId(UgcSvr);       // rpc批量上报ugc某条排行记录

    public static final MonitorId attr_ugc_rpc_get_map_rank_list_info = new MonitorId(UgcSvr);        // rpc 获取 地图排行榜信息
    public static final MonitorId attr_ugc_rpc_get_map_setting_info = new MonitorId(UgcSvr);        // rpc 获取 地图设置信息 失败
    public static final MonitorId attr_ugc_player_community_http_req = new MonitorId(UgcSvr); // 玩家社区http请求
    public static final MonitorId attr_ugc_procedures = new MonitorId(UgcSvr); // UGC流程
    public static final MonitorId attr_ugc_procedures_check_fail = new MonitorId(UgcSvr); // UGC流程检查失败
    public static final MonitorId attr_ugc_procedures_execute_fail = new MonitorId(UgcSvr); // UGC流程执行失败
    public static final MonitorId attr_ugc_procedures_execute_exception = new MonitorId(UgcSvr); // UGC流程执行异常
    public static final MonitorId attr_ugc_procedures_rollback_fail = new MonitorId(UgcSvr); // UGC流程回滚失败
    public static final MonitorId attr_ugc_achievement_get = new MonitorId(Gamesvr);  // 自定义属性拉取
    public static final MonitorId attr_ugc_achievement_edit = new MonitorId(Gamesvr);  // 自定义属性编辑
    public static final MonitorId attr_ugc_achievement_edit_over_limit = new MonitorId(UgcSvr);  // 自定义属性配置超限
    public static final MonitorId attr_ugc_achievement_edit_publish = new MonitorId(UgcSvr);  // 编辑已发布的自定义属性
    public static final MonitorId attr_ugc_achievement_edit_exist = new MonitorId(UgcSvr);  // 编辑已经存在的成就
    public static final MonitorId attr_ugc_achievement_edit_add = new MonitorId(UgcSvr);  // 编辑不存在的成就
    public static final MonitorId attr_ugc_achievement_load_fail = new MonitorId(UgcSvr);  // 成就加载失败了

    // 大厅
    public static final MonitorId attr_lobby_lobby_num = new MonitorId(Lobby); // 大厅数
    public static final MonitorId attr_lobby_normal_lobby_num = new MonitorId(Lobby); // 正常大厅数
    public static final MonitorId attr_lobby_player_num = new MonitorId(Lobby); // 玩家总数
    public static final MonitorId attr_lobby_normal_player_num = new MonitorId(Lobby); // 正常玩家总数
    public static final MonitorId attr_lobby_avg_normal_player_num = new MonitorId(Lobby); // 平均正常玩家数
    public static final MonitorId attr_lobby_avg_wait_player_num = new MonitorId(Lobby); // 平均等待玩家数
    public static final MonitorId attr_lobby_avg_offline_player_num = new MonitorId(Lobby); // 平均离线玩家数
    public static final MonitorId attr_lobby_idle_lobby_num = new MonitorId(Lobby); // 空闲大厅数
    public static final MonitorId attr_lobby_idle_player_num = new MonitorId(Lobby); // 空闲大厅玩家数
    public static final MonitorId attr_lobby_merge_lobby_num = new MonitorId(Lobby); // 合并大厅数
    public static final MonitorId attr_lobby_merge_influence_player_num = new MonitorId(Lobby); // 合并影响人数
    public static final MonitorId attr_lobby_total_create_num = new MonitorId(Lobby); // 总的创建大厅数量
    public static final MonitorId attr_lobby_total_terminate_num = new MonitorId(Lobby); //销毁的大厅数量
    public static final MonitorId attr_lobby_total_terminate_player_num = new MonitorId(Lobby); //销毁大厅时的累计总人数
    public static final MonitorId attr_lobby_total_player_enter_num = new MonitorId(Lobby); //总的进人人次数
    public static final MonitorId attr_lobby_total_player_leave_num = new MonitorId(Lobby); //总的离开人数
    public static final MonitorId attr_lobby_total_player_time = new MonitorId(Lobby); //总的玩家在大厅的分钟数
    public static final MonitorId attr_lobby_total_merge_lobby_num = new MonitorId(Lobby); //总合并大厅数
    public static final MonitorId attr_lobby_total_merge_influence_player_num = new MonitorId(Lobby); //大厅合并总影响人数
    public static final MonitorId attr_lobby_total_player_num_time = new MonitorId(Lobby); //大厅玩家数为N时的总分钟时间
    public static final MonitorId attr_lobby_total_player_time_num = new MonitorId(Lobby); //相同在线时长的累计人数
    public static final MonitorId attr_lobby_player_perf_weight_mean = new MonitorId(Lobby); // 大厅玩家性能权重平均值
    public static final MonitorId attr_lobby_player_perf_weight_variance = new MonitorId(Lobby); // 大厅玩家性能权重方差

    public static final MonitorId attr_lobby_ds_create_fail = new MonitorId(Lobby); //大厅DS创建失败
    public static final MonitorId attr_lobby_allocate_fail = new MonitorId(Lobby); //大厅分配失败
    public static final MonitorId attr_lobby_ugc_map_base_info = new MonitorId(Lobby);  //大厅ugc地图信息
    public static final MonitorId attr_lobby_migrate_fail = new MonitorId(Lobby); //大厅迁移失败
    // 大厅分配
    public static final MonitorId attr_lobbyalloc_relay_lobbyreqmessage_cnt = new MonitorId(LobbyAlloc); // 转发分配大厅请求次数
    public static final MonitorId attr_lobbyalloc_relay_lobbyrspmessage_cnt = new MonitorId(LobbyAlloc); // 转发分配大厅请求回包次数
    public static final MonitorId attr_lobbyalloc_relay_queue_deep = new MonitorId(LobbyAlloc); // 转发队列深度
    public static final MonitorId attr_lobbyalloc_alloc_lobbysvrnode_cnt = new MonitorId(LobbyAlloc); // 分配lobbysvr节点次数

    // 米大师
    public static final MonitorId attr_midas_deliver_ntf_num = new MonitorId(Midas);  // 发货通知次数@发货通知次数
    public static final MonitorId attr_midas_deliver_num = new MonitorId(Midas);  // 发货次数@发货次数
    public static final MonitorId attr_midas_present_num = new MonitorId(Midas);  // 赠送次数@赠送次数
    public static final MonitorId attr_midas_http_req = new MonitorId(Midas);  // 请求次数@请求次数
    public static final MonitorId attr_midas_update_balance_num = new MonitorId(Midas);  // 查询余额次数@查询余额次数
    public static final MonitorId attr_midas_direct_buy_num = new MonitorId(Midas);  // 直购次数@直购次数
    public static final MonitorId attr_midas_diamond_buy_num = new MonitorId(Midas);  // 钻石购买次数@钻石购买次数
    public static final MonitorId attr_midas_coin_pay_retry_num = new MonitorId(Midas);  // 星钻支付重试次数@星钻支付重试次数
    public static final MonitorId attr_midas_recharge_num = new MonitorId(Midas);  // 充值次数@充值次数
    public static final MonitorId attr_midas_present_over_limit = new MonitorId(Midas);  // 赠送数量过大@赠送数量过大
    public static final MonitorId attr_midas_bill_count_over_limit = new MonitorId(Midas);  // 订单数量过大@订单数量过大
    public static final MonitorId attr_midas_present_not_open_pay_retry_num = new MonitorId(Midas);  // notOpenPay导致赠送重试次数@notOpenPay导致赠送重试次数
    public static final MonitorId attr_midas_present_login_status_retry_num = new MonitorId(Midas);  // 登陆态失效导致赠送重试次数@登陆态失效导致赠送重试次数
    public static final MonitorId attr_midas_present_lock_fail_retry_num = new MonitorId(Midas);  // 锁失败导致赠送重试次数@锁失败导致赠送重试次数
    public static final MonitorId attr_midas_deliver_fail_count_limit = new MonitorId(Midas);  // 限购导致发货失败次数@限购导致发货失败次数
    public static final MonitorId attr_midas_publish_goods_num = new MonitorId(Midas);  // 上架UGC内购商品次数@上架UGC内购商品次数
    public static final MonitorId attr_midas_apply_ugc_partner_num = new MonitorId(Midas);  // 申请入驻UGC商户次数@申请入驻UGC商户次数

    // 序号服务
    public static final MonitorId attr_seq_alloc_num = new MonitorId(SeqSvr); //分配次数
    public static final MonitorId attr_seq_alloc_new_num = new MonitorId(SeqSvr); //用新序号的分配次数
    public static final MonitorId attr_seq_alloc_reuse_num = new MonitorId(SeqSvr); //复用已释放序号的分配次数
    public static final MonitorId attr_seq_use_percent = new MonitorId(SeqSvr); //使用百分比
    public static final MonitorId attr_seq_query_num = new MonitorId(SeqSvr); //查询次数
    public static final MonitorId attr_seq_query_cache_hit_num = new MonitorId(SeqSvr); //查询时没命中缓存次数
    public static final MonitorId attr_seq_heartbeat_num = new MonitorId(SeqSvr); //序号心跳次数
    public static final MonitorId attr_seq_leak_num = new MonitorId(SeqSvr); //序号泄露次数
    public static final MonitorId attr_seq_timeout_num = new MonitorId(SeqSvr); //序号超时回收次数
    public static final MonitorId attr_seq_free_num = new MonitorId(SeqSvr); //序号释放次数
    public static final MonitorId attr_seq_free_cache_num = new MonitorId(SeqSvr); //序号cache次数
    public static final MonitorId attr_seq_seg_alloc_num = new MonitorId(SeqSvr); //db段分配次数
    public static final MonitorId attr_seq_seg_alloc_delay_num = new MonitorId(SeqSvr); //db段分配延迟次数
    public static final MonitorId attr_seq_db_insert_num = new MonitorId(SeqSvr); //数据库插入次数
    public static final MonitorId attr_seq_db_update_num = new MonitorId(SeqSvr); //数据库更新次数
    public static final MonitorId attr_seq_db_delete_num = new MonitorId(SeqSvr); //数据库删除次数
    public static final MonitorId attr_seq_db_get_num = new MonitorId(SeqSvr); //数据库删除次数

    // ds idc选择相关信息
    public static final MonitorId attr_idcds_report_load = new MonitorId(DsIdcLoad);      // 写入ds idc负载
    public static final MonitorId attr_idcds_collect_load = new MonitorId(DsIdcLoad);     // 收集ds idc负载
    public static final MonitorId attr_idcds_unhealthy_idc_cnt = new MonitorId(DsIdcLoad);    // 不健康的idc数量
    public static final MonitorId attr_idcds_chose_idc_fallback = new MonitorId(DsIdcLoad);    // 挑选idc时, 落到兜底的不考虑健康度
    public static final MonitorId attr_idcds_lack_network_info = new MonitorId(DsIdcLoad);    // 队伍成员探测的idc信息key不一致(缺少某些idc的测速)
    public static final MonitorId attr_idcds_no_idc_list = new MonitorId(DsIdcLoad);          // 队伍成员没有选出idc
    //匹配分配相关监控
    public static final MonitorId attr_matchalloc_relay_matchreqmessage_cnt = new MonitorId(MatchAllocSvr);       // 转发匹配请求次数 @转发匹配请求次数
    public static final MonitorId attr_matchalloc_relay_queue_deep = new MonitorId(MatchAllocSvr);           // 转发队列长度 @转发队列的长度
    public static final MonitorId attr_matchalloc_alloc_matchsvrnode_cnt = new MonitorId(MatchAllocSvr);           // 分配matchsvr节点次数 @分配matchsvr节点次数
    public static final MonitorId attr_matchalloc_relay_matchrspmessage_cnt = new MonitorId(MatchAllocSvr);       // 转发匹配返回次数 @转发匹配返回次数

    // begin TYC 监控

    public static final MonitorId attr_tyc_online_player = new MonitorId(BattleSvr); //tyc 在线人数
    public static final MonitorId attr_tyc_robot_player = new MonitorId(BattleSvr); //tyc 机器人数量
    public static final MonitorId attr_tyc_battleinfo_real_player_avg = new MonitorId(BattleSvr); // tyc 平均每局真人

    public static final MonitorId attr_tyc_battle_real_player_count = new MonitorId(BattleSvr); //tyc 每个房间真人数量
    public static final MonitorId attr_tyc_battle_max_live_sec = new MonitorId(BattleSvr); //tyc 每个房间最大存活时间
    public static final MonitorId attr_tyc_battle_avg_live_sec = new MonitorId(BattleSvr); //tyc 每个房间平均存活时间
    public static final MonitorId attr_tyc_battle_ongoing_query_count = new MonitorId(Gamesvr); //重连返回房间
    public static final MonitorId attr_tyc_battleinfo_heartbeat_timeout = new MonitorId(BattleSvr); // tyc battleinfo 心跳超时
    public static final MonitorId attr_tyc_ds_end_count = new MonitorId(BattleSvr);// tyc ds 销毁
    public static final MonitorId attr_tyc_tf_ds_alive = new MonitorId(BattleSvr);// tf ds 存活时长
    public static final MonitorId attr_tyc_tf_ds_alive_range = new MonitorId(BattleSvr);// tf ds 存活时长
    public static final MonitorId attr_tyc_tf_ds_enter_player_count = new MonitorId(BattleSvr);// ds 存活周期中，进入玩家的总人数
    public static final MonitorId attr_tyc_tf_migrate_req = new MonitorId(BattleSvr);// tf ds 存活时长
    public static final MonitorId attr_tyc_tf_ds_migrate_count = new MonitorId(BattleSvr);// tf ds 迁移次数

    public static final MonitorId attr_tyc_tycoon_receive_battlesvr_on_count = new MonitorId(TycoonSvr);// 接收到battlesvr 心跳的服务器个数
    public static final MonitorId attr_tyc_tycoon_receive_battlesvr_down_count = new MonitorId(TycoonSvr);// 接收到battlesvr 心跳的服务器个数
    public static final MonitorId attr_tyc_tycoon_manager_battlesvr_total_count = new MonitorId(TycoonSvr);// 接收到battlesvr 心跳的服务器个数

    public static final MonitorId attr_tyc_tycoon_dsc_region_migrate_task_cnt = new MonitorId(TycoonSvr);         // 发起DS迁移 dsc region 的个数
    public static final MonitorId attr_tyc_tycoon_dsc_region_migrate_task_done_cnt = new MonitorId(TycoonSvr);    // 完成DS迁移 dsc region 的个数
    public static final MonitorId attr_tyc_tycoon_dsc_region_migrate_ds_cnt = new MonitorId(TycoonSvr);           // 需要迁移的ds总数
    public static final MonitorId attr_tyc_tycoon_dsc_region_migrate_ds_success_cnt = new MonitorId(TycoonSvr);   // 迁移成功的ds总数
    public static final MonitorId attr_tyc_tycoon_dsc_region_migrate_ds_failed_cnt = new MonitorId(TycoonSvr);    // 迁移失败的ds总数

    public static final MonitorId attr_type_tycoon_dsc_region_migrate_ds_not_process_cnt = new MonitorId(TycoonSvr); // 疑似因battlesvr滚动更新导致ds没有迁移的数量

    public static final MonitorId attr_tyc_tycoon_player_survival_time_Sec = new MonitorId(BattleSvr);// battlesvr上玩家生存时间秒
    public static final MonitorId attr_tyc_tycoon_player_kill_monster_num = new MonitorId(BattleSvr);// battlesvr上击杀野怪数量
    public static final MonitorId attr_tyc_tycoon_player_kill_player_num = new MonitorId(BattleSvr);// battlesvr上击杀玩家数量
    public static final MonitorId attr_tyc_tycoon_player_killed_num = new MonitorId(BattleSvr);// battlesvr上被击杀玩家数量
    public static final MonitorId attr_tyc_tycoon_player_failed_waveId = new MonitorId(BattleSvr);// battlesvr上玩家失败的waveid
    public static final MonitorId attr_tyc_tf_settle_num = new MonitorId(BattleSvr);// 塔防模式结算次数
    public static final MonitorId attr_tyc_tds_settle_num = new MonitorId(BattleSvr);// tds模式结算次数

    public static final MonitorId attr_tyc_tds_player_survival_time_Sec = new MonitorId(BattleSvr);// battlesvr上玩家生存时间秒
    public static final MonitorId attr_tyc_tds_player_kill_monster_num = new MonitorId(BattleSvr);// battlesvr上击杀野怪数量
    public static final MonitorId attr_tyc_tds_player_kill_player_num = new MonitorId(BattleSvr);// battlesvr上击杀玩家数量
    public static final MonitorId attr_tyc_td_player_killed_num = new MonitorId(BattleSvr);// battlesvr上被击杀玩家数量
    public static final MonitorId attr_tyc_tds_player_failed_waveId = new MonitorId(BattleSvr);// battlesvr上玩家失败的waveid

    // battlesvr-omd
    public static final MonitorId attr_omd_player_result_survival_minute = new MonitorId(BattleSvr);// battlesvr上玩家生存时间分钟
    public static final MonitorId attr_omd_player_change_level_num = new MonitorId(BattleSvr);// 兽人模式关卡切换次数
    public static final MonitorId attr_omd_player_match_type_num = new MonitorId(BattleSvr);// 兽人模式游玩各个模式次数
    public static final MonitorId attr_omd_player_difficulty_num = new MonitorId(BattleSvr);// 兽人模式游玩各个难度次数
    public static final MonitorId attr_omd_player_regist_days = new MonitorId(BattleSvr);// 兽人模玩家注册天数统计
    public static final MonitorId attr_omd_player_diff_days_2_last_day = new MonitorId(BattleSvr);// 兽人模玩家当前与上次登录天数差值
    public static final MonitorId attr_omd_player_result_num = new MonitorId(BattleSvr);// 兽人模式游玩结果次数

    //tyc预留
    public static final MonitorId attr_omd_game_param_1 = new MonitorId(Gamesvr);// 预留1
    public static final MonitorId attr_omd_game_param_2 = new MonitorId(Gamesvr);// 预留2
    public static final MonitorId attr_omd_game_param_3 = new MonitorId(Gamesvr);// 预留3
    public static final MonitorId attr_omd_game_param_4 = new MonitorId(Gamesvr);// 预留4
    public static final MonitorId attr_omd_game_param_5 = new MonitorId(Gamesvr);// 预留5
    public static final MonitorId attr_omd_game_param_6 = new MonitorId(Gamesvr);// 预留6
    public static final MonitorId attr_omd_game_param_7 = new MonitorId(Gamesvr);// 预留7
    public static final MonitorId attr_omd_game_param_8 = new MonitorId(Gamesvr);// 预留8

    // LevelChooser
    public static final MonitorId attr_match_no_level_available = new MonitorId(LevelChooser);   // 匹配关卡随机选择失败
    public static final MonitorId attr_custom_no_level_available = new MonitorId(LevelChooser);  // 自定义房间关卡随机选择失败
    public static final MonitorId attr_custom_use_default_levels = new MonitorId(LevelChooser);  // 自定义房间关卡随机使用默认逻辑
    public static final MonitorId attr_choose_among_recent_error = new MonitorId(LevelChooser);  // 近期关卡去重失败

    // dsdbsvr
    public static final MonitorId attr_tyc_dsdbsvr_save_db_req = new MonitorId(DsDbSvr);
    public static final MonitorId attr_tyc_dsdbsvr_get_db_req = new MonitorId(DsDbSvr);
    public static final MonitorId attr_tyc_dsdbsvr_update_backpack_req = new MonitorId(DsDbSvr);
    public static final MonitorId attr_tyc_dsdbsvr_req_times = new MonitorId(DsDbSvr);//请求次数
    public static final MonitorId attr_tyc_dsdbsvr_max_tps = new MonitorId(DsDbSvr);//最大tps
    public static final MonitorId attr_tyc_dsdbsvr_avg_cost_ms = new MonitorId(DsDbSvr);//平均请求耗时
    public static final MonitorId attr_tyc_dsdbsvr_max_cost_ms = new MonitorId(DsDbSvr);//最大请求耗时
    public static final MonitorId attr_spgame_dsdbsvr_starp_save_commondb_req = new MonitorId(DsDbSvr);

    public static final MonitorId attr_spgame_dsdbsvr_starp_batch_save_commondb_req = new MonitorId(DsDbSvr);
    public static final MonitorId attr_spgame_dsdbsvr_starp_batch_del_commondb_req = new MonitorId(DsDbSvr);

    public static final MonitorId attr_spgame_dsdbsvr_starp_del_commondb_req = new MonitorId(DsDbSvr);
    public static final MonitorId attr_spgame_dsdbsvr_starp_get_commondb_req = new MonitorId(DsDbSvr);
    public static final MonitorId attr_spgame_dsdbsvr_starp_get_player_commondb_by_key_req = new MonitorId(DsDbSvr);
    public static final MonitorId attr_tyc_bak_1 = new MonitorId(TycoonSvr);//备用1
    public static final MonitorId attr_tyc_bak_2 = new MonitorId(TycoonSvr);//备用2
    public static final MonitorId attr_tyc_bak_3 = new MonitorId(TycoonSvr);//备用3
    public static final MonitorId attr_tyc_bak_4 = new MonitorId(TycoonSvr);//备用4
    public static final MonitorId attr_tyc_bak_5 = new MonitorId(TycoonSvr);//备用5

    // end TYC 监控

    // MetaAiLab Ai Lab Rpc相关(目前主要是匹配)
    public static final MonitorId attr_metaai_dispatch_res = new MonitorId(MetaAiRpc);                // 派发收到的回包(call future complete) @派发收到的回包
    public static final MonitorId attr_metaai_dispatch_other_err = new MonitorId(MetaAiRpc);          // 框架错误 @框架错误
    public static final MonitorId attr_metaai_dispatch_timeout = new MonitorId(MetaAiRpc);            // 回包超时 @回包超时
    public static final MonitorId attr_metaai_recv_queue_size = new MonitorId(MetaAiRpc);             // 收包队列长度 @收包队列长度
    public static final MonitorId attr_metaai_add_recv_queue = new MonitorId(MetaAiRpc);              // 响应添加到收包队列 @响应添加到收包队列
    public static final MonitorId attr_metaai_send_req = new MonitorId(MetaAiRpc);                    // 实际发送请求 @实际发送请求
    public static final MonitorId attr_metaai_send_queue_size = new MonitorId(MetaAiRpc);             // 发包队列长度 @发包队列长度
    public static final MonitorId attr_metaai_add_send_queue = new MonitorId(MetaAiRpc);              // 请求添加到发送队列 @请求添加到发送队列
    public static final MonitorId attr_metaai_lose_connection = new MonitorId(MetaAiRpc);             // 连接中断 连接中断
    public static final MonitorId attr_metaai_connected = new MonitorId(MetaAiRpc);                   // 连接建立 @连接建立
    public static final MonitorId attr_metaai_get_param = new MonitorId(MetaAiRpc);                   // GetParam请求数量 @GetParam请求数量
    public static final MonitorId attr_metaai_get_match = new MonitorId(MetaAiRpc);                   // GetMatch请求数量 @GetMatch请求数量
    public static final MonitorId attr_metaai_moba_param = new MonitorId(MetaAiRpc);                  // MobaParam请求数量 @MobaParam请求数量
    public static final MonitorId attr_metaai_result_ntf = new MonitorId(MetaAiRpc);                  // ResultNtf请求数量 @ResultNtf请求数量
    public static final MonitorId attr_metaai_matched_team_cnt = new MonitorId(MetaAiRpc);            // 缀合的队伍数量 @缀合的队伍数量
    public static final MonitorId attr_metaai_unmatched_team_cnt = new MonitorId(MetaAiRpc);          // 未缀合的队伍数量 @未缀合的队伍数量
    public static final MonitorId attr_metaai_matched_game_cnt = new MonitorId(MetaAiRpc);            // 缀合的对局数量 @缀合的对局数量
    public static final MonitorId attr_metaai_get_match_req_cost_ms = new MonitorId(MetaAiRpc);       // ai lab getMatch请求耗时 @aiLab getMatch请求耗时
    public static final MonitorId attr_metaai_get_param_req_cost_ms = new MonitorId(MetaAiRpc);       // ai lab getParam请求耗时 @aiLab getParam请求耗时
    public static final MonitorId attr_metaai_ai_lab_match_cost_ms = new MonitorId(MetaAiRpc);        // ai lab缀合局匹配成功时长 @aiLab缀合局匹配成功时长
    public static final MonitorId attr_metaai_normal_match_cost_ms = new MonitorId(MetaAiRpc);        // 普通匹配成功时长 @普通匹配成功时长
    public static final MonitorId attr_metaai_match_team_cost_report_count = new MonitorId(MetaAiRpc); // 匹配成功时长上报数量 @匹配成功时长上报数量
    public static final MonitorId attr_metaai_ai_lab_avg_human_cnt = new MonitorId(MetaAiRpc);        // ai lab缀合局真人数 @aiLab缀合局真人数
    public static final MonitorId attr_metaai_ai_lab_warm_avg_human_cnt = new MonitorId(MetaAiRpc);   // ai lab缀合温暖局真人数 @aiLab缀合温暖局真人数
    public static final MonitorId attr_metaai_match_succ_rep_count = new MonitorId(MetaAiRpc);        // matchSucc上报次数 @matchSucc上报次数
    public static final MonitorId attr_metaai_normal_match_human_cnt = new MonitorId(MetaAiRpc);        // 普通匹配局真人数 @普通匹配局真人数
    public static final MonitorId attr_metaai_get_match_player_cnt = new MonitorId(MetaAiRpc);        // GetMatch发送的玩家数量 @GetMatch发送的玩家数量
    public static final MonitorId attr_metaai_get_match_team_cnt = new MonitorId(MetaAiRpc);          // GetMatch发送的玩家队伍数量 @GetMatch发送的玩家队伍数量
    public static final MonitorId attr_metaai_chase_warm_param = new MonitorId(MetaAiRpc);                  // ChaseWarmParam请求数量 @ChaseWarmParam请求数量
    public static final MonitorId attr_metaai_chase_get_bot = new MonitorId(MetaAiRpc);               // chase玩法获取ai难度设置 @chase玩法获取ai难度设置

    // cronjob
    public static final MonitorId attr_cronjob_execute = new MonitorId(Cronjob);      // cronjob执行次数 @cronjob执行次数
    public static final MonitorId attr_cronjob_schedule = new MonitorId(Cronjob);     // cronjob调度次数 @cronjob调度次数

    //家园
    public static final MonitorId attr_xiaowosvr_xiaowo_num = new MonitorId(XiaoWoSvr);                  //家园个数 @当前家园总数
    public static final MonitorId attr_xiaowosvr_create_xiaowo = new MonitorId(XiaoWoSvr);                //创建家园个数 @创建家园
    public static final MonitorId attr_xiaowosvr_player_num = new MonitorId(XiaoWoSvr);            //玩家总数 @玩家总数
    public static final MonitorId attr_xiaowosvr_xiaowo_create_timeout = new MonitorId(XiaoWoSvr);        //CREATE_GAME_SESSION_TIMEOUT个数 @CREATE_GAME_SESSION_TIMEOUT总数
    public static final MonitorId attr_xiaowosvr_xiaowo_create_abnormal_stop = new MonitorId(XiaoWoSvr);  //CREATE_GAME_SESSION_ABNORMAL_STOP个数 @CREATE_GAME_SESSION_ABNORMAL_STOP总数
    public static final MonitorId attr_xiaowosvr_xiaowo_create_process_force_terminated = new MonitorId(XiaoWoSvr);  //CREATE_GAME_SESSION_DS_PROCESS_FORCE_TERMINATED个数 @CREATE_GAME_SESSION_DS_PROCESS_FORCE_TERMINATED总数
    public static final MonitorId attr_xiaowosvr_xiaowo_create_fleet_force_terminated = new MonitorId(XiaoWoSvr);  //CREATE_GAME_SESSION_FLEET_FORCE_TERMINATED个数 @CREATE_GAME_SESSION_FLEET_FORCE_TERMINATED总数
    public static final MonitorId attr_xiaowosvr_xiaowo_create_game_session_too_long = new MonitorId(XiaoWoSvr);  //CREATE_GAME_SESSION_GAME_SESSION_TOO_LONG个数 @CREATE_GAME_SESSION_GAME_SESSION_TOO_LONG总数
    public static final MonitorId attr_xiaowosvr_xiaowo_end_abnormal = new MonitorId(XiaoWoSvr);          //GAME_SESSION_ENDCODE_ABNORMAL个数 @GAME_SESSION_ENDCODE_ABNORMAL总数
    public static final MonitorId attr_xiaowosvr_xiaowo_end_ds_process_force_terminated = new MonitorId(XiaoWoSvr);          //GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED个数 @GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED总数
    public static final MonitorId attr_xiaowosvr_xiaowo_end_fleet_force_terminated = new MonitorId(XiaoWoSvr);          //GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED个数 @GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED总数
    public static final MonitorId attr_xiaowosvr_xiaowo_end_game_seesion_too_long = new MonitorId(XiaoWoSvr);          //GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG个数 @GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG总数
    public static final MonitorId attr_xiaowosvr_xiaowo_end_process_ending_without_terminate_game_session = new MonitorId(XiaoWoSvr);          //GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION个数 @GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION总数
    public static final MonitorId attr_xiaowosvr_ds_force_terminate = new MonitorId(XiaoWoSvr);            //家园强制结束次数 @家园强制结束
    public static final MonitorId attr_xiaowosvr_migrate = new MonitorId(XiaoWoSvr); //小窝迁移侧
    public static final MonitorId attr_xiaowosvr_be_migrate = new MonitorId(XiaoWoSvr); //小窝被迁移侧
    public static final MonitorId attr_xiaowosvr_migrate_old_set_svrid = new MonitorId(XiaoWoSvr); //老号设置ServerID 现网不该出现
    public static final MonitorId attr_xiaowosvr_migrate_protect = new MonitorId(XiaoWoSvr); //触发迁移保护
    public static final MonitorId attr_xiaowosvr_dbxiaowo_update_fail = new MonitorId(XiaoWoSvr);            //家园存盘出错 @家园存盘出错
    public static final MonitorId attr_xiaowosvr_dbxiaowo_invalid_version = new MonitorId(XiaoWoSvr);            //家园存盘版本号冲突 @家园存盘版本号冲突

    // ugc
    public static final MonitorId attr_xiaowo_publish_ok = new MonitorId(Gamesvr);              // 小窝发布成功（总体）
    public static final MonitorId attr_xiaowo_publish_fail = new MonitorId(Gamesvr);              // 小窝发布失败（总体）
    public static final MonitorId attr_xiaowo_create_ok = new MonitorId(Gamesvr);              // 小窝创建成功
    public static final MonitorId attr_xiaowo_create_fail = new MonitorId(Gamesvr);              // 小窝创建失败（总体）
    public static final MonitorId attr_xiaowo_create_ugc_fail = new MonitorId(Gamesvr);              // 小窝创建ugcsvr返回失败
    public static final MonitorId attr_xiaowo_create_xiaowo_fail = new MonitorId(Gamesvr);              // 小窝创建xiaowosvr返回失败
    public static final MonitorId attr_xiaowo_publish_check_fail = new MonitorId(Gamesvr);        // 小窝发布家具检测失败
    public static final MonitorId attr_xiaowo_publish_ugc_fail = new MonitorId(Gamesvr);          // 小窝发布，ugcsvr返回失败
    public static final MonitorId attr_xiaowo_publish_xiaowo_fail = new MonitorId(Gamesvr);          // 小窝发布，xiaowosvr返回失败
    public static final MonitorId attr_farm_create_fail = new MonitorId(Gamesvr);              // 农场创建失败（总体）
    public static final MonitorId attr_farm_create_farm_fail = new MonitorId(Gamesvr);              // 农场创建farmosvr返回失败

    // 派对
    public static final MonitorId attr_party_publish = new MonitorId(Gamesvr); // 派对发布
    public static final MonitorId attr_party_first_page_fetch = new MonitorId(Gamesvr); // 派对首页拉取
    public static final MonitorId attr_party_pagination_fetch = new MonitorId(Gamesvr); // 派对非首页拉取
    public static final MonitorId attr_party_search = new MonitorId(Gamesvr); // 派对搜索
    public static final MonitorId attr_party_es_index = new MonitorId(Gamesvr); // 派对ES写入
    public static final MonitorId attr_party_es_update = new MonitorId(Gamesvr); // 派对ES更新
    public static final MonitorId attr_party_es_fetch = new MonitorId(Gamesvr); // 派对ES获取
    public static final MonitorId attr_party_xiaowo_recom_es_fetch = new MonitorId(Gamesvr); // 小窝推荐ES拉取

    public static final MonitorId attr_competition_c2s_req = new MonitorId(C2sReq); // 客户端cs转发到赛事的累计请求 @客户端cs转发到赛事请求
    public static final MonitorId attr_nr3e8_c2s_req = new MonitorId(C2sReq); // 客户端cs转发到大富翁的累计请求 @客户端cs转发到大富翁请求
    public static final MonitorId attr_gamesvr_nr3e8_item_forward_fail = new MonitorId(Gamesvr); // 玩家背包NR3E8道具自动使用失败

    // 小窝ds
    public static final MonitorId attr_xiaowo_ds_create_fail = new MonitorId(XiaoWoSvr);

    // 方案
    public static final MonitorId attr_layout_save = new MonitorId(Gamesvr); // 方案保存
    public static final MonitorId attr_layout_del = new MonitorId(Gamesvr);  // 方案删除

    // 背包
    public static final MonitorId attr_create_item_new = new MonitorId(Gamesvr); // 背包创建新道具ID
    public static final MonitorId attr_add_item_check_version_err = new MonitorId(Gamesvr); // 背包发道具版本号检测失败
    public static final MonitorId attr_player_item_size = new MonitorId(Gamesvr); // 玩家背包道具数量

    // dscalloc
    public static final MonitorId attr_dscalloc_trigger_refresh_overtime = new MonitorId(DscAllocSvr); // DSC状态刷新超时
    public static final MonitorId attr_dscalloc_random_dsc_instance_alloc = new MonitorId(DscAllocSvr); // DSC实例随机分配
    public static final MonitorId attr_dscalloc_weight_alloc = new MonitorId(DscAllocSvr); // DSC实例加权分配
    public static final MonitorId attr_dscalloc_precheck_passed_dsa_remain_cpu_core_cnt = new MonitorId(DscAllocSvr); // 通过预检的dsa剩余核数(分Region)
    public static final MonitorId attr_dscalloc_total_alloc_num = new MonitorId(DscAllocSvr); // 总的DSC分配次数
    public static final MonitorId attr_dscalloc_total_cpu_core_cnt = new MonitorId(DscAllocSvr); // CPU总核数(分Region)

    public static final MonitorId attr_batch_get_public_cnt = new MonitorId(Statistics); // 批量获取PlayerPublic请求次数
    public static final MonitorId attr_batch_get_public_num = new MonitorId(Statistics); // 批量获取PlayerPublic请求数量
    public static final MonitorId attr_batch_get_public_fields = new MonitorId(Statistics); // 批量获取PlayerPublic字段请求数量
    public static final MonitorId attr_batch_get_public_req_cnt = new MonitorId(Statistics); // @batchGetPlayerPublicMap接口请求次数
    public static final MonitorId attr_batch_get_public_redis_hit_cache_num = new MonitorId(Statistics); // @batchGetPlayerPublicMap缓存命中数量
    public static final MonitorId attr_batch_get_public_from_tcaplus_cnt = new MonitorId(Statistics); // @batchGetPlayerPublicMap拉取DB次数
    public static final MonitorId attr_batch_get_public_from_tcaplus_num = new MonitorId(Statistics); // @batchGetPlayerPublicMap拉取DB数量

    public static final MonitorId attr_insert_player_extra_info_table = new MonitorId(Statistics); // 插入玩家额外信息表次数
    public static final MonitorId attr_update_player_extra_info_table = new MonitorId(Statistics); // 更新玩家额外信息表次数
    public static final MonitorId attr_replace_player_extra_info_table = new MonitorId(Statistics); // 替换玩家额外信息表次数
    public static final MonitorId attr_update_player_extra_info_table_to_redis = new MonitorId(Statistics); // 更新玩家额外信息表到redis的次数
    public static final MonitorId attr_batch_get_player_extra_info_table_from_redis = new MonitorId(Statistics); // 从redis批量获取玩家额外信息表次数
    public static final MonitorId attr_batch_get_player_extra_info_table_from_tcaplus = new MonitorId(Statistics); // 从tcaplus批量获取玩家额外信息表次数
    public static final MonitorId attr_get_player_recent_play_cnt = new MonitorId(Statistics);    // 请求玩家最近游玩记录的次数

    // 社交表演
    public static final MonitorId attr_social_performance_sync_ds_cfg = new MonitorId(SocialPerformance); // 社交表演同步ds配置
    public static final MonitorId attr_social_performance_sync_ds_cfg_retry_give_up = new MonitorId(SocialPerformance); // 社交表演同步ds配置重试失败

    // aigc
    public static final MonitorId attr_aigc_sync_action = new MonitorId(AigcSvr);  // 发往aigc平台的同步请求
    public static final MonitorId attr_ugc_aigc_gen_voice = new MonitorId(Gamesvr);
    public static final MonitorId attr_ugc_aigc_gen_anicap = new MonitorId(Gamesvr);
    public static final MonitorId attr_ugc_aigc_gen_answer = new MonitorId(Gamesvr);

    public static final MonitorId attr_ugc_match_lobby_detail = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_match_ugc_id_check = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_room_recommend_list = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_room_match_type_id = new MonitorId(UgcSvr);

    public static final MonitorId attr_player_bag_grids_limit_warn = new MonitorId(Gamesvr); // 玩家背包格子上限预警
    // 抽奖
    public static final MonitorId attr_raffle_bi_discount_error = new MonitorId(Raffle);
    public static final MonitorId attr_raffle_bi_error = new MonitorId(Raffle);


    // 弹幕
    public static final MonitorId attr_danmu_cache_get = new MonitorId(DanMuSvr);  // 尝试拿缓存
    public static final MonitorId attr_danmu_cache_load = new MonitorId(DanMuSvr);  // 加载块
    public static final MonitorId attr_danmu_detail_bytes = new MonitorId(DanMuSvr);  // 拉取字节数
    public static final MonitorId attr_danmu_deal_queue_size = new MonitorId(DanMuSvr);  // 弹幕队列大小
    public static final MonitorId attr_danmu_deal_process = new MonitorId(DanMuSvr);  // 操作处理情况
    public static final MonitorId attr_danmu_deal_process_busy = new MonitorId(DanMuSvr);  // 因繁忙而无法处理的操作
    public static final MonitorId attr_danmu_cache_gen = new MonitorId(DanMuSvr);  // 重新gen的频率
    public static final MonitorId attr_danmu_delete = new MonitorId(DanMuSvr);  //
    public static final MonitorId attr_danmu_detail = new MonitorId(DanMuSvr);  //
    public static final MonitorId attr_danmu_detail_flush = new MonitorId(DanMuSvr);  //
    public static final MonitorId attr_danmu_like = new MonitorId(DanMuSvr);  //
    public static final MonitorId attr_danmu_send = new MonitorId(DanMuSvr);  //
    public static final MonitorId attr_danmu_ugc_set_info_get = new MonitorId(DanMuSvr);  // ugc相关弹幕信息
    public static final MonitorId attr_danmu_ugc_info_update = new MonitorId(DanMuSvr);  // 更新ugc相关弹幕信息

    public static final MonitorId attr_ugc_video_examine = new MonitorId(Gamesvr);    // 视频审核

    // sampleroomsvr
    public static final MonitorId attr_sampleroomsvr_update_fail = new MonitorId(SampleRoomSvr);            //  存盘失败
    public static final MonitorId attr_sampleroomsvr_invalid_version = new MonitorId(SampleRoomSvr);            // 存盘版本号冲突
    public static final MonitorId attr_sampleroomsvr_create_success = new MonitorId(SampleRoomSvr);       // 创建样板间实例
    public static final MonitorId attr_sampleroomsvr_create_fail = new MonitorId(SampleRoomSvr);
    public static final MonitorId attr_sampleroomsvr_alloc_success = new MonitorId(SampleRoomSvr);        // 分配成功
    public static final MonitorId attr_sampleroomsvr_takeplace_expire = new MonitorId(SampleRoomSvr);        // 占位超时

    // ugcdatasotresvr
    public static final MonitorId attr_ugcdatasotresvr_queue_size_warn = new MonitorId(UgcDataStoreSvr);   // 请求队列告警
    public static final MonitorId attr_ugcdatasotresvr_queue_size_full = new MonitorId(UgcDataStoreSvr);   // 请求队列溢出
    public static final MonitorId attr_ugcdatasotresvr_paid_item_add = new MonitorId(UgcDataStoreSvr);     // 星钻发货
    public static final MonitorId attr_ugcdatasotresvr_get_map_setting_fail = new MonitorId(UgcDataStoreSvr);     // 获取地图配置失败
    public static final MonitorId attr_ugcdatasotresvr_common_forward = new MonitorId(UgcDataStoreSvr);     // 通用转发到UgcDatastore
    public static final MonitorId attr_ugcdatasotresvr_set_ratelimit_fail = new MonitorId(UgcDataStoreSvr);     // datastore 被限频了
    public static final MonitorId attr_ugcdatasotresvr_rank_opt_ratelimit_fail = new MonitorId(UgcDataStoreSvr);     // datastore 排行榜 被限频了
    public static final MonitorId attr_ugcdatasotresvr_write_back_db = new MonitorId(UgcDataStoreSvr);     // db回写
    public static final MonitorId attr_ugcdatasotresvr_save_irpcreq_get = new MonitorId(UgcDataStoreSvr);      // 存储get请求
    public static final MonitorId attr_ugcdatasotresvr_save_irpcreq_one_player_get = new MonitorId(UgcDataStoreSvr);      // 存储单人拉取 get请求
    public static final MonitorId attr_ugcdatasotresvr_save_irpcreq_set = new MonitorId(UgcDataStoreSvr);      // 存储set请求
    public static final MonitorId attr_ugcdatasotresvr_save_rpcreq_get = new MonitorId(UgcDataStoreSvr);      // 存储get请求

    public static final MonitorId attr_ugcdatasotresvr_custumkv_packsize = new MonitorId(UgcDataStoreSvr);      // 自定义kv的包大小
    public static final MonitorId attr_ugcdatasotresvr_over_custumkv_packsize = new MonitorId(UgcDataStoreSvr);      // 超过自定义kv的包大小
    public static final MonitorId attr_ugcdatasotresvr_near_over_custumkv_packsize = new MonitorId(UgcDataStoreSvr);      // 接近超过自定义kv的包大小
    public static final MonitorId attr_ugcdatasotresvr_dbkvsize_warn = new MonitorId(UgcDataStoreSvr);        // 存储容量预警
    public static final MonitorId attr_ugcdatasotresvr_dbkvsize_err = new MonitorId(UgcDataStoreSvr);         // 存储容量超出

    public static final MonitorId attr_ugcdatasotresvr_rpc_fetch_top_rank = new MonitorId(UgcDataStoreSvr);       // rpc拉取ugc排行榜, gamesvr上报
    public static final MonitorId attr_ugcdatastoresvr_rpc_fetch_top_rank_req = new MonitorId(UgcDataStoreSvr);       // rpc拉取ugc排行榜, ugcdatastoresvr上报
    public static final MonitorId attr_ugcdatasotresvr_rpc_get_player_rank = new MonitorId(UgcDataStoreSvr);       // rpc拉取指定player的ugc排行
    public static final MonitorId attr_ugcdatasotresvr_rpc_get_rankno_rank = new MonitorId(UgcDataStoreSvr);       // rpc拉取指定rankno的ugc排行
    public static final MonitorId attr_ugcdatasotresvr_rpc_delete_rank = new MonitorId(UgcDataStoreSvr);       // rpc删除的ugc某条排行记录, gamesvr上报
    public static final MonitorId attr_ugcdatastoresvr_rpc_delete_rank_req = new MonitorId(UgcDataStoreSvr);       // rpc删除的ugc某条排行记录, ugcdatastoresvr上报
    public static final MonitorId attr_ugcdatasotresvr_rpc_update_rank = new MonitorId(UgcDataStoreSvr);       // rpc上报ugc某条排行记录, gamesvr上报
    public static final MonitorId attr_ugcdatastoresvr_rpc_update_rank_req = new MonitorId(UgcDataStoreSvr);       // rpc上报ugc某条排行记录, ugcdatastoresvr上报
    public static final MonitorId attr_ugcdatasotresvr_rpc_batch_get_player_public = new MonitorId(UgcDataStoreSvr);       // 批量爬取playerpublic

    public static final MonitorId attr_ugcdatasotresvr_ds_irpc_update_rank = new MonitorId(UgcDataStoreSvr);       // ds发过来的irpc update rank
    public static final MonitorId attr_ugcdatasotresvr_ds_irpc_delete_rank_entry = new MonitorId(UgcDataStoreSvr);       // ds发过来的irpc delete rank entry
    public static final MonitorId attr_ugcdatasotresvr_ds_irpc_get_rank_by_uid = new MonitorId(UgcDataStoreSvr);       // ds发过来的irpc get rank entry by uid
    public static final MonitorId attr_ugcdatasotresvr_ds_irpc_get_rank_by_no = new MonitorId(UgcDataStoreSvr);       // ds发过来的irpc get rank entry by no
    public static final MonitorId attr_ugcdatasotresvr_ds_irpc_get_rank = new MonitorId(UgcDataStoreSvr);       // ds发过来的irpc get rank

    public static final MonitorId attr_current_datastore_online = new MonitorId(UgcDataStoreSvr);         // datastore实时在线人数 @实时在线人数
    public static final MonitorId attr_current_datastore_online_percent = new MonitorId(UgcDataStoreSvr);         // datastore实时在线人数百分比 @实时在线人数
    public static final MonitorId attr_current_datastore_online_packsize = new MonitorId(UgcDataStoreSvr);         // datastore实时在线缓存的大小
    public static final MonitorId attr_current_datastore_mapsetting = new MonitorId(UgcDataStoreSvr);         // datastore实时在线mapsetting数
      
    public static final MonitorId attr_ugc_player_public_cache_count = new MonitorId(UgcDataStoreSvr); //缓存的数量
    public static final MonitorId attr_ugc_player_public_attr_count = new MonitorId(UgcDataStoreSvr);
    public static final MonitorId attr_ugc_player_public_array_attr_count = new MonitorId(UgcDataStoreSvr);
    public static final MonitorId attr_ugc_player_public_array_attr_element_count = new MonitorId(UgcDataStoreSvr);

    public static final MonitorId attr_ugc_player_public_load = new MonitorId(UgcDataStoreSvr); //加载玩家公开数据到缓存
    public static final MonitorId attr_ugc_player_public_unload = new MonitorId(UgcDataStoreSvr); //从缓存中卸载玩家公开数据
    public static final MonitorId attr_ugc_player_public_sync_start = new MonitorId(UgcDataStoreSvr); //玩家进入
    public static final MonitorId attr_ugc_player_public_sync_end = new MonitorId(UgcDataStoreSvr); //玩家离开
    public static final MonitorId attr_ugc_player_public_heartbeat = new MonitorId(UgcDataStoreSvr); //玩家心跳

    public static final MonitorId attr_ugc_player_public_get_attrs = new MonitorId(UgcDataStoreSvr); //获取互动数据存档
    public static final MonitorId attr_ugc_player_public_modify_attrs = new MonitorId(UgcDataStoreSvr); //修改互动数据命令数
    public static final MonitorId attr_ugc_player_public_modify_attr_cmds = new MonitorId(UgcDataStoreSvr); //修改互动数据存档
    public static final MonitorId attr_ugc_player_public_get_array_attrs = new MonitorId(UgcDataStoreSvr); //获取互动列表存档
    public static final MonitorId attr_ugc_player_public_modify_array_attrs = new MonitorId(UgcDataStoreSvr); //修改互动列表存档
    public static final MonitorId attr_ugc_player_public_batch_get_attrs = new MonitorId(UgcDataStoreSvr);  //批量获取多个玩家的多个互动数值

    public static final MonitorId attr_ugc_player_public_save_redis_total_count = new MonitorId(UgcDataStoreSvr); //存档redis次数
    public static final MonitorId attr_ugc_player_public_save_redis_attr_total_count = new MonitorId(UgcDataStoreSvr); //存档redis的属性数量
    public static final MonitorId attr_ugc_player_public_del_redis_total_count = new MonitorId(UgcDataStoreSvr); //删除redis缓存次数
    public static final MonitorId attr_ugc_player_public_heartbeat_redis_total_count = new MonitorId(UgcDataStoreSvr); //redis缓存保活次数
    

    public static final MonitorId attr_ugc_player_public_get_attrs_limit = new MonitorId(UgcDataStoreSvr); //获取互动数据存档总限频
    public static final MonitorId attr_ugc_player_public_modify_attrs_limit = new MonitorId(UgcDataStoreSvr); //修改互动数据存档总限频。
    public static final MonitorId attr_ugc_player_public_get_array_attrs_limit = new MonitorId(UgcDataStoreSvr); //获取互动列表存档总限频
    public static final MonitorId attr_ugc_player_public_modify_array_attrs_limit = new MonitorId(UgcDataStoreSvr); //修改互动列表存档总限频

    public static final MonitorId attr_ugc_player_public_op_get_attrs_limit = new MonitorId(UgcDataStoreSvr); //获取互动数据存档源玩家限频
    public static final MonitorId attr_ugc_player_public_op_modify_attrs_limit = new MonitorId(UgcDataStoreSvr); //修改互动数据存档源玩家限频
    public static final MonitorId attr_ugc_player_public_op_get_array_attrs_limit = new MonitorId(UgcDataStoreSvr); //获取互动列表存档源玩家限频
    public static final MonitorId attr_ugc_player_public_op_modify_array_attrs_limit = new MonitorId(UgcDataStoreSvr); //修改互动列表存档源玩家限频
    public static final MonitorId attr_ugc_player_public_op_batch_get_attrs_limit = new MonitorId(UgcDataStoreSvr); //批量获取多玩家互动属性限频

    public static final MonitorId attr_ugc_player_public_tick_queue_count = new MonitorId(UgcDataStoreSvr); //玩家tick队列长度
    public static final MonitorId attr_ugc_player_public_tick_queue_proc_total_count = new MonitorId(UgcDataStoreSvr); //玩家tick队列长度处理数量
    public static final MonitorId attr_ugc_player_public_tick_queue_cost_total_time = new MonitorId(UgcDataStoreSvr); //玩家tick队列长度耗时

    public static final MonitorId attr_ugc_player_public_heartbeat_timeout_queue_count = new MonitorId(UgcDataStoreSvr); //玩家心跳检查队列长度
    public static final MonitorId attr_ugc_player_public_heartbeat_timeout_queue_tick_remove_total_count = new MonitorId(UgcDataStoreSvr); //玩家心跳检测队列每帧处理数量
    public static final MonitorId attr_ugc_player_public_heartbeat_timeout_queue_tick_cost_total_time = new MonitorId(UgcDataStoreSvr); //玩家心跳检测队列每帧处理耗时

    public static final MonitorId attr_ugc_player_public_auto_refresh_attrs_queue_count = new MonitorId(UgcDataStoreSvr); //玩家自动刷新属性队列长度
    public static final MonitorId attr_ugc_player_public_auto_refresh_attrs_queue_tick_proc_total_count = new MonitorId(UgcDataStoreSvr); //玩家自动刷新属性队列处理数量
    public static final MonitorId attr_ugc_player_public_auto_refresh_attrs_queue_tick_cost_total_time = new MonitorId(UgcDataStoreSvr); //玩家自动刷新属性队列处理耗时
    public static final MonitorId attr_ugc_player_public_auto_refresh_attrs_queue_tick_refresh_total_count = new MonitorId(UgcDataStoreSvr); //玩家自动刷新属性队列刷新玩家数量

    public static final MonitorId attr_ugcdatasotresvr_show_data_get_all = new MonitorId(UgcDataStoreSvr);  // 获取所有玩家的请求 只有ds
    public static final MonitorId attr_ugcdatastoresvr_show_data_ds_get = new MonitorId(UgcDataStoreSvr);  // ds内获取玩家数据
    public static final MonitorId attr_ugcdatastoresvr_show_data_ds_edit = new MonitorId(UgcDataStoreSvr);  // ds内编辑玩家数据
    public static final MonitorId attr_ugcdatastoresvr_show_data_get = new MonitorId(UgcDataStoreSvr);  // 接口获取玩家数据
    public static final MonitorId attr_ugcdatastoresvr_show_data_edit = new MonitorId(UgcDataStoreSvr); // 接口编辑玩家数据
    public static final MonitorId attr_ugcdatastoresvr_show_data_edit_limit_fail = new MonitorId(UgcDataStoreSvr);  // 操作到限频
    public static final MonitorId attr_ugcdatastoresvr_show_data_flush = new MonitorId(UgcDataStoreSvr);  // 数据刷新db
    public static final MonitorId attr_ugcdatastoresvr_show_data_cache_size = new MonitorId(UgcDataStoreSvr);  // 缓存个数
    public static final MonitorId attr_ugcdatastoresvr_show_data_cache_percent = new MonitorId(UgcDataStoreSvr);  // 缓存占比
    public static final MonitorId attr_ugcdatastoresvr_show_data_cache_move = new MonitorId(UgcDataStoreSvr);  // 缓存转移
    public static final MonitorId attr_ugcdatastoresvr_show_data_snapshot_get = new MonitorId(UgcDataStoreSvr); // 接口玩家快照数据拉取
    public static final MonitorId attr_ugcdatastoresvr_show_data_ugc_ach_status_revert = new MonitorId(UgcDataStoreSvr); // 想要进行数据回退
    public static final MonitorId attr_ugcdatastoresvr_show_data_ugc_ach_process_revert = new MonitorId(UgcDataStoreSvr); // 接口玩家快照数据拉取

    public static final MonitorId attr_ugcdatastoresvr_ugc_common_item_add = new MonitorId(UgcDataStoreSvr); // ugc通用物品添加
    public static final MonitorId attr_ugcdatastoresvr_ugc_common_item_use = new MonitorId(UgcDataStoreSvr); // ugc通用物品使用
    public static final MonitorId attr_ugcdatastoresvr_ugc_common_item_get = new MonitorId(UgcDataStoreSvr); // ugc通用物品获取

    public static final MonitorId attr_ugc_ai_take_over_report = new MonitorId(BattleSvr);                // ugc对局ai接管上报
    public static final MonitorId attr_ugc_ai_take_over_data_report = new MonitorId(UgcDataStoreSvr);     // ugc对局ai接管数据上报
    public static final MonitorId attr_battle_ai_take_over_report = new MonitorId(BattleSvr);             // 对局ai接管上报
    public static final MonitorId attr_battle_get_moba_ailab_battle_info = new MonitorId(BattleSvr);      // 二次获取ailab匹配信息
    public static final MonitorId attr_battle_arena_ai_select_card = new MonitorId(BattleSvr);      // 二次获取ailab匹配信息

    public static final MonitorId attr_ugcsvr_show_data_snapshot_get = new MonitorId(UgcSvr); // 接口玩家快照数据拉取

    // ams
    public static final MonitorId attr_ams_item_trigger_auto_use = new MonitorId(Ams);                // AMS道具触发发货
    public static final MonitorId attr_ams_yumi_item_trigger_auto_use = new MonitorId(Ams);                // AMS道具触发发货
    public static final MonitorId attr_ams_item_confirm = new MonitorId(Ams);                         // AMS道具已发货
    public static final MonitorId attr_ams_item_sold_out = new MonitorId(Ams);                        // AMS道具已售罄
    public static final MonitorId attr_ams_item_error = new MonitorId(Ams);                            // AMS道具发放错误

    public static final MonitorId attr_streamsvr_action = new MonitorId(StreamSvr);
    public static final MonitorId attr_streamsvr_ai_edit_assistant_http_req = new MonitorId(StreamSvr);           // ai编辑助手http请求
    public static final MonitorId attr_streamsvr_ai_edit_assistant_http_req_cost_ms = new MonitorId(StreamSvr);   // ai编辑助手http请求耗时(以毫秒为单位)
    public static final MonitorId attr_streamsvr_ai_edit_assistant_init = new MonitorId(StreamSvr);               // ai编辑助手初始化
    public static final MonitorId attr_streamsvr_ai_edit_assistant_chat = new MonitorId(StreamSvr);               // ai编辑助手聊天
    public static final MonitorId attr_streamsvr_ai_edit_assistant_close = new MonitorId(StreamSvr);              // ai编辑助手关闭
    
    public static final MonitorId attr_ugc_danmu_set_info = new MonitorId(UgcSvr);  // 设置弹幕信息

    // 商品投放开关控制
    public static final MonitorId attr_commercial_switch_cfg_err = new MonitorId(OutputControl); // 商品投放开关配置错误

    // 通用产出控制
    public static final MonitorId attr_output_check_result = new MonitorId(OutputControl); // 通用产出模块检查
    public static final MonitorId attr_output_task = new MonitorId(OutputControl); // 通用产出模块key检查-玩家任务
    public static final MonitorId attr_output_activity = new MonitorId(OutputControl); // 通用产出模块key检查-玩家活动
    public static final MonitorId attr_output_mall = new MonitorId(OutputControl); // 通用产出模块key检查-玩家商城产出
    public static final MonitorId attr_output_item = new MonitorId(OutputControl); // 通用产出模块key检查-玩家道具产出

    public static final MonitorId attr_output_s_task = new MonitorId(OutputSpecialControl); // 通用产出模块key检查-玩家任务
    public static final MonitorId attr_output_s_activity = new MonitorId(OutputSpecialControl); // 通用产出模块key检查-玩家活动
    public static final MonitorId attr_output_s_mall = new MonitorId(OutputSpecialControl); // 通用产出模块key检查-玩家商城产出
    public static final MonitorId attr_output_s_item = new MonitorId(OutputSpecialControl); // 通用产出模块key检查-玩家道具产出

    //农场
    public static final MonitorId attr_farmsvr_cs_num = new MonitorId(FarmSvr);                  //农场cs量 @当前农场总数
    public static final MonitorId attr_farmsvr_farm_num = new MonitorId(FarmSvr);                  //农场个数 @当前农场总数
    public static final MonitorId attr_farmsvr_create_farm = new MonitorId(FarmSvr);                //创建农场个数 @创建农场
    public static final MonitorId attr_farmsvr_player_num = new MonitorId(FarmSvr);            //玩家总数 @玩家总数
    public static final MonitorId attr_farmsvr_farm_create_timeout = new MonitorId(FarmSvr);        //CREATE_GAME_SESSION_TIMEOUT个数 @CREATE_GAME_SESSION_TIMEOUT总数
    public static final MonitorId attr_farmsvr_farm_create_abnormal_stop = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_ABNORMAL_STOP个数 @CREATE_GAME_SESSION_ABNORMAL_STOP总数
    public static final MonitorId attr_farmsvr_farm_create_process_force_terminated = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_DS_PROCESS_FORCE_TERMINATED个数 @CREATE_GAME_SESSION_DS_PROCESS_FORCE_TERMINATED总数
    public static final MonitorId attr_farmsvr_farm_create_fleet_force_terminated = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_FLEET_FORCE_TERMINATED个数 @CREATE_GAME_SESSION_FLEET_FORCE_TERMINATED总数
    public static final MonitorId attr_farmsvr_farm_create_game_session_too_long = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_GAME_SESSION_TOO_LONG个数 @CREATE_GAME_SESSION_GAME_SESSION_TOO_LONG总数
    public static final MonitorId attr_farmsvr_farm_end_abnormal = new MonitorId(FarmSvr);          //GAME_SESSION_ENDCODE_ABNORMAL个数 @GAME_SESSION_ENDCODE_ABNORMAL总数
    public static final MonitorId attr_farmsvr_farm_end_ds_process_force_terminated = new MonitorId(FarmSvr);          //GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED个数 @GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED总数
    public static final MonitorId attr_farmsvr_farm_end_fleet_force_terminated = new MonitorId(FarmSvr);          //GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED个数 @GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED总数
    public static final MonitorId attr_farmsvr_farm_end_game_seesion_too_long = new MonitorId(FarmSvr);          //GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG个数 @GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG总数
    public static final MonitorId attr_farmsvr_farm_end_process_ending_without_terminate_game_session = new MonitorId(FarmSvr);          //GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION个数 @GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION总数
    public static final MonitorId attr_farmsvr_ds_force_terminate = new MonitorId(FarmSvr);            //农场强制结束次数 @农场强制结束
    public static final MonitorId attr_farmsvr_migrate = new MonitorId(FarmSvr); //农场迁移侧
    public static final MonitorId attr_farmsvr_be_migrate = new MonitorId(FarmSvr); //农场被迁移侧
    public static final MonitorId attr_farmsvr_migrate_old_set_svrid = new MonitorId(FarmSvr); //老号设置ServerID 现网不该出现
    public static final MonitorId attr_farmsvr_migrate_protect = new MonitorId(FarmSvr); //触发迁移保护
    public static final MonitorId attr_farmsvr_dbfarm_update_fail = new MonitorId(FarmSvr);            //农场存盘出错 @农场存盘出错
    public static final MonitorId attr_farmsvr_dbfarm_invalid_version = new MonitorId(FarmSvr);            //农场存盘版本号冲突 @农场存盘版本号冲突
    public static final MonitorId attr_farmsvr_farm_remove = new MonitorId(FarmSvr); // 农场内存淘汰

    public static final MonitorId attr_farmsvr_stop_unfinished_db_farm_count = new MonitorId(FarmSvr); // farmsvr停服未完成存盘的农场数量
    public static final MonitorId attr_farmsvr_farm_bag_grids_limit_warn = new MonitorId(FarmSvr); // 农场背包格子上限预警
    public static final MonitorId attr_farmsvr_create_item_new = new MonitorId(FarmSvr); // 背包创建新道具ID
    public static final MonitorId attr_farmsvr_steal_crop = new MonitorId(FarmSvr); // 偷菜
    public static final MonitorId attr_farmsvr_evict = new MonitorId(FarmSvr); // 驱逐
    public static final MonitorId attr_farmsvr_gift = new MonitorId(FarmSvr); // 送礼

    public static final MonitorId attr_farmsvr_daily_add_exp_warning = new MonitorId(FarmSvr); // 每日获得农场经验超过阈值预警
    public static final MonitorId attr_farmsvr_daily_add_coin_warning = new MonitorId(FarmSvr); // 每日获得农场币超过阈值预警
    public static final MonitorId attr_farmsvr_daily_steal_times_warning = new MonitorId(FarmSvr); // 每日偷菜获得道具次数超过阈值预警
    public static final MonitorId attr_farmsvr_totalStolen_times_Warning = new MonitorId(FarmSvr); // 单作物被偷次数阈值预警
    public static final MonitorId attr_farmsvr_fertilize_times_Warning = new MonitorId(FarmSvr); // 单作物被施肥次数阈值预警
    public static final MonitorId attr_farmsvr_befertilized_times_Warning = new MonitorId(FarmSvr); // 每日被施肥次数阈值预警

    public static final MonitorId attr_farmsvr_crop_plant = new MonitorId(FarmSvr); // 种植养殖物
    public static final MonitorId attr_farmsvr_crop_care = new MonitorId(FarmSvr); // 照料养殖物
    public static final MonitorId attr_farmsvr_crop_harvest = new MonitorId(FarmSvr); // 收获养殖物
    public static final MonitorId attr_farmsvr_crop_destroy = new MonitorId(FarmSvr); // 销毁养殖物
    public static final MonitorId attr_farmsvr_crop_steal = new MonitorId(FarmSvr); // 偷取养殖物
    public static final MonitorId attr_farmsvr_crop_encourage = new MonitorId(FarmSvr); // 鼓励养殖物
    public static final MonitorId attr_farmsvr_plant_fish = new MonitorId(FarmSvr); // 养鱼
    public static final MonitorId attr_farmsvr_fishing_start = new MonitorId(FarmSvr);
    public static final MonitorId attr_farmsvr_fishing_end = new MonitorId(FarmSvr);
    public static final MonitorId attr_farmsvr_steal_fish = new MonitorId(FarmSvr); // 炸鱼
    public static final MonitorId attr_farmsvr_fishing_card_pack = new MonitorId(FarmSvr); // 钓卡包
    public static final MonitorId attr_farmsvr_steal_card_pack = new MonitorId(FarmSvr); // 偷卡包
    public static final MonitorId attr_farmsvr_open_card_pack = new MonitorId(FarmSvr); // 开卡包

    public static final MonitorId attr_farmsvr_crop_ripe_quality_bronze = new MonitorId(FarmSvr); // 养殖物普通成熟
    public static final MonitorId attr_farmsvr_crop_ripe_quality_silver = new MonitorId(FarmSvr); // 养殖物白银成熟
    public static final MonitorId attr_farmsvr_crop_ripe_quality_gold = new MonitorId(FarmSvr); // 养殖物黄金成熟
    public static final MonitorId attr_farmsvr_crop_ripe_in_weekend = new MonitorId(FarmSvr); // 养殖物周末成熟
    public static final MonitorId attr_farmsvr_fertilize = new MonitorId(FarmSvr); // 施肥

    public static final MonitorId attr_farmsvr_fish_ripe_quality_bronze = new MonitorId(FarmSvr); // 鱼塘普通成熟
    public static final MonitorId attr_farmsvr_fish_ripe_quality_silver = new MonitorId(FarmSvr); // 鱼塘白银成熟
    public static final MonitorId attr_farmsvr_fish_ripe_quality_gold = new MonitorId(FarmSvr); // 鱼塘黄金成熟
    public static final MonitorId attr_farmsvr_fish_ripe_in_weekend = new MonitorId(FarmSvr); // 鱼塘周末成熟

    public static final MonitorId attr_farmsvr_sysconf_error_and_use_default = new MonitorId(FarmSvr); // 农场系统配置出错并使用默认
    public static final MonitorId attr_cooksvr_sysconf_error_and_use_default = new MonitorId(FarmSvr);
    public static final MonitorId attr_gamesvr_farm_item_forward_fail = new MonitorId(Gamesvr); // 玩家背包农场道具自动使用失败

    public static final MonitorId attr_farmsvr_crop_steal_remind_ignored_count_warning = new MonitorId(FarmSvr); // 农作物被忽略提醒次数

    public static final MonitorId attr_farmsvr_pet_feed = new MonitorId(FarmSvr); // 农场宠物喂食
    public static final MonitorId attr_farmsvr_pet_evict = new MonitorId(FarmSvr); // 农场宠物驱逐
    public static final MonitorId attr_farmsvr_pet_fertilize_origin = new MonitorId(FarmSvr); // 宠物祈愿-来源
    public static final MonitorId attr_farmsvr_pet_fertilize_allow_trace = new MonitorId(FarmSvr); // 宠物祈愿-允许追随
    public static final MonitorId attr_farmsvr_pet_fertilize_real_trace = new MonitorId(FarmSvr); // 宠物祈愿-实际追随
    public static final MonitorId attr_farmsvr_pet_fertilize_real_trace_change_quality = new MonitorId(FarmSvr); // 宠物祈愿-实际追随改变了成熟质量
    public static final MonitorId attr_farmsvr_pet_gift_prepare = new MonitorId(FarmSvr); // 宠物送礼-准备礼物
    public static final MonitorId attr_farmsvr_pet_gift_receive = new MonitorId(FarmSvr); // 宠物送礼-收取礼物
    public static final MonitorId attr_farmsvr_vile_delay_eat_prebook = new MonitorId(FarmSvr); // 霸王花吞食-预订
    public static final MonitorId attr_farmsvr_vile_delay_eat_done = new MonitorId(FarmSvr); // 霸王花延迟吞食-完成
    public static final MonitorId attr_farmsvr_vile_immediate_eat = new MonitorId(FarmSvr); // 霸王花立刻吞食

    public static final MonitorId attr_farmsvr_villager_summon = new MonitorId(FarmSvr); // 邀请村民
    public static final MonitorId attr_farmsvr_villager_deport = new MonitorId(FarmSvr); // 请离村民
    public static final MonitorId attr_farmsvr_villager_prepare_normal_gift = new MonitorId(FarmSvr); // 村民产出日常礼物
    public static final MonitorId attr_farmsvr_villager_prepare_ondemand_gift = new MonitorId(FarmSvr); // 村民产出按需礼物
    public static final MonitorId attr_farmsvr_villager_prepare_fullfavor_gift = new MonitorId(FarmSvr); // 村民产出好感度礼物
    public static final MonitorId attr_farmsvr_villager_prepare_festival_gift = new MonitorId(FarmSvr); // 村民产出节日礼物
    public static final MonitorId attr_farmsvr_villager_present_gift = new MonitorId(FarmSvr); // 村民送礼
    public static final MonitorId attr_farmsvr_villager_refresh_normal_gift = new MonitorId(FarmSvr); // 村民日常礼物刷新
    public static final MonitorId attr_farmsvr_villager_refresh_ondemand_gift = new MonitorId(FarmSvr); // 村民按需礼物刷新

    public static final MonitorId attr_farmsvr_party_publish = new MonitorId(FarmSvr); // 农场派对发布
    public static final MonitorId attr_farmsvr_party_default_query_first = new MonitorId(FarmSvr); // 农场派对首页默认查询
    public static final MonitorId attr_farmsvr_party_default_query_pit = new MonitorId(FarmSvr); // 农场派对非首页默认查询
    public static final MonitorId attr_farmsvr_party_search_query = new MonitorId(FarmSvr); // 农场派对搜索查询
    public static final MonitorId attr_farmsvr_farm_num_ds = new MonitorId(FarmSvr);                  //农场ds活跃人数统计
    public static final MonitorId attr_farmsvr_god_stone_polish = new MonitorId(FarmSvr); // 神石打磨


    // 农场小屋
    public static final MonitorId attr_housesvr_house_num = new MonitorId(FarmSvr);                  //农场小屋个数 @当前农场小屋总数
    public static final MonitorId attr_housesvr_create_house = new MonitorId(FarmSvr);                //创建农场小屋个数 @创建农场小屋
    public static final MonitorId attr_housesvr_player_num = new MonitorId(FarmSvr);            //玩家总数 @玩家总数
    public static final MonitorId attr_housesvr_house_create_timeout = new MonitorId(FarmSvr);        //CREATE_GAME_SESSION_TIMEOUT个数 @CREATE_GAME_SESSION_TIMEOUT总数
    public static final MonitorId attr_housesvr_house_create_abnormal_stop = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_ABNORMAL_STOP个数 @CREATE_GAME_SESSION_ABNORMAL_STOP总数
    public static final MonitorId attr_housesvr_house_create_process_force_terminated = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_DS_PROCESS_FORCE_TERMINATED个数 @CREATE_GAME_SESSION_DS_PROCESS_FORCE_TERMINATED总数
    public static final MonitorId attr_housesvr_house_create_fleet_force_terminated = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_FLEET_FORCE_TERMINATED个数 @CREATE_GAME_SESSION_FLEET_FORCE_TERMINATED总数
    public static final MonitorId attr_housesvr_house_create_game_session_too_long = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_GAME_SESSION_TOO_LONG个数 @CREATE_GAME_SESSION_GAME_SESSION_TOO_LONG总数
    public static final MonitorId attr_housesvr_house_end_abnormal = new MonitorId(FarmSvr);          //GAME_SESSION_ENDCODE_ABNORMAL个数 @GAME_SESSION_ENDCODE_ABNORMAL总数
    public static final MonitorId attr_housesvr_house_end_ds_process_force_terminated = new MonitorId(FarmSvr);          //GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED个数 @GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED总数
    public static final MonitorId attr_housesvr_house_end_fleet_force_terminated = new MonitorId(FarmSvr);          //GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED个数 @GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED总数
    public static final MonitorId attr_housesvr_house_end_game_seesion_too_long = new MonitorId(FarmSvr);          //GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG个数 @GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG总数
    public static final MonitorId attr_housesvr_house_end_process_ending_without_terminate_game_session = new MonitorId(FarmSvr);          //GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION个数 @GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION总数
    public static final MonitorId attr_housesvr_ds_force_terminate = new MonitorId(FarmSvr);            //农场强制结束次数 @农场强制结束
    public static final MonitorId attr_house_ds_create_fail = new MonitorId(FarmSvr);

    public static final MonitorId attr_housesvr_house_remove = new MonitorId(FarmSvr); // 农场小屋内存淘汰
    public static final MonitorId attr_housesvr_dbhouse_update_fail = new MonitorId(FarmSvr);            //农场小屋存盘出错 @农场存盘出错
    public static final MonitorId attr_housesvr_dbhouse_invalid_version = new MonitorId(FarmSvr);            //农场小屋存盘版本号冲突 @农场存盘版本号冲突
    public static final MonitorId attr_house_create_house_fail = new MonitorId(Gamesvr);              // 农场小屋创建farmosvr返回失败
    public static final MonitorId attr_house_proc_crop_finish_quality_bronze = new MonitorId(FarmSvr); // 加工物普通成熟
    public static final MonitorId attr_house_proc_crop_finish_quality_silver = new MonitorId(FarmSvr); // 加工物白银成熟
    public static final MonitorId attr_house_proc_crop_finish_quality_gold = new MonitorId(FarmSvr); // 加工物黄金成熟
    public static final MonitorId attr_house_proc_crop_finish_in_weekend = new MonitorId(FarmSvr); // 加工物周末翻倍
    public static final MonitorId attr_house_on_load_error = new MonitorId(FarmSvr); // 小屋onload失败
    public static final MonitorId attr_house_on_init_error = new MonitorId(FarmSvr); // 小屋init失败
    public static final MonitorId attr_house_on_online_dating_success = new MonitorId(FarmSvr); // 推荐在线小屋成功
    public static final MonitorId attr_house_on_online_dating_fail = new MonitorId(FarmSvr); // 推荐在线小屋失败
    public static final MonitorId attr_house_decorate = new MonitorId(FarmSvr); // 内景装修
    public static final MonitorId attr_housesvr_furniture_buy = new MonitorId(FarmSvr); // 农场小屋家具购买
    public static final MonitorId attr_housesvr_furniture_sell = new MonitorId(FarmSvr); // 农场小屋家具出售
    public static final MonitorId attr_farmsvr_furniture_buy = new MonitorId(FarmSvr); // 农场通用家具购买
    public static final MonitorId attr_villagersvr_furniture_buy = new MonitorId(FarmSvr); // 农场村庄家具购买

    public static final MonitorId attr_Farmsvr_dbVillage_update_fail = new MonitorId(FarmSvr); //
    public static final MonitorId attr_Farmsvr_dbVillage_invalid_version = new MonitorId(FarmSvr); //

    public static final MonitorId attr_housesvr_house_num_ds = new MonitorId(FarmSvr);                  //小屋ds活跃人数统计

    // 餐厅
    public static final MonitorId attr_cooksvr_cook_num = new MonitorId(FarmSvr);    //餐厅个数 @当前农场小屋总数
    public static final MonitorId attr_cooksvr_create_cook = new MonitorId(FarmSvr);  //创建餐厅个数 @创建农场小屋
    public static final MonitorId attr_cooksvr_player_num = new MonitorId(FarmSvr);      //玩家总数 @玩家总数
    public static final MonitorId attr_cooksvr_cook_create_timeout = new MonitorId(FarmSvr);        //CREATE_GAME_SESSION_TIMEOUT个数 @CREATE_GAME_SESSION_TIMEOUT总数
    public static final MonitorId attr_cooksvr_cook_create_abnormal_stop = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_ABNORMAL_STOP个数 @CREATE_GAME_SESSION_ABNORMAL_STOP总数
    public static final MonitorId attr_cooksvr_cook_create_process_force_terminated = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_DS_PROCESS_FORCE_TERMINATED个数 @CREATE_GAME_SESSION_DS_PROCESS_FORCE_TERMINATED总数
    public static final MonitorId attr_cooksvr_cook_create_fleet_force_terminated = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_FLEET_FORCE_TERMINATED个数 @CREATE_GAME_SESSION_FLEET_FORCE_TERMINATED总数
    public static final MonitorId attr_cooksvr_cook_create_game_session_too_long = new MonitorId(FarmSvr);  //CREATE_GAME_SESSION_GAME_SESSION_TOO_LONG个数 @CREATE_GAME_SESSION_GAME_SESSION_TOO_LONG总数
    public static final MonitorId attr_cooksvr_cook_end_abnormal = new MonitorId(FarmSvr); //GAME_SESSION_ENDCODE_ABNORMAL个数 @GAME_SESSION_ENDCODE_ABNORMAL总数
    public static final MonitorId attr_cooksvr_cook_end_ds_process_force_terminated = new MonitorId(FarmSvr); //GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED个数 @GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED总数
    public static final MonitorId attr_cooksvr_cook_end_fleet_force_terminated = new MonitorId(FarmSvr);  //GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED个数 @GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED总数
    public static final MonitorId attr_cooksvr_cook_end_game_seesion_too_long = new MonitorId(FarmSvr);  //GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG个数 @GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG总数
    public static final MonitorId attr_cooksvr_cook_end_process_ending_without_terminate_game_session = new MonitorId(FarmSvr); //GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION个数 @GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION总数
    public static final MonitorId attr_cooksvr_ds_force_terminate = new MonitorId(FarmSvr);  //农场强制结束次数 @农场强制结束
    public static final MonitorId attr_cook_ds_create_fail = new MonitorId(FarmSvr);

    public static final MonitorId attr_cooksvr_cook_remove = new MonitorId(FarmSvr); // 餐厅内存淘汰
    public static final MonitorId attr_cooksvr_dbcook_update_fail = new MonitorId(FarmSvr);    //餐厅存盘出错 @农场存盘出错
    public static final MonitorId attr_cooksvr_dbcook_invalid_version = new MonitorId(FarmSvr);//餐厅存盘版本号冲突 @农场存盘版本号冲突
    public static final MonitorId attr_cook_create_cook_fail = new MonitorId(Gamesvr);         // 餐厅创建farmosvr返回失败
    public static final MonitorId attr_cooksvr_furniture_buy = new MonitorId(FarmSvr); // 农场餐厅家具购买
    public static final MonitorId attr_cooksvr_furniture_sell = new MonitorId(FarmSvr); // 农场餐厅家具出售
    public static final MonitorId attr_cooksvr_cook_num_ds = new MonitorId(FarmSvr);                  //餐厅ds活跃人数统计
    // 方案
    public static final MonitorId attr_house_layout_save = new MonitorId(FarmSvr); // 方案保存
    public static final MonitorId attr_house_layout_del = new MonitorId(FarmSvr);  // 方案删除

    // 餐厅贵宾
    public static final MonitorId attr_farmsvr_cook_visitant_prebook_arrive = new MonitorId(FarmSvr);
    public static final MonitorId attr_farmsvr_cook_visitant_steal_arrive = new MonitorId(FarmSvr);
    public static final MonitorId attr_farmsvr_cook_visitant_steal = new MonitorId(FarmSvr);
    public static final MonitorId attr_farmsvr_cook_visitant_serve = new MonitorId(FarmSvr);
    public static final MonitorId attr_farmsvr_cook_visitant_settle_success = new MonitorId(FarmSvr);
    public static final MonitorId attr_farmsvr_cook_visitant_settle_fail = new MonitorId(FarmSvr);
    public static final MonitorId attr_farmsvr_cook_visitant_served_expire = new MonitorId(FarmSvr);


    // 活动服 ActivitySvr
    public static final MonitorId attr_activitysvr_actdb_create = new MonitorId(ActivitySvr); // 活动服玩家db插入
    public static final MonitorId attr_activitysvr_actdb_load = new MonitorId(ActivitySvr);   // 活动服玩家db加载
    public static final MonitorId attr_activitysvr_actdb_update = new MonitorId(ActivitySvr); // 活动服玩家db更新
    public static final MonitorId attr_activitysvr_cache_add = new MonitorId(ActivitySvr);    // 活动服玩家缓存添加
    public static final MonitorId attr_activitysvr_cache_remove = new MonitorId(ActivitySvr); // 活动服玩家缓存移除
    public static final MonitorId attr_activitysvr_cache_size = new MonitorId(ActivitySvr);   // 活动服玩家缓存列表大小
    public static final MonitorId attr_activitysvr_savelist_size = new MonitorId(ActivitySvr);    // 活动服存盘队列大小
    public static final MonitorId attr_activitysvr_weedlist_size = new MonitorId(ActivitySvr);    // 活动服淘汰队列大小
    public static final MonitorId attr_activitysvr_savinglist_size = new MonitorId(ActivitySvr);  // 活动服存盘中队列大小
    public static final MonitorId attr_activitysvr_cachesize_Warning = new MonitorId(ActivitySvr); //活动服缓存即将过载
    public static final MonitorId attr_activitysvr_task_complete = new MonitorId(ActivitySvr); //活动服任务完成
    public static final MonitorId attr_activitysvr_savedata_fail = new MonitorId(ActivitySvr); //活动服存盘失败
    public static final MonitorId attr_activitysvr_OfflineDataWrite_success = new MonitorId(ActivitySvr); //活动服离线数据写成功
    public static final MonitorId attr_activitysvr_OfflineDataWrite_fail = new MonitorId(ActivitySvr); //活动服离线数据写失败
    public static final MonitorId attr_activitysvr_send_reward_mail = new MonitorId(ActivitySvr); //活动服发送奖励邮件
    public static final MonitorId attr_activitysvr_record_event = new MonitorId(ActivitySvr); //活动服接受事件
    public static final MonitorId attr_activitysvr_outfit_gear = new MonitorId(ActivitySvr); //活动服->国庆换装活动生成服装
    public static final MonitorId attr_activitysvr_outfit_equip = new MonitorId(ActivitySvr); //活动服->国庆换装活动服选择服装
    public static final MonitorId attr_activitysvr_outfit_item = new MonitorId(ActivitySvr); //活动服->国庆换装活动捡到换装道具
    public static final MonitorId attr_activitysvr_bookoffriend_farm_gift = new MonitorId(ActivitySvr);   //活动服->花房活动农场赠礼
    public static final MonitorId attr_activitysvr_bookoffriend_recruited = new MonitorId(ActivitySvr);   //活动服->花房活动被召回触发积分处理
    public static final MonitorId attr_activitysvr_bookoffriend_recruit = new MonitorId(ActivitySvr);     //活动服->花房活动召回触发积分处理
    public static final MonitorId attr_activitysvr_travelingdog = new MonitorId(ActivitySvr); //活动服 旅行狗狗出游人数
    public static final MonitorId attr_activitysvr_travelingdog_reward = new MonitorId(ActivitySvr); //活动服 旅行狗狗奖励领取人数
    public static final MonitorId attr_activitysvr_travelingdog_mail_reward = new MonitorId(ActivitySvr); //活动服 旅行狗狗邮件发奖人数
    public static final MonitorId attr_activitysvr_fashion_fund_buy_size = new MonitorId(ActivitySvr);//活动服->时装基金购买人数
    public static final MonitorId attr_activitysvr_fashion_fund_before_return_player_size = new MonitorId(ActivitySvr);//活动服->时装基金领取购买周期内返还人数
    public static final MonitorId attr_activitysvr_fashion_fund_after_return_player_size = new MonitorId(ActivitySvr);//活动服->时装基金领取购买周期后返还人数
    public static final MonitorId attr_activitysvr_fashion_fund_before_return_coin = new MonitorId(ActivitySvr);//活动服->时装基金领取购买周期内返还金额
    public static final MonitorId attr_activitysvr_fashion_fund_after_return_coin = new MonitorId(ActivitySvr);//活动服->时装基金领取购买周期后返还金额
    public static final MonitorId attr_activitysvr_lottery_size = new MonitorId(ActivitySvr);//活动服->活动抽奖次数
    public static final MonitorId attr_activitysvr_lottery_WolfTeamChestGiftConf_today_limit_size = new MonitorId(ActivitySvr);//活动服->活动抽奖检查每日上限
    public static final MonitorId attr_activitysvr_lottery_WolfTeamChestGiftConf_total_limit_size = new MonitorId(ActivitySvr);//活动服->活动抽奖检查总上限
    public static final MonitorId attr_activitysvr_lottery_WolfTeamChestGiftConf_total_size = new MonitorId(ActivitySvr);//活动服->活动抽奖各个奖励发放次数
    public static final MonitorId attr_activitysvr_lottery_WolfTeamChestGiftConf_today_size = new MonitorId(ActivitySvr);//活动服->活动抽奖各个奖励每日发放次数
    public static final MonitorId attr_activitysvr_conanIp_ReceiveCardAward_size = new MonitorId(ActivitySvr);//活动服->柯南IP活跃活动领奖
    public static final MonitorId attr_activitysvr_conanIp_AddBuff_size = new MonitorId(ActivitySvr);//活动服->柯南IP活跃活动添加buff
    public static final MonitorId attr_activitysvr_collectDivineFarmerReceiveAward = new MonitorId(ActivitySvr);//活动服->神农集灵周期行领奖


    public static final MonitorId attr_activitysvr_actdb_create_team = new MonitorId(ActivitySvr); // 活动服队伍db插入
    public static final MonitorId attr_activitysvr_actdb_delete_team = new MonitorId(ActivitySvr); // 活动服队伍db删除
    public static final MonitorId attr_activitysvr_actdb_update_team = new MonitorId(ActivitySvr); // 活动服队伍db更新
    public static final MonitorId attr_activitysvr_actdb_query_team = new MonitorId(ActivitySvr);  // 活动服队伍db查询
    public static final MonitorId attr_activitysvr_actdb_batch_query_team = new MonitorId(ActivitySvr);  // 活动服队伍db批量查询

    //评分引导
    public static final MonitorId attr_gamesvr_score_guide_event_trigger_times = new MonitorId(Gamesvr); // 评分引导累计事件触发次数
    public static final MonitorId attr_gamesvr_score_guide_datamore_http_error = new MonitorId(Gamesvr); // 评分引导累计请求datamore接口异常

    // 农场ds
    public static final MonitorId attr_farm_ds_create_fail = new MonitorId(FarmSvr);

    //pod 初始化
    public static final MonitorId attr_pod_init_cnt = new MonitorId(Pod); //pod初始化耗时
    public static final MonitorId attr_pod_init_cost_time = new MonitorId(Pod); //pod初始化耗时
    // dsr
    public static final MonitorId attr_dsr_db_read = new MonitorId(DsRecoverySvr);                            // DSR读DB
    public static final MonitorId attr_dsr_db_write = new MonitorId(DsRecoverySvr);                           // DSR写DB
    public static final MonitorId attr_dsr_notify_ds_event = new MonitorId(DsRecoverySvr);                    // DSR通知DS事件
    public static final MonitorId attr_dsr_lock_create_game_session_req = new MonitorId(DsRecoverySvr);       // DSR收到的占位创建DS的请求
    public static final MonitorId attr_dsr_lock_create_game_session = new MonitorId(DsRecoverySvr);           // DSR处理占位
    public static final MonitorId attr_dsr_modify_subscriber_req = new MonitorId(DsRecoverySvr);              // DSR收到的修改订阅者的请求
    public static final MonitorId attr_dsr_modify_subscriber = new MonitorId(DsRecoverySvr);                  // DSR处理修改订阅者
    public static final MonitorId attr_dsr_migrate_game_session_req = new MonitorId(DsRecoverySvr);           // DSR收到的迁移GameSession的请求
    public static final MonitorId attr_dsr_migrate_game_session = new MonitorId(DsRecoverySvr);               // DSR处理迁移GameSession
    public static final MonitorId attr_dsr_game_session_end_event = new MonitorId(DsRecoverySvr);             // DSR收到GameSession结束事件
    public static final MonitorId attr_dsr_dsa_lost_event = new MonitorId(DsRecoverySvr);                     // DSR收到DSA事件
    public static final MonitorId attr_dsr_game_session_timeout_event = new MonitorId(DsRecoverySvr);         // DSR收到GameSession恢复事件
    public static final MonitorId attr_dsr_update_game_session_status = new MonitorId(DsRecoverySvr);         // DSR更新GameSession状态
    public static final MonitorId attr_dsr_set_notify_fail_result = new MonitorId(DsRecoverySvr);             // DSR设置通知失败结果
    public static final MonitorId attr_dsr_create_recovery_game_session_event = new MonitorId(DsRecoverySvr); // DSR收到创建需要恢复的GameSession
    public static final MonitorId attr_dsr_ds_game_session_cnt = new MonitorId(DsRecoverySvr);                // DSR获取的单个DsId下GameSession数
    public static final MonitorId attr_dsr_delete_game_session_cnt = new MonitorId(DsRecoverySvr);            // DSR删除的GameSession数
    public static final MonitorId attr_dsr_create_game_session_cnt = new MonitorId(DsRecoverySvr);            // DSR创建的GameSession数
    public static final MonitorId attr_dsr_upgrade_game_session = new MonitorId(DsRecoverySvr);               // DSR更新指定GameSession

    // spdbsvr
    public static final MonitorId attr_spdbsvr_part_key_count_1 = new MonitorId(SpDbSvr);         // partkey查询结果数量小于1

    public static final MonitorId attr_spdbsvr_part_key_count_10 = new MonitorId(SpDbSvr);         // partkey查询结果数量小于10

    public static final MonitorId attr_spdbsvr_part_key_count_50 = new MonitorId(SpDbSvr);         // partkey查询结果数量小于50

    public static final MonitorId attr_spdbsvr_part_key_count_100 = new MonitorId(SpDbSvr);        // partkey查询结果数量小于100

    public static final MonitorId attr_spdbsvr_part_key_count_200 = new MonitorId(SpDbSvr);        // partkey查询结果数量小于200

    public static final MonitorId attr_spdbsvr_part_key_count_500 = new MonitorId(SpDbSvr);        // partkey查询结果数量小于500

    public static final MonitorId attr_spdbsvr_part_key_count_large_500 = new MonitorId(SpDbSvr);  // partkey查询结果数量大于500

    public static final MonitorId attr_spdbsvr_part_key_max_count = new MonitorId(SpDbSvr);        // partkey查询结果数量最大值

    public static final MonitorId attr_spdbsvr_inner_que_length = new MonitorId(SpDbSvr);          // 内部队列长度 @内部队列长度

    public static final MonitorId attr_spdbsvr_key2_records_max_count = new MonitorId(SpDbSvr);    // key2最大数据条目 @key2最大数据条目

    public static final MonitorId attr_spdbsvr_rpc_key2_count = new MonitorId(SpDbSvr);            // key2请求数量 @key2请求数量

    // starpmailsvr
    public static final MonitorId attr_starpmailsvr_inner_que_length = new MonitorId(StarPMailSvr); // 内部队列长度 @内部队列长度


    //pod 优雅退出
    public static final MonitorId attr_pod_offline_cnt = new MonitorId(Pod); //优雅退出请求次数
    public static final MonitorId attr_pod_offline_check_cnt = new MonitorId(Pod); //优雅退出检查次数
    public static final MonitorId attr_pod_offline_coro_job_count = new MonitorId(Pod); //优雅退出时协议job数量
    public static final MonitorId attr_pod_offline_coro_async_count = new MonitorId(Pod); //优雅退出时异步协程数量
    public static final MonitorId attr_pod_offline_after_time = new MonitorId(Pod);    //优雅退出时剩余时间
    public static final MonitorId attr_pod_offline_service_count = new MonitorId(Pod); //优雅退出时服务数量
    public static final MonitorId attr_pod_offline_cost_time = new MonitorId(Pod); //优雅退出耗时

    //pod 预下线
    public static final MonitorId attr_pod_pre_offline_cnt = new MonitorId(Pod); //预下线请求次数
    public static final MonitorId attr_pod_pre_offline_check_cnt = new MonitorId(Pod); //预下线检查次数
    public static final MonitorId attr_pod_pre_offline_coro_job_count = new MonitorId(Pod); //预下线时协议job数量
    public static final MonitorId attr_pod_pre_offline_coro_async_count = new MonitorId(Pod); //预下线时异步协程数量
    public static final MonitorId attr_pod_pre_offline_after_time = new MonitorId(Pod);    //预下线时剩余时间
    public static final MonitorId attr_pod_pre_offline_service_count = new MonitorId(Pod); //预下线时服务数量
    public static final MonitorId attr_pod_pre_offline_cost_time = new MonitorId(Pod); //预下线耗时

    public static final MonitorId attr_battlesvr_mid_join = new MonitorId(BattleSvr); // 中途加入战斗

    public static final MonitorId attr_roomsvr_mid_join = new MonitorId(RoomSvr); // 房间请求中途加入战斗

    public static final MonitorId attr_gray_redis_add_count = new MonitorId(Gray); //内存灰度标记添加数量
    public static final MonitorId attr_gray_redis_update_count = new MonitorId(Gray); //内存灰度标记更新数量
    public static final MonitorId attr_gray_redis_query_count = new MonitorId(Gray); // redis灰度标记查询次数 
    public static final MonitorId attr_gray_redis_query_miss_count = new MonitorId(Gray); // redis灰度标记miss次数
    public static final MonitorId attr_gray_cache_add_count = new MonitorId(Gray); //内存灰度标记缓存新增数量
    public static final MonitorId attr_gray_cache_size = new MonitorId(Gray); //内存灰度标记缓存数量
    public static final MonitorId attr_gray_route_error = new MonitorId(Gray); //灰度数据路由到了非灰度节点
    public static final MonitorId attr_proto_version_set_error = new MonitorId(Gray); //RPC版本号设置失败
    
    public static final MonitorId attr_battlesvr_player_cnt_gs_ds_not_same = new MonitorId(BattleSvr); // 房间请求中途加入战斗

    public static final MonitorId attr_pulsar_req_info = new MonitorId(Pulsar);// pulsar请求信息 支持俩个参数和0个参数
    public static final MonitorId attr_battlesvr_mid_join_match = new MonitorId(BattleSvr); // 匹配中途加入战斗
    public static final MonitorId attr_battlesvr_mid_join_room = new MonitorId(BattleSvr); // 邀请中途加入战斗
    
    public static final MonitorId attr_cachesvr_master_add_count = new MonitorId(CacheSvr); //master添加次数
    public static final MonitorId attr_cachesvr_master_reload_count = new MonitorId(CacheSvr); //master reload次数
    public static final MonitorId attr_cachesvr_master_remove_count = new MonitorId(CacheSvr); //master移除次数
    public static final MonitorId attr_cachesvr_slave_add_count = new MonitorId(CacheSvr); //slave添加次数
    public static final MonitorId attr_cachesvr_slave_remove_count = new MonitorId(CacheSvr); //slave移除次数
    public static final MonitorId attr_cachesvr_update_count = new MonitorId(CacheSvr); //缓存被更新次数
    public static final MonitorId attr_cachesvr_update_attrs_count = new MonitorId(CacheSvr); //缓存属性被更新次数
    public static final MonitorId attr_cachesvr_master_count = new MonitorId(CacheSvr); //master数量
    public static final MonitorId attr_cachesvr_slave_count = new MonitorId(CacheSvr); //slave数量
    public static final MonitorId attr_cachesvr_master_attr_count = new MonitorId(CacheSvr); //masterAttr数量
    public static final MonitorId attr_cachesvr_slave_attr_count = new MonitorId(CacheSvr); //masterAttr数量
    public static final MonitorId attr_cachesvr_master_attr_key_len = new MonitorId(CacheSvr); //masterKeyAttr长度
    public static final MonitorId attr_cachesvr_master_attr_value_len = new MonitorId(CacheSvr); //masterValueAttr长度
    public static final MonitorId attr_cachesvr_slave_attr_key_len = new MonitorId(CacheSvr); //slaveKeyAttr长度
    public static final MonitorId attr_cachesvr_slave_attr_value_len = new MonitorId(CacheSvr); //slaveValueAttr长度
    public static final MonitorId attr_cachesvr_slave_loading_count = new MonitorId(CacheSvr);  //同时请求加载的slave数量
    public static final MonitorId attr_cacehsvr_salve_load_refuse_total_count = new MonitorId(CacheSvr); //超过loading上限，强制拒绝的数量
    
    public static final MonitorId attr_cachesvr_slave_meta_count = new MonitorId(CacheSvr); //slaveMeta数量
    public static final MonitorId attr_cachesvr_heartbeat_check_queue_size = new MonitorId(CacheSvr); //心跳检测队列长度
    public static final MonitorId attr_cachesvr_master_valid_check_queue_size = new MonitorId(CacheSvr); //主缓存异常检测队列长度
    public static final MonitorId attr_cachesvr_heartbeat_tick_cost_time = new MonitorId(CacheSvr); //tick检测耗时
    public static final MonitorId attr_cachesvr_master_valid_check_tick_cost_time = new MonitorId(CacheSvr); //tick检测耗时

    public static final MonitorId attr_cachesvr_rpc_send_heartbeat = new MonitorId(CacheSvr);
    public static final MonitorId attr_cachesvr_rpc_slave_cache_remove_ntf_to_master = new MonitorId(CacheSvr);
    public static final MonitorId attr_cachesvr_rpc_master_ntf_modify_to_slave = new MonitorId(CacheSvr); //同步修改给cachesvr
    public static final MonitorId attr_cachesvr_rpc_master_ntf_remove_to_slave = new MonitorId(CacheSvr);
    public static final MonitorId attr_cachesvr_rpc_slave_cache_load_from_master = new MonitorId(CacheSvr);
    public static final MonitorId attr_cachesvr_rpc_kick_cache = new MonitorId(CacheSvr);
    public static final MonitorId attr_cachesvr_rpc_modify_cache = new MonitorId(CacheSvr); 
    
    public static final MonitorId attr_cachesvr_add_slave_meta_total_count = new MonitorId(CacheSvr); //master移除slaveMeta数量
    public static final MonitorId attr_cachesvr_remove_slave_meta_total_count = new MonitorId(CacheSvr); //master移除slaveMeta数量

    public static final MonitorId attr_cachesvr_batch_get_total_count = new MonitorId(CacheSvr);  //请求批量获取的次数
    public static final MonitorId attr_cachesvr_batch_get_total_cost_time = new MonitorId(CacheSvr); //请求批量花费时间
    public static final MonitorId attr_cachesvr_batch_get_total_cache_count = new MonitorId(CacheSvr); //请求批量获取的数量
    public static final MonitorId attr_cachesvr_batch_load_total_count = new MonitorId(CacheSvr); //请求加载slave次数
    public static final MonitorId attr_cachesvr_batch_load_total_cost_time = new MonitorId(CacheSvr); //请求加载slave耗时
    public static final MonitorId attr_cachesvr_batch_load_total_cache_count = new MonitorId(CacheSvr); //请求加载slave数量

    public static final MonitorId attr_cachesvr_redis_load_count = new MonitorId(CacheSvr);
    public static final MonitorId attr_cachesvr_redis_update_count = new MonitorId(CacheSvr);
    public static final MonitorId attr_cachesvr_redis_delete_count = new MonitorId(CacheSvr);
    public static final MonitorId attr_cachesvr_redis_exception_count = new MonitorId(CacheSvr);

    
    public static final MonitorId attr_cachesvr_ugc_player_public_load_no_record = new MonitorId(CacheSvr); //请求加载的玩家数据不存在
    

    public static final MonitorId attr_ugcappsvr_common_req = new MonitorId(UgcAppSvr);           // ugcappsvr通用请求
    public static final MonitorId attr_ugcappsvr_common_req_cmd = new MonitorId(UgcAppSvr);       // 命令字维度下的ugcappsvr通用请求
    public static final MonitorId attr_ugcappsvr_common_req_result = new MonitorId(UgcAppSvr);    // 请求结果维度下的ugcappsvr通用请求
    public static final MonitorId attr_ugcappsvr_ugc_achievement_get = new MonitorId(UgcAppSvr);  // 自定义属性拉取
    public static final MonitorId attr_ugcappsvr_ugc_achievement_edit = new MonitorId(UgcAppSvr);  // 自定义属性编辑


    public static final MonitorId attr_battle_common_broadcast = new MonitorId(Gamesvr);

    public static final MonitorId attr_battle_feature_conf_lost = new MonitorId(BattleSvr); // 找不到玩法的依赖ds配置

    public static final MonitorId attr_hot_res_sync_from_remote = new MonitorId(HotRes);
    public static final MonitorId attr_hot_res_sync_bytes_from_remote = new MonitorId(HotRes);
    public static final MonitorId attr_hot_res_cache_update = new MonitorId(HotRes);
    public static final MonitorId attr_hot_res_deliver = new MonitorId(HotRes);
    public static final MonitorId attr_hot_res_map_size = new MonitorId(HotRes);
    public static final MonitorId attr_hot_res_sharding_size = new MonitorId(HotRes);
    public static final MonitorId attr_tbuspp_shard = new MonitorId(Shard);
    public static final MonitorId attr_online_shard = new MonitorId(Shard);
    public static final MonitorId attr_lobby_shard = new MonitorId(Shard);

    public static final MonitorId attr_online_pc = new MonitorId(Gamesvr);
    public static final MonitorId attr_online_cloud = new MonitorId(Gamesvr); // 云游在线人次
    public static final MonitorId attr_online_va = new MonitorId(Gamesvr);    // VA在线人次

    public static final MonitorId attr_warm_score_update = new MonitorId(Gamesvr);  //温暖分变化
    public static final MonitorId attr_player_hug_other = new MonitorId(Player); //玩家抱起陌生人的数据

    // 卡牌系统
    public static final MonitorId attr_card_bag_open_count = new MonitorId(Gamesvr);                      // 卡包开启
    public static final MonitorId attr_ext_card_bag_count = new MonitorId(Gamesvr);                  // 获得额外卡包
    public static final MonitorId attr_card_trade_cache_visit_count = new MonitorId(Gamesvr);             // 交易信息缓存访问
    public static final MonitorId attr_card_trade_direct_visit_count = new MonitorId(Gamesvr);            // 交易信息直接访问
    public static final MonitorId attr_card_trade_create_count = new MonitorId(Gamesvr);                  // 交易创建
    public static final MonitorId attr_card_trade_complete_count = new MonitorId(Gamesvr);                // 交易完成
    public static final MonitorId attr_card_trade_share_count = new MonitorId(Gamesvr);                   // 交易分享
    public static final MonitorId attr_card_wild_card_get_count = new MonitorId(Gamesvr);                 // 万能牌获取
    public static final MonitorId attr_card_wild_card_exchange_count = new MonitorId(Gamesvr);            // 万能牌兑换


    // 分享礼包系统
    public static final MonitorId attr_share_gift_cache_visit_count = new MonitorId(Gamesvr);             // 分享礼包信息缓存访问
    public static final MonitorId attr_share_gift_direct_batch_visit_count = new MonitorId(Gamesvr);            // 分享礼包信息批量访问db
    public static final MonitorId attr_share_gift_direct_one_visit_count = new MonitorId(Gamesvr);            // 分享礼包信息单个访问db
    public static final MonitorId attr_share_gift_insert_count = new MonitorId(Gamesvr);                  // insert分享礼包
    public static final MonitorId attr_share_gift_update_count = new MonitorId(Gamesvr);                  // update分享礼包
    public static final MonitorId attr_share_gift_history_update_count = new MonitorId(Gamesvr);           // 分享礼包历史记录db更新
    public static final MonitorId attr_share_gift_send_chat_count = new MonitorId(Gamesvr);           // 分享礼包扣除道具后生成分享失败
    public static final MonitorId attr_share_gift_history_count = new MonitorId(Gamesvr);                  // 分享礼包历史记录缓存数量

    // 皮肤分享系统
    public static final MonitorId attr_fashion_share_pull_userattr_count = new MonitorId(Gamesvr);            // 皮肤分享拉取playerUser

    public static final MonitorId attr_ugc_initiate_evaluation = new MonitorId(Gamesvr);  // 发起评审
    public static final MonitorId attr_ugc_evaluation_list_get_limit = new MonitorId(UgcSvr);  // 拉取评审列表限频
    public static final MonitorId attr_ugc_evaluation_list_get_self = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_evaluation_list_search_self = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_evaluation_list_get = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_evaluation_list_search = new MonitorId(UgcSvr);

    public static final MonitorId attr_async_es_search = new MonitorId(AsyncSvr);
    public static final MonitorId attr_async_es_delete = new MonitorId(AsyncSvr);
    public static final MonitorId attr_async_es_create = new MonitorId(AsyncSvr);
    public static final MonitorId attr_async_es_update = new MonitorId(AsyncSvr);


    public static final MonitorId attr_cocsvr_match_error = new MonitorId(CocSvr); // coc匹配出错
    public static final MonitorId attr_cocsvr_pvp_match_cnt = new MonitorId(CocSvr); // 匹配总次数
    public static final MonitorId attr_cocsvr_pvp_match_player_cnt = new MonitorId(CocSvr); // 匹配到玩家的总次数
    public static final MonitorId attr_cocsvr_robot_resource_error = new MonitorId(CocSvr); // coc机器人配置错误
    public static final MonitorId attr_cocsvr_stop_unfinished_db_player_count = new MonitorId(CocSvr); // cocsvr停服未完成存盘的玩家数量

    public static final MonitorId attr_coc_player_load_from_db = new MonitorId(CocPlayer);
    public static final MonitorId attr_coc_player_remove_from_cache = new MonitorId(CocPlayer);
    public static final MonitorId attr_coc_player_register_suc = new MonitorId(CocPlayer);
    public static final MonitorId attr_coc_player_register_fail = new MonitorId(CocPlayer);
    public static final MonitorId attr_coc_player_cache_count = new MonitorId(CocPlayer);
    public static final MonitorId attr_coc_player_active_count = new MonitorId(CocPlayer);  // 在场景中的玩家数量
    public static final MonitorId attr_coc_player_mem_size_max = new MonitorId(CocPlayer);  // cocPlayer对象最大大小
    public static final MonitorId attr_coc_player_mem_size_avg = new MonitorId(CocPlayer);  // cocPlayer对象平均大小
    public static final MonitorId attr_coc_player_db_size_max = new MonitorId(CocPlayer);   // CocUserAttr属性DB存盘数据最大大小
    public static final MonitorId attr_coc_player_db_size_avg = new MonitorId(CocPlayer);   // CocUserAttr属性DB存盘数据平均大小

    public static final MonitorId attr_coc_enter_game_scene_suc = new MonitorId(CocGameScene);
    public static final MonitorId attr_coc_enter_game_scene_failed = new MonitorId(CocGameScene);
    public static final MonitorId attr_coc_exit_game_scene = new MonitorId(CocGameScene);

    public static final MonitorId attr_ugc_extra_config_get = new MonitorId(UgcSvr);  // ds内获取玩家数据
    public static final MonitorId attr_ugc_extra_config_edit = new MonitorId(UgcSvr);  // ds内编辑玩家数据
    public static final MonitorId attr_hok_xiaobai_roominfoid = new MonitorId(Gamesvr); //hok 小白roomInfoId
    public static final MonitorId attr_hok_normal_abtest = new MonitorId(Gamesvr); //hok 实验组普通用户触发
    public static final MonitorId attr_hok_update_xiaobai_score = new MonitorId(Gamesvr); //hok 更新小白用户积分
    public static final MonitorId attr_hok_stop_update_xiaobai_score = new MonitorId(Gamesvr); //hok 停止更新小白用户积分
    public static final MonitorId attr_hok_user_in_ailab = new MonitorId(BattleSvr); //hok 用户对局机器人选择ailab
    public static final MonitorId attr_hok_fight_direct_flag = new MonitorId(Gamesvr); //hok 战斗引导标记

    public static final MonitorId attr_livelink_stream_on_count = new MonitorId(Gamesvr); // livelink开播次数
    public static final MonitorId attr_livelink_stream_off_count = new MonitorId(Gamesvr); // livelink关播次数
    public static final MonitorId attr_livelink_game_begin_count = new MonitorId(Gamesvr); // livelink开启对局送礼次数
    public static final MonitorId attr_livelink_game_end_count = new MonitorId(Gamesvr); // livelink关闭对局送礼次数
    public static final MonitorId attr_livelink_stream_on_online_player_count = new MonitorId(Gamesvr); // livelink开播在线玩家人数

    public static final MonitorId attr_hok_reset_unlock_condition = new MonitorId(Gamesvr); // 重置解锁条件
    public static final MonitorId attr_hok_call_ai_invite_failed = new MonitorId(Gamesvr); //hok 调用邀请接口失败
    public static final MonitorId attr_hok_ai_invite_count_changed = new MonitorId(Gamesvr); //hok 调用邀请接口失败

    public static final MonitorId attr_discover_db_request_count = new MonitorId(Gamesvr);// 发现系统引导查db结果

    public static final MonitorId attr_plat_common_config = new MonitorId(ConfigSvr); // 平台通用配置获取

    // GameApi
    public static final MonitorId attr_gameapi_cs_ntf_forward = new MonitorId(GameApi);                   // 转发cs协议


    public static final MonitorId attr_passwordCode_create = new MonitorId(Gamesvr);// 口令码产生
    public static final MonitorId attr_passwordCode_use = new MonitorId(Gamesvr);// 口令码产生
    public static final MonitorId attr_passwordCode_repeat = new MonitorId(Gamesvr);// 口令码冲突

    public static final MonitorId attr_ugc_match_config_sync_remote = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_match_config_parse_remote = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_match_config_sync_cache = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_match_config_parse_cache = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_match_config_set_cache = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_match_config_map_cache = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_match_config_map_reload_cache = new MonitorId(UgcSvr);

    public static final MonitorId attr_ugc_cover_video_key_ask = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_cover_video_set = new MonitorId(UgcSvr);
    public static final MonitorId attr_ugc_cover_video_clear = new MonitorId(UgcSvr);

    // 事件
    public static final MonitorId attr_event_dispatch_count = new MonitorId(Event);                   // 本地触发事件
    public static final MonitorId attr_event_remote_receive_count = new MonitorId(Event);             // 跨服接收事件
    public static final MonitorId attr_event_remote_send_count = new MonitorId(Event);                // 跨服发送事件
    public static final MonitorId attr_event_remote_send_queue = new MonitorId(Event);                // 跨服发送事件队列
    public static final MonitorId attr_event_remote_send_time = new MonitorId(Event);                 // 跨服发送事件耗时

    // 通用红点
    public static final MonitorId attr_general_red_dot_add_cnt = new MonitorId(Gamesvr);  // 添加通用红点
    public static final MonitorId attr_general_red_dot_delete_cnt = new MonitorId(Gamesvr);  // 删除通用红点

    //ArenaSvr
    public static final MonitorId attr_arenasvr_player_season_stat = new MonitorId(ArenaSvr);
    public static final MonitorId attr_arenasvr_player_season_add_stat = new MonitorId(ArenaSvr);
    public static final MonitorId attr_arenasvr_hot_value_reload = new MonitorId(ArenaSvr);
    public static final MonitorId attr_arenasvr_ds_frame = new MonitorId(ArenaSvr);
    public static final MonitorId attr_arenasvr_min_ds_frame = new MonitorId(ArenaSvr);
    public static final MonitorId attr_arenasvr_suit_reload = new MonitorId(ArenaSvr);

    //DsCommonMonitor
    public static final MonitorId attr_ds_common_monitor_upstream_bandwidth = new MonitorId(DsCommonMonitor);     // 上行带宽
    public static final MonitorId attr_ds_common_monitor_downlink_bandwidth = new MonitorId(DsCommonMonitor);     // 下行带宽
    public static final MonitorId attr_ds_common_monitor_noplayer_ds_count = new MonitorId(DsCommonMonitor);      // 无玩家ds数量
    public static final MonitorId attr_ds_common_monitor_enable_ai_commentary_ds_count = new MonitorId(DsCommonMonitor);       // 开启AI解说ds数量

    // MidJoin
    public static final MonitorId attr_room_mid_join_status_sync = new MonitorId(RoomSvr);
    public static final MonitorId attr_room_direct_join_batch = new MonitorId(RoomSvr);

    public static final MonitorId attr_battle_mid_join_status_sync = new MonitorId(BattleSvr);

    public static final MonitorId attr_room_quick_join_pre = new MonitorId(Gamesvr);
    public static final MonitorId attr_room_quick_join = new MonitorId(Gamesvr);

    // 结算 BattleSettlement
    public static final MonitorId attr_chase_records_attr_too_many = new MonitorId(BattleSettlement);


    //spaccpuntsvr
    public static final MonitorId attr_spaccountsvr_player_reigster_count = new MonitorId(SpAccountSvr);  //  starpaccountsvr注册数量
    public static final MonitorId attr_spaccountsvr_remove_from_cache = new MonitorId(SpAccountSvr);  //  玩家缓存淘汰统计
    public static final MonitorId attr_spaccountsvr_refresh_3sec_tick_cost_time = new MonitorId(SpAccountSvr); //3秒tick耗时
    public static final MonitorId attr_spaccountsvr_refresh_5sec_tick_cost_time = new MonitorId(SpAccountSvr); //5秒tick耗时
    public static final MonitorId attr_spaccountsvr_refresh_daily_cost_time = new MonitorId(SpAccountSvr); //日刷新耗时
    public static final MonitorId attr_spaccountsvr_refresh_weekly_cost_time = new MonitorId(SpAccountSvr); //周刷新耗时

    public static final MonitorId attr_starpsvr_visitor_occupy = new MonitorId(StarPSvr);  //  访客占位

    // timer
    public static final MonitorId attr_timer_total_cnt = new MonitorId(Timer);
    public static final MonitorId attr_timer_add_cnt = new MonitorId(Timer);
    public static final MonitorId attr_timer_run_cnt = new MonitorId(Timer);
    public static final MonitorId attr_timer_cancel_cnt = new MonitorId(Timer);
    public static final MonitorId attr_timer_total_cost_ns = new MonitorId(Timer);
    public static final MonitorId attr_timer_max_cost_ns = new MonitorId(Timer);

    //sp es
    public static final MonitorId attr_starp_es_write = new MonitorId(StarPSvr);  //房间ES写入
    public static final MonitorId attr_starp_es_search = new MonitorId(StarPSvr); // 普通房间查询
    public static final MonitorId attr_starp_es_search_str = new MonitorId(StarPSvr); //房间搜索关键词
    public static final MonitorId attr_starp_es_search_official = new MonitorId(StarPSvr); // 官方房间查询
    public static final MonitorId attr_starp_es_search_official_quick_enter = new MonitorId(StarPSvr); // 官方房间快速加入查询
    public static final MonitorId attr_starp_es_write_cost_time = new MonitorId(StarPSvr); // 房间ES写耗时统计
    public static final MonitorId attr_starp_es_search_cost_time = new MonitorId(StarPSvr); // 房间ES不带关键词搜索耗时统计
    public static final MonitorId attr_starp_es_search_str_cost_time = new MonitorId(StarPSvr); // 房间ES带关键词搜索耗时统计
    public static final MonitorId attr_starp_remove_from_cache = new MonitorId(StarPSvr);   // 房间缓存淘汰统计

    public static final MonitorId attr_starp_room_online_normal = new MonitorId(StarPSvr);   //房间人数统计
    public static final MonitorId attr_starp_room_online_visitor = new MonitorId(StarPSvr);   //房间人数统计
    public static final MonitorId attr_starp_room_member_normal = new MonitorId(StarPSvr);   //房间人数统计
    public static final MonitorId attr_starp_room_member_visitor = new MonitorId(StarPSvr);   //房间人数统计

    public static final MonitorId attr_starp_online_count = new MonitorId(StarPSvr);      //在线人数统计

    public static final MonitorId attr_starp_room_in_cache = new MonitorId(StarPSvr); //缓存中的房间数量

    public static final MonitorId attr_starp_refresh_3sec_tick_cost_time = new MonitorId(StarPSvr); //3秒tick耗时
    public static final MonitorId attr_starp_refresh_5min_tick_cost_time = new MonitorId(StarPSvr); //5分钟tick耗时
    public static final MonitorId attr_starp_refresh_1hour_tick_cost_time = new MonitorId(StarPSvr); //1小时tick耗时
    public static final MonitorId attr_starp_refresh_daily_cost_time = new MonitorId(StarPSvr); //日刷新耗时

    public static final MonitorId attr_starp_group_refresh_daily_cost_time = new MonitorId(StarPGroupSvr); //日刷新耗时(groupsvr)
    public static final MonitorId attr_starp_group_refresh_3sec_tick_cost_time = new MonitorId(StarPGroupSvr); //日刷新耗时(groupsvr)

    public static final MonitorId attr_starp_guild_refresh_daily_cost_time = new MonitorId(StarPGuildSvr);
    //重置框架相关  MonitorModule:TimeRefresh
    public static final MonitorId attr_time_refresh_foreach_module_count = new MonitorId(TimeRefresh); //遍历次数
    public static final MonitorId attr_time_refresh_module_count = new MonitorId(TimeRefresh); //模块数量
    public static final MonitorId attr_gamesvr_refresh_daily_cost_time = new MonitorId(Gamesvr); //日刷新耗时
    public static final MonitorId attr_gamesvr_refresh_weekly_cost_time = new MonitorId(Gamesvr); //周刷新耗时
    public static final MonitorId attr_gamesvr_refresh_monthly_cost_time = new MonitorId(Gamesvr); //月刷新耗时
    public static final MonitorId attr_gamesvr_remove_from_cache = new MonitorId(Gamesvr);   // 缓存淘汰统计
    public static final MonitorId attr_starp_batch_get_guild_public_req_cnt = new MonitorId(Statistics); // @GuildPublicDao.batchGet接口请求次数
    public static final MonitorId attr_starpgroup_es_search_str = new MonitorId(StarPGroupSvr); // starp宗门搜索带关键词
    public static final MonitorId attr_starpgroup_es_search = new MonitorId(StarPGroupSvr); // starp宗门搜索不带关键词
    public static final MonitorId attr_starpgroup_es_search_str_cost_time = new MonitorId(StarPGroupSvr); // starp宗门带关键词搜索耗时统计
    public static final MonitorId attr_starpgroup_es_search_cost_time = new MonitorId(StarPGroupSvr); // starp宗门不带关键词搜索耗时统计
    public static final MonitorId attr_starproom_publish_recruit = new MonitorId(StarPRoomSvr); // SP缓存招募次数
    public static final MonitorId attr_starproom_redis_recruit = new MonitorId(StarPRoomSvr); // SP招募redis次数
    public static final MonitorId attr_spdb_ds_interaction_szie = new MonitorId(SpDbSvr); // SP离线消息单次查询大小

    public static final MonitorId attr_battle_multi_scene_cnt = new MonitorId(BattleSvr);  // 总多场景对局个数
    public static final MonitorId attr_battle_multi_scene_matchtype_cnt = new MonitorId(BattleSvr);  // 多场景对局个数
    public static final MonitorId attr_battle_multi_scene_matchtype_sub_scene_cnt = new MonitorId(BattleSvr);  // 子场景个数监控
    public static final MonitorId attr_battle_multi_scene_kill_scene_cnt = new MonitorId(BattleSvr);  // 被管理杀掉的场景个数
    public static final MonitorId attr_battle_multi_scene_change_scene_req = new MonitorId(BattleSvr);  // 发起场景改变的请求
    public static final MonitorId attr_battle_multi_scene_change_scene_ec = new MonitorId(BattleSvr);  // 发起场景切换预处理结果
    public static final MonitorId attr_battle_multi_scene_create_scene = new MonitorId(BattleSvr);  // 创建子场景结果
    public static final MonitorId attr_battle_multi_scene_create_scene_player = new MonitorId(BattleSvr);  // 创建子场景结果-玩家个数
    public static final MonitorId attr_battle_multi_scene_mid_join_scene_player = new MonitorId(BattleSvr);  // 加入子场景结果-玩家个数
    public static final MonitorId attr_battle_multi_scene_brief_error = new MonitorId(BattleSvr);  // 多场景的依赖数据异常
    public static final MonitorId attr_battle_multi_scene_kick_player = new MonitorId(BattleSvr);  // 多场景进行踢人
    // scene 时长 sub_scene 时长

    public static final MonitorId attr_battle_arena_boss_monitor = new MonitorId(BattleSvr);  // arena boss监控
    public static final MonitorId attr_battle_arena_gs_reserved_monitor = new MonitorId(Gamesvr);  // arena gs监控reserve

    public static final MonitorId attr_op_attr_operator_cli = new MonitorId(AttrOp); // 远程操作 @操作类型
    public static final MonitorId attr_op_attr_operator_svr = new MonitorId(AttrOp); // 远程操作 @操作类型



    
    // LOGGER字段后不要加入MonitorId 否则脚本生成监控json时会忽略
    private static final Logger LOGGER = LogManager.getLogger("zhiyanMonitor");

    private static final WeANamedNoIntEnumRegistry<MonitorId> registry = WeANamedNoIntEnumRegistry.builder(
                    MonitorId.class, MonitorId.class.getPackageName())
            .isSubClassAddEnumPrefix(true)
            .build();

    static {
        registry.registerEnums();
        LOGGER.info("MonitorId registerNoIntEnums succ");
    }

    private final MonitorModule monitorModule;
    private final Map<String, String> tagMap = new HashMap<>();

    protected MonitorId(MonitorModule metricModule) {
        this.monitorModule = metricModule;
//        tagMap.put(ZhiyanMonitor.zhiyanModuleKey, this.monitorModule.name());
//        tagMap.put(ZhiyanMonitor.zhiyanMonitorId, name());
    }

    public MonitorModule getMonitorModule() {
        return monitorModule;
    }

    public Map<String, String> getTagMap() {
        return tagMap;
    }

    public static MonitorId forName(String name) {
        return registry.forName(name);
    }

    public static MonitorId valueOf(String name) {
        return MonitorId.forName(name);
    }

    public static Collection<MonitorId> values() {
        return registry.values();
    }
}
