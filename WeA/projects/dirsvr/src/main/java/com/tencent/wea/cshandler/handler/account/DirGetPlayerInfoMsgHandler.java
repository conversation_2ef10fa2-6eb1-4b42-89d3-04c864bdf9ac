package com.tencent.wea.cshandler.handler.account;

import com.google.protobuf.Message;
import com.tencent.blackoperate.BlackOperateUtil;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.transferplat.TransferPlatUtil;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.WhiteList;
import com.tencent.resourceloader.resclass.ResServerWhiteAddrData;
import com.tencent.resourceloader.resclass.ResServerWhiteData;
import com.tencent.resourceloader.resclass.TestBanIpCityData;
import com.tencent.resourceloader.resclass.XlsWhiteListPlayerGroupConfData;
import com.tencent.resourcepatcher.ResPatcher;
import com.tencent.rpc.limiter.MsgRateLimitMgr;
import com.tencent.rpc.limiter.DynamicLimitEnum;
import com.tencent.serverinfomanager.ServerInfo;
import com.tencent.tbuspp.TbusppApi;
import com.tencent.tbuspp.TbusppManager;
import com.tencent.tbuspp.TbusppUtil;
import com.tencent.tcaplus.dao.OpenIdToUidDao;
import com.tencent.tconnd.AbstractPbClientRequestHandler;
import com.tencent.tconnd.Session;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.NKStopWatch;
import com.tencent.timiutil.time.TxStopWatch;
import com.tencent.timiutil.tool.ServerInfoTool;
import com.tencent.wea.framework.DirEngine;
import com.tencent.wea.framework.DirLoadBalancer;
import com.tencent.wea.framework.limit.DirIpBlackListManager;
import com.tencent.wea.framework.limit.DirIpReqRecordManager;
import com.tencent.wea.framework.limit.DirReqLimitManager;
import com.tencent.wea.framework.limit.DirReqType;
import com.tencent.wea.namedenum.servertype.WeAServerType;
import com.tencent.wea.protocol.CsAccount;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.common.AccountTransferState;
import com.tencent.wea.protocol.CsPlayer;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.common.SvrInfo;
import com.tencent.wea.protocol.common.TlogRequiredFields;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tlog.flow.TlogPlayerLoginFail;
import com.tencent.wea.xlsRes.ResServerWhite;
import com.tencent.wea.xlsRes.keywords.PlatformID;
import com.tencent.wea.xlsRes.keywords.WhiteListPriorityEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * dir获取玩家信息消息处理
 *
 * <AUTHOR>
 * @date 2021/07/02
 */
public class DirGetPlayerInfoMsgHandler extends AbstractPbClientRequestHandler {

    /**
     * 日志记录器
     */
    private static final Logger LOGGER = LogManager.getLogger(DirGetPlayerInfoMsgHandler.class);

    static private boolean isPressBot(String openId) {
        return openId.contains("ROBOT_PRESSTEST");
    }

    /**
     * 处理
     *
     * @param session 会话
     * @param header  头
     * @param request 请求
     * @return {@link Message.Builder}
     */
    @Override
    public Message.Builder handle(Session session, CsHead.CSHeader header, Message request) {
        /* 测试代码
        if(!MsgRateLimitMgr.getInstance().dynamicConsume(DynamicLimitEnum.DIR_GET_PLAYER_INFO, 2, 0.01, 1)) {
            NKErrorCode.ReqLimit.throwError("req limit");
            return null;
        }*/
        TxStopWatch stopWatch = NKStopWatch.SW_DirGetPlayerInfo.getStopWatch("getPlayerInfoHandle");
        stopWatch.mark("getPlayerInfoHandle start");
        CsAccount.DirGetPlayerInfo_C2S_Msg reqMsg = (CsAccount.DirGetPlayerInfo_C2S_Msg) request;
        CsAccount.DirGetPlayerInfo_S2C_Msg.Builder rspBuilder = CsAccount.DirGetPlayerInfo_S2C_Msg.newBuilder();
        LOGGER.debug("getPlayerInfoMsg start, ip:{} openId:{} platId:{}", session.getIpAddr(), session.getOpenid(), reqMsg.getPlatId());

        DirIpReqRecordManager.getInstance().addReq(session.getIpAddr());
        if (DirIpBlackListManager.getInstance().inBlackTime(session.getIpAddr())) {
            NKErrorCode.DirInBlackList.throwError("in black list, ip:{}", session.getIpAddr());
        }
        if (StringUtils.isBlank(session.getOpenid())) {
            NKErrorCode.SessionStateError.throwError("tconnd session opend is null");
        }

        boolean useDirCountryBlackList = PropertyFileReader.getRealTimeBooleanItem("useDirCountryBlackList", true);
        if (TestBanIpCityData.getInstance() != null && useDirCountryBlackList) {
            List<String> location = BlackOperateUtil.getIpLocation(session.getIpAddr());
            if (TestBanIpCityData.getInstance().isBanCity(location)) {
                TlogRequiredFields.Builder publicFields = TlogRequiredFields.newBuilder();
                publicFields.setVopenid(session.getOpenid())
                        .setVRoleID(session.getUid())
                        .setServerIp(String.valueOf(Framework.getInstance().getServerId()))
                        .setClientIP(session.getIpAddr());
                TlogPlayerLoginFail tlogPlayerLoginFail = TlogPlayerLoginFail.hire(publicFields);

                tlogPlayerLoginFail.setErrorCode(NKErrorCode.BanCityIPLoginLimit.getValue())
                        .setIsRegister(99)
                        .logToTlogd();
                LOGGER.warn(
                        "player {} {} in country black list, not allow login, ip {}, location {}",
                        session.getOpenid(),
                        session.getUid(),
                        session.getIpAddr(),
                        StringUtils.join(location.toArray(), "-"));
                session.sendNtfMsg(MsgTypes.MSG_TYPE_IPBANNEDNTF, CsPlayer.IPBannedNtf.newBuilder());
                NKErrorCode.BanLoginIPCityLimit.throwError("invalid country");
            }
        }

        // 配置控制强制停服
        boolean closeServer = PropertyFileReader.getRealTimeBooleanItem("ForceCloseServer", false);
        rspBuilder.setIsServerClose(closeServer ? 1 : 0);
        if (closeServer) {
            return rspBuilder;
        }

        NKErrorCode limitErrorCode = DirReqLimitManager.getInstance().addReq(
                DirReqType.GetPlayerInfo, session.getIpAddr(), session.getOpenid());
        if (limitErrorCode != NKErrorCode.OK) {
            LOGGER.warn("getPlayerInfoMsg req limit, ip:{} openId:{} platId:{}", session.getIpAddr(), session.getOpenid(), reqMsg.getPlatId());
            limitErrorCode.throwError("req limit");
        }

        if (ResPatcher.needCheck(reqMsg.getResourceVer())) {
            if (ResPatcher.dumpRequiredPatches(reqMsg.getResourceVer(), null, rspBuilder.getResPatchInfoBuilder())) {
                // continue the full logic, not repeat
                // return rspBuilder;
                var patchInfo = rspBuilder.getResPatchInfo();
                LOGGER.info("{} dumpRequiredPatches for version from:{} to:{} size:{}", session.getOpenid()
                        , reqMsg.getResourceVer(), patchInfo.getVersion(), patchInfo.getEntriesCount());
            }
        }

        stopWatch.mark("getPlayerInfoHandle.beforeOpenIdToUid");
        CsAccount.AccountInfo.Builder accountInfoBuilder = CsAccount.AccountInfo.newBuilder();
        accountInfoBuilder.setOpenId(session.getOpenid());

        // 登录时转区状态拦截
        long uid = 0L;
        List<TcaplusDb.OpenIdToUid> openIdToUidList = OpenIdToUidDao.getDataListByOpenId(session.getOpenid());
        if (openIdToUidList != null && !openIdToUidList.isEmpty()) {
            for (TcaplusDb.OpenIdToUid openIdToUid : openIdToUidList) {
                // 转区中
                if (openIdToUid.getTransferStatus() == AccountTransferState.ACCOUNT_STATE_TRANSFER_DOING_VALUE
                        || openIdToUid.getTransferStatus() == AccountTransferState.ACCOUNT_STATE_TRANSFER_FAIL_VALUE) {
                    NKErrorCode.AccountTransferPlatFail.throwError("user is already in transfer status, openid:{}, platId:{}, uid:{}",
                            openIdToUid.getOpenid(), openIdToUid.getPlatId(), openIdToUid ,openIdToUid.getUid());
                }

                if (openIdToUid.getPlatId() == reqMsg.getPlatId() && openIdToUid.getUid() != 0) {
                    uid = openIdToUid.getUid();
                    accountInfoBuilder.setModUid(uid);
                }
            }
        }

        //final long uid = OpenIdToUidDao.getUidByOpenIdAndPlatIdByCheck(session.getOpenid(), reqMsg.getPlatId());
        //if (uid != 0) {
        //    accountInfoBuilder.setModUid(uid);
        //}
        stopWatch.mark("getPlayerInfoHandle.afterOpenIdToUid");

        String ip = PropertyFileReader.getRealTimeItem("gamesvr_outterip", "");

        if (DirEngine.getInstance().isPressTest() && !isPressBot(reqMsg.getOpenid())) {
            LOGGER.info("detect non press bot login on press server, openid:{}", reqMsg.getOpenid());
            ip = "press.ymzx.qq.com";
        }

        String port;
        if (Boolean.parseBoolean(PropertyFileReader.getItem("dir_lb_enable", "false"))) {
            ServerInfo server = DirLoadBalancer.getInstance().chooseGamesvr();
            if (server == null) {
                LOGGER.fatal("getPlayerInfoMsg is full, ip:{} openId:{} platId:{}",
                        session.getIpAddr(), session.getOpenid(), reqMsg.getPlatId());
                NKErrorCode.ModServerUserFull.throwError("server is full");
            }
            port = String.valueOf(server.getPort());
        } else {
            port = PropertyFileReader.getRealTimeItem("gamesvr_outterport", "0");
        }

        /**
         * 白名单进入灰度地址
         */
        if (!ServerInfoTool.isOverseaEnv()) { //非国际化环境才开启白名单
            stopWatch.mark("getPlayerInfoHandle.beforeWhite");
            boolean forceCheckWhiteList = PropertyFileReader.getRealTimeBooleanItem("ForceCheckDirWhiteList", false);
            if ("editor".equals(reqMsg.getToken())) {
                forceCheckWhiteList = false;
            }

            int level = WhiteList.getWhiteListPriorityLevel("", reqMsg.getOpenid(), reqMsg.getPlatId(), "login");
            if (forceCheckWhiteList && level < WhiteListPriorityEnum.WhiteListPriority_High_VALUE) {
                LOGGER.info("getPlayerInfoMsg not in whitelist, ip:{} openId:{} platId:{} level:{}",
                        session.getIpAddr(), session.getOpenid(), reqMsg.getPlatId(), level);
                NKErrorCode.DIR_WHITE_LIST_CHECK_FAIL.throwError("not in whitelist");
            }

            if (!PropertyFileReader.getRealTimeBooleanItem("allowLogin", true)) {
                //白名单检查
                LOGGER.info("found in device white list plat={} openid={}  loginPriority={}, ban login",
                        reqMsg.getPlatId(), reqMsg.getOpenid(), level);
                if (level == WhiteListPriorityEnum.WhiteListPriority_Super_VALUE) {
                    //login all status server
                } else {
                    NKErrorCode.ServerMaintain.throwError("ServerMaintain");
                }
            }
        }

        ResServerWhite.ServerWhiteData data = ResServerWhiteData.getInstance().get(accountInfoBuilder.getOpenId());
        if (data != null) {
            ResServerWhite.ServerWhiteAddrData addrData = ResServerWhiteAddrData.getInstance()
                    .get(data.getAddrID());
            if (addrData != null) {
                port = addrData.getPort();
                ip = addrData.getIp();
            }
            accountInfoBuilder.addAllPermission(data.getPermissionList());
        }

        rspBuilder.setAccount(accountInfoBuilder);
        stopWatch.mark("getPlayerInfoHandle.afterWhite");
        SvrInfo.Builder svrInfo = SvrInfo.newBuilder();
        svrInfo.setSvrIp(ip);
        svrInfo.setSvrPort(port);
        String gamesvrInstData = DirLoadBalancer.getInstance().chooseGamesvr(session.getOpenid(), uid);
        if (gamesvrInstData != null && gamesvrInstData.isEmpty()) {
            gamesvrInstData = DirLoadBalancer.getInstance().chooseGamesvrByClientVersion(session.getOpenid(), reqMsg.getClientVer());
        }
        if (gamesvrInstData == null) {
            NKErrorCode.DirNoSuitableGamServer.throwError("no suitable gamesvr");
        } else if (!gamesvrInstData.isEmpty()) {
            rspBuilder.setGameSvrInstanceData(gamesvrInstData);
        }

        setWaterMarkOpen(reqMsg.getOpenid(), rspBuilder);

        rspBuilder.setSvrinfo(svrInfo);
        rspBuilder.setIsServerClose(closeServer ? 1 : 0);
        stopWatch.mark("getPlayerInfoHandle.afterUpdateNoticeInfo");
        stopWatch.dump(0);

        // 连接私服
        if (PropertyFileReader.getRealTimeBooleanItem("private_svr_enable", false)) {
            rspBuilder.setGameSvrFuncId(WeAServerType.ST_PrivateServer_VALUE);
        }

        LOGGER.info("getPlayerInfoMsg success, ip:{} openId:{} platId:{} gamesvrInstData:{} serverInfo:{}", 
              session.getIpAddr(), session.getOpenid(), reqMsg.getPlatId(), gamesvrInstData, svrInfo);
        return rspBuilder;
    }

    private void setWaterMarkOpen(String openID, CsAccount.DirGetPlayerInfo_S2C_Msg.Builder rspBuilder) {
        if (!PropertyFileReader.getRealTimeBooleanItem("water_mark_open", false)) {
            rspBuilder.setIsWaterMark(false);
            LOGGER.debug("setWaterMarkOpen water_mark_open close openId:{}", openID);
            return;
        }
        if (XlsWhiteListPlayerGroupConfData.getInstance()
                .isInAdminWhiteList(openID, "WaterMarkWhiteList")) {
            LOGGER.info("setWaterMarkOpen isInAdminWhiteList openId:{}", openID);
            rspBuilder.setIsWaterMark(false);
            return;
        }
        LOGGER.debug("setWaterMarkOpen not isInAdminWhiteList openId:{}", openID);
        rspBuilder.setIsWaterMark(true);
    }
}
