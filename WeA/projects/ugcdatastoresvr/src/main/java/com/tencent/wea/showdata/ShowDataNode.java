package com.tencent.wea.showdata;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.tcaplus.TcaplusErrorCode;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.wea.protocol.common.UgcShowDataStoreOperate;
import com.tencent.wea.protocol.common.UgcShowDataStoreWrapper;
import com.tencent.wea.showdata.ShowDataWrapper.OperateRet;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDbWrapper;
import com.tencent.wea.tcaplus.TcaplusDbWrapper.UgcShowDataStoreTable;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ShowDataNode {

    private static final Logger LOGGER = LogManager.getLogger(ShowDataNode.class);

    private boolean dirty;
    private TcaplusDbWrapper.UgcShowDataStoreTable showDataStoreTable;
    private int version;
    private long lastFlush;
    private int flushFailCnt;
    private long lastUpdate;
    private boolean invalidVersion;
    private boolean isInTickUserQueueProcess = false;   // 是否在tick队列处理中

    public ShowDataNode(TcaplusDbWrapper.UgcShowDataStoreTable showDataStoreTable, int version) {
        this.dirty = false;
        this.showDataStoreTable = showDataStoreTable;
        this.version = version;
        this.lastFlush = 0;
        this.flushFailCnt = 0;
        this.lastUpdate = Framework.currentTimeMillis();
        this.invalidVersion = false;
    }

    private void setDirty() {
        this.dirty = true;
    }

    private void clearDirty() {
        this.dirty = false;
    }

    private boolean isDirty() {
        return this.dirty;
    }

    public void setIsInTickUserQueueProcess(boolean isInTickUserQueueProcess) {
        this.isInTickUserQueueProcess = isInTickUserQueueProcess;
    }

    public boolean isInTickUserQueueProcess() {
        return this.isInTickUserQueueProcess;
    }

    private UgcShowDataStoreTable getShowDataStoreTable() {
        return this.showDataStoreTable;
    }

    public TcaplusDb.UgcShowDataStoreTable.Builder getUgcShowDataStoreTableCopy() {
        return TcaplusDb.UgcShowDataStoreTable.newBuilder()
                .mergeFrom(getShowDataStoreTable().getUgcShowDataStoreTableBuilder().build());
    }

    private long getLastFlush() {
        return this.lastFlush;
    }

    private int getFlushFailCnt() {
        return this.flushFailCnt;
    }

    private void clearFlushFailCnt() {
        this.flushFailCnt = 0;
    }

    private void incrFlushFailCnt() {
        this.flushFailCnt += 1;
    }

    private void setVersion(int version) {
        this.version = version;
    }

    private int getVersion() {
        return this.version;
    }

    private void setLastUpdate() {
        this.lastUpdate = Framework.currentTimeMillis();
    }

    public long getLastUpdate() {
        return this.lastUpdate;
    }

    private void setInvalidVersion() {
        this.invalidVersion = true;
    }

    private boolean getInvalidVersion() {
        return this.invalidVersion;
    }

    private long getUgcId() {
        return getShowDataStoreTable().getUgcId();
    }

    private long getUid() {
        return getShowDataStoreTable().getUid();
    }

    // 判断是否需要写回到db中
    public boolean isNeedFlush() {
        return isDirty() && ShowDataConfMgr.getInstance().canTryFlush(getLastFlush());
    }

    // 写会到db中
    public void flush() {
        int runStep = 0;
        try {
            boolean checkIsDirty = isDirty();
            // 1. 处理更新标记
            this.lastFlush = Framework.currentTimeMillis();  // 处理刷新标记
            setLastUpdate();
            clearDirty();
            runStep = 1;
            // 2. 写入数据
            NKPair<TcaplusErrorCode, Integer> res = ShowDataDao.getInstance()
                    .setShowDataStoreWithVersion(getShowDataStoreTable(), getVersion());
            TcaplusErrorCode ec = res.getKey();
            if (ec.invalidVersion()) {
                // 如果版本不对就让这条记录立刻在记录中移除
                setInvalidVersion();
                runStep = 3;
                // 打日志
                LOGGER.error("ugcId:{} uid:{} set failed invalid version", getUgcId(), getUid());
                Monitor.getInstance().add.fail(MonitorId.attr_ugcdatastoresvr_show_data_flush, 1);
                return;
            } else if (!ec.noError()) {
                // 这里数据有异常 需要重新尝试写入
                incrFlushFailCnt();
                runStep = 4;
                // 打日志
                LOGGER.error("ugcId:{} uid:{} set failed ec:{}", getUgcId(), getUid(), ec.getValue());
                Monitor.getInstance().add.fail(MonitorId.attr_ugcdatastoresvr_show_data_flush, 1);
                return;
            }
            // 3. 写入成功了，处理更新字段
            runStep = 2;
            setVersion(res.getValue());
            clearFlushFailCnt();
            // 4. 需要删除一下这个数据的redis快照缓存
            if (checkIsDirty) {
                ShowDataRedisMgr.getInstance().delUgcShowDataSnapshot(getUgcId(), getUid());
            }
            // 打日志
            LOGGER.debug("ugcId:{} uid:{} flush success", getUgcId(), getUid());
            Monitor.getInstance().add.succ(MonitorId.attr_ugcdatastoresvr_show_data_flush, 1);
        } catch (Exception e) {
            LOGGER.error("flush failed ugcId:{} uid:{} version:{} e:", getUgcId(), getUid(), getVersion());
        } finally {
            // 有异常检查步骤
            if (runStep == 1 || runStep == 4) {
                setDirty();
                // 打日志
                LOGGER.info("ugcId:{} uid:{} will try flush again runStep:{}", getUgcId(), getUid(), runStep);
            }
        }
    }

    // 检查是否脏并回写
    public void tryFlush() {
        if (isDirty()) {
            flush();
        }
    }

    // 是否需要从缓存中移除
    public boolean isNeedRemove() {
        if (getInvalidVersion()) {
            LOGGER.warn("ugcId:{} uid:{} node version invalid", getShowDataStoreTable().getUgcId(),
                    getShowDataStoreTable().getUid());
            return true;
        } else if (ShowDataConfMgr.getInstance().checkFlushFailCnt(getFlushFailCnt())) {
            LOGGER.error("ugcId:{} uid:{} flush to db to many error", getShowDataStoreTable().getUgcId(),
                    getShowDataStoreTable().getUid());
            return true;
        }
        // 检查是不是太长时间没更新了
        return ShowDataConfMgr.getInstance().canTryRemote(getLastUpdate());
    }

    // 获取数据的key值
    public NKPair<Long, Long> GetKey() {
        return new NKPair<>(getShowDataStoreTable().getUgcId(), getShowDataStoreTable().getUid());
    }

    // 获取wrapper
    private ShowDataWrapper<?> getShowDataWrapper(int dataType) throws NKRuntimeException {
        ShowDataWrapper<?> wrapper = ShowDataWrapperMgr.getInstance()
                .getShowDataWrapper(dataType, getShowDataStoreTable());
        if (wrapper == null) {
            LOGGER.error("ugcId:{} uid:{} dataType:{} not exist", getShowDataStoreTable().getUgcId(),
                    getShowDataStoreTable().getUid(), dataType);
            NKErrorCode.UgcDataStoreShowDataFunctionNotExist.throwErrorIfNotOk("dataType:{} not exit", dataType);
        } else if (!wrapper.isFuncOpenRealTime()) {
            LOGGER.info("ugcId:{} uid:{} dataType:{} closed", getShowDataStoreTable().getUgcId(),
                    getShowDataStoreTable().getUid(), dataType);
            NKErrorCode.UgcDataStoreShowDataFunctionClosed.throwErrorIfNotOk("dataType:{} closed", dataType);
        }
        return wrapper;
    }

    // 处理获取请求
    public void doGetReq(List<UgcShowDataStoreWrapper> input, List<UgcShowDataStoreWrapper> output)
            throws NKRuntimeException {
        // 处理获取更新
        setLastUpdate();
        for (UgcShowDataStoreWrapper one : input) {
            try {
                // 1. 获取接口
                ShowDataWrapper<?> wrapper = getShowDataWrapper(one.getHead().getDataType());
                // 2. 处理获取请求
                UgcShowDataStoreWrapper.Builder oneBuilder = UgcShowDataStoreWrapper.newBuilder();
                NKErrorCode ec = wrapper.getShowDataStoreTable(one.getBody(), oneBuilder);
                if (ec != NKErrorCode.OK) {
                    continue;
                }
                output.add(oneBuilder.build());
            } catch (Exception e) {
                LOGGER.error("e:", e);
            }
        }
    }

    // 处理设置请求
    public void doEditReq(long battleId, UgcShowDataStoreOperate operate, UgcShowDataStoreWrapper.Builder output)
            throws NKRuntimeException {
        // 处理获取更新
        setLastUpdate();
        // 1. 获取接口
        ShowDataWrapper<?> wrapper = getShowDataWrapper(operate.getHead().getDataType());
        // 2. 处理设置请求
        OperateRet ret = wrapper.editShowDataStoreTable(battleId, operate);
        wrapper.copyAfterEditShowDataStoreTable(operate, output);
        // 2.5 判断错误码
        ret.getEc().throwErrorIfNotOk("update failed");
        // 3. 设置数据为脏
        if (ret.isChanged()) {
            setDirty();
        }
    }
}
