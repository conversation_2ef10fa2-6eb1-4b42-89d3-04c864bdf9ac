package com.tencent.wea.mgr;

import com.tencent.cachelock.CacheLockAgent;
import com.tencent.cachelock.CacheLockAgent.ReleaseLockReason;
import com.tencent.cachelock.CacheLockErrorCode;
import com.tencent.distributedLock.DistributedLockType;
import com.tencent.nk.commonframework.Framework;
import com.tencent.cachelock.CacheRestoreAble;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.util.NKPair;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.framework.UgcDataStoreEngine;
import com.tencent.wea.protocol.common.LocalServiceType;
import com.tencent.wea.protocol.common.UgcPlayerDataStoreData;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class DataStoreMgr extends CacheRestoreAble{
    private static final Logger LOGGER = LogManager.getLogger(DataStoreMgr.class);

    public static DataStoreMgr getInstance() {
        return DataStoreMgr.InstanceHolder.instance;
    }

    static class InstanceHolder {
        public static DataStoreMgr instance = new DataStoreMgr();
    }

    // tick消费队列
    private final Queue<NKPair<Long, Long>> tickKeyQueue = new ConcurrentLinkedQueue<>();
    private DataStoreCache dataStoreCache = null;
    private Map<String, Integer> serialSizeStaticsMap;
    private MapSettingCache mapSettingCache = null; // 地图设置信息
    private long lastTickPlayerTime = Framework.currentTimeMillis();
    private long lastReportCacheSizeTime = Framework.currentTimeMillis();
    public int init(CacheLockAgent cacheLockAgent) {
        boolean res = initAgent(cacheLockAgent, DistributedLockType.DISTRIBUTED_LOCK_TYPE_DATASTORE_PLAYER_CACHE);
        if (!res){
            LOGGER.error("CacheRestoreAble.initAgent failed!");
            return -1;
        }
        initSerialSizeStaticsMap();

        int playerCapacity = DataStoreConfMgr.getInstance().getPlayerCacheCapacity();
        LOGGER.info("dataStoreCache construct capacity:{}", playerCapacity);
        dataStoreCache = new DataStoreCache(playerCapacity);

        int mapSettingCapacity = PropertyFileReader.getRealTimeIntItem("ugc_map_setting_cache_capacity", 1000);  // 按mapid获取设置信息的缓存数
        long mapSettingExpire = PropertyFileReader.getRealTimeLongItem("ugc_map_setting_cache_expire",6000L);  // 6000 毫秒 就是6秒
        LOGGER.info("construct mapSettingCapacity:{} expire {}", mapSettingCapacity, mapSettingExpire);
        mapSettingCache = new MapSettingCache(mapSettingCapacity, mapSettingExpire);
        return 0;
    }

    public void onNotifyRenewalFailed(long ugcId, long uid) {
        LOGGER.error("onNotifyRenewalFailed ugcid {} uid {}", ugcId, uid);
        //removeByUid(uid);
        dataStoreCache.remove(new NKPair<>(ugcId, uid));
    }

        // 获得玩家锁
    public boolean acquireLock(long ugcId, long uid) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("acquireLock ugcId {} uid {}", ugcId, uid);
        }
        return UgcDataStoreEngine.getSpecInstance().getCacheLockAgent().acquireLock(
                DistributedLockType.DISTRIBUTED_LOCK_TYPE_DATASTORE_PLAYER_CACHE, ugcId, uid);
    }

    // 释放玩家锁
    public CacheLockErrorCode releaseLock(long ugcId, long uid, CacheLockAgent.ReleaseLockReason reason) {
        LOGGER.info("releaseLock ugcId {} uid {}", ugcId, uid);
        CacheLockErrorCode releaseRet = CacheLockErrorCode.ERR_UNKONW;
        try {
            releaseRet = UgcDataStoreEngine.getSpecInstance().getCacheLockAgent().releaseLock(
                    DistributedLockType.DISTRIBUTED_LOCK_TYPE_DATASTORE_PLAYER_CACHE, ugcId, uid,  reason, 1);
            if(CacheLockErrorCode.ERR_OK != releaseRet){
                LOGGER.error("onNotifyReleaseLock failed! uid[{}] releaseRet[{}] reason[{}]",
                        uid, releaseRet.getValue(), reason);
            }
        } catch (Exception e) {
            LOGGER.error("exception: ", e);
        }

        return releaseRet;
    }

    public boolean isOfflining() {
        return ServerEngine.getInstance().isOfflining();
    }

    public void executorLocalProcess() {

        int dataStoreTickInternal = DataStoreConfMgr.getInstance().getTickInternal();
        cacheAgent.internalStatic.tick();
        if (Framework.currentTimeMillis() - this.lastTickPlayerTime >= dataStoreTickInternal) {
            tickPlayer();
            this.lastTickPlayerTime = Framework.currentTimeMillis();
        }
        // 10 秒收集一次在线
        if (Framework.currentTimeMillis() - this.lastReportCacheSizeTime >= 10 * DateUtils.ONE_SECOND_MILLIS) {
            LOGGER.info("report online datastore size {} percent {}",
                dataStoreCache.getSize(), dataStoreCache.getSize() * 100 / DataStoreConfMgr.getInstance().getPlayerCacheCapacity());
            Monitor.getInstance().set.total(MonitorId.attr_current_datastore_online,
                    dataStoreCache.getSize());
            Monitor.getInstance().set.total(MonitorId.attr_current_datastore_online_percent,
                    dataStoreCache.getSize() * 100 / DataStoreConfMgr.getInstance().getPlayerCacheCapacity());
            Monitor.getInstance().set.total(MonitorId.attr_current_datastore_mapsetting,
                    mapSettingCache.getSize());

            // 统计在线的缓存包体大小
            Collection<DataStoreNode> allData = dataStoreCache.getAllData();
            // 每轮都初始化一下
            initSerialSizeStaticsMap();
            for (DataStoreNode dataStoreNode : allData) {
                String serialSizeStr = getPackSizeStr(dataStoreNode.getLastCheckSerialSize());
                if (!serialSizeStaticsMap.containsKey(serialSizeStr)) {
                    serialSizeStaticsMap.put(serialSizeStr, 0);
                }
                serialSizeStaticsMap.put(serialSizeStr, serialSizeStaticsMap.get(serialSizeStr) + 1);
            }
            for (Map.Entry<String, Integer> entry : serialSizeStaticsMap.entrySet()) {
                LOGGER.info("serialSizeStaticsMap: " + entry.getKey() + " " + entry.getValue());
                Monitor.getInstance().set.total(MonitorId.attr_current_datastore_online_packsize, entry.getValue(),
                        new String[]{entry.getKey()});
            }

            this.lastReportCacheSizeTime = Framework.currentTimeMillis();
        }
    }

    public String getPackSizeStr(int serializedSize) {
        int lowerBound = (serializedSize / 10240) * 10;
        int upperBound = lowerBound + 10;
        return lowerBound + "k-" + upperBound + "k";
    }

    public void removeCache(long ugcId, long uid) {
        dataStoreCache.remove(new NKPair<>(ugcId, uid));
    }

    private void tickPlayer() {
        Collection<NKPair<Long, Long >> allKeys = dataStoreCache.getAllKeys();
        if (tickKeyQueue.isEmpty()) {
            tickKeyQueue.addAll(allKeys);
        }

        int maxHandleCnt = DataStoreConfMgr.getInstance().getMaxHandleCntEveryTick();
        int handleCount = Math.min(maxHandleCnt, tickKeyQueue.size());
        for (int i = 0; i < handleCount; i++){
            NKPair<Long, Long> key = tickKeyQueue.poll();
            if (key == null) {
                continue;
            }

            // 基于ugcId+uid获取对应的数据节点
            DataStoreNode tempDataStoreNode = dataStoreCache.getCacheNodeIfPresent(key.getKey(), key.getValue());
            if (tempDataStoreNode == null) {
                continue;
            }

            // 前置判断一些处理定时处理的条件, 若不符合这些条件, 就直接跳过, 无需添加到用户队列中排队执行
            if (!(tempDataStoreNode.isNeedWriteBackDB() || (tempDataStoreNode.isDirty() && isOfflining()) ||
                    tempDataStoreNode.isNeedRemove() || isOfflining())) {
                continue;
            }

            // 若节点已经处于处理队列中, 则跳过
            if (tempDataStoreNode.isInTickUserQueueProcess()) {
                continue;
            }

            // 设置节点正在处理中标记
            tempDataStoreNode.setIsInTickUserQueueProcess(true);

            try {
                // 排队执行 免得乱来
                CurrentExecutorUtil.runJobSequentialByKey(key.getValue(), LocalServiceType.LOCAL_UGCDATASTORE_STORE_SERVICE, () -> {
                     DataStoreNode dataStoreNode = dataStoreCache.getCacheNodeIfPresent(key.getKey(), key.getValue());
                     if (dataStoreNode == null) {
                         LOGGER.debug("getIfPresent key  ugcId {} uid {} is not exist", key.getKey(), key.getValue());
                         return null;
                     }

                     try {
                         // 若需要写回db 或者 数据脏了并且服务正在下线中, 则直接写回db
                         if (dataStoreNode.isNeedWriteBackDB() || (dataStoreNode.isDirty() && isOfflining())) {
                             dataStoreNode.writeBackDB();
                         }

                         // 如果需要移除 或者  服务在下线中  就直接移除
                         if (dataStoreNode.isNeedRemove() || isOfflining()) {
                             //LOGGER.info("runJob start key  ugcId {} uid {} currnetugcid {}", key.getKey(), key.getValue(), dataStoreCache.getUgcIdByUid(key.getValue()));
                             LOGGER.info("remove key  ugcId {} uid {} is need remove", dataStoreNode.GetKey().getKey(), dataStoreNode.GetKey().getValue());
                             // 删除后 释放锁
                             dataStoreCache.remove(dataStoreNode.GetKey());
                             // 释放锁
                             CacheLockErrorCode releaseRet = DataStoreMgr.getInstance().releaseLock(dataStoreNode.GetKey().getKey(), dataStoreNode.GetKey().getValue(),
                                     ReleaseLockReason.Quit);
                             if (releaseRet != CacheLockErrorCode.ERR_OK) {
                                 LOGGER.error("tickAllPlayerPerSecond releaseLock failed ugcId {} uid {} releaseRet {}", dataStoreNode.GetKey().getKey(), dataStoreNode.GetKey().getValue(), releaseRet.getValue());
                             }
                             //LOGGER.info("runJob end key  ugcId {} uid {} currnetugcid {}", key.getKey(), key.getValue(), dataStoreCache.getUgcIdByUid(key.getValue()));
                         }
                     } catch (Exception ex) {
                         LOGGER.error("tickDataStorePlayer catch exception, ugcId:{}, uid:{}, ",
                                 key.getKey(), key.getValue(), ex);
                     } finally {
                         if (dataStoreNode != null) {
                             // 清理节点正在处理中标记
                             dataStoreNode.setIsInTickUserQueueProcess(false);
                         }
                     }

                     return null;
                }, "tickDataStorePlayer", false);
            } catch (Exception e) {
                LOGGER.error("tickAllPlayerPerSecond error ugcId {} uid {} error {}", key.getKey(), key.getValue(), e.getMessage());

                if (tempDataStoreNode != null) {
                    // 清理节点正在处理中标记
                    tempDataStoreNode.setIsInTickUserQueueProcess(false);
                }
            }
        }
    }

    public DataStoreNode getCacheNode( long ugcId, long uid) {
        //LOGGER.info("getCacheNode start ugcId {} uid {}", ugcId, uid);
        // 服务如果正在下线的话 不允许再操作从db中加载数据 cache中有就给 没有就不给了
        if (ServerEngine.getInstance().isOfflining()) {
            LOGGER.info("server is offlining ugcId {}, uid {}", ugcId, uid);
            return dataStoreCache.getCacheNodeIfPresent(ugcId, uid);
        }
        // 统一这里请求加锁
        if (!acquireLock(ugcId, uid)) {
            LOGGER.error("acquireLock failed uid {}", uid);
            return null;
        }
        DataStoreNode dataStoreNode = dataStoreCache.getCacheNode(ugcId, uid);
        //LOGGER.info("getCacheNode end ugcId {} uid {}", ugcId, uid);
        return dataStoreNode;
    }

    public int getCacheNodeNumByUid(long uid) {
        return dataStoreCache.getUgcIdNumByUid(uid);
    }

    public UgcPlayerDataStoreData getPlayerData( long ugcId, long uid) {
        DataStoreNode dataStore = getCacheNode(ugcId, uid);
        if (dataStore == null) {
            return null;
        }
        return dataStore.getDataStoreData();
    }

    public long getCacheSize() {
        return dataStoreCache.getSize();
    }

    public boolean isMapSingleSave(long mapId) {
        return mapSettingCache.isMapSingleSave(mapId);
    }

    public void initSerialSizeStaticsMap() {
        serialSizeStaticsMap = new HashMap<>();
        //for (int i = 0; i < 20; i++) {
        //    serialSizeStaticsMap.put(i*10 + "k-" + (i*10 + 10) + "k", 0);
        //}
    }
}
