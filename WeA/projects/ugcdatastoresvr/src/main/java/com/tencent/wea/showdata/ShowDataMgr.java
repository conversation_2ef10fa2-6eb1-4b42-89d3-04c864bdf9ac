package com.tencent.wea.showdata;

import com.tencent.cachelock.CacheLockAgent;
import com.tencent.cachelock.CacheLockAgent.ReleaseLockReason;
import com.tencent.cachelock.CacheLockDBDriver;
import com.tencent.cachelock.CacheLockErrorCode;
import com.tencent.cachelock.CacheRestoreAble;
import com.tencent.distributedLock.DistributedLockType;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.util.NKPair;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.protocol.common.LocalServiceType;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ShowDataMgr extends CacheRestoreAble {

    private static final Logger LOGGER = LogManager.getLogger(ShowDataMgr.class);

    private final Queue<NKPair<Long, Long>> tickKeyQueue = new ConcurrentLinkedQueue<>();
    private ShowDataCache showDataCache;
    private long lastTick = Framework.currentTimeMillis();
    private long lastReport = Framework.currentTimeMillis();
    private CacheLockAgent cacheLockAgent = null;

    private void setLastTick() {
        this.lastTick = Framework.currentTimeMillis();
    }

    private long getLastTick() {
        return this.lastTick;
    }

    private void setLastReport() {
        this.lastReport = Framework.currentTimeMillis();
    }

    private long getLastReport() {
        return this.lastReport;
    }

    public int init() {
        int cachelockValidTime = PropertyFileReader.getRealTimeIntItem("ugcDataStoreMinCachelockValidTime",
                CacheLockAgent.MIN_CACHELOCK_CACHE_VALID_TIME);
        int cachelockDBValidTime = PropertyFileReader.getRealTimeIntItem("ugcDataStoreMinCachelockDBValidTime",
                CacheLockAgent.MIN_CACHELOCK_DB_VALID_TIME);
        cacheLockAgent = new CacheLockAgent(com.tencent.wea.rpc.service.UgcdatastoreService.get(),
                CacheLockDBDriver.getInstance(), true, false, cachelockValidTime, cachelockDBValidTime);

        boolean res = initAgent(cacheLockAgent, DistributedLockType.DISTRIBUTED_LOCK_TYPE_SHOW_DATASTORE_PLAYER_CACHE);
        if (!res) {
            LOGGER.error("CacheRestoreAble.initAgent failed!");
            return -1;
        }

        int playerCapacity = ShowDataConfMgr.getInstance().getPlayerCacheCapacity();
        LOGGER.info("showDataCache construct capacity:{}", playerCapacity);
        showDataCache = new ShowDataCache(playerCapacity);

        return 0;
    }

    public void onNotifyRenewalFailed(long ugcId, long uid) {
        LOGGER.error("onNotifyRenewalFailed ugcid {} uid {}", ugcId, uid);
        //removeByUid(uid);
        showDataCache.remove(new NKPair<>(ugcId, uid));
    }

    // 获得玩家锁
    public boolean acquireLock(long ugcId, long uid) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("acquireLock ugcId {} uid {}", ugcId, uid);
        }
        return cacheLockAgent.acquireLock(
                DistributedLockType.DISTRIBUTED_LOCK_TYPE_SHOW_DATASTORE_PLAYER_CACHE, ugcId, uid);
    }

    // 释放玩家锁
    public CacheLockErrorCode releaseLock(long ugcId, long uid, CacheLockAgent.ReleaseLockReason reason) {
        LOGGER.info("releaseLock ugcId {} uid {}", ugcId, uid);
        CacheLockErrorCode releaseRet = CacheLockErrorCode.ERR_UNKONW;
        try {
            releaseRet = cacheLockAgent.releaseLock(
                    DistributedLockType.DISTRIBUTED_LOCK_TYPE_SHOW_DATASTORE_PLAYER_CACHE, ugcId, uid, reason, 1);
            if (CacheLockErrorCode.ERR_OK != releaseRet) {
                LOGGER.error("onNotifyReleaseLock failed! uid[{}] releaseRet[{}] reason[{}]",
                        uid, releaseRet.getValue(), reason);
            } else {
                // 锁释放了 处理下监控
                if (reason == CacheLockAgent.ReleaseLockReason.Preempt) {
                    Monitor.getInstance().add.total(MonitorId.attr_ugcdatastoresvr_show_data_cache_move, 1);
                }
            }
        } catch (Exception e) {
            LOGGER.error("exception: ", e);
        }

        return releaseRet;
    }

    public boolean isOfflining() {
        return ServerEngine.getInstance().isOfflining();
    }

    public void executorLocalProcess() {
        // 一致性缓存锁心跳
        cacheAgent.internalStatic.tick();
        // 处理tick逻辑
        if (ShowDataConfMgr.getInstance().canTryTickCache(getLastTick())) {
            setLastTick();
            tickCache();
        }
        // 10 秒收集一次在线
        if (Framework.currentTimeMillis() - getLastReport() >= 10 * DateUtils.ONE_SECOND_MILLIS) {
            setLastReport();
            // 打上监控
            long cacheSize = showDataCache.getSize();
            long cacheCapacityPercent =
                    showDataCache.getSize() * 100 / ShowDataConfMgr.getInstance().getPlayerCacheCapacity();
            LOGGER.info("report showdata size {} percent {}", cacheSize, cacheCapacityPercent);
            Monitor.getInstance().set.total(MonitorId.attr_ugcdatastoresvr_show_data_cache_size, cacheSize);
            Monitor.getInstance().set.total(MonitorId.attr_ugcdatastoresvr_show_data_cache_percent,
                    cacheCapacityPercent);
        }
    }

    public void removeCache(long ugcId, long uid) {
        showDataCache.remove(new NKPair<>(ugcId, uid));
    }

    private void tickCache() {
        if (tickKeyQueue.isEmpty()) {
            tickKeyQueue.addAll(showDataCache.getAllKeys());
        }
        int eventCnt = Math.min(ShowDataConfMgr.getInstance().getEveryTickEventSize(), tickKeyQueue.size());
        for (int i = 0; i < eventCnt; i++) {
            NKPair<Long, Long> key = tickKeyQueue.poll();
            if (key == null) {
                continue;
            }

            // 基于ugcId+uid获取对应的数据节点
            ShowDataNode tempDataStoreNode = showDataCache.getCacheNodeIfPresent(key.getKey(), key.getValue());
            if (tempDataStoreNode == null) {
                continue;
            }

            // 前置判断一些处理定时处理的条件, 若不符合这些条件, 就直接跳过, 无需添加到用户队列中排队执行
            if (!(tempDataStoreNode.isNeedFlush() || isOfflining() || tempDataStoreNode.isNeedRemove())) {
                continue;
            }

            // 若节点已经处于处理队列中, 则跳过
            if (tempDataStoreNode.isInTickUserQueueProcess()) {
                continue;
            }

            // 设置节点正在处理中标记
            tempDataStoreNode.setIsInTickUserQueueProcess(true);

            try {
                // 排队执行 免得乱来
                CurrentExecutorUtil.runJobSequentialByKey(key.getValue(),
                        LocalServiceType.LOCAL_UGCDATASTORE_STORE_SERVICE, () -> {
                            ShowDataNode dataStoreNode =
                                    showDataCache.getCacheNodeIfPresent(key.getKey(), key.getValue());
                            if (dataStoreNode == null) {
                                LOGGER.debug("getIfPresent key  ugcId {} uid {} is not exist", key.getKey(),
                                        key.getValue());
                                return null;
                            }

                            try {
                                if (dataStoreNode.isNeedFlush()) {
                                    dataStoreNode.flush();
                                }

                                //如果是下线中 就不用 isNeedWriteBackDB 这个判断了
                                if (isOfflining()) {
                                    dataStoreNode.tryFlush();
                                }

                                // 如果需要移除 或者  服务在下线中  就直接移除
                                if (dataStoreNode.isNeedRemove() || isOfflining()) {
                                    // 删除后 释放锁
                                    showDataCache.remove(dataStoreNode.GetKey());
                                    // 释放锁
                                    CacheLockErrorCode releaseRet = ShowDataMgr.getInstance()
                                            .releaseLock(dataStoreNode.GetKey().getKey(), dataStoreNode.GetKey().getValue(),
                                                    ReleaseLockReason.Quit);
                                    if (releaseRet != CacheLockErrorCode.ERR_OK) {
                                        LOGGER.error(
                                                "tickAllPlayerPerSecond releaseLock failed ugcId {} uid {} releaseRet {}",
                                                dataStoreNode.GetKey().getKey(), dataStoreNode.GetKey().getValue(),
                                                releaseRet.getValue());
                                    }
                                }
                            } catch (Exception ex) {
                                LOGGER.error("tickDataStorePlayer catch exception, ugcId:{}, uid:{}, ",
                                        key.getKey(), key.getValue(), ex);
                            } finally {
                                if (dataStoreNode != null) {
                                    // 清理节点正在处理中标记
                                    dataStoreNode.setIsInTickUserQueueProcess(false);
                                }
                            }

                            return null;
                        }, "tickDataStorePlayer", false);
            } catch (Exception e) {
                LOGGER.error("tickCache error ugcId {} uid {} error {}", key.getKey(), key.getValue(),
                        e.getMessage());

                if (tempDataStoreNode != null) {
                    // 清理节点正在处理中标记
                    tempDataStoreNode.setIsInTickUserQueueProcess(false);
                }
            }
        }
    }

    public ShowDataNode getCacheNode(long ugcId, long uid) {
        //LOGGER.info("getCacheNode start ugcId {} uid {}", ugcId, uid);
        // 服务如果正在下线的话 不允许再操作从db中加载数据 cache中有就给 没有就不给了
        if (ServerEngine.getInstance().isOfflining()) {
            LOGGER.info("server is offlining ugcId {}, uid {}", ugcId, uid);
            return showDataCache.getCacheNodeIfPresent(ugcId, uid);
        }
        // 统一这里请求加锁
        if (!acquireLock(ugcId, uid)) {
            LOGGER.error("acquireLock failed uid {}", uid);
            return null;
        }
        ShowDataNode dataStoreNode = showDataCache.getCacheNode(ugcId, uid);
        //LOGGER.info("getCacheNode end ugcId {} uid {}", ugcId, uid);
        return dataStoreNode;
    }

    public ShowDataNode getCacheNodeIfPresent(long ugcId, long uid) {
        return showDataCache.getCacheNodeIfPresent(ugcId, uid);
    }

    public long getCacheSize() {
        return showDataCache.getSize();
    }

    public static ShowDataMgr getInstance() {
        return ShowDataMgr.InstanceHolder.instance;
    }

    public static class InstanceHolder {

        public static ShowDataMgr instance = new ShowDataMgr();
    }

}
