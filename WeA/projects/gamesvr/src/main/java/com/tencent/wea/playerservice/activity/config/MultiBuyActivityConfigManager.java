package com.tencent.wea.playerservice.activity.config;

import com.tencent.wea.xlsRes.ResMultiBuyActivity.*;
import com.tencent.wea.common.NKErrorCode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 多买多送活动配置管理器
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public class MultiBuyActivityConfigManager {
    
    private static final Logger LOGGER = LogManager.getLogger(MultiBuyActivityConfigManager.class);
    
    private static final MultiBuyActivityConfigManager INSTANCE = new MultiBuyActivityConfigManager();
    
    // 活动统计数据
    private final Map<Integer, ActivityStatistics> statisticsMap = new ConcurrentHashMap<>();
    
    // 活动状态控制
    private final Map<Integer, Boolean> activityStatusMap = new ConcurrentHashMap<>();
    
    private MultiBuyActivityConfigManager() {
    }
    
    public static MultiBuyActivityConfigManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * 检查活动是否启用
     */
    public boolean isActivityEnabled(int activityId) {
        return activityStatusMap.getOrDefault(activityId, true);
    }
    
    /**
     * 设置活动状态
     */
    public void setActivityStatus(int activityId, boolean isEnabled) {
        activityStatusMap.put(activityId, isEnabled);
        LOGGER.info("Activity status changed, activityId:{}, enabled:{}", activityId, isEnabled);
    }
    
    /**
     * 获取活动统计数据
     */
    public ActivityStatistics getActivityStatistics(int activityId) {
        return statisticsMap.computeIfAbsent(activityId, id -> new ActivityStatistics(id));
    }
    
    /**
     * 记录购买统计
     */
    public void recordPurchase(int activityId, long uid, int commodityId, int originalPrice, 
                              int finalPrice, int discountId) {
        ActivityStatistics stats = getActivityStatistics(activityId);
        stats.recordPurchase(uid, commodityId, originalPrice, finalPrice, discountId);
    }
    
    /**
     * 记录双倍折扣解锁
     */
    public void recordDoubleDiscountUnlock(int activityId, long uid) {
        ActivityStatistics stats = getActivityStatistics(activityId);
        stats.recordDoubleDiscountUnlock(uid);
    }
    
    /**
     * 验证活动配置
     */
    public boolean validateActivityConfig(MultiBuyActivityConf config) {
        if (config == null) {
            return false;
        }
        
        // 检查基本配置
        if (config.getActivityId() <= 0 || config.getDailyBuyThreshold() <= 0) {
            LOGGER.error("Invalid activity config, activityId:{}, threshold:{}", 
                        config.getActivityId(), config.getDailyBuyThreshold());
            return false;
        }
        
        // 检查商品列表
        if (config.getCommodityIdsList().isEmpty()) {
            LOGGER.error("Empty commodity list for activity:{}", config.getActivityId());
            return false;
        }
        
        // 检查折扣池
        if (config.getDiscountPoolIdsList().isEmpty()) {
            LOGGER.error("Empty discount pool for activity:{}", config.getActivityId());
            return false;
        }
        
        // 验证折扣池配置
        for (int poolId : config.getDiscountPoolIdsList()) {
            MultiBuyDiscountPoolConf poolConf = MultiBuyDiscountPoolConfData.getInstance().get(poolId);
            if (poolConf == null) {
                LOGGER.error("Discount pool config not found, activityId:{}, poolId:{}", 
                            config.getActivityId(), poolId);
                return false;
            }
            
            if (poolConf.getDiscountValue() <= 0 || poolConf.getDiscountValue() > 100) {
                LOGGER.error("Invalid discount value, activityId:{}, poolId:{}, value:{}", 
                            config.getActivityId(), poolId, poolConf.getDiscountValue());
                return false;
            }
            
            if (poolConf.getWeight() <= 0) {
                LOGGER.error("Invalid weight, activityId:{}, poolId:{}, weight:{}", 
                            config.getActivityId(), poolId, poolConf.getWeight());
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 活动统计数据
     */
    public static class ActivityStatistics {
        private final int activityId;
        private final AtomicLong totalParticipants = new AtomicLong(0);
        private final AtomicLong totalPurchases = new AtomicLong(0);
        private final AtomicLong totalDiscountAmount = new AtomicLong(0);
        private final AtomicLong doubleDiscountUnlockCount = new AtomicLong(0);
        private final Map<Integer, AtomicLong> discountUsageCount = new ConcurrentHashMap<>();
        private final Map<Integer, AtomicLong> commodityPurchaseCount = new ConcurrentHashMap<>();
        private final Set<Long> participantUids = ConcurrentHashMap.newKeySet();
        
        public ActivityStatistics(int activityId) {
            this.activityId = activityId;
        }
        
        public void recordPurchase(long uid, int commodityId, int originalPrice, int finalPrice, int discountId) {
            participantUids.add(uid);
            totalParticipants.set(participantUids.size());
            totalPurchases.incrementAndGet();
            totalDiscountAmount.addAndGet(originalPrice - finalPrice);
            
            discountUsageCount.computeIfAbsent(discountId, k -> new AtomicLong(0)).incrementAndGet();
            commodityPurchaseCount.computeIfAbsent(commodityId, k -> new AtomicLong(0)).incrementAndGet();
        }
        
        public void recordDoubleDiscountUnlock(long uid) {
            doubleDiscountUnlockCount.incrementAndGet();
        }
        
        // Getters
        public int getActivityId() { return activityId; }
        public long getTotalParticipants() { return totalParticipants.get(); }
        public long getTotalPurchases() { return totalPurchases.get(); }
        public long getTotalDiscountAmount() { return totalDiscountAmount.get(); }
        public long getDoubleDiscountUnlockCount() { return doubleDiscountUnlockCount.get(); }
        public Map<Integer, AtomicLong> getDiscountUsageCount() { return discountUsageCount; }
        public Map<Integer, AtomicLong> getCommodityPurchaseCount() { return commodityPurchaseCount; }
        
        /**
         * 获取统计摘要
         */
        public String getStatisticsSummary() {
            return String.format(
                "Activity[%d] - Participants:%d, Purchases:%d, TotalDiscount:%d, DoubleDiscountUnlocks:%d",
                activityId, getTotalParticipants(), getTotalPurchases(), 
                getTotalDiscountAmount(), getDoubleDiscountUnlockCount()
            );
        }
    }
    
    /**
     * 获取所有活动统计摘要
     */
    public Map<Integer, String> getAllActivityStatisticsSummary() {
        Map<Integer, String> summaryMap = new HashMap<>();
        for (Map.Entry<Integer, ActivityStatistics> entry : statisticsMap.entrySet()) {
            summaryMap.put(entry.getKey(), entry.getValue().getStatisticsSummary());
        }
        return summaryMap;
    }
    
    /**
     * 重置活动统计数据
     */
    public void resetActivityStatistics(int activityId) {
        statisticsMap.remove(activityId);
        LOGGER.info("Reset activity statistics, activityId:{}", activityId);
    }
    
    /**
     * 获取活动热度排行（按参与人数）
     */
    public List<ActivityStatistics> getActivityRankingByParticipants(int topN) {
        return statisticsMap.values().stream()
                .sorted((a, b) -> Long.compare(b.getTotalParticipants(), a.getTotalParticipants()))
                .limit(topN)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取折扣使用效率统计
     */
    public Map<Integer, Double> getDiscountEfficiencyStats(int activityId) {
        ActivityStatistics stats = getActivityStatistics(activityId);
        Map<Integer, Double> efficiencyMap = new HashMap<>();
        
        long totalUsage = stats.getDiscountUsageCount().values().stream()
                .mapToLong(AtomicLong::get)
                .sum();
        
        if (totalUsage > 0) {
            for (Map.Entry<Integer, AtomicLong> entry : stats.getDiscountUsageCount().entrySet()) {
                double efficiency = (double) entry.getValue().get() / totalUsage * 100;
                efficiencyMap.put(entry.getKey(), efficiency);
            }
        }
        
        return efficiencyMap;
    }
}
