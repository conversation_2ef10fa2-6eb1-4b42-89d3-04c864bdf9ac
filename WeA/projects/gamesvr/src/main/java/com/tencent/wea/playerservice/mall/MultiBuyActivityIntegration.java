package com.tencent.wea.playerservice.mall;

import com.tencent.wea.playerservice.activity.implement.MultiBuyMultiGiftActivity;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResMultiBuyActivity.MultiBuyDiscountResult;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.common.NKErrorCode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 多买多送活动与商城系统集成
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public class MultiBuyActivityIntegration {
    
    private static final Logger LOGGER = LogManager.getLogger(MultiBuyActivityIntegration.class);
    
    private final Player player;
    
    public MultiBuyActivityIntegration(Player player) {
        this.player = player;
    }
    
    /**
     * 获取多买多送活动实例
     */
    public MultiBuyMultiGiftActivity getMultiBuyActivity() {
        return (MultiBuyMultiGiftActivity) player.getActivityManager()
                .getRunningActivityByActivityType(ActivityType.AT_MultiBuyMultiGift);
    }
    
    /**
     * 检查商品是否参与多买多送活动
     */
    public boolean isMultiBuyActivityCommodity(int commodityId) {
        MultiBuyMultiGiftActivity activity = getMultiBuyActivity();
        return activity != null && activity.isActivityCommodity(commodityId);
    }
    
    /**
     * 计算活动折扣价格
     */
    public ActivityDiscountInfo calculateActivityDiscount(int commodityId, int originalPrice) {
        MultiBuyMultiGiftActivity activity = getMultiBuyActivity();
        if (activity == null || !activity.isActivityCommodity(commodityId)) {
            return new ActivityDiscountInfo(originalPrice, originalPrice, false, 0, 0);
        }
        
        try {
            MultiBuyDiscountResult discountResult = activity.calculateDiscount(commodityId);
            int finalPrice = (originalPrice * discountResult.getFinalDiscount()) / 100;
            int savedAmount = originalPrice - finalPrice;
            
            LOGGER.debug("Calculated activity discount, uid:{}, commodityId:{}, originalPrice:{}, " +
                        "finalPrice:{}, discount:{}, hasDoubleDiscount:{}", 
                        player.getUid(), commodityId, originalPrice, finalPrice, 
                        discountResult.getFinalDiscount(), discountResult.getHasDoubleDiscount());
            
            return new ActivityDiscountInfo(
                originalPrice, 
                finalPrice, 
                true, 
                discountResult.getFinalDiscount(),
                savedAmount
            );
            
        } catch (Exception e) {
            LOGGER.error("Failed to calculate activity discount, uid:{}, commodityId:{}", 
                        player.getUid(), commodityId, e);
            return new ActivityDiscountInfo(originalPrice, originalPrice, false, 0, 0);
        }
    }
    
    /**
     * 通知活动商品购买成功
     */
    public void notifyActivityPurchase(int commodityId, int buyNum, int finalPrice) {
        MultiBuyMultiGiftActivity activity = getMultiBuyActivity();
        if (activity != null && activity.isActivityCommodity(commodityId)) {
            // 活动会通过事件监听自动处理，这里可以做额外的处理
            LOGGER.debug("Notified activity purchase, uid:{}, commodityId:{}, buyNum:{}, finalPrice:{}", 
                        player.getUid(), commodityId, buyNum, finalPrice);
        }
    }
    
    /**
     * 活动折扣信息
     */
    public static class ActivityDiscountInfo {
        private final int originalPrice;
        private final int finalPrice;
        private final boolean hasDiscount;
        private final int discountValue;
        private final int savedAmount;
        
        public ActivityDiscountInfo(int originalPrice, int finalPrice, boolean hasDiscount, 
                                  int discountValue, int savedAmount) {
            this.originalPrice = originalPrice;
            this.finalPrice = finalPrice;
            this.hasDiscount = hasDiscount;
            this.discountValue = discountValue;
            this.savedAmount = savedAmount;
        }
        
        public int getOriginalPrice() { return originalPrice; }
        public int getFinalPrice() { return finalPrice; }
        public boolean hasDiscount() { return hasDiscount; }
        public int getDiscountValue() { return discountValue; }
        public int getSavedAmount() { return savedAmount; }
    }
    
    /**
     * 预览折扣信息（不实际消耗折扣）
     */
    public ActivityDiscountPreview previewDiscount(int commodityId, int originalPrice) {
        MultiBuyMultiGiftActivity activity = getMultiBuyActivity();
        if (activity == null || !activity.isActivityCommodity(commodityId)) {
            return new ActivityDiscountPreview(originalPrice, originalPrice, false, 100, 0);
        }
        
        // 获取预览折扣（不消耗）
        int previewDiscount = activity.previewNextDiscount();
        boolean willUseDoubleDiscount = activity.getActivityData().getHasDoubleDiscount() && 
                                       !activity.getActivityData().getDoubleDiscountUsedToday();
        
        int finalDiscount = willUseDoubleDiscount ? 
                           (previewDiscount * previewDiscount) / 100 : previewDiscount;
        int previewPrice = (originalPrice * finalDiscount) / 100;
        int estimatedSaving = originalPrice - previewPrice;
        
        return new ActivityDiscountPreview(
            originalPrice, 
            previewPrice, 
            willUseDoubleDiscount, 
            finalDiscount, 
            estimatedSaving
        );
    }
    
    /**
     * 活动折扣预览信息
     */
    public static class ActivityDiscountPreview {
        private final int originalPrice;
        private final int previewPrice;
        private final boolean willUseDoubleDiscount;
        private final int previewDiscount;
        private final int estimatedSaving;
        
        public ActivityDiscountPreview(int originalPrice, int previewPrice, boolean willUseDoubleDiscount,
                                     int previewDiscount, int estimatedSaving) {
            this.originalPrice = originalPrice;
            this.previewPrice = previewPrice;
            this.willUseDoubleDiscount = willUseDoubleDiscount;
            this.previewDiscount = previewDiscount;
            this.estimatedSaving = estimatedSaving;
        }
        
        public int getOriginalPrice() { return originalPrice; }
        public int getPreviewPrice() { return previewPrice; }
        public boolean willUseDoubleDiscount() { return willUseDoubleDiscount; }
        public int getPreviewDiscount() { return previewDiscount; }
        public int getEstimatedSaving() { return estimatedSaving; }
    }
}
