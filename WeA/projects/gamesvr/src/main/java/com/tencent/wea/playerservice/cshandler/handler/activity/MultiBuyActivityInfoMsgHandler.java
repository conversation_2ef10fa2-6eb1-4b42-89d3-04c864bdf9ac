package com.tencent.wea.playerservice.cshandler.handler.activity;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.activity.implement.MultiBuyMultiGiftActivity;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsMultiBuyActivity;
import com.tencent.wea.xlsRes.ResMultiBuyActivity.ActivityCommodityInfo;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * 获取多买多送活动信息协议处理器
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public class MultiBuyActivityInfoMsgHandler extends AbstractGsClientRequestHandler {
    
    private static final Logger LOGGER = LogManager.getLogger(MultiBuyActivityInfoMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        CsMultiBuyActivity.MultiBuyActivityInfo_C2S_Msg reqMsg = 
            (CsMultiBuyActivity.MultiBuyActivityInfo_C2S_Msg) request;
        
        LOGGER.debug("MultiBuyActivityInfo request, uid:{}, activityId:{}", 
                    player.getUid(), reqMsg.getActivityId());
        
        CsMultiBuyActivity.MultiBuyActivityInfo_S2C_Msg.Builder rspMsg = 
            CsMultiBuyActivity.MultiBuyActivityInfo_S2C_Msg.newBuilder();
        
        // 获取活动实例
        MultiBuyMultiGiftActivity activity = (MultiBuyMultiGiftActivity) player.getActivityManager()
                .getRunningActivityByActivityType(ActivityType.AT_MultiBuyMultiGift);
        
        if (activity == null) {
            LOGGER.warn("MultiBuyMultiGiftActivity not found, uid:{}", player.getUid());
            rspMsg.setActivityId(reqMsg.getActivityId());
            return rspMsg;
        }
        
        try {
            // 检查每日重置
            activity.checkAndResetDaily();
            
            // 获取活动数据
            var activityData = activity.getActivityData();
            var activityConf = activity.getActivityConf();
            
            // 获取活动商品列表
            List<ActivityCommodityInfo> commodities = activity.getActivityCommodities();
            
            // 构建响应
            rspMsg.setActivityId(activity.getActivityId())
                  .setDailyBuyCount(activityData.getDailyBuyCount())
                  .setDailyBuyThreshold(activityConf.getDailyBuyThreshold())
                  .setHasDoubleDiscount(activityData.getHasDoubleDiscount())
                  .setDoubleDiscountUsedToday(activityData.getDoubleDiscountUsedToday())
                  .addAllCommodities(commodities)
                  .setTotalSavedMoney(activityData.getTotalSavedMoney())
                  .setRemainingDiscounts(activity.getRemainingDiscountCount());
            
            LOGGER.debug("MultiBuyActivityInfo response, uid:{}, dailyBuyCount:{}, hasDoubleDiscount:{}, commodityCount:{}", 
                        player.getUid(), activityData.getDailyBuyCount(), 
                        activityData.getHasDoubleDiscount(), commodities.size());
            
        } catch (Exception e) {
            LOGGER.error("Failed to handle MultiBuyActivityInfo, uid:{}", player.getUid(), e);
            rspMsg.setActivityId(reqMsg.getActivityId());
        }
        
        return rspMsg;
    }
}
