package com.tencent.wea.playerservice.cshandler.handler.activity;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.activity.implement.MultiBuyMultiGiftActivity;
import com.tencent.wea.playerservice.mall.MultiBuyActivityIntegration;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsMultiBuyActivity;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.common.NKErrorCode;
import com.tencent.wea.common.ItemChangeReason;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 多买多送活动购买协议处理器
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public class MultiBuyActivityBuyMsgHandler extends AbstractGsClientRequestHandler {
    
    private static final Logger LOGGER = LogManager.getLogger(MultiBuyActivityBuyMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        CsMultiBuyActivity.MultiBuyActivityBuy_C2S_Msg reqMsg = 
            (CsMultiBuyActivity.MultiBuyActivityBuy_C2S_Msg) request;
        
        LOGGER.info("MultiBuyActivityBuy request, uid:{}, activityId:{}, commodityId:{}, buyNum:{}", 
                   player.getUid(), reqMsg.getActivityId(), reqMsg.getCommodityId(), reqMsg.getBuyNum());
        
        CsMultiBuyActivity.MultiBuyActivityBuy_S2C_Msg.Builder rspMsg = 
            CsMultiBuyActivity.MultiBuyActivityBuy_S2C_Msg.newBuilder();
        
        try {
            // 获取活动实例
            MultiBuyMultiGiftActivity activity = (MultiBuyMultiGiftActivity) player.getActivityManager()
                    .getRunningActivityByActivityType(ActivityType.AT_MultiBuyMultiGift);
            
            if (activity == null) {
                LOGGER.warn("MultiBuyMultiGiftActivity not found, uid:{}", player.getUid());
                rspMsg.setResult(NKErrorCode.ActivityNotExist.getValue());
                return rspMsg;
            }
            
            // 检查是否为活动商品
            if (!activity.isActivityCommodity(reqMsg.getCommodityId())) {
                LOGGER.warn("Commodity not in activity, uid:{}, commodityId:{}", 
                           player.getUid(), reqMsg.getCommodityId());
                rspMsg.setResult(NKErrorCode.InvalidParams.getValue());
                return rspMsg;
            }
            
            // 获取商品配置
            MallCommodity commodityConf = MallCommodityConf.getInstance().get(reqMsg.getCommodityId());
            if (commodityConf == null) {
                LOGGER.error("Commodity config not found, uid:{}, commodityId:{}", 
                            player.getUid(), reqMsg.getCommodityId());
                rspMsg.setResult(NKErrorCode.ResNotFound.getValue());
                return rspMsg;
            }
            
            // 计算活动折扣
            MultiBuyActivityIntegration integration = new MultiBuyActivityIntegration(player);
            MultiBuyActivityIntegration.ActivityDiscountInfo discountInfo = 
                integration.calculateActivityDiscount(reqMsg.getCommodityId(), commodityConf.getPrice());
            
            // 记录购买前的状态
            var activityData = activity.getActivityData();
            int buyCountBefore = activityData.getDailyBuyCount();
            boolean hadDoubleDiscount = activityData.getHasDoubleDiscount();
            
            // 执行购买（使用活动折扣价格）
            int buyResult = player.getMallManager().MallCommodityBuy(
                reqMsg.getCommodityId(), 
                reqMsg.getBuyNum(), 
                discountInfo.getFinalPrice(), // 使用活动折扣价格
                false, // 非直购
                ItemChangeReason.ICR_MultiBuyActivity, 
                reqMsg.getActivityId(), 
                null
            );
            
            if (buyResult == 0) { // 购买成功
                // 更新活动数据中的节省金额
                int totalSaved = discountInfo.getSavedAmount() * reqMsg.getBuyNum();
                activityData.setTotalSavedMoney(activityData.getTotalSavedMoney() + totalSaved);
                
                // 检查是否解锁明日双倍折扣
                boolean unlockDoubleDiscount = false;
                var activityConf = activity.getActivityConf();
                if (activityConf.getEnableDoubleDiscount() && 
                    buyCountBefore < activityConf.getDailyBuyThreshold() &&
                    activityData.getDailyBuyCount() >= activityConf.getDailyBuyThreshold()) {
                    unlockDoubleDiscount = true;
                }
                
                // 构建成功响应
                rspMsg.setResult(0)
                      .setUsedDiscount(discountInfo.getDiscountValue())
                      .setFinalPrice(discountInfo.getFinalPrice() * reqMsg.getBuyNum())
                      .setUnlockDoubleDiscount(unlockDoubleDiscount)
                      .setSavedAmount(totalSaved);
                
                LOGGER.info("MultiBuyActivityBuy success, uid:{}, commodityId:{}, buyNum:{}, " +
                           "finalPrice:{}, savedAmount:{}, unlockDoubleDiscount:{}", 
                           player.getUid(), reqMsg.getCommodityId(), reqMsg.getBuyNum(), 
                           discountInfo.getFinalPrice() * reqMsg.getBuyNum(), totalSaved, unlockDoubleDiscount);
                
            } else {
                // 购买失败
                rspMsg.setResult(buyResult);
                LOGGER.warn("MultiBuyActivityBuy failed, uid:{}, commodityId:{}, buyResult:{}", 
                           player.getUid(), reqMsg.getCommodityId(), buyResult);
            }
            
        } catch (Exception e) {
            LOGGER.error("Failed to handle MultiBuyActivityBuy, uid:{}, commodityId:{}", 
                        player.getUid(), reqMsg.getCommodityId(), e);
            rspMsg.setResult(NKErrorCode.SystemError.getValue());
        }
        
        return rspMsg;
    }
}
