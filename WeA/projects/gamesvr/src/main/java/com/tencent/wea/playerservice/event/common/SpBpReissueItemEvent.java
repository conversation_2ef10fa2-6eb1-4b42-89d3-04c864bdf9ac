package com.tencent.wea.playerservice.event.common;

import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.common.SpBpEventData;
import com.tencent.wea.xlsRes.keywords.EventDataType;
import com.tencent.wea.xlsRes.keywords.EventType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class SpBpReissueItemEvent extends BasePlayerEvent {

    private static final Logger LOGGER = LogManager.getLogger(SpBpReissueItemEvent.class);

    public SpBpReissueItemEvent(Player player) {
        super(player);
    }

    public SpBpReissueItemEvent setEventData(SpBpEventData eventData) {
        setProtoData(EventDataType.EDT_SPBpEventData_VALUE, eventData);
        return this;
    }


    @Override
    public long getStatisticParam() {
        return 0;
    }

    @Override
    public long getStatisticData() {
        return 0;
    }

    @Override
    public int getEventType() {
        return EventType.ET_SPBusinessPlan.getNumber();
    }
}
