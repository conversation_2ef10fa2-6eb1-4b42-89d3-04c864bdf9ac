package com.tencent.wea.playerservice.activity.implement;

import com.google.protobuf.util.Timestamps;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.resourceloader.resclass.ActivityMainConfig;
import com.tencent.resourceloader.resclass.TaskConfData;
import com.tencent.resourceloader.resclass.TaskGroupData;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.ActivityUnit;
import com.tencent.wea.attr.TaskLifeTime;
import com.tencent.wea.outputcontrol.BaseOutputModule;
import com.tencent.wea.outputcontrol.OutputModuleUtil;
import com.tencent.wea.playerservice.outputcontrol.PlayerTaskOutputModule;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResActivity;
import com.tencent.wea.xlsRes.ResTask;
import com.tencent.wea.xlsRes.keywords.ActivityTimeType;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.GeneralOutputPeriodType;
import com.tencent.wea.xlsRes.keywords.TaskLoadType;
import com.tencent.wea.xlsRes.keywords.TaskType;

public class ReturningDiffersActivity extends TaskAndShopActivity {
    public ReturningDiffersActivity(Player player, ActivityUnit activityUnit) {
        super(player, activityUnit);
    }

    @Override
    public ActivityType getType() {
        return ActivityType.ATReturningDiffers;
    }

    // 回归玩家接第一个任务组， 常规玩家接第二个任务组
    @Override
    public void refreshActivityTask() {
        ResActivity.ActivityMainConfig cfg = ActivityMainConfig.getInstance().get(this.activityId);
        if (cfg == null) {
            NKErrorCode.ActivityMainConfigNotExist
                    .throwError("ActivityMainConfig {} not exist", this.activityId);
            return;
        }
        if (cfg.getActivityTaskGroupCount() < 2) {
            NKErrorCode.ActivityTaskNotExist
                    .throwError("ActivityMainConfig {} task group count < 2", this.activityId);
            return;
        }

        var currSec = Framework.currentTimeSec();
        if (player.getUserAttr().getPlayerPublicProfileInfo().getReturning() &&
                player.getUserAttr().getPlayerPublicProfileInfo().getReturnExpiredSec() > currSec) {
            // 用回流活动的时间
//            long beginTime = DateUtils.getDayBeginTimeSec(
//                    player.getUserAttr().getReturningInfo().getReturnActivity().getBeginTime());
//            long endTime = player.getUserAttr().getPlayerPublicProfileInfo().getReturnExpiredSec();
//            ResActivity.ActivityTimeData activityTimeData = ResActivity.ActivityTimeData.newBuilder()
//                    .setTimeType(ActivityTimeType.ATT_NORMAL)
//                    .setBeginTime(Timestamps.fromSeconds(beginTime))
//                    .setEndTime(Timestamps.fromSeconds(endTime))
//                    .setShowBeginTime(Timestamps.fromSeconds(beginTime))
//                    .setShowEndTime(Timestamps.fromSeconds(endTime))
//                    .build();
//            refreshTaskOfGroup(cfg.getActivityTaskGroup(0), activityTimeData);

            // 用常规活动时间，回流活动控制任务刷新
            refreshTaskOfGroup(cfg.getActivityTaskGroup(0), cfg.getTimeInfo());
            removeTaskGroup(cfg.getActivityTaskGroup(1));
        } else {
            refreshTaskOfGroup(cfg.getActivityTaskGroup(1), cfg.getTimeInfo());
            removeTaskGroup(cfg.getActivityTaskGroup(0));
        }
    }

    protected void removeTaskGroup(int taskGroupId) {
        ResActivity.ActivityMainConfig cfg = ActivityMainConfig.getInstance().get(this.activityId);
        if (cfg == null) {
            LOGGER.error("removeTaskGroup activity {} task group {} config not exist",
                    this.activityId, taskGroupId);
            return;
        }
        if (!cfg.getActivityTaskGroupList().contains(taskGroupId)) {
            LOGGER.error("removeTaskGroup activity {} doesnt own task group {}",
                    this.activityId, taskGroupId);
            return;
        }
        var taskGroupConfig = TaskGroupData.getInstance().getTaskGroup(taskGroupId);
        if (taskGroupConfig == null) {
            LOGGER.error("removeTaskGroup activity {} task group {} config not exist",
                    this.activityId, taskGroupId);
            return;
        }
        if (taskGroupConfig.getLoadType() != TaskLoadType.TaskLoadType_Activity) {
            LOGGER.error("removeTaskGroup activity {} task group {} is not activity load type",
                    activityId, taskGroupId);
            return;
        }

        var taskMgr = player.getTaskManager();

        boolean realRemoveTaskGroup = false;
        for (int taskId : taskGroupConfig.getTaskIdListList()) {
            ResTask.TaskConf taskConf = TaskConfData.getInstance().getTaskConf(taskId);
            if (taskConf == null) {
                LOGGER.error("removeTaskGroup activity {} task group {} task {} cfg not exist",
                        activityId, groupId, taskId);
                continue;
            }
            if (taskMgr.getTask(taskId) == null) {
                continue;
            }
            realRemoveTaskGroup = true;
            taskMgr.removeTask(taskId);

        }
        if (realRemoveTaskGroup) {
            LOGGER.info("removeTaskGroup activity {} task group {} removed",
                    activityId, taskGroupId);
        }
    }

    public static class TaskOutputFunc implements PlayerTaskOutputModule.CustomActivityTaskFunc {

        // 计算相关任务的产出控制周期key
        @Override
        public long calcTaskOutputPeriodKey(Player player, int activityId, int taskId, long atSec) {
            TaskType taskType = TaskConfData.getInstance().getTaskType(taskId);
            GeneralOutputPeriodType periodType = OutputModuleUtil.getPeriodTypeByTaskType(taskType.getNumber());
            if (periodType != GeneralOutputPeriodType.GOPT_Lifelong) {
                return BaseOutputModule.calcPeriodKeyByPeriodType(periodType, atSec);
            }
            // 使用本次回流档期开始时间
            return player.getUserAttr().getReturningInfo().getReturnActivity().getBeginTime();
        }
    }
}
