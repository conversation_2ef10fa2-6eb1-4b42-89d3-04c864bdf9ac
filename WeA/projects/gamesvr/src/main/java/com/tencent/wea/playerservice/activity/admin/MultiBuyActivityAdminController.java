package com.tencent.wea.playerservice.activity.admin;

import com.tencent.wea.playerservice.activity.config.MultiBuyActivityConfigManager;
import com.tencent.wea.xlsRes.ResMultiBuyActivity.*;
import org.springframework.web.bind.annotation.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.List;

/**
 * 多买多送活动运营管理接口
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
@RestController
@RequestMapping("/admin/multi-buy-activity")
public class MultiBuyActivityAdminController {
    
    private static final Logger LOGGER = LogManager.getLogger(MultiBuyActivityAdminController.class);
    
    private final MultiBuyActivityConfigManager configManager = MultiBuyActivityConfigManager.getInstance();
    
    /**
     * 获取活动统计数据
     */
    @GetMapping("/statistics/{activityId}")
    public MultiBuyActivityConfigManager.ActivityStatistics getActivityStatistics(@PathVariable int activityId) {
        LOGGER.info("Admin request activity statistics, activityId:{}", activityId);
        return configManager.getActivityStatistics(activityId);
    }
    
    /**
     * 获取所有活动统计摘要
     */
    @GetMapping("/statistics/summary")
    public Map<Integer, String> getAllActivityStatisticsSummary() {
        LOGGER.info("Admin request all activity statistics summary");
        return configManager.getAllActivityStatisticsSummary();
    }
    
    /**
     * 设置活动状态
     */
    @PostMapping("/status/{activityId}")
    public ApiResponse setActivityStatus(@PathVariable int activityId, @RequestParam boolean enabled) {
        LOGGER.info("Admin set activity status, activityId:{}, enabled:{}", activityId, enabled);
        
        try {
            configManager.setActivityStatus(activityId, enabled);
            return ApiResponse.success("Activity status updated successfully");
        } catch (Exception e) {
            LOGGER.error("Failed to set activity status, activityId:{}, enabled:{}", activityId, enabled, e);
            return ApiResponse.error("Failed to update activity status: " + e.getMessage());
        }
    }
    
    /**
     * 重置活动统计数据
     */
    @PostMapping("/statistics/reset/{activityId}")
    public ApiResponse resetActivityStatistics(@PathVariable int activityId) {
        LOGGER.info("Admin reset activity statistics, activityId:{}", activityId);
        
        try {
            configManager.resetActivityStatistics(activityId);
            return ApiResponse.success("Activity statistics reset successfully");
        } catch (Exception e) {
            LOGGER.error("Failed to reset activity statistics, activityId:{}", activityId, e);
            return ApiResponse.error("Failed to reset activity statistics: " + e.getMessage());
        }
    }
    
    /**
     * 获取活动热度排行
     */
    @GetMapping("/ranking/participants")
    public List<MultiBuyActivityConfigManager.ActivityStatistics> getActivityRankingByParticipants(
            @RequestParam(defaultValue = "10") int topN) {
        LOGGER.info("Admin request activity ranking by participants, topN:{}", topN);
        return configManager.getActivityRankingByParticipants(topN);
    }
    
    /**
     * 获取折扣使用效率统计
     */
    @GetMapping("/discount-efficiency/{activityId}")
    public Map<Integer, Double> getDiscountEfficiencyStats(@PathVariable int activityId) {
        LOGGER.info("Admin request discount efficiency stats, activityId:{}", activityId);
        return configManager.getDiscountEfficiencyStats(activityId);
    }
    
    /**
     * 验证活动配置
     */
    @PostMapping("/validate-config")
    public ApiResponse validateActivityConfig(@RequestBody MultiBuyActivityConf config) {
        LOGGER.info("Admin validate activity config, activityId:{}", config.getActivityId());
        
        try {
            boolean isValid = configManager.validateActivityConfig(config);
            if (isValid) {
                return ApiResponse.success("Activity config is valid");
            } else {
                return ApiResponse.error("Activity config validation failed");
            }
        } catch (Exception e) {
            LOGGER.error("Failed to validate activity config, activityId:{}", config.getActivityId(), e);
            return ApiResponse.error("Failed to validate activity config: " + e.getMessage());
        }
    }
    
    /**
     * 获取活动状态
     */
    @GetMapping("/status/{activityId}")
    public ApiResponse getActivityStatus(@PathVariable int activityId) {
        boolean enabled = configManager.isActivityEnabled(activityId);
        return ApiResponse.success("Activity status retrieved", Map.of("enabled", enabled));
    }
    
    /**
     * API响应封装类
     */
    public static class ApiResponse {
        private boolean success;
        private String message;
        private Object data;
        
        public ApiResponse(boolean success, String message, Object data) {
            this.success = success;
            this.message = message;
            this.data = data;
        }
        
        public static ApiResponse success(String message) {
            return new ApiResponse(true, message, null);
        }
        
        public static ApiResponse success(String message, Object data) {
            return new ApiResponse(true, message, data);
        }
        
        public static ApiResponse error(String message) {
            return new ApiResponse(false, message, null);
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Object getData() { return data; }
    }
    
    /**
     * 获取实时活动监控数据
     */
    @GetMapping("/monitor/{activityId}")
    public Map<String, Object> getActivityMonitorData(@PathVariable int activityId) {
        LOGGER.info("Admin request activity monitor data, activityId:{}", activityId);
        
        MultiBuyActivityConfigManager.ActivityStatistics stats = configManager.getActivityStatistics(activityId);
        boolean enabled = configManager.isActivityEnabled(activityId);
        Map<Integer, Double> discountEfficiency = configManager.getDiscountEfficiencyStats(activityId);
        
        return Map.of(
            "activityId", activityId,
            "enabled", enabled,
            "statistics", stats,
            "discountEfficiency", discountEfficiency,
            "timestamp", System.currentTimeMillis()
        );
    }
    
    /**
     * 批量操作活动状态
     */
    @PostMapping("/batch-status")
    public ApiResponse batchSetActivityStatus(@RequestBody Map<Integer, Boolean> activityStatusMap) {
        LOGGER.info("Admin batch set activity status, count:{}", activityStatusMap.size());
        
        try {
            for (Map.Entry<Integer, Boolean> entry : activityStatusMap.entrySet()) {
                configManager.setActivityStatus(entry.getKey(), entry.getValue());
            }
            return ApiResponse.success("Batch activity status updated successfully");
        } catch (Exception e) {
            LOGGER.error("Failed to batch set activity status", e);
            return ApiResponse.error("Failed to batch update activity status: " + e.getMessage());
        }
    }
}
