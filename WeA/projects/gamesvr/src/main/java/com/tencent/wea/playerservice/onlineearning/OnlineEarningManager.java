package com.tencent.wea.playerservice.onlineearning;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tencent.condition.event.player.common.AmsItemResultEvent;
import com.tencent.condition.event.player.common.TaskFinishEvent;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.eventcenter.EventConsumer;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.nk.util.guid.BillNoIdGenerator;
import com.tencent.resourceloader.ResHolder;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.MiscConf;
import com.tencent.resourceloader.resclass.OnlineEarningConfData;
import com.tencent.resourceloader.resclass.TaskConfData;
import com.tencent.resourceloader.resclass.TaskGroupData;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.util.Pb2JsonUtil;
import com.tencent.wea.attr.AbstractAttrObj;
import com.tencent.wea.attr.OnlineEarningInfo;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.interaction.player.MailInteraction.TlogSendReason;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.event.common.PlayerChangeCoinEvent;
import com.tencent.wea.playerservice.event.common.PlayerIAAOpEvent;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.onlineearning.submodules.AllowanceTaskHandler;
import com.tencent.wea.playerservice.onlineearning.submodules.BankTaskHandler;
import com.tencent.wea.playerservice.onlineearning.submodules.BaseSubModule;
import com.tencent.wea.playerservice.onlineearning.submodules.BaseTaskHandler;
import com.tencent.wea.playerservice.onlineearning.submodules.CardTaskHandler;
import com.tencent.wea.playerservice.onlineearning.submodules.CheckinTaskHandler;
import com.tencent.wea.playerservice.onlineearning.submodules.CommonTask;
import com.tencent.wea.playerservice.onlineearning.submodules.ContinueAdTaskHandler;
import com.tencent.wea.playerservice.onlineearning.submodules.OnlineBonusTaskHandler;
import com.tencent.wea.playerservice.onlineearning.submodules.OpRecord;
import com.tencent.wea.playerservice.onlineearning.submodules.ReservationTaskHandler;
import com.tencent.wea.playerservice.onlineearning.submodules.TreasureBoxTaskHandler;
import com.tencent.wea.playerservice.onlineearning.submodules.Withdrawal;
import com.tencent.wea.playerservice.onlineearning.submodules.WithdrawalTaskHandler;
import com.tencent.wea.playerservice.onlineearning.util.OnlineEarningCfgUtil;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.protocol.AttrOnlineEarningData.proto_OnlineEarningData;
import com.tencent.wea.protocol.CsNotice.ChangedItemInfo;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningChangeNtf;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningDirectRewardTask_C2S_Msg;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningDirectRewardTask_S2C_Msg;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningEnter_C2S_Msg;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningEnter_S2C_Msg;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningEntranceNtf;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningFetchOpRecord_C2S_Msg;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningFetchOpRecord_S2C_Msg;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningGetOpRecordSize_C2S_Msg;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningGetOpRecordSize_S2C_Msg;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningReserve_C2S_Msg;
import com.tencent.wea.protocol.CsOnlineearning.OnlineEarningReserve_S2C_Msg;
import com.tencent.wea.protocol.CsPlayer.ABTestType;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.MailAttachment;
import com.tencent.wea.protocol.common.MailAttachmentList;
import com.tencent.wea.protocol.common.OnlineEarningItemChangeOp;
import com.tencent.wea.protocol.common.OnlineEarningOp;
import com.tencent.wea.protocol.common.OnlineEarningOpType;
import com.tencent.wea.xlsRes.ResOnlineEarning.OnlineEarningCoinConf;
import com.tencent.wea.xlsRes.ResTask.TaskRewardConf;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.IAAType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.ItemUseTypeOperate;
import com.tencent.wea.xlsRes.keywords.TaskType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.StringJoiner;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class OnlineEarningManager extends PlayerModule {

    private static final Logger LOGGER = LogManager.getLogger(OnlineEarningManager.class);
    private final SubModuleContainer subModules;
    private final List<EventConsumer> consumers;
    private final ClientAttrSyncer clientAttrSyncer;
    private boolean allowEntrance = false;
    private long lastOpAppendRefreshTs;


    public OnlineEarningManager(Player player) {
        super(GameModuleId.GMI_OnlineEarning, player);

        subModules = new SubModuleContainer();

        subModules.add(new Withdrawal(player));
        subModules.add(new OpRecord(player));

        subModules.add(new CheckinTaskHandler(player));
        subModules.add(new TreasureBoxTaskHandler(player));
        subModules.add(new CardTaskHandler(player));
        subModules.add(new BankTaskHandler(player));
        subModules.add(new ContinueAdTaskHandler(player));
        subModules.add(new AllowanceTaskHandler(player));
        subModules.add(new OnlineBonusTaskHandler(player));
        subModules.add(new ReservationTaskHandler(player));
        subModules.add(new WithdrawalTaskHandler(player));
        subModules.add(new CommonTask(player));

        consumers = new ArrayList<>();
        consumers.add(new OnCoinChange());
        consumers.add(new OnIAA());
        consumers.add(new OnAmsResult());
        consumers.add(new OnTaskFinish());
        consumers.forEach(player.getEventSwitch()::register);

        clientAttrSyncer = new ClientAttrSyncer();
    }

    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    @Override
    public void onRegister() throws NKCheckedException {

    }

    @Override
    public void afterRegister() throws NKCheckedException {

    }

    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    @Override
    public void onLoad() throws NKCheckedException {

    }

    @Override
    public void afterLoad() {

    }

    @Override
    public void prepareLogin() throws NKCheckedException {

    }

    @Override
    public void onLogin() throws NKCheckedException {
        clientAttrSyncer.reset();
        lastOpAppendRefreshTs = 0;
    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {

    }

    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {
        ResHolder resHolder = ResLoader.getResHolder();
        long currentMs = DateUtils.currentTimeMillis();

        var misc = MiscConf.getInLoadingInstance(resHolder).getMiscConf().getOnlineEarning();
        var conf = OnlineEarningConfData.getInLoadingInstance(resHolder).get(getCurrentInfo().getId());

        if (conf != null && misc.hasResetCoinIntervalDay()) {
            int itv = DateUtils.getDayInterval(player.getUserAttr().getBasicInfo().getLastLoginTimeMs(), currentMs);
            if (itv > misc.getResetCoinIntervalDay()) {
                var billNo = BillNoIdGenerator.getBusinessBillNo("OnlineEarningReset");
                int withdraw = conf.getWithdraw().getCoinType();
                if (withdraw != 0) {
                    player.getBagManager().setMoneyNum(withdraw, 0, ItemChangeReason.ICR_OnlineEarningReset_VALUE, billNo);
                    LOGGER.warn("reset withdraw coin, uid:{} withdraw:{}", player.getUid(), withdraw);
                }

                int gold = conf.getGold().getCoinType();
                if (gold != 0) {
                    player.getBagManager().setMoneyNum(gold, 0, ItemChangeReason.ICR_OnlineEarningReset_VALUE, billNo);
                    LOGGER.warn("reset gold coin, uid:{} gold:{}", player.getUid(), gold);
                }
            }
        }

        if (isActivated()) {
            initModules(resHolder, false, false, currentMs);
            LOGGER.debug("online earning init mod after login finish, uid:{}", player.getUid());
        }

        resetIfExpired(currentMs);
        allocIfAbsent(currentMs);

        allowEntrance = ntfEntrance();
    }

    @Override
    public void onLogout() {
        consumers.forEach(player.getEventSwitch()::unregister);
    }

    @Override
    public void onMidNight() {
        long currentMs = DateUtils.currentTimeMillis();
        ResHolder resHolder = ResLoader.getResHolder();

        if (isActivated()) {
            refreshDayCounters(currentMs);
            refreshModules(resHolder, currentMs);
        }
    }

    @Override
    public void onWeekStart() {

    }

    @Override
    public void onEveryHourStart() {
        long currentMs = DateUtils.currentTimeMillis();
        ResHolder resHolder = ResLoader.getResHolder();

        if (isActivated()) {
            refreshModules(resHolder, currentMs);
        }
    }

    public void onlineProc() {
        long currentMs = DateUtils.currentTimeMillis();

        // procEntrance(currentMs);
        procOpAppend(currentMs);
        // procSync(currentMs);
    }

    public void collectAndSyncDirtyToClient() {
        clientAttrSyncer.sync();
    }

    public void collectDirty() {
        clientAttrSyncer.collectDirty();
    }

    public OnlineEarningEnter_S2C_Msg.Builder handleEnter(OnlineEarningEnter_C2S_Msg req) {
        OnlineEarningEnter_S2C_Msg.Builder resBuilder = OnlineEarningEnter_S2C_Msg.newBuilder();

        if (!allowEntrance) {
            LOGGER.error("online earning entrance not allowed, uid:{}", player.getUid());
            NKErrorCode.OnlineEarningIsClosed.throwError("online earning not allow");
            return resBuilder;
        }

        var resHolder = ResLoader.getResHolder();

        var info = getCurrentInfo();
        var conf = OnlineEarningConfData.getInLoadingInstance(resHolder).get(info.getId());

        if (conf == null) {
            LOGGER.error("online earning conf not found, uid:{} id:{}", player.getUid(), info.getId());
            NKErrorCode.ResNotFound.throwError("online earning conf not found");
            return resBuilder;
        }

        // 检查时间
        long currentMs = DateUtils.currentTimeMillis();
        if (conf.getStartTime().getSeconds() > currentMs / 1000 || conf.getEndTime().getSeconds() < currentMs / 1000) {
            LOGGER.error("online earning not in time, uid:{} id:{}", player.getUid(), info.getId());
            NKErrorCode.OnlineEarningNotWithinTime.throwError("online earning not in time");
            return resBuilder;
        }

        if (OnlineEarningCfgUtil.findBindConf(resHolder, info.getId(), currentMs) == null) {
            LOGGER.error("online earning bind not found, uid:{} id:{}", player.getUid(), info.getId());
            NKErrorCode.ResNotFound.throwError("online earning not found");
            return resBuilder;
        }

        // 首次进入
        if (!isActivated()) {
            boolean isNew = player.getUserAttr().getOnlineEarningData().getFirstEnterTs() == 0;

            if (isNew) {
                player.getUserAttr().getOnlineEarningData().setFirstEnterTs(currentMs);
                LOGGER.info("online earning first enter all, uid:{} ts:{}", player.getUid(), currentMs);
            }

            info.setFirstEnterTs(currentMs).setLastEnterTs(currentMs);
            initDayCounters(currentMs);
            getCurrentInfo().setLastOpTs(currentMs);

            LOGGER.info("online earning first enter current, uid:{} id:{} ts:{}", player.getUid(),
                    getCurrentInfo().getId(), currentMs);

            initModules(resHolder, isNew, true, currentMs);
            LOGGER.debug("online earning enter for first time, uid:{} id:{}", player.getUid(), info.getId());

            return resBuilder;
        }

        // 后续进入
        info.setLastEnterTs(currentMs);
        keepalive(currentMs);

        refreshModules(resHolder, currentMs);
        LOGGER.debug("online earning enter again, uid:{} id:{}", player.getUid(), info.getId());

        return resBuilder;
    }

    public OnlineEarningFetchOpRecord_S2C_Msg.Builder handleFetch(OnlineEarningFetchOpRecord_C2S_Msg req) {
        OnlineEarningFetchOpRecord_S2C_Msg.Builder resBuilder = OnlineEarningFetchOpRecord_S2C_Msg.newBuilder()
                .setOpType(req.getOpType()).setFromIndex(req.getFromIndex()).setToIndex(req.getToIndex());

        if (!allowEntrance) {
            LOGGER.error("online earning fetch op not allowed, uid:{}", player.getUid());
            NKErrorCode.OnlineEarningIsClosed.throwError("online earning not allow");
            return resBuilder;
        }

        // 刷新活跃时间戳
        keepalive();

        var opRecord = subModules.get(OpRecord.class);
        if (opRecord == null) {
            LOGGER.error("online earning fetch op record not support, uid:{}", player.getUid());
            NKErrorCode.Unsupported.throwError("online earning fetch op record not support");
            return resBuilder;
        }

        var res = opRecord.fetch(req.getOpType(), req.getFromIndex(), req.getToIndex());
        resBuilder.addAllOps(res);

        var counter = getCurrentInfo().getOpRecordInfo().getOpCounter(req.getOpType());
        if (counter != null) {
            resBuilder.setSize(counter.getValue());
        }

        return resBuilder;
    }

    public OnlineEarningGetOpRecordSize_S2C_Msg.Builder handleGetSize(OnlineEarningGetOpRecordSize_C2S_Msg req) {
        OnlineEarningGetOpRecordSize_S2C_Msg.Builder resBuilder = OnlineEarningGetOpRecordSize_S2C_Msg.newBuilder()
                .setOpType(req.getOpType());

        if (!allowEntrance) {
            LOGGER.error("online earning get op size not allowed, uid:{}", player.getUid());
            NKErrorCode.OnlineEarningIsClosed.throwError("online earning not allow");
            return resBuilder;
        }

        // 刷新活跃时间戳
        keepalive();

        var opRecord = subModules.get(OpRecord.class);
        if (opRecord == null) {
            LOGGER.error("online earning get op record size not support, uid:{}", player.getUid());
            NKErrorCode.Unsupported.throwError("online earning get op record size not support");
            return resBuilder;
        }

        var counter = getCurrentInfo().getOpRecordInfo().getOpCounter(req.getOpType());
        if (counter != null) {
            resBuilder.setSize(counter.getValue());
        }

        return resBuilder;
    }

    public OnlineEarningReserve_S2C_Msg.Builder handleReserve(OnlineEarningReserve_C2S_Msg req) {
        OnlineEarningReserve_S2C_Msg.Builder resBuilder = OnlineEarningReserve_S2C_Msg.newBuilder();

        if (!allowEntrance) {
            LOGGER.error("online earning reservation not allowed, uid:{}", player.getUid());
            NKErrorCode.OnlineEarningIsClosed.throwError("online earning not allow");
            return resBuilder;
        }

        // 刷新活跃时间戳
        long currentMs = DateUtils.currentTimeMillis();
        keepalive(currentMs);

        var handler = subModules.get(ReservationTaskHandler.class);
        if (handler == null) {
            LOGGER.error("online earning reservation not support, uid:{}", player.getUid());
            NKErrorCode.Unsupported.throwError("online earning reservation not support");
            return resBuilder;
        }

        NKErrorCode errorCode = handler.reserve(req.getTaskId(), currentMs);
        collectAndSyncDirtyToClient();

        if (errorCode.hasError()) {
            LOGGER.error("online earning reservation failed, uid:{} err:{}", player.getUid(), errorCode);
            errorCode.throwError("online earning reservation failed");
            return resBuilder;
        }

        return resBuilder;
    }

    public OnlineEarningDirectRewardTask_S2C_Msg.Builder handleDirectReward(OnlineEarningDirectRewardTask_C2S_Msg req) {
        OnlineEarningDirectRewardTask_S2C_Msg.Builder resBuilder = OnlineEarningDirectRewardTask_S2C_Msg.newBuilder();

        if (!allowEntrance) {
            LOGGER.error("online earning reservation not allowed, uid:{}", player.getUid());
            NKErrorCode.OnlineEarningIsClosed.throwError("online earning not allow");
            return resBuilder;
        }

        // 刷新活跃时间戳
        keepalive();

        var itemChangeReason = ItemChangeReason.forNumber(req.getRewardReason());
        if (itemChangeReason == null || itemChangeReason == ItemChangeReason.ICR_Unknown) {
            itemChangeReason = ItemChangeReason.ICR_OnlineEarningTask;
        }

        NKErrorCode errorCode = rewardTask(req.getTaskIdListList(), itemChangeReason);
        collectAndSyncDirtyToClient();

        if (errorCode.hasError()) {
            LOGGER.error("reward direct reward failed, uid:{} taskIds:{} err:{}", player.getUid(),
                    req.getTaskIdListList(), errorCode);
            errorCode.throwError("reward direct reward failed");
            return resBuilder;
        }

        return resBuilder;
    }

    private NKErrorCode rewardTask(List<Integer> taskIdList, ItemChangeReason reason) {
        ResHolder resHolder = ResLoader.getResHolder();
        long currentMs = DateUtils.currentTimeMillis();

        Map<Integer, List<Integer>> typeTaskIds = Maps.newHashMap();
        for (int taskId : taskIdList) {
            var taskConf = TaskConfData.getInstance().getTaskConf(taskId);
            if (taskConf == null) {
                LOGGER.error("online earning group task not found, uid:{} task:{}", player.getUid(), taskId);
                return NKErrorCode.ResNotFound;
            }

            var handler = subModules.getTaskHandler(taskConf.getType().getNumber());
            if (handler == null) {
                LOGGER.error("online earning group task not support, uid:{} task:{}", player.getUid(), taskId);
                return NKErrorCode.Unsupported;
            }

            typeTaskIds.computeIfAbsent(taskConf.getType().getNumber(), k -> Lists.newArrayList()).add(taskId);
        }

        NKErrorCode errorCode = NKErrorCode.OK;

        for (var group : typeTaskIds.entrySet()) {
            var taskType = group.getKey();
            var taskIds = group.getValue();

            for (int taskId : taskIds) {
                var handler = subModules.getTaskHandler(taskType);
                var res = handler.handleRewardTask(resHolder, taskId, reason, currentMs);

                if (res.hasError()) {
                    errorCode = res;
                    LOGGER.error("online earning group task reward failed, uid:{} task:{}", player.getUid(), taskId);
                    continue;
                }
            }
        }

        return errorCode;
    }

    public TaskRewardConf getTaskRewardConf(int taskId) {
        var conf = TaskConfData.getInstance().getTaskConf(taskId);
        if (conf == null) {
            LOGGER.error("online earning task conf not found, uid:{} task:{}", player.getUid(), taskId);
            return TaskRewardConf.getDefaultInstance();
        }

        var handler = subModules.getTaskHandler(conf.getType().getNumber());
        if (handler == null) {
            LOGGER.error("online earning task conf not support, uid:{} task:{} type:{}", player.getUid(), taskId,
                    conf.getType());
            return TaskRewardConf.getDefaultInstance();
        }

        return handler.getTaskReward(conf.getType().getNumber(), taskId).build();
    }


    public boolean isActivated() {
        var info = getCurrentInfo();

        // 玩家必须进入到网赚页面之后，才会激活
        return info.getId() != 0 && info.getFirstEnterTs() != 0;
    }

    /**
     * 获得储蓄罐比例
     *
     * @return long
     */
    public long getBankRate() {
        if (!isActivated()) {
            return 0;
        }

        return getCurrentInfo().getBank().getCashbackRate();
    }

    /**
     * 获取已提现次数
     *
     * @return int
     */
    public int getWithdrawTimes() {
        if (!isActivated()) {
            return 0;
        }

        return getCurrentInfo().getWithdrawal().getTimes();
    }

    /**
     * 获取已提现的金额（分）
     *
     * @return long
     */
    public long getWithdrawAmount() {
        if (!isActivated()) {
            return 0;
        }

        return getCurrentInfo().getWithdrawal().getAmount();
    }

    /**
     * 获取可折现金额（分）
     *
     * @return long
     */
    public long getExchangeableAmount() {
        if (!isActivated()) {
            return 0;
        }

        return BaseTaskHandler.getExchangeableAmount(player, getCurrentInfo().getId());
    }

    /**
     * 获取已拥有提款券最小兑现额度（分）
     *
     * @return long
     */
    public long getMinWithdrawalTicketAmount() {
        if (!isActivated()) {
            return 0;
        }

        return BaseTaskHandler.getMinWithdrawalTicketAmount(player, getCurrentInfo().getId());
    }

    /**
     * 获得活跃天数
     *
     * @return int
     */
    public int getActiveDays() {
        if (!isActivated()) {
            return 0;
        }

        return getCurrentInfo().getTotalDayCount();
    }

    /**
     * 获得回归天数
     *
     * @return int
     */
    public int getReturningDays() {
        if (!isActivated()) {
            return 0;
        }

        return getCurrentInfo().getReturningDays();
    }

    /**
     * 获得7天签到完成轮数
     *
     * @return int
     */
    public int getCheckinCompleteWeek() {
        if (!isActivated()) {
            return 0;
        }

        return getCurrentInfo().getCheckin().getWeekCompleteCount();
    }

    /**
     * 获取继续广告今日次数
     *
     * @return int
     */
    public int getContinueAdTodayTimes() {
        if (!isActivated()) {
            return 0;
        }

        var tasks = getCurrentInfo().getTaskInfo().getTaskIdLists(TaskType.TaskType_OnlineEarningContinueAd_VALUE);
        if (tasks == null || tasks.getTaskRepeatsSize() == 0) {
            return 0;
        }

        var repeat = tasks.getTaskRepeats().valuesIterator().next();
        return repeat == null ? 0 : repeat.getValue();
    }

    private void initModules(ResHolder resHolder, boolean isNew, boolean isCurNew, long currentMs) {
        if (isActivated()) {
            subModules.initAll(resHolder, isNew, isCurNew, currentMs);
            LOGGER.debug("online earning initialized, uid:{} id:{}", player.getUid(), getCurrentInfo().getId());
        }

        collectAndSyncDirtyToClient();
    }

    private void refreshModules(ResHolder resHolder, long currentMs) {
        if (isActivated()) {
            subModules.refreshAll(resHolder, currentMs);
            LOGGER.debug("online earning refreshed, uid:{} id:{}", player.getUid(), getCurrentInfo().getId());
        }

        collectAndSyncDirtyToClient();
    }

    private void procOpAppend(long currentMs) {
        long itv = PropertyFileReader.getRealTimeLongItem("online_earning_op_append_itv", 1_000L);
        if (Math.abs(currentMs - lastOpAppendRefreshTs) <= itv) {
            return;
        }

        lastOpAppendRefreshTs = currentMs;
        if (!isActivated()) {
            return;
        }

        var opRecord = subModules.get(OpRecord.class);
        if (opRecord == null) {
            return;
        }

        opRecord.proc(currentMs);
    }

    private void closeEntrance() {
        allowEntrance = false;

        var ntfBuilder = OnlineEarningEntranceNtf.newBuilder().setIsOpen(false);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ONLINEEARNINGENTRANCENTF, ntfBuilder);
    }

    private boolean ntfEntrance() {
        if (!player.getIaaManager().isGranted(Lists.newArrayList(2, 3))) {
            return false;
        }

        OnlineEarningEntranceNtf.Builder ntfBuilder = OnlineEarningEntranceNtf.newBuilder().setIsOpen(false);

        var info = getCurrentInfo();
        if (info.getId() == 0) {
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ONLINEEARNINGENTRANCENTF, ntfBuilder);
            return false;
        }

        var conf = OnlineEarningConfData.getInstance().get(info.getId());
        if (conf == null) {
            LOGGER.error("online earning conf not found, uid:{} id:{}", player.getUid(), info.getId());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ONLINEEARNINGENTRANCENTF, ntfBuilder);
            return false;
        }

        boolean hideAll = PropertyFileReader.getRealTimeBooleanItem("online_earning_hide_all", false);
        if (hideAll || conf.getIsTemporaryHide()) {
            LOGGER.debug("online earning is temporary hide, uid:{} id:{}", player.getUid(), info.getId());
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ONLINEEARNINGENTRANCENTF, ntfBuilder);
            return false;
        }

        ntfBuilder.setIsOpen(true).setEarningId(conf.getId()).setStartSec(conf.getStartTime().getSeconds())
                .setEndSec(conf.getEndTime().getSeconds());
        player.sendNtfMsg(MsgTypes.MSG_TYPE_ONLINEEARNINGENTRANCENTF, ntfBuilder);

        LOGGER.info("online earning entrance is prepared, uid:{} id:{}", player.getUid(), info.getId());
        return true;
    }

    private void allocIfAbsent(long currentMs) {
        var info = getCurrentInfo();
        if (info.getFirstEnterTs() != 0) {
            LOGGER.debug("online earning already entered, uid:{} id:{}", player.getUid(), info.getId());
            return;
        }

        // 没有进入过，重置数据
        info.clear();

        int abtestId = player.getAbTestMgr().getTABTestGroupId(ABTestType.ABTT_ONLINE_EARNING);
        var conf = OnlineEarningCfgUtil.findConf(ResLoader.getResHolder(), abtestId, currentMs);
        if (conf == null) {
            LOGGER.debug("online earning conf not found, uid:{} abtestId:{}", player.getUid(), abtestId);
            return;
        }

        LOGGER.info("online earning allocated, uid:{} id:{} abtest:{}", player.getUid(), info.getId(), abtestId);
        info.setId(conf.getId());
    }

    private void resetIfExpired(long currentMs) {
        var info = getCurrentInfo();
        if (info.getId() == 0 || info.getFirstEnterTs() == 0) {
            return;
        }

        var conf = OnlineEarningConfData.getInstance().get(info.getId());
        if (conf == null) {
            LOGGER.error("online earning conf not found and reset, uid:{} id:{}", player.getUid(), info.getId());
            info.clear();
            return;
        }

        long currentSec = currentMs / 1000;
        if (conf.getStartTime().getSeconds() <= currentSec && currentSec <= conf.getEndTime().getSeconds()) {
            return;
        }

        LOGGER.info("online earning expired, uid:{} id:{}", player.getUid(), info.getId());

        sendExpiredMail();
        info.clear();
    }

    private void sendExpiredMail() {
        var info = getCurrentInfo();
        if (info.getId() == 0 || info.getFirstEnterTs() == 0) {
            return;
        }

        var conf = OnlineEarningConfData.getInstance().get(info.getId());
        if (conf == null) {
            LOGGER.error("online earning conf not found, uid:{} id:{}", player.getUid(), info.getId());
            return;
        }

        // 获取替换邮件附件
        MailAttachmentList.Builder builder = MailAttachmentList.newBuilder();

        var withdraw = getReplacement(conf.getWithdraw());
        if (withdraw != null) {
            builder.addList(withdraw);
        }

        var gold = getReplacement(conf.getGold());
        if (gold != null) {
            builder.addList(gold);
        }

        // 清空活动货币
        var billNo = BillNoIdGenerator.getBusinessBillNo("OnlineEarningExpire");
        if (conf.getWithdraw().getCoinType() != 0) {
            var coin = conf.getWithdraw().getCoinType();
            player.getBagManager().setMoneyNum(coin, 0, ItemChangeReason.ICR_OnlineEarningExpire_VALUE, billNo);
        }

        if (conf.getGold().getCoinType() != 0) {
            var coin = conf.getGold().getCoinType();
            player.getBagManager().setMoneyNum(coin, 0, ItemChangeReason.ICR_OnlineEarningExpire_VALUE, billNo);
        }

        // 清空提现券
        ChangedItems removed = new ChangedItems(ItemChangeReason.ICR_OnlineEarningExpire_VALUE, "");
        removed.setBusBillNo(billNo);

        for (var itemId : BackpackItem.getInstance()
                .getItemIdByUseType(ItemUseTypeOperate.IUTO_OnlineEarningWithdrawal)) {
            var num = player.getBagManager().getItemNumByItemId(itemId);
            if (num > 0) {
                removed.mergeItemInfo(itemId, num);
            }
        }

        if (!removed.getChangeItems().isEmpty()) {
            player.getBagManager().minItemsAsPossible(removed);
            LOGGER.warn("remove all withdrawal tickets, uid:{} tickets:{}", player.getUid(), removed);
        }

        if (conf.getExpireMailId() == 0) {
            LOGGER.debug("expire mail id is 0, uid:{} id:{}", player.getUid(), info.getId());
            return;
        }

        if (builder.getListCount() == 0) {
            LOGGER.debug("no replacement and skip expire mail, uid:{} id:{}", player.getUid(), info.getId());
            return;
        }

        var res = MailInteraction.sendTemplateMail(player.getUid(), conf.getExpireMailId(), builder,
                TlogSendReason.onlineEarningExpire);

        LOGGER.info("send expire mail, uid:{} id:{} res:{} attach:{}", player.getUid(), info.getId(), res,
                Pb2JsonUtil.getPbMsg(builder));
    }

    private MailAttachment.Builder getReplacement(OnlineEarningCoinConf conf) {
        if (!conf.hasExpireRep()) {
            LOGGER.debug("no expire rep, uid:{} coin:{}", player.getUid(), conf.getCoinType());
            return null;
        }

        long num = player.getBagManager().getItemNumByItemId(conf.getCoinType());
        LOGGER.debug("player residual coin, uid:{} num:{}", player.getUid(), num);

        var rep = conf.getExpireRep();
        if (rep.getPer() == 0 || rep.getItemId() == 0 || rep.getItemNum() == 0) {
            LOGGER.debug("invalid expire rep, uid:{} coin:{}", player.getUid(), conf.getCoinType());
            return null;
        }

        long repNum = (num / rep.getPer()) * rep.getItemNum();
        if (repNum == 0) {
            LOGGER.debug("no need to replace, uid:{} coin:{} own:{}", player.getUid(), conf.getCoinType(), num);
            return null;
        }

        return MailAttachment.newBuilder()
                .setItemIfo(ItemInfo.newBuilder().setItemId(rep.getItemId()).setItemNum(repNum));
    }

    private void keepalive() {
        keepalive(DateUtils.currentTimeMillis());
    }

    private void keepalive(long currentMs) {
        refreshDayCounters(currentMs);
        getCurrentInfo().setLastOpTs(currentMs);
    }

    private void refreshDayCounters(long currentMs) {
        if (!isActivated()) {
            clearDayCounters();
            return;
        }

        var info = getCurrentInfo();

        // 今日已经刷新，跳过
        if (DateUtils.isSameDay(currentMs, info.getLastDayCountRefreshTs())) {
            return;
        }

        boolean isValid = DateUtils.isSameDay(info.getLastOpTs(), info.getLastDayCountRefreshTs());
        info.setLastDayCountRefreshTs(currentMs);

        int returnItv = MiscConf.getInstance().getMiscConf().getOnlineEarning().getReturningIntervalDay();
        returnItv = returnItv <= 0 ? 7 : returnItv;

        long returnMax = MiscConf.getInstance().getMiscConf().getOnlineEarning().getReturningMaxDay();
        returnMax = returnMax <= 0 ? 7 : returnMax;

        // 天数的实际距离，与lastOpTs有关
        int dayItv = DateUtils.getDayInterval(currentMs, info.getLastOpTs());

        // 先刷新Op，后刷新天数，直接自增
        if (dayItv == 0) {
            info.addTotalDayCount(1).addConsecutiveDayCount(1);
            if (info.getReturningDays() > 0) {
                info.addReturningDays(1);
                if (info.getReturningDays() > returnMax) {
                    info.setReturningTs(0).setReturningDays(0);
                }
            }

            LOGGER.debug("inc day counters since player has op today, uid:{} day:{} consecutive:{} return:{}",
                    player.getUid(), info.getTotalDayCount(), info.getConsecutiveDayCount(), info.getReturningDays());
            return;
        }

        // 如果昨天还有Op，那今天的计数先自增
        if (dayItv == 1) {
            info.addTotalDayCount(1).addConsecutiveDayCount(1);
            if (info.getReturningDays() > 0) {
                info.addReturningDays(1);
                if (info.getReturningDays() > returnMax) {
                    info.setReturningTs(0).setReturningDays(0);
                }
            }

            LOGGER.debug("pre-inc day counters since player had op yesterday, uid:{} day:{} consecutive:{} return:{}",
                    player.getUid(), info.getTotalDayCount(), info.getConsecutiveDayCount(), info.getReturningDays());
            return;
        }

        // 如果昨天没有Op，那么连续天数中断，并且检查是否为回归玩家
        if (isValid) {
            info.addTotalDayCount(1);
            if (info.getReturningDays() > 0) {
                info.addReturningDays(1);
                if (info.getReturningDays() > returnMax) {
                    info.setReturningTs(0).setReturningDays(0);
                }
            }
        }

        info.setConsecutiveStartTs(currentMs).setConsecutiveDayCount(1);
        if (dayItv > returnItv) {
            info.setReturningTs(currentMs).setReturningDays(1);
        }

        LOGGER.debug("inc day counters, uid:{} day:{} consecutive:{} return:{} isValid:{}", player.getUid(),
                info.getTotalDayCount(), info.getConsecutiveDayCount(), info.getReturningDays(), isValid);
    }

    private void initDayCounters(long currentMs) {
        if (!isActivated()) {
            LOGGER.error("refuse to init day counters since not activated, uid:{}", player.getUid());
            return;
        }

        var info = getCurrentInfo();
        info.setTotalDayCount(1).setConsecutiveDayCount(1).setConsecutiveStartTs(currentMs).setReturningTs(0)
                .setReturningDays(0).setLastDayCountRefreshTs(currentMs);

        LOGGER.debug("init day counters, uid:{}", player.getUid());
    }

    private void clearDayCounters() {
        var info = getCurrentInfo();
        info.setTotalDayCount(0).setConsecutiveDayCount(0).setConsecutiveStartTs(0).setReturningTs(0)
                .setReturningDays(0).setLastDayCountRefreshTs(0);

        LOGGER.debug("clear day counters, uid:{}", player.getUid());
    }

    public OnlineEarningInfo getCurrentInfo() {
        return player.getUserAttr().getOnlineEarningData().getCurrentInfo();
    }

    private static class SubModuleContainer {

        private final List<NKPair<String, BaseSubModule>> subModuleSeq = Lists.newArrayList();
        private final Map<Class<?>, BaseSubModule> subModules = new HashMap<>();
        private final Map<Integer, BaseTaskHandler> tasks = new HashMap<>();

        protected SubModuleContainer() {
        }

        public void add(BaseSubModule subModule) {
            subModuleSeq.add(new NKPair<>(subModule.getClass().getSimpleName(), subModule));
            subModules.put(subModule.getClass(), subModule);

            if (subModule instanceof BaseTaskHandler) {
                var handler = (BaseTaskHandler) subModule;
                for (var taskType : handler.getRelatedTaskTypes()) {
                    tasks.put(taskType, handler);
                }
            }
        }


        @SuppressWarnings("unchecked")
        public <T extends BaseSubModule> T get(Class<T> tClass) {
            return (T) subModules.get(tClass);
        }

        public BaseTaskHandler getTaskHandler(int taskType) {
            return tasks.get(taskType);
        }

        @Override
        public String toString() {
            StringJoiner joiner = new StringJoiner(",", "[", "]");
            for (var key : subModules.keySet()) {
                joiner.add(key.getSimpleName());
            }

            return joiner.toString();
        }

        public void initAll(ResHolder resHolder, boolean isNew, boolean isCurNew, long currentMs) {
            for (var kv : subModuleSeq) {
                var name = kv.getKey();
                var subModule = kv.getValue();

                try {
                    NKErrorCode errorCode = subModule.init(resHolder, isNew, isCurNew, currentMs);
                    if (errorCode.hasError()) {
                        LOGGER.error("init sub module failed, name:{} error:{}", name, errorCode);
                    }
                } catch (Exception e) {
                    LOGGER.error("init sub module failed, name:{} error:{}", name, e);
                }
            }
        }

        public void refreshAll(ResHolder resHolder, long currentMs) {
            for (var kv : subModuleSeq) {
                var name = kv.getKey();
                var subModule = kv.getValue();

                try {
                    NKErrorCode errorCode = subModule.refresh(resHolder, currentMs);
                    if (errorCode.hasError()) {
                        LOGGER.error("refresh sub module failed, name:{} error:{}", name, errorCode);
                    }
                } catch (Exception e) {
                    LOGGER.error("refresh sub module failed, name:{} error:{}", name, e);
                }
            }
        }
    }

    public class ClientAttrSyncer {

        protected proto_OnlineEarningData.Builder clientDirtyAttr = null;
        protected long dirtyFieldCnt = 0;

        private boolean fullSynced = false;

        private ClientAttrSyncer() {
        }

        private void reset() {
            clientDirtyAttr = null;
            dirtyFieldCnt = 0;
            fullSynced = false;
        }

        public void sync() {
            if (!player.isOnline()) {
                LOGGER.debug("skip sync since not online, uid:{}", player.getUid());
                return;
            }

            if (!fullSynced) {
                syncFull();
                return;
            }

            syncIncremental();
        }

        private void syncFull() {
            if (!player.isOnline()) {
                LOGGER.debug("skip sync full since not online, uid:{}", player.getUid());
                return;
            }

            var data = player.getUserAttr().getOnlineEarningData().getCopyCsBuilder();
            var builder = OnlineEarningChangeNtf.newBuilder().setIsFull(true).setData(data);

            player.getTaskManager().sendTaskChangeNtf();
            player.getUserAttrMgr().collectAndSyncDirtyToClient();
            player.sendNtfMsg(MsgTypes.MSG_TYPE_ONLINEEARNINGCHANGENTF, builder);

            fullSynced = true;
            clientDirtyAttr = null;
            dirtyFieldCnt = 0;
        }

        private void syncIncremental() {
            NKErrorCode errorCode = collectDirty();
            if (errorCode.hasError()) {
                LOGGER.error("sync incremental failed, uid:{} err:{}", player.getUid(), errorCode);
                return;
            }

            player.getTaskManager().sendTaskChangeNtf();
            player.getUserAttrMgr().collectAndSyncDirtyToClient();

            if (dirtyFieldCnt > 0) {
                var builder = OnlineEarningChangeNtf.newBuilder().setIsFull(false).setData(clientDirtyAttr);
                player.sendNtfMsg(MsgTypes.MSG_TYPE_ONLINEEARNINGCHANGENTF, builder);
            }

            dirtyFieldCnt = 0;
            clientDirtyAttr = null;
        }

        public NKErrorCode collectDirty() {
            if (!player.isOnline() || !fullSynced) {
                clientDirtyAttr = null;
                dirtyFieldCnt = 0;
                return NKErrorCode.OnlineEarningAttrNotFullySynced;
            }

            if (clientDirtyAttr == null) {
                clientDirtyAttr = proto_OnlineEarningData.newBuilder();
            }

            dirtyFieldCnt += player.getUserAttr().getOnlineEarningData()
                    .collectDirtyToDto(clientDirtyAttr, AbstractAttrObj.attrNoCsTags);

            return NKErrorCode.OK;
        }
    }

    private class OnIAA implements EventConsumer {

        @SubscribeEvent(routers = EventRouterType.ERT_ConsumerPlayerIAAOp)
        public NKErrorCode onEvent(PlayerIAAOpEvent event) throws NKRuntimeException {
            var info = getCurrentInfo();
            if (!isActivated()) {
                LOGGER.debug("skip since not open, uid:{} id:{}", player.getUid(), info.getId());
                return NKErrorCode.OK;
            }

            var conf = OnlineEarningConfData.getInstance().get(info.getId());
            if (conf == null) {
                LOGGER.error("online earning conf not found, uid:{} id:{}", player.getUid(), info.getId());
                return NKErrorCode.OK;
            }

            if (event.getType() == IAAType.IAAT_OnlineEarningTask_VALUE) {

                // 刷新活跃时间戳
                keepalive();

                if (event.getParam().isEmpty()) {
                    LOGGER.error("invalid param, uid:{} id:{}", player.getUid(), info.getId());
                    return NKErrorCode.OK;
                }

                int taskId = event.getParam().get(0);
                var taskConf = TaskConfData.getInstance().getTaskConf(taskId);
                if (taskConf == null) {
                    LOGGER.error("invalid task, uid:{} task:{}", player.getUid(), taskId);
                    return NKErrorCode.OK;
                }

                var handler = subModules.getTaskHandler(taskConf.getType().getNumber());
                if (handler == null) {
                    LOGGER.error("task handler not found, uid:{} id:{} type:{}", player.getUid(), info.getId(),
                            taskConf.getType());
                    return NKErrorCode.OK;
                }

                var errorCode = handler.handleIAAOp(event);
                if (errorCode.hasError()) {
                    LOGGER.error("task handle iaa op failed, uid:{} id:{} type:{} err:{}", player.getUid(),
                            info.getId(), taskConf.getType(), errorCode);
                }

                return NKErrorCode.OK;
            } else if (event.getType() == IAAType.IAAT_OnlineEarningWithdrawal_VALUE) {

                // 刷新活跃时间戳
                keepalive();

                if (event.getParam().isEmpty()) {
                    LOGGER.error("invalid param, uid:{} id:{}", player.getUid(), info.getId());
                    return NKErrorCode.OK;
                }

                var handler = subModules.get(Withdrawal.class);
                if (handler == null) {
                    LOGGER.error("handler not found, uid:{} id:{}", player.getUid(), info.getId());
                    return NKErrorCode.OK;
                }

                var errorCode = handler.handleIAAOp(event);
                if (errorCode.hasError()) {
                    LOGGER.error("task handle iaa op failed, uid:{} id:{} param:{} err:{}", player.getUid(),
                            info.getId(), event.getParam(), errorCode);
                }

                return NKErrorCode.OK;
            }

            return NKErrorCode.OK;
        }

        @Override
        public boolean isDestroyed() {
            return false;
        }
    }

    private class OnCoinChange implements EventConsumer {

        @SubscribeEvent(routers = EventRouterType.ERT_ConsumerPlayerChangeCoin)
        public NKErrorCode onEvent(PlayerChangeCoinEvent event) throws NKRuntimeException {
            var info = getCurrentInfo();
            if (!isActivated()) {
                return NKErrorCode.OK;
            }

            var conf = OnlineEarningConfData.getInstance().get(info.getId());
            if (conf == null) {
                LOGGER.error("online earning conf not found, uid:{} id:{}", player.getUid(), info.getId());
                return NKErrorCode.OK;
            }

            // 过期清理，不显示在操作记录里
            if (event.getReason() == ItemChangeReason.ICR_OnlineEarningExpire_VALUE) {
                LOGGER.debug("skip expire item remove record, uid:{}", player.getUid());
                return NKErrorCode.OK;
            }

            // check withdraw
            var withdraw = conf.getWithdraw();
            if (withdraw.getCoinType() != 0) {
                appendIfExists(OnlineEarningOpType.OEOT_CashChange_VALUE, withdraw.getCoinType(),
                        event.getChangeCoinMap(), event.getReason(), event.getSubReason());
            }

            // check gold
            var gold = conf.getGold();
            if (gold.getCoinType() != 0) {
                appendIfExists(OnlineEarningOpType.OEOT_GoldCoinChange_VALUE, gold.getCoinType(),
                        event.getChangeCoinMap(), event.getReason(), event.getSubReason());
            }

            return NKErrorCode.OK;
        }

        private void appendIfExists(int opType, int coinType, Map<Integer, ChangedItemInfo.Builder> changeMap,
                int reason, long subReason) {
            var changeItem = changeMap.getOrDefault(coinType, null);
            if (changeItem == null) {
                return;
            }

            if (changeItem.getCountAfterChange() == changeItem.getCountBeforeChange()) {
                return;
            }

            OnlineEarningItemChangeOp.Builder builder = OnlineEarningItemChangeOp.newBuilder().setItemId(coinType)
                    .setDelta(changeItem.getCountAfterChange() - changeItem.getCountBeforeChange()).setReason(reason)
                    .setSubReason(subReason);

            if (reason == ItemChangeReason.ICR_OnlineEarningTask_VALUE
                    || reason == ItemChangeReason.ICR_TaskComplete_VALUE
                    || reason == ItemChangeReason.ICR_OnlineEarningCommonTask_VALUE) {
                var taskGroupConf = TaskGroupData.getInstance().getTaskGroupByTaskId((int) subReason);
                int taskGroupId = taskGroupConf == null ? 0 : taskGroupConf.getId();
                int taskType = taskGroupConf == null ? 0 : taskGroupConf.getType().getNumber();
                builder.addExtraReasons(taskType).addExtraReasons(taskGroupId);
            }

            var op = subModules.get(OpRecord.class);
            if (op != null) {
                NKErrorCode errorCode = op.append(opType, OnlineEarningOp.newBuilder().setItemChange(builder));
                if (errorCode.hasError()) {
                    LOGGER.error("append op record failed, uid:{} coin:{} before:{} after:{} err:{}", player.getUid(),
                            coinType, changeItem.getCountBeforeChange(), changeItem.getCountAfterChange(), errorCode);
                }
            }
        }

        @Override
        public boolean isDestroyed() {
            return false;
        }
    }

    private class OnAmsResult implements EventConsumer {

        @SubscribeEvent(routers = EventRouterType.ERT_Player)
        public NKErrorCode onEvent(AmsItemResultEvent event) throws NKRuntimeException {
            var info = getCurrentInfo();
            if (!isActivated()) {
                LOGGER.debug("skip since not open, uid:{} id:{}", player.getUid(), info.getId());
                return NKErrorCode.OK;
            }

            var param = event.getParam();
            var result = event.getResult();
            var price = findPrice(param.getAmsId());

            if (price.isPresent()) {
                NKErrorCode errorCode = NKErrorCode.forNumberOrUnknown(result.getResult());
                if (errorCode.hasError()) {
                    LOGGER.error("withdraw result failed, uid:{} amsId:{} price:{} result:{} err:{}", player.getUid(),
                            param.getAmsId(), price, result.getResult(), errorCode);
                }

                TlogFlowMgr.sendPlayToEarnWithdrawFlow(player, errorCode.isOk() ? 1 : 2,
                        String.format("%.2f", 0.01 * price.get()), errorCode, param.getBillNo());
            }

            return NKErrorCode.OK;
        }

        @Override
        public boolean isDestroyed() {
            return false;
        }

        private int findAmsItemId(ResHolder resHolder, int amsId) {
            var itemIds = BackpackItem.getInLoadingInstance(resHolder).getItemIdByUseType(ItemUseTypeOperate.IUTO_AMS);
            for (var itemId : itemIds) {
                var conf = BackpackItem.getInLoadingInstance(resHolder).get(itemId);
                if (conf == null || conf.getUseParamCount() == 0) {
                    continue;
                }

                if (conf.getUseParam(0) == amsId) {
                    return itemId;
                }
            }

            return 0;
        }

        private Optional<Long> findPrice(int amsId) {
            var resHolder = ResLoader.getResHolder();

            var amsItemId = findAmsItemId(resHolder, amsId);
            if (amsItemId == 0) {
                LOGGER.error("cannot find item id by ams id, uid:{} amsId:{}", player.getUid(), amsId);
                return Optional.empty();
            }

            var itemIds = BackpackItem.getInLoadingInstance(resHolder)
                    .getItemIdByUseType(ItemUseTypeOperate.IUTO_OnlineEarningWithdrawal);
            for (var itemId : itemIds) {
                var conf = BackpackItem.getInLoadingInstance(resHolder).get(itemId);
                if (conf == null || conf.getUseParamCount() < 4) {
                    continue;
                }

                if (conf.getUseParam(2) == amsItemId) {
                    return Optional.of((long) conf.getUseParam(1));
                }
            }

            LOGGER.error("withdraw item not found, uid:{} amsId:{} amsItem:{}", player.getUid(), amsId, amsItemId);
            return Optional.empty();
        }
    }

    private class OnTaskFinish implements EventConsumer {

        @SubscribeEvent(routers = EventRouterType.ERT_Player)
        public NKErrorCode onEvent(TaskFinishEvent event) throws NKRuntimeException {
            var info = getCurrentInfo();
            if (!isActivated()) {
                return NKErrorCode.OK;
            }

            var taskGroupConf = TaskGroupData.getInstance().getTaskGroupByTaskId(event.getTaskId());
            if (taskGroupConf != null && info.getCommonTaskGroups().contains(taskGroupConf.getId())) {
                LOGGER.debug("finish online earning common task, uid:{} taskId:{} group:{} filter:{}", player.getUid(),
                        event.getTaskId(), taskGroupConf.getId(), info.getCommonTaskGroupsList());
                keepalive();
            }

            return NKErrorCode.OK;
        }

        @Override
        public boolean isDestroyed() {
            return false;
        }
    }
}
