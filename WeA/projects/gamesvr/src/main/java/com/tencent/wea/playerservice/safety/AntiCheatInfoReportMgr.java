package com.tencent.wea.playerservice.safety;

import com.google.protobuf.ByteString;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.timer.TimeoutAction;
import com.tencent.nk.timer.TimerType;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.tconnd.Session;
import com.tencent.tconnd.TconndManager;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.tss.TssExecuteHandler;
import com.tencent.tss.TssManager;
import com.tencent.ugcsafe.UicEnum;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.playerref.PlayerRef;
import com.tencent.wea.playerservice.playerref.PlayerRefMgr;
import com.tencent.wea.protocol.CsPlayer;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.xlsRes.keywords.TconndApiAccount;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 反外挂信息上报管理类
 */
public class AntiCheatInfoReportMgr implements TssExecuteHandler {
    private static final Logger LOGGER = LogManager.getLogger(AntiCheatInfoReportMgr.class);

    private final ConcurrentLinkedQueue<AntiCheatUserInfo> loginQueue = new ConcurrentLinkedQueue<>();  // 反外挂用户登录队列
    private final ConcurrentLinkedQueue<AntiCheatUserInfo> logoutQueue = new ConcurrentLinkedQueue<>(); // 反外挂用户登出队列
    private final ConcurrentLinkedQueue<AntiCheatUserInfo> tickQueue = new ConcurrentLinkedQueue<>();   // 反外挂心跳队列

    private final ConcurrentHashMap<Long, Boolean> userLoginFlagMap = new ConcurrentHashMap<>();    // 反外挂用户初始化成功信息map

    private static final Integer DEFAULT_ONLINE_QUEUE_MAX_SIZE = 4096;      // 反外挂用户登录/登出队列默认大小
    private static final Integer DEFAULT_ONLINE_PROCESS_CNT = 20;           // 反外挂用户登录/登出每轮处理数据个数
    private static final Integer DEFAULT_TICK_QUEUE_MAX_SIZE = 10240;       // 反外挂心跳队列默认大小
    private static final Integer DEFAULT_TICK_PROCESS_CNT = 100;            // 反外挂心跳每轮处理数据个数

    private static final Integer DEFAULT_TIMER_DELAY_TIME = 100;                // 反外挂处理定时器默认延迟生效时间
    private static final Integer DEFAULT_ONLINE_TIMER_TIME_INTERVAL = 100;      // 反外挂登录登出定时器默认时间间隔
    private static final Integer DEFAULT_TICK_TIMER_TIME_INTERVAL = 250;        // 反外挂心跳定时器默认时间间隔

    private long queueMonitorReportTime = Framework.currentTimeMillis();        // 反外挂队列数据上报监控时间戳
    private static final Integer QUEUE_MONITOR_REPORT_INTERVAL = 60 * 1000;          // 反外挂队列数据监控上报间隔时间

    @Override
    public void sendAntiCheatInfoToClient(TssManager.AntiCheatInfo cheatInfo) {

        // 反外挂信息为空直接返回
        if (cheatInfo == null) {
            return;
        }

        try {
            // 反外挂信息上报开关判断
            if (!isAntiCheatInfoReportEnabled()) {
                return;
            }

            // 发送反外挂信息给客户端
            sendAntiCheatDataToClient(cheatInfo.getOpenId(), cheatInfo.getUid(), cheatInfo.getAntiData());
        } catch (Exception ex) {
            LOGGER.error("sendAntiCheatInfoToClient catch exception, ", ex);
        }
    }

    private static class InstanceHolder {
        public static AntiCheatInfoReportMgr instance = new AntiCheatInfoReportMgr();
    }

    public static AntiCheatInfoReportMgr getInstance() {
        return InstanceHolder.instance;
    }

    /**
     * 管理类初始化
     * @return 0 success
     * @throws NKCheckedException
     */
    public int init() throws NKCheckedException {

        // 反外挂登录登出定时器初始化
        ServerEngine.getInstance().getDefaultService().callJob(() ->
                        CurrentExecutorUtil.addRepeatTimerWithMillisecs(TimerType.AntiCheatLoginAndLogoutTimer,
                                getAntiCheatTimerDelayTime(), getAntiCheatOnlineTimerTimeInterval(), true, onlineTimer),
                "AntiCheatLoginAndLogoutTimer", false);

        // 反外挂心跳定时器初始化
        ServerEngine.getInstance().getDefaultService().callJob(() ->
                        CurrentExecutorUtil.addRepeatTimerWithMillisecs(TimerType.AntiCheatTickTimer,
                                getAntiCheatTimerDelayTime(), getAntiCheatTickTimerTimeInterval(), true, tickTimer),
                "AntiCheatTickTimer", false);
        LOGGER.info("AntiCheatInfoReportMgr init success");

        return 0;
    }

    /**
     * 将用户添加到反外挂登录队列中
     * @param player 用户信息
     */
    public void addLoginInfo(Player player) {

        // 用户为空直接返回
        if (player == null) {
            return;
        }

        try {
            // 反外挂功能开关判断
            if (!isAntiCheatInfoReportEnabled()) {
                // 若全局反外挂功能开闭, 但玩家在反外挂场景有效, 推送设置标记为false的反外挂初始化消息给客户端
                if (checkUserIsValidInAntiCheatScene(player, true)) {
                    sendPlayerAntiCheatInfoInitNtf(player.getUid(), false);
                }
                return;
            }

            // 若玩家在反外挂场景无效, 直接返回
            if (!checkUserIsValidInAntiCheatScene(player, true)) {
                return;
            }

            // 针对已经初始化反外挂成功的账号, 直接发送ntf消息给客户端
            if (userLoginFlagMap.containsKey(player.getUid()) && userLoginFlagMap.get(player.getUid())) {
                sendPlayerAntiCheatInfoInitNtf(player.getUid(), true);
                return;
            }

            // 登录队列已满, 直接返回
            if (loginQueue.size() >= getOnlineQueueSize()) {
                LOGGER.error("login queue is full, size:{}", loginQueue.size());
                return;
            }

            // 添加登录信息到队列中
            AntiCheatUserInfo userInfo = new AntiCheatUserInfo(player.getOpenId(), player.getAccountType().getNumber(),
                    player.getPlatId(), player.getUid(), player.getName(), "");
            loginQueue.offer(userInfo);

            boolean antiCheatLogSwitch = PropertyFileReader.getRealTimeBooleanItem("anti_cheat_log_switch", false);
            if (antiCheatLogSwitch && LOGGER.isDebugEnabled()) {
                LOGGER.debug("add anti cheat login info, info:{}, size:{}", userInfo.toString(), loginQueue.size());
            }
        } catch (Exception ex) {
            LOGGER.error("addLoginInfo catch exception, uid:{}, ", player.getUid(), ex);
        }
    }

    /**
     * 将用户添加到反外挂登出队列中
     * @param player 用户信息
     */
    public void addLogoutInfo(Player player) {

        // 用户为空直接返回
        if (player == null) {
            return;
        }

        try {
            // 反外挂功能开关判断
            if (!isAntiCheatInfoReportEnabled()) {
                return;
            }

            // 若玩家在反外挂场景无效, 直接返回
            if (!checkUserIsValidInAntiCheatScene(player, false)) {
                return;
            }

            // 登出队列已满, 直接返回
            if (logoutQueue.size() >= getOnlineQueueSize()) {
                LOGGER.error("logout queue is full, size:{}", logoutQueue.size());
                return;
            }

            // 添加登出信息到队列中
            AntiCheatUserInfo userInfo = new AntiCheatUserInfo(player.getOpenId(), player.getAccountType().getNumber(),
                    player.getPlatId(), player.getUid(), player.getName(), "");
            logoutQueue.offer(userInfo);

            boolean antiCheatLogSwitch = PropertyFileReader.getRealTimeBooleanItem("anti_cheat_log_switch", false);
            if (antiCheatLogSwitch && LOGGER.isDebugEnabled()) {
                LOGGER.debug("add anti cheat logout info, info:{}, size:{}", userInfo.toString(), logoutQueue.size());
            }
        } catch (Exception ex) {
            LOGGER.error("addLogoutInfo catch exception, uid:{}, ", player.getUid(), ex);
        }
    }

    /**
     * 将用户添加到反外挂心跳队列中
     * @param player 用户信息
     */
    public void addTickInfo(Player player) {

        // 用户为空直接返回
        if (player == null) {
            return;
        }

        try {
            // 反外挂功能开关判断
            if (!isAntiCheatInfoReportEnabled()) {
                return;
            }

            // 若玩家在反外挂场景无效, 直接返回
            if (!checkUserIsValidInAntiCheatScene(player, false)) {
                return;
            }

            // 登出队列已满, 直接返回
            if (tickQueue.size() >= getTickQueueSize()) {
                LOGGER.error("tick queue is full, size:{}", tickQueue.size());
                return;
            }

            // 添加登出信息到队列中
            AntiCheatUserInfo userInfo = new AntiCheatUserInfo(player.getOpenId(), player.getAccountType().getNumber(),
                    player.getPlatId(), player.getUid(), player.getName(), "");
            tickQueue.offer(userInfo);

            boolean antiCheatLogSwitch = PropertyFileReader.getRealTimeBooleanItem("anti_cheat_log_switch", false);
            if (antiCheatLogSwitch && LOGGER.isDebugEnabled()) {
                LOGGER.debug("add anti cheat tick info, info:{}, size:{}", userInfo.toString(), tickQueue.size());
            }
        } catch (Exception ex) {
            LOGGER.error("addTickInfo catch exception, uid:{}, ", player.getUid(), ex);
        }
    }

    /**
     * 发送反外挂上报信息给ace平台
     * @param player
     * @param antiData
     */
    public void sendAntiCheatReportInfoToAce(Player player, byte[] antiData) {

        // 用户为空直接返回
        if (player == null) {
            return;
        }

        try {
            // 反外挂功能开关判断
            if (!isAntiCheatInfoReportEnabled()) {
                return;
            }

            // 若需上报的反外挂数据为空, 直接返回
            if (antiData.length == 0) {
                return;
            }

            // 若玩家在反外挂场景无效, 直接返回
            if (!checkUserIsValidInAntiCheatScene(player, false)) {
                return;
            }

            // 开启新的协程job进行异步处理
            CurrentExecutorUtil.runJob(() -> {
                try {
                    // 发送反外挂数据给ace平台
                    TssManager.getInstance().antiSendDataToAce(player.getOpenId(), UicEnum.ACCOUNT_TYPE.ACCOUNT_TYPE_COMMON,
                            UicEnum.PLATFORM.PC, player.getUid(), player.getAccountType().getNumber(),
                            antiData, "", UicEnum.transferPlatId(player.getPlatId()).getValue());
                } catch (Exception ex) {
                    LOGGER.error("sendAntiCheatReportInfoToAce catch exception, uid:{}, ", player.getUid(), ex);
                }

                return null;
            }, "antiSendDataToAceJob", false);
        } catch (Exception ex) {
            LOGGER.error("sendAntiCheatReportInfoToAce catch exception, uid:{}, ", player.getUid(), ex);
        }
    }

    /**
     * 发送反外挂初始化通知给客户端（全局开关关闭时发送false
     * @param player 玩家信息对象
     */
    public void sendPlayerAntiCheatInfoInitNtf(Player player) {

        if (player == null) {
            return;
        }

        // 若玩家在反外挂场景无效, 直接返回
        if (!checkUserIsValidInAntiCheatScene(player, true)) {
            return;
        }

        sendPlayerAntiCheatInfoInitNtf(player.getUid(), isAntiCheatInfoReportEnabled());
    }

    /**
     * 发送反外挂上报信息给客户端
     * @param openId 用户openid
     * @param uid 用户uid
     * @param antiData 反外挂数据
     */
    private void sendAntiCheatDataToClient(String openId, long uid, byte[] antiData) {

        // 玩家没有初始化反外挂功能成功, 直接返回
        if (!userLoginFlagMap.containsKey(uid)) {
            return;
        }

        try {
            // 基于uid获取连接session
            Session session = TconndManager.getInstance().getSessionByUid(uid);
            if (session == null) {
                return;
            }

            // 拼接ntf消息数据
            CsPlayer.PlayerAntiCheatInfoToClientNtf.Builder ntfBuilder = CsPlayer.PlayerAntiCheatInfoToClientNtf.newBuilder();
            ntfBuilder.setAntiData(ByteString.copyFrom(antiData));

            // 发送ntf消息给客户端
            session.sendNtfMsg(MsgTypes.MSG_TYPE_PLAYERANTICHEATINFOTOCLIENTNTF, ntfBuilder, null);
        } catch (Exception ex) {
            LOGGER.error("sendAntiCheatDataToClient catch exception, openId:{}, uid:{}, ", openId, uid, ex);
        }
    }

    /**
     * 反外挂登录信息处理逻辑
     */
    private Callable loginScanner = () -> {

        AntiCheatUserInfo userInfo;
        int num = 0;

        while ((userInfo = loginQueue.poll()) != null) {
            try {
                AntiCheatUserInfo finalUserInfo = new AntiCheatUserInfo(userInfo.getOpenId(), userInfo.getAccountType(),
                        userInfo.getPlatId(), userInfo.getUid(), userInfo.getNickName(), userInfo.getClientIp());

                CurrentExecutorUtil.runJob(() -> {
                    try {
                        // 新增用户到ace平台
                        int ret = TssManager.getInstance().antiAddUser(finalUserInfo.getOpenId(),
                                UicEnum.ACCOUNT_TYPE.ACCOUNT_TYPE_COMMON, UicEnum.PLATFORM.PC,
                                finalUserInfo.getUid(), finalUserInfo.getNickName(), finalUserInfo.getClientIp(),
                                finalUserInfo.getAccountType(), "", UicEnum.transferPlatId(finalUserInfo.getPlatId()).getValue());
                        if (ret != 0) {
                            LOGGER.error("antiAddUser failed, uid:{}, ret:{}", finalUserInfo.getUid(), ret);
                            // 发送反外挂信息初始化ntf给客户端
                            sendPlayerAntiCheatInfoInitNtf(finalUserInfo.getUid(), false);
                        } else {
                            // 发送反外挂信息初始化ntf给客户端
                            sendPlayerAntiCheatInfoInitNtf(finalUserInfo.getUid(), true);

                            // 将用户添加到初始化反外挂功能成功的map中
                            userLoginFlagMap.put(finalUserInfo.getUid(), true);
                        }
                    } catch (Exception ex) {
                        LOGGER.error("loginScanner runJob catch exception, uid:{}, ", finalUserInfo.getUid(), ex);
                    }

                    return null;
                }, "antiAddUserJob", false);
            } catch (Exception ex) {
                LOGGER.error("loginScanner runJob catch exception, uid:{}, ", userInfo.getUid(), ex);
            }

            // 处理数量控制
            if (++num >= getTickProcessCnt()) {
                break;
            }
        }

        return 0;
    };

    /**
     * 反外挂登出信息处理逻辑
     */
    private Callable logoutScanner = () -> {

        AntiCheatUserInfo userInfo;
        int num = 0;

        while ((userInfo = logoutQueue.poll()) != null) {
            // 用户没有初始化反外挂功能成功, 直接返回
            if (!userLoginFlagMap.containsKey(userInfo.getUid())) {
                continue;
            }

            try {
                AntiCheatUserInfo finalUserInfo = new AntiCheatUserInfo(userInfo.getOpenId(), userInfo.getAccountType(),
                        userInfo.getPlatId(), userInfo.getUid(), userInfo.getNickName(), userInfo.getClientIp());
                // 从初始化反外挂功能成功的map中移除用户
                userLoginFlagMap.remove(finalUserInfo.getUid());

                CurrentExecutorUtil.runJob(() -> {
                    try {
                        // 从ace平台删除用户
                        int ret = TssManager.getInstance().antiDeleteUser(finalUserInfo.getOpenId(),
                                UicEnum.ACCOUNT_TYPE.ACCOUNT_TYPE_COMMON, UicEnum.PLATFORM.PC,
                                finalUserInfo.getUid(), finalUserInfo.getAccountType(), "",
                                UicEnum.transferPlatId(finalUserInfo.getPlatId()).getValue());
                        if (ret != 0) {
                            LOGGER.error("antiDeleteUser failed, uid:{}, ret:{}", finalUserInfo.getUid(), ret);
                        }

                    } catch (Exception ex) {
                        LOGGER.error("logoutScanner runJob catch exception, uid:{}, ", finalUserInfo.getUid(), ex);
                    }

                    return null;
                }, "antiDeleteUserJob", false);
            } catch (Exception ex) {
                LOGGER.error("logoutScanner runJob catch exception, uid:{}, ", userInfo.getUid(), ex);
            }

            // 处理数量控制
            if (++num >= getOnlineProcessCnt()) {
                break;
            }
        }

        return 0;
    };

    /**
     * 反外挂在线信息处理逻辑
     */
    private TimeoutAction onlineTimer = () -> {

        try {
            // 处理登录队列
            loginScanner.call();

            // 处理登出队列
            logoutScanner.call();
        } catch (Exception ex) {
            LOGGER.error("onlineScanner catch exception, ", ex);
        }
    };

    /**
     * 反外挂心跳信息处理逻辑
     */
    private Callable tickScanner = () -> {

        AntiCheatUserInfo userInfo;
        int num = 0;

        while ((userInfo = tickQueue.poll()) != null) {
            // 用户没有初始化反外挂功能成功, 直接跳过
            if (!userLoginFlagMap.containsKey(userInfo.getUid())) {
                continue;
            }

            try {
                AntiCheatUserInfo finalUserInfo = new AntiCheatUserInfo(userInfo.getOpenId(), userInfo.getAccountType(),
                        userInfo.getPlatId(), userInfo.getUid(), userInfo.getNickName(), userInfo.getClientIp());
                CurrentExecutorUtil.runJob(() -> {
                    try {
                        // 反外挂用户心跳上报
                        TssManager.getInstance().antiTickUser(finalUserInfo.getOpenId(), UicEnum.ACCOUNT_TYPE.ACCOUNT_TYPE_COMMON,
                                UicEnum.PLATFORM.PC, finalUserInfo.getUid(), finalUserInfo.getNickName(),
                                finalUserInfo.getClientIp(), finalUserInfo.getAccountType(), "",
                                UicEnum.transferPlatId(finalUserInfo.getPlatId()).getValue());
                    } catch (Exception ex) {
                        LOGGER.error("antiTickUser failed, uid:{}, ", finalUserInfo.getUid(), ex);
                    }

                    return null;
                }, "antiTickUserJob", false);
            } catch (Exception ex) {
                LOGGER.error("tickScanner runJob catch exception, uid:{}, ", userInfo.getUid(), ex);
            }

            // 处理数量控制
            if (++num >= getTickProcessCnt()) {
                LOGGER.info("anti cheat tick queue size, size:{}", tickQueue.size());
                break;
            }
        }

        return 0;
    };

    /**
     * 反外挂心跳信息定时处理器
     */
    private TimeoutAction tickTimer = () -> {

        try {
            antiCheatQueueSizeMonitorReport();

            // 处理心跳队列
            tickScanner.call();
        } catch (Exception ex) {
            LOGGER.error("tickScanner catch exception, ", ex);
        }
    };

    /**
     * 发送反外挂登录成功初始化通知
     * @param uid 用户uid
     * @oaram initFlag 初始化是否成功标记
     */
    private void sendPlayerAntiCheatInfoInitNtf(long uid, boolean initFlag) {

        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
            if (playerRef != null) {
                // 发送反外挂登录操作初始化ntf给客户端
                playerRef.sendPlayerAntiCheatInfoInitNtf(initFlag);
            }
        } catch (Exception ex) {
            LOGGER.error("sendPlayerAntiCheatInfoInitNtf catch exception, uid:{}, ", uid, ex);
        }
    }

    /**
     * 检查用户在反外挂场景是否有效
     * @param player 玩家
     * @param isLogin 是否登录
     * @return true/false  在反外挂功能有效范围内返回true, 反之返回false
     */
    private boolean checkUserIsValidInAntiCheatScene(Player player, boolean isLogin) {

        // 玩家为空, 返回false
        if (player == null) {
            return false;
        }

        // 非压测环境下, 若账号为机器人账号, 返回false
        if (!ServerEngine.getInstance().isPressTest() && Player.isRobotOpenid(player.getOpenId())) {
            return false;
        }

        if (isLogin) {
            // 登录场景
            // 判断用户是否在反外挂测试白名单里
            if (!checkAntiCheatInfoReportWhiteUid(player.getUid())) {
                // 账号为非白名单用户场景下
                // 若账号为非pc平台账号, 返回false
                return checkIsPcAccount(player) || checkIsSteamAccount(player);
            }
        } else {
            // 非登录场景
            // 若玩家没有初始化反外挂功能成功, 返回false
            return userLoginFlagMap.containsKey(player.getUid());
        }

        return true;
    }

    /**
     * 判断是否在反外挂功能白名单里面
     * @param uid 用户uid
     * @return true/false  在白名单中返回true, 反之返回false
     */
    private boolean checkAntiCheatInfoReportWhiteUid(long uid) {

        if (!isAntiCheatWhiteUidEnabled()) {
            return false;
        }

        String antiCheatWhiteStr = getAntiCheatWhiteUidStr();
        if (antiCheatWhiteStr.isEmpty()) {
            return false;
        }

        Set<String> uidWhiteSet = new HashSet<>(Arrays.asList(antiCheatWhiteStr.split(",")));
        return uidWhiteSet.contains(String.valueOf(uid));
    }

    /**
     * 反外挂队列大小监控上报
     */
    private void antiCheatQueueSizeMonitorReport() {

        long currentTimeMs = Framework.currentTimeMillis();
        if (currentTimeMs - queueMonitorReportTime >= QUEUE_MONITOR_REPORT_INTERVAL) {
            queueMonitorReportTime = currentTimeMs;

            // 上报各个队列大小数据
            Monitor.getInstance().add.total(MonitorId.attr_anti_cheat_login_queue_size, loginQueue.size());
            Monitor.getInstance().add.total(MonitorId.attr_anti_cheat_logout_queue_size, logoutQueue.size());
            Monitor.getInstance().add.total(MonitorId.attr_anti_cheat_tick_queue_size, tickQueue.size());
        }
    }

    /**
     * 判断是否是pc账号
     * @param player 玩家数据
     * @return true: 是pc账号; false: 非pc账号
     */
    private boolean checkIsPcAccount(Player player) {

        if (player == null) {
            return false;
        }

        if (player.getSession() == null || player.getSession().getClientInfo() == null ||
                !player.getSession().getClientInfo().getIsPC()) {
            return false;
        }

        // 若账号为编辑器账号, 返回false
        return player.getPlatId() == 0 || player.getPlatId() == 1;
    }

    /**
     * 判断是否是steam账号
     * @param player 玩家数据
     * @return true: 是steam账号; false: 非steam账号
     */
    private boolean checkIsSteamAccount(Player player) {

        if (player == null) {
            return false;
        }

        return player.getAccountType() == TconndApiAccount.TCONND_ITOP_CHANNEL_STEAM;
    }

    /**
     * 获取在线处理上报队列大小
     * @return 上报队列大小
     */
    private int getOnlineQueueSize() {
        return PropertyFileReader.getRealTimeIntItem("anti_cheat_info_report_online_queue_size", DEFAULT_ONLINE_QUEUE_MAX_SIZE);
    }

    /**
     * 获取每轮在线处理数据个数
     * @return 每轮处理数据个数
     */
    private int getOnlineProcessCnt() {
        return PropertyFileReader.getRealTimeIntItem("anti_cheat_info_report_online_process_cnt", DEFAULT_ONLINE_PROCESS_CNT);
    }

    /**
     * 获取心跳处理队列大小
     * @return 心跳处理队列大小
     */
    private int getTickQueueSize() {
        return PropertyFileReader.getRealTimeIntItem("anti_cheat_info_report_tick_queue_size", DEFAULT_TICK_QUEUE_MAX_SIZE);
    }

    /**
     * 获取每轮心跳处理数量
     * @return 每轮心跳处理数据个数
     */
    private int getTickProcessCnt() {
        return PropertyFileReader.getRealTimeIntItem("anti_cheat_info_report_tick_process_cnt", DEFAULT_TICK_PROCESS_CNT);
    }

    /**
     * 是否开启反外挂上报
     * @return true/false  开关
     */
    private boolean isAntiCheatInfoReportEnabled() {
        return PropertyFileReader.getRealTimeBooleanItem("enable_anti_cheat_info_report", true);
    }

    /**
     * 是否开启反外挂白名单
     * @return true/false  开关
     */
    private boolean isAntiCheatWhiteUidEnabled() {
        return PropertyFileReader.getRealTimeBooleanItem("enable_anti_cheat_white", true);
    }

    /**
     * 获取反外挂白名单列表
     * @return 白名单列表
     */
    private String getAntiCheatWhiteUidStr() {
        return PropertyFileReader.getRealTimeItem("anti_cheat_white_list", "");
    }

    /**
     * 获取反外挂定时器延迟时间
     * @return 反外挂定时器延迟时间
     */
    private int getAntiCheatTimerDelayTime() {
        return PropertyFileReader.getRealTimeIntItem("anti_cheat_timer_delay_time", DEFAULT_TIMER_DELAY_TIME);
    }

    /**
     * 获取反外挂在线定时器时间间隔
     * @return 反外挂在线定时器时间间隔
     */
    private int getAntiCheatOnlineTimerTimeInterval() {
        return PropertyFileReader.getRealTimeIntItem("anti_cheat_online_timer_time_interval", DEFAULT_ONLINE_TIMER_TIME_INTERVAL);
    }

    /**
     * 获取反外挂心跳定时器时间间隔
     * @return 反外挂心跳定时器时间间隔
     */
    private int getAntiCheatTickTimerTimeInterval() {
        return PropertyFileReader.getRealTimeIntItem("anti_cheat_tick_timer_time_interval", DEFAULT_TICK_TIMER_TIME_INTERVAL);
    }
}
