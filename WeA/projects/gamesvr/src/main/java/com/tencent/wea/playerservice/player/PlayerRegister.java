package com.tencent.wea.playerservice.player;


import com.tencent.ipdb.IpDbApi;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.resourceloader.resclass.RegionalConf;
import com.tencent.tcaplus.dao.OpenIdToUidDao;
import com.tencent.tconnd.Session;
import com.tencent.tconnd.TconndManager;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.tool.ServerInfoTool;
import com.tencent.util.VersionUtil;
import com.tencent.wea.playerservice.login.LoginStatMgr;
import com.tencent.wea.playerservice.login.LoginStepEnum;
import com.tencent.wea.playerservice.player.PlayerMgr.RemovePlayerReason;
import com.tencent.wea.playerservice.playerref.PlayerRefMgr;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.protocol.CsPlayer;
import com.tencent.wea.protocol.common.MetaDataType;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.xlsRes.ResRegional;
import ipdb.jni.IpInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 玩家注册
 *
 * <AUTHOR>
 * @date 2020/12/28
 */
public class PlayerRegister {

    private static final Logger LOGGER = LogManager.getLogger(PlayerMgr.class);
    // 默认国家地区代码 G1
    private static int DEFAULT_COUNRTY_CODE = 0;

    public static TcaplusDb.Player registerNewPlayer(CsPlayer.Login_C2S_Msg reqMsg, Session session) {
        long newuid = reqMsg.getRegisterUid();
        TcaplusDb.Player player = TcaplusDb.Player.newBuilder()
                .setUid(newuid).setPlatid(reqMsg.getPlatId())
                .setOpenid(session.getOpenid()).build();
        return player;
    }

    /**
     * 玩家注册
     *
     * @param reqMsg 要求的信息
     * @param session 会话
     * @return {@link Player}
     * @throws NKCheckedException nkchecked例外
     */
    public static Player registerPlayer(CsPlayer.Login_C2S_Msg reqMsg, Session session) throws NKCheckedException {
        session.markStopWatch("PlayerMgr.registerPlayer");
        long newuid = reqMsg.getRegisterUid();
        if (newuid <= 0) {
            NKErrorCode.PlayerRegisterFailUidError.throwError("PlayerRegisterFailUidError");
        }

        session.markStopWatch("UidGenerator.getInstance().getGuid");
        reqMsg = reqMsg.toBuilder().setRegisterUid(newuid).build();
        LOGGER.warn("registerPlayer, registerUid: {}, openid: {}", reqMsg.getRegisterUid(), reqMsg.getOpenid());

        // TODO 注册的时候设置session的uid, 这样注册过程中断线的时候可以找到uid排队
        // TODO session的多线程管理需要统一整理下
        session.setUid(newuid);
        TconndManager.getInstance().addUidSessionMap(session);
//        PlayerLogin.lockRegisterUid(session);
        Player p = null;
        try {
            int regitsterContextId = LoginStatMgr.startStep(reqMsg.getOpenid(), LoginStepEnum.LS_REGISTER, LoginStepEnum.LS_LOGIN);
            PlayerRefMgr.getInstance().setLoadingPlayer(newuid, true);
            
            int createPlayerContextId = LoginStatMgr.startStep(reqMsg.getOpenid(), LoginStepEnum.LS_CREATE_PLAYER, regitsterContextId);
            TcaplusDb.Player dbPlayer = registerNewPlayer(reqMsg, session);
            LoginStatMgr.endStep(reqMsg.getOpenid(), LoginStepEnum.LS_CREATE_PLAYER, createPlayerContextId);

            p = new Player(dbPlayer, 0);
            p.getUserAttr().getPlayerProfileInfo().setAccountType(session.getAccountType());
            p.getUserAttr().getPlayerPublicBasicInfo().setAccountType(session.getAccountType())
                    //设置注册分发渠道
                    .setRegChannelDis(reqMsg.getRegChannelDis())
                    .setTrackId(reqMsg.getTrackId())
                    .setRegisterPlat(reqMsg.getLoginPlat())
                    .setLoginPlat(reqMsg.getLoginPlat());
            p.getUserAttr().getBasicInfo().setIsRegistering(true);
            p.setLoginByPc(session.getClientInfo().systemSoftware.contains("Windows"));
            // 设置更新大版本时间
            long curClientVersion = VersionUtil.encodeClientVersion(reqMsg.getClientVersion());
            p.setLastUpdateMainVersion(curClientVersion);
            p.getUserAttr().getPlayerPublicGameSettings()
                    .setClientVersion64(VersionUtil.encodeClientVersion(reqMsg.getClientVersion()))
                    .setHideLocation(true);

            LOGGER.debug("get player by register new {}", p.getUid());
            if (!session.getAccessToken().isEmpty()) {
                p.getUserAttr().getPlayerProfileInfo().setAccessToken(session.getAccessToken());
            } else {
                p.getUserAttr().getPlayerProfileInfo().setAccessToken(reqMsg.getAccessToken());
            }
            p.getUserAttr().getPlayerProfileInfo().setRegisterDeviceId(reqMsg.getDeviceId());

            //设置国家代码
            p.getUserAttr().getPlayerPublicBasicInfo().setRegisterRegionId(DEFAULT_COUNRTY_CODE);

            if (ServerInfoTool.isOverseaEnv()) {
                //国际化环境使用IP推测出国家代码
                if (!setPlayerCounrtyCodeBySession(session, p)) {
                    LOGGER.error("setPlayerCounrtyCode error! player[{}]", p.getUid());
                }
            }
            //设置AB实验客户端过滤字段 要在onRegister前设置
            p.getAbTestMgr().setAbTestClientFilterLabels(reqMsg.getAbTestClientFilterLabelList());

            p.onRegister();
            session.markStopWatch("onRegister");
            //到这里，数据约束已经完成，player的数据是正确的
            //insert openidtouid
//            boolean issucc = OpenIdToUidDao.insertOpenidUidMap(p.getOpenId(), p.getPlatId(), p.getUid()); // not fini
//            if (!issucc) {
//                NKErrorCode.UnknownError.throwError("insertOpendUidMap fail");
//            }

            session.markStopWatch("PlayerLocation.getInstance().insertZoneTable");
            int insertDbContextId = LoginStatMgr.startStep(reqMsg.getOpenid(), LoginStepEnum.LS_INSERT_DB, regitsterContextId);
            //copy data to dbPlayer and insert new Player record
            boolean issucc = p.insertPlayer();
            if (!issucc) {
                Monitor.getInstance().add.total(MonitorId.attr_reg_insert_player_fail, 1);
                NKErrorCode.PlayerRegisterFailInsertPlayerFail.throwError("insertPlayer fail");
            }
            PlayerRefMgr.getInstance().addPlayer(p);
            session.markStopWatch("insertPlayer");
            issucc = OpenIdToUidDao.insertOpenidUid(p.getOpenId(), p.getPlatId(), p.getUid(),
                    session.getAccountType().getNumber(), true, 0);
            if (!issucc) {
                Monitor.getInstance().add.total(MonitorId.attr_reg_insert_openid2uid_fail, 1);
                NKErrorCode.PlayerRegisterFailInsertOpenIdToUidFail.throwError("insert OpenIdToUid fail");
            }
            LoginStatMgr.endStep(reqMsg.getOpenid(), LoginStepEnum.LS_INSERT_DB, insertDbContextId);
//            issucc = OpenIdToUidDao.updateOpenIdUidMapRegisterFini(p.getOpenId(), p.getPlatId(), p.getUid());
//            if (!issucc) {
//                NKErrorCode.UnknownError.throwError("updateOpendUidMapRegisterFini fail");
//            }
            session.markStopWatch("updateOpendUidMapRegisterFini");
            //到这里，玩家的数据已经注册并正确落地，之后开始常规流程,这之前需要把player put到loader里
            //注册流水
            TlogFlowMgr.sendModPlayerRegisterFlow(p, session.getClientInfo());
            p.load();
            if (ServerEngine.getInstance().getMetadataClient().onLoad(MetaDataType.MDT_Player, p.getUid()) != 0) {
                LOGGER.error("onLoad reportProxy fail");
            }
            session.markStopWatch("load");
            LoginStatMgr.endStep(reqMsg.getOpenid(), LoginStepEnum.LS_REGISTER, regitsterContextId);
            // 预先处理一下邀请关联
            if (reqMsg.hasInviterInfo() && reqMsg.getInviterInfo().getInviterUid() > 0) {
                p.setInviter(reqMsg.getInviterInfo().getInviterUid(), reqMsg.getInviterInfo().getActivityId(), false);
            }
        } catch (Exception e) {
            PlayerRefMgr.getInstance().removePlayer(newuid, RemovePlayerReason.RegisterException);
            LoginStatMgr.endStep(reqMsg.getOpenid(), LoginStepEnum.LS_INSERT_DB,
                    "insert db exception");
            LoginStatMgr.endStep(reqMsg.getOpenid(), LoginStepEnum.LS_REGISTER,
                    "register exception");
            throw e;
        } finally {
            PlayerRefMgr.getInstance().setLoadingPlayer(newuid, false);
        }
        p.afterRegister(); // after register可能会有player的获取，因此要放在playerLoader.put后面
        p.flushUserAttr();
        //LOGGER.info("register ok openid:{} uid:{} pointId:{} name:{}", reqMsg.getOpenid(), p.getUid(), p
        // .getUserAttr().getWorldInfo().getBigScenePos(), p.getName());
        return p;
    }

    /**
     * 设置玩家国家代码
     *
     * @param session 连接信息
     * @param player 新创建的玩家
     * @return 操作结果
     */
    private static boolean setPlayerCounrtyCodeBySession(Session session, Player player) {
        String ipAddr = session.getIpAddr();
        if (null == ipAddr) {
            // 缺少IP字段;
            LOGGER.error("setPlayerCounrtyCodeBySession error! missing ipAddr field! player[{}]", player.getUid());
            return false;
        }

        if (!isValidIP(ipAddr)) {
            // 客户端发送的IP格式非法
            LOGGER.error("setPlayerCounrtyCodeBySession error! invalid ipAddr format! player[{}] ipAddr[{}]",
                    player.getUid(), ipAddr);
            return false;
        }

        IpInfo result = new IpInfo();
        int res = IpDbApi.getInstance().Query(ipAddr, result);
        if (0 != res) {
            LOGGER.error("setPlayerCounrtyCodeBySession error! ipAddr query fail! player[{}] ipAddr[{}]",
                    player.getUid(), ipAddr);
            return false;
        }

        ResRegional.Regional regionConf = RegionalConf.getInstance().getByName(result.contry);
        if (null == regionConf) {
            // 未查询到相关地区
            LOGGER.error("setPlayerCounrtyCodeBySession error! regionConf is nul! player[{}] ipAddr[{}] contry[{}]",
                    player.getUid(), ipAddr, result.contry);
            return false;
        }

        int countryCode = regionConf.getId();
        LOGGER.debug("setPlayerCounrtyCodeBySession is ok! player[{}] ipAddr[{}] contry[{}] countryCode[{}]",
                player.getUid(), ipAddr, result.contry, countryCode);
        return setPlayerCounrtyCode(countryCode, player);
    }

    private static boolean setPlayerCounrtyCode(int countryCode, Player player) {
        if (null == player) {
            LOGGER.error("setPlayerCounrtyCode error! player is nul!");
            return false;
        }

        player.getUserAttr().getPlayerPublicBasicInfo().setRegisterRegionId(countryCode);
        LOGGER.debug("setPlayerCounrtyCode is ok! player[{}] countryCode[{}]", player.getUid(), countryCode);
        return true;
    }

    /**
     * IP格式校验
     *
     * @param ip 输入IP
     * @return 操作结果
     */
    private static boolean isValidIP(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        for (String part : parts) {
            try {
                int value = Integer.parseInt(part);
                if (value < 0 || value > 255) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }

        return true;
    }
}
