package com.tencent.wea.playerservice.cshandler.handler.activity;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.activity.implement.MultiBuyMultiGiftActivity;
import com.tencent.wea.playerservice.mall.MultiBuyActivityIntegration;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsMultiBuyActivity;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 多买多送活动折扣预览协议处理器
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public class MultiBuyDiscountPreviewMsgHandler extends AbstractGsClientRequestHandler {
    
    private static final Logger LOGGER = LogManager.getLogger(MultiBuyDiscountPreviewMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        CsMultiBuyActivity.MultiBuyDiscountPreview_C2S_Msg reqMsg = 
            (CsMultiBuyActivity.MultiBuyDiscountPreview_C2S_Msg) request;
        
        LOGGER.debug("MultiBuyDiscountPreview request, uid:{}, activityId:{}, commodityId:{}", 
                    player.getUid(), reqMsg.getActivityId(), reqMsg.getCommodityId());
        
        CsMultiBuyActivity.MultiBuyDiscountPreview_S2C_Msg.Builder rspMsg = 
            CsMultiBuyActivity.MultiBuyDiscountPreview_S2C_Msg.newBuilder();
        
        try {
            // 获取活动实例
            MultiBuyMultiGiftActivity activity = (MultiBuyMultiGiftActivity) player.getActivityManager()
                    .getRunningActivityByActivityType(ActivityType.AT_MultiBuyMultiGift);
            
            // 获取商品配置
            MallCommodity commodityConf = MallCommodityConf.getInstance().get(reqMsg.getCommodityId());
            if (commodityConf == null) {
                LOGGER.warn("Commodity config not found, uid:{}, commodityId:{}", 
                           player.getUid(), reqMsg.getCommodityId());
                rspMsg.setCommodityId(reqMsg.getCommodityId())
                      .setOriginalPrice(0)
                      .setPreviewDiscount(100)
                      .setPreviewPrice(0)
                      .setWillUseDoubleDiscount(false)
                      .setEstimatedSaving(0);
                return rspMsg;
            }
            
            int originalPrice = commodityConf.getPrice();
            
            if (activity == null || !activity.isActivityCommodity(reqMsg.getCommodityId())) {
                // 不是活动商品，返回原价
                rspMsg.setCommodityId(reqMsg.getCommodityId())
                      .setOriginalPrice(originalPrice)
                      .setPreviewDiscount(100)
                      .setPreviewPrice(originalPrice)
                      .setWillUseDoubleDiscount(false)
                      .setEstimatedSaving(0);
                return rspMsg;
            }
            
            // 获取折扣预览
            MultiBuyActivityIntegration integration = new MultiBuyActivityIntegration(player);
            MultiBuyActivityIntegration.ActivityDiscountPreview preview = 
                integration.previewDiscount(reqMsg.getCommodityId(), originalPrice);
            
            // 构建响应
            rspMsg.setCommodityId(reqMsg.getCommodityId())
                  .setOriginalPrice(preview.getOriginalPrice())
                  .setPreviewDiscount(preview.getPreviewDiscount())
                  .setPreviewPrice(preview.getPreviewPrice())
                  .setWillUseDoubleDiscount(preview.willUseDoubleDiscount())
                  .setEstimatedSaving(preview.getEstimatedSaving());
            
            LOGGER.debug("MultiBuyDiscountPreview response, uid:{}, commodityId:{}, originalPrice:{}, " +
                        "previewPrice:{}, discount:{}, willUseDoubleDiscount:{}", 
                        player.getUid(), reqMsg.getCommodityId(), preview.getOriginalPrice(), 
                        preview.getPreviewPrice(), preview.getPreviewDiscount(), preview.willUseDoubleDiscount());
            
        } catch (Exception e) {
            LOGGER.error("Failed to handle MultiBuyDiscountPreview, uid:{}, commodityId:{}", 
                        player.getUid(), reqMsg.getCommodityId(), e);
            
            // 返回默认值
            int originalPrice = commodityConf != null ? commodityConf.getPrice() : 0;
            rspMsg.setCommodityId(reqMsg.getCommodityId())
                  .setOriginalPrice(originalPrice)
                  .setPreviewDiscount(100)
                  .setPreviewPrice(originalPrice)
                  .setWillUseDoubleDiscount(false)
                  .setEstimatedSaving(0);
        }
        
        return rspMsg;
    }
}
