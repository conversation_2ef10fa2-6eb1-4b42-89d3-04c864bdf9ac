package com.tencent.wea.playerservice.activity.implement;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.resourceloader.resclass.ActivityChapterTaskData;
import com.tencent.resourceloader.resclass.TaskConfData;
import com.tencent.resourceloader.resclass.TaskGroupData;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.ActivityUnit;
import com.tencent.wea.attr.ReturnLinearRedeemActivity;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.event.consumer.PlayerLinearRedeemActivityProcessTlogConsumer;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.task.RunTask;
import com.tencent.wea.protocol.CsActivity;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.xlsRes.ResActivity;
import com.tencent.wea.xlsRes.ResActivity.ActivityChapterTaskConfig;
import com.tencent.wea.xlsRes.ResTask.TaskConf;
import com.tencent.wea.xlsRes.ResTask.TaskGroup;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.TaskStatus;
import java.util.Collection;
import java.util.List;

/**
 * @program: WeA
 * @description: 线性兑换活动
 * @author: nichtsun
 * @create: 2023-04-10
 **/

public class LinearRedeemActivity extends TaskActivity {

    public static final int ACTIVITY_PARAM_SIZE = 4;

    public LinearRedeemActivity(Player player,
            ActivityUnit activityUnit) {
        super(player, activityUnit);
        // 注册消费者，进行进度的tlog上报
        if (player != null) {
            PlayerLinearRedeemActivityProcessTlogConsumer playerLinearRedeemActivityProcessTlogConsumer = new PlayerLinearRedeemActivityProcessTlogConsumer(
                    player, this.activityId);
            player.getEventSwitch().register(playerLinearRedeemActivityProcessTlogConsumer);
        }
    }

    @Override
    public void init(boolean isNew) {
        super.init(isNew);
    }

    @Override
    protected int getTaskGroupNumber() {
        // 进度任务组+每日任务组+累积任务组
        // 考虑后续有扩展可能，客户端将进度放到第一位，其余总和为一个列表展示
        return 3;
    }

    @Override
    public ActivityType getType() {
        return ActivityType.ATLinearRedeem;
    }

    @Override
    public void checkActivityRedPoint() {
        if (checkAllMainTaskComplete()) {
            delRedDot();
            return;
        }
        super.checkActivityRedPoint();
    }

    private boolean checkAllMainTaskComplete() {
        if (getActivityMainConfig().getActivityTaskGroupCount() > 0) {
            TaskGroup taskGroupConfig = TaskGroupData.getInstance().getTaskGroup(getActivityMainConfig().getActivityTaskGroup(0));
            if (taskGroupConfig != null) {
                boolean allMainTaskCompleteAndRewarded = true;
                for (int taskId : taskGroupConfig.getTaskIdListList()) {
                    TaskConf taskConf = TaskConfData.getInstance().getTaskConf(taskId);
                    if (taskConf == null) {
                        continue;
                    }
                    RunTask task = player.getTaskManager().getTask(taskId);
                    if (task != null && task.getStatus().getNumber() < TaskStatus.TS_Rewarded_VALUE) {
                        allMainTaskCompleteAndRewarded = false;
                        break;
                    }
                }
                if (allMainTaskCompleteAndRewarded) {
                    return true;
                }
            }
        }
        return false;
    }

    public NKErrorCode getLinearRedeemActivityPiggyBankCoin(CsActivity.ObtainedPiggyBankCoin_C2S_Msg reqMsg){
        List<Integer> supplementParamsList = getActivityMainConfig().getActivitySupplementParamsList();
        if (supplementParamsList.isEmpty()) {
            return NKErrorCode.ActivityLinearRedeemNotSupplementParamsConfigError;
        }else if (supplementParamsList.size() == 4) {
            ReturnLinearRedeemActivity returnLinearRedeem = getActivityUnit().getDetailData().getReturnLinearRedeem();
            if (returnLinearRedeem.getPiggyBank()==0){
                return NKErrorCode.ActivityLinearRedeemPiggyBankIsNull;
            }
            int itemType = supplementParamsList.get(1);
            ChangedItems changeItem = new ChangedItems(itemType,returnLinearRedeem.getPiggyBank(),
                    ItemChangeReason.ICR_GetLinearRedeemPiggyBank.getNumber(), "");
            changeItem.setActivityId(activityId);
            NKPair<NKErrorCode, ItemChangeDetails> addRes = player.getBagManager().AddItems2(
                    changeItem, true);
            NKErrorCode errorCode = addRes.getKey();
            if (!errorCode.isOk()) {
                LOGGER.error("get linear redeem piggy bank coin failed:{},{},{}",
                        player.getUid(), changeItem.toString(), errorCode);
                return errorCode;
            }
            returnLinearRedeem.clear();
            player.getUserAttrMgr().collectAndSyncDirtyToClient();
        }
        return NKErrorCode.OK;

    }

    @Override
    public void onMidNight() {
    }

    @Override
    public void afterLoginFinish() {
        List<Integer> supplementParamsList = getActivityMainConfig().getActivitySupplementParamsList();
        if (supplementParamsList.isEmpty()) {
            LOGGER.debug("player LinearRedeemActivity supplementParamsList is empty {} {}",player.getUid(),this.activityId);
        }else{
            if (supplementParamsList.size() == ACTIVITY_PARAM_SIZE) {
                long loginTimeMs = player.getUserAttr().getBasicInfo().getLoginTimeMs();
                long lastHeartBeatTimeMs = player.getUserAttr().getBasicInfo().getLastHeartBeatTimeMs();
                //如果存在时钟回拨，则不处理
                if (loginTimeMs < lastHeartBeatTimeMs) {
                    LOGGER.error("player LinearRedeemActivity time anomaly: loginTimeMs {} is earlier than lastHeartBeatTimeMs {} for uid {} activityId {}",
                            loginTimeMs, lastHeartBeatTimeMs, player.getUid(), this.activityId);
                    return;
                }
                ReturnLinearRedeemActivity returnLinearRedeem = getActivityUnit().getDetailData().getReturnLinearRedeem();
                int piggyBank = returnLinearRedeem.getPiggyBank();
                Integer maxAmount = supplementParamsList.get(3);
                Integer intervalTime = supplementParamsList.get(0);
                if (piggyBank < maxAmount){
                    long diffHours = (loginTimeMs - lastHeartBeatTimeMs) / 1000 / 60 / 60;
                    double floorDiffHours = Math.floor((double) diffHours /intervalTime);
                    if (floorDiffHours!=0){
                        Integer itemNum = supplementParamsList.get(2);
                        double needPlusNums = floorDiffHours * itemNum;
                        if (piggyBank + needPlusNums<maxAmount){
                            returnLinearRedeem.setPiggyBank(piggyBank + (int)needPlusNums);
                        }else {
                            returnLinearRedeem.setPiggyBank(maxAmount);

                        }
                    }
                    addNewRedDot();
                }
            }else {
                LOGGER.error("player LinearRedeemActivity supplementParamsList is error {} {}",player.getUid(),this.activityId);
            }
        }

    }

    @Override
    public void refresh (){
        List<Integer> supplementParamsList = getActivityMainConfig().getActivitySupplementParamsList();
        if (supplementParamsList.isEmpty()) {
            LOGGER.debug("player supplementParamsList is empty {} {}",player.getUid(),this.activityId);
        }else{
            if (supplementParamsList.size() == ACTIVITY_PARAM_SIZE) {
                long everyHourRefreshTimeMs = player.getUserAttr().getBasicInfo().getEveryHourRefreshTimeMs();
                long currentTimeMillis = Framework.currentTimeMillis();
                boolean sameHour = DateUtils.isSameHour(everyHourRefreshTimeMs, currentTimeMillis);
                if(!sameHour){
                    // 24 间隔时间;3563 货币类型;20 每次发放个数;500 存钱罐上限
                    Integer intervalTime = supplementParamsList.get(0);
                    //Integer itemType =
                    if (intervalTime != 0){
                        intervalTime--;
                    }else {
                        intervalTime = supplementParamsList.get(0);
                        Integer itemNum = supplementParamsList.get(2);
                        Integer maxAmount = supplementParamsList.get(3);
                        ReturnLinearRedeemActivity returnLinearRedeem = getActivityUnit().getDetailData().getReturnLinearRedeem();
                        if(returnLinearRedeem.getPiggyBank()<maxAmount){
                            returnLinearRedeem.setPiggyBank(returnLinearRedeem.getPiggyBank()+itemNum);
                            CsActivity.PiggyBankChangeNtf.Builder ntf = CsActivity.PiggyBankChangeNtf.newBuilder();
                            ntf.setDepositValue(itemNum).setCurrentValue(returnLinearRedeem.getPiggyBank()+itemNum).setActivityId(this.activityId);
                            player.sendNtfMsg(MsgTypes.MSG_TYPE_PIGGYBANKCHANGENTF, ntf);
                            player.getUserAttrMgr().collectAndSyncDirtyToClient();
                            addNewRedDot();
                            LOGGER.debug("player returnLinearRedeemActivity PiggyBank {} {} {}",
                                    player.getUid(),this.activityId,returnLinearRedeem.getPiggyBank());
                        }
                    }

                }

            }else {
                LOGGER.error("player supplementParamsList is error {} {}",player.getUid(),this.activityId);
            }
        }
    }
}
