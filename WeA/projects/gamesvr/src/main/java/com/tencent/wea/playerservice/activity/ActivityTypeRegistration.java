package com.tencent.wea.playerservice.activity;

import com.tencent.activity.ActivityConfigObject;
import com.tencent.wea.xlsRes.keywords.ActivityType;

/**
 * 活动类型注册配置
 * 
 * 需要在ActivityType枚举中添加：AT_MultiBuyMultiGift = 1024;
 * 需要在ActivityFactory中注册活动类
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public class ActivityTypeRegistration {
    
    /**
     * 注册多买多送活动类型
     * 在系统启动时调用
     */
    public static void registerMultiBuyMultiGiftActivity() {
        // 注册活动配置对象
        ActivityConfigObject config = new ActivityConfigObject(
            ActivityType.AT_MultiBuyMultiGift,  // 活动类型
            false,                              // 不是全局唯一活动
            false                               // 不是DS关注活动
        );
        
        // 设置活动运行环境
        config.isGameSvrRun = true;            // 运行在GameSvr服务上
        config.isActivitySvrRun = false;       // 不运行在活动服务上
        
        // 注册到活动配置管理器
        ActivityConfigManager.getInstance().registerActivityConfig(config);
        
        // 在ActivityFactory中注册活动类
        ActivityFactory.registerActivity(
            ActivityType.AT_MultiBuyMultiGift, 
            MultiBuyMultiGiftActivity.class
        );
    }
}

// 需要在ResKeywords.proto中添加活动类型定义：
/*
enum ActivityType {
    // ... 其他活动类型
    AT_MultiBuyMultiGift = 1024;  // 多买多送活动
}
*/

// 需要在ItemChangeReason枚举中添加：
/*
enum ItemChangeReason {
    // ... 其他原因
    ICR_MultiBuyActivity = 1024;  // 多买多送活动
}
*/
