package com.tencent.wea.playerservice.task;

import com.tencent.cache.Cache;
import com.tencent.cache.CacheErrorCode;
import com.tencent.cache.CacheUtil;
import com.tencent.condition.event.player.common.PlayerPasswordCodeBeUsedEvent;
import com.tencent.condition.event.player.common.PlayerUsePasswordCodeEvent;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.nk.util.random.util.RandomCodeUtil;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.resclass.ActivityMianGanData;
import com.tencent.resourceloader.resclass.ChannelDisableSceneConf;
import com.tencent.resourceloader.resclass.CupsConfigData;
import com.tencent.resourceloader.resclass.PasswordCodeTaskConfData;
import com.tencent.resourceloader.resclass.QuickRewardConfData;
import com.tencent.resourceloader.resclass.TaskConfData;
import com.tencent.resourceloader.resclass.TaskGroupData;
import com.tencent.task.TaskTimeUtil;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.timiutil.tool.FunctionUtil;
import com.tencent.util.VersionUtil;
import com.tencent.wea.attr.PasswordCodeData;
import com.tencent.wea.attr.Task;
import com.tencent.wea.attr.TaskFinish;
import com.tencent.wea.attr.TaskInfo;
import com.tencent.wea.attr.TaskLifeTime;
import com.tencent.wea.interaction.player.PlayerInteractionInvoker;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ChangedItems.ChangeItem;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.reward.QuickRewardInterface;
import com.tencent.wea.playerservice.task.manager.BaseTaskManager;
import com.tencent.wea.playerservice.task.manager.LuckyFriendTaskManager;
import com.tencent.wea.protocol.CsTask.GetTaskPassWordCode_S2C_Msg;
import com.tencent.wea.protocol.CsTask.TaskChangeNtf;
import com.tencent.wea.protocol.CsTask.UseTaskPassWordCode_C2S_Msg;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SrActivity;
import com.tencent.wea.protocol.common.RewardItemInfo;
import com.tencent.wea.protocol.common.TaskRefreshTriggerType;
import com.tencent.wea.protocol.common.TaskStatusInfo;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PiiTaskPasswordCodeUseParams;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionData;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionInstruction;
import com.tencent.wea.tlog.flow.TlogPassWordCodeFlow;
import com.tencent.wea.xlsRes.ResActivityFunPara;
import com.tencent.wea.xlsRes.ResBurdenReduceTask;
import com.tencent.wea.xlsRes.ResCommon;
import com.tencent.wea.xlsRes.ResReward.QuickRewardConf;
import com.tencent.wea.xlsRes.ResReward.QuickRewardType;
import com.tencent.wea.xlsRes.ResTask;
import com.tencent.wea.xlsRes.ResTask.TaskConf;
import com.tencent.wea.xlsRes.ResTask.TaskGroup;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.TaskStatus;
import com.tencent.wea.xlsRes.keywords.TaskType;
import io.netty.util.collection.IntObjectHashMap;
import it.unimi.dsi.fastutil.ints.IntArraySet;
import it.unimi.dsi.fastutil.ints.IntSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 任务系统
 *
 * <AUTHOR>
 */
public class TaskModule extends PlayerModule implements QuickRewardInterface {

    private static final Logger LOGGER = LogManager.getLogger(TaskModule.class);

    /**
     * 上次刷新时间
     */
    private long lastRefreshTimeMs;

    /**
     * 玩家的任务属性
     */
    private final TaskInfo taskInfo;

    /**
     * 正在运行的所有任务
     */
    private final HashMap<Integer, RunTask> tasks;

    /**
     * 需要判断时间的任务
     */
    private final IntSet tasksNeedTimeCheck;

    /**
     * 需要判断加载时间
     */
    private final IntSet tasksNeedLoadTimeCheck;

    /**
     * 上次判断时间的时间
     */
    private long nextTimeCheckMin;

    /**
     * 所有任务模块
     */
    private final IntObjectHashMap<BaseTaskManager> taskManagers;

    /**
     * 将要删除的任务
     */
    private final HashSet<Integer> runningTasksToDelete = new HashSet<>(16);

    /**
     * 变化的任务，需要下发通知
     */
    private final HashMap<Integer, TaskStatusInfo> tasksChangeMap = new HashMap<>();

    /**
     * 是否全量同步任务
     */
    private boolean isFullNtf = false;

    public TaskModule(Player player) {
        super(GameModuleId.GMI_TaskManager, player);
        int taskMaxSize = TaskConfData.getInstance().getTaskConfigMap().size();
        tasks = new HashMap<>(taskMaxSize);
        tasksNeedTimeCheck = new IntArraySet();
        tasksNeedLoadTimeCheck = new IntArraySet();
        taskInfo = player.getUserAttr().getTaskInfo();
        taskManagers = TaskFactory.getAllTaskManager(player);
    }

    public BaseTaskManager getTaskManagerByType(int taskType) {
        return taskManagers.get(taskType);
    }

    public <T extends BaseTaskManager> T getTaskManagerByType(int taskType, Class<T> clazz) {
        BaseTaskManager baseTaskManager = taskManagers.get(taskType);
        if (clazz.isInstance(baseTaskManager)) {
            return clazz.cast(baseTaskManager);
        }

        return null;
    }

    /**
     * 更新任务配置表中内容
     */
    public void updateTaskConf(int taskId, ResTask.TaskConf taskConf) {
        RunTask task = getTask(taskId);
        if (task == null) {
            return;
        }

        task.setResTask(taskConf);
    }

    private List<TaskFinish> getFinishTaskList() {
        return new ArrayList<>(taskInfo.getFinishTask().values());
    }

    private boolean isTaskShowEnd(ResTask.TaskConf taskConf) {
        long now = Framework.currentTimeMillis();
        long showEndTime = taskConf.getShowTime().getEndTime();
        if (showEndTime == 0) {
            showEndTime = Long.MAX_VALUE;
        }
        return now > showEndTime;
    }

    private boolean isTaskShowEnd(TaskLifeTime taskTimeInfo) {
        long now = Framework.currentTimeMillis();
        return now > taskTimeInfo.getShowEndTime();
    }

    public boolean isRegisterTimeInTaskShowRange(ResTask.TaskConf taskConf) {
        long registerTime = player.getRegisterTime();
        long showBeginTime = taskConf.getShowTime().getBeginTime();
        if (registerTime < showBeginTime) {
            return false;
        }
        long showEndTime = taskConf.getShowTime().getEndTime();
        if (showEndTime == 0) {
            showEndTime = Long.MAX_VALUE;
        }
        return registerTime < showEndTime;
    }

    public boolean isRegisterTimeInTaskDoTimeRange(ResTask.TaskConf taskConf) {
        long registerTime = player.getRegisterTime();
        long doBeginTime = taskConf.getDoTime().getBeginTime();
        if (registerTime < doBeginTime) {
            return false;
        }
        long doEndTime = taskConf.getDoTime().getEndTime();
        if (doEndTime == 0) {
            doEndTime = Long.MAX_VALUE;
        }
        return registerTime < doEndTime;
    }

    /**
     * 判断任务是否完成
     * @param taskId 任务id
     * @return boolean
     */
    public boolean isTaskComplete(int taskId) {
        Task attrTask = getAttrTask(taskId);
        if (attrTask == null) {
            return getFinishTask(taskId) != null;
        }
        return attrTask.getStatus().getNumber() == TaskStatus.TS_Completed_VALUE ||
                attrTask.getStatus().getNumber() == TaskStatus.TS_Finish_VALUE;
    }

    /**
     * 删除任务
     * @param taskId 任务id
     */
    public void removeTask(int taskId) {
        markDeleteRunningTask(taskId);
        deleteFinishTask(taskId);
    }

    private boolean checkTaskVisible(int taskId) {
        if (!checkTaskValid(taskId)) {
            return false;
        }
        TaskConf taskConf = TaskConfData.getInstance().getTaskConf(taskId);
        if (taskConf == null) {
            return false;
        }
        if (taskConf.getExtraConf().getLoginConditionCount() == 0) {
            return true;
        }
        for (ResCommon.PlayerLoginCondition loginCondition : taskConf.getExtraConf().getLoginConditionList()) {
            if (player.checkLoginCondition(loginCondition)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 注册任务
     *
     * @param taskId 任务id
     * @param taskConf 任务配置
     * @param taskTimeInfo 任务时间信息
     * @return 是否注册成功
     * @throws NKRuntimeException 异常
     */
    public RunTask registerTask(int taskId, ResTask.TaskConf taskConf, TaskLifeTime taskTimeInfo)
            throws NKRuntimeException {
        BaseTaskManager taskManager = getTaskManagerByType(taskConf.getType().getNumber());
        if (taskManager == null) {
            LOGGER.warn("task {} type {} manager not exist", taskId, taskConf.getType());
            return null;
        }

        if (!checkTaskVisible(taskId)) {
            LOGGER.debug("player {} add task {} not visible", player.getUid(), taskId);
            return null;
        }

        // 超过展示时间
        boolean taskShowEnd;
        if (taskTimeInfo == null) {
            taskShowEnd = isTaskShowEnd(taskConf);
        } else {
            taskShowEnd = isTaskShowEnd(taskTimeInfo);
        }
        if (taskShowEnd) {
            markDeleteRunningTask(taskId);
            return null;
        }

        if (taskConf.hasLoadTime()) {
            tasksNeedLoadTimeCheck.add(taskId);
        }
        // 不在加载时间内
        if (!TaskTimeUtil.checkLoadTime(taskConf)) {
            LOGGER.debug("player {} skip register task {} not in load time {}",
                    player.getUid(), taskId, taskConf.getLoadTime());
            return null;
        }

        // 已结束的任务处理
        if (getFinishTask(taskId) != null) {
            // 删除已结束的
            if (getAttrTask(taskId) != null) {
                markDeleteRunningTask(taskId);
            }
            if (taskConf.getShowTime().hasEndTime()) {
                tasksNeedTimeCheck.add(taskId);
            }
            taskManager.addTask(taskId);
            return null;
        }
        if (taskConf.getFrontTask() > 0 && !isTaskComplete(taskConf.getFrontTask())) {
            return null;
        }

        RunTask task = getTask(taskId);
        if (task != null) {
            if (!ServerEngine.getInstance().isBusiness()) {
                LOGGER.warn("player {} register exist task {}", player.getUid(), taskId);
            }
            if (taskTimeInfo != null) {
                setAttrTaskTimeInfo(taskTimeInfo, task.getAttrTask());
            }
            return null;
        }
        if (!checkExtraConf(taskId, taskConf.getExtraConf())) {
            return null;
        }
        if (taskConf.getDisable()) {
            return null;
        }
        Task attrTask = getAttrTask(taskId);
        if (attrTask == null) {
            attrTask = generateTask(taskId);
        }

        // 设置时间
        setAttrTaskTimeInfo(taskTimeInfo, attrTask);

        // 注册任务
        try {
            task = new RunTask(player, attrTask, taskConf);
            task.init();
        } catch (Exception e) {
            LOGGER.error("register task {} error, get task from TaskFactory failed: ", taskId, e);
            return null;
        }
        tasks.put(taskId, task);
        taskManager.addTask(taskId);
        if (task.needTimeCheck()) {
            tasksNeedTimeCheck.add(taskId);
        } else {
            tasksNeedTimeCheck.remove(taskId);
        }
        if (isFullNtf) {
            addChangeTaskInfo(task.getTaskChangeInfo(false));
        }
        task.refreshTask(TaskRefreshTriggerType.TRTT_Normal);
        return task;
    }

    public void addChangeTaskInfo(TaskStatusInfo taskStatusInfo) {
        tasksChangeMap.put((int) taskStatusInfo.getId(), taskStatusInfo);
    }

    private void setAttrTaskTimeInfo(TaskLifeTime taskTimeInfo, Task attrTask) {
        if (taskTimeInfo != null) {
            if (attrTask.getTimeInfo().getNotUseAttrTime()) {
                attrTask.getTimeInfo().switchToTaskLifeTime();
            }
            attrTask.getTimeInfo().getTaskLifeTime().setShowBeginTime(taskTimeInfo.getShowBeginTime());
            attrTask.getTimeInfo().getTaskLifeTime().setShowEndTime(taskTimeInfo.getShowEndTime());
            attrTask.getTimeInfo().getTaskLifeTime().setDoBeginTime(taskTimeInfo.getDoBeginTime());
            attrTask.getTimeInfo().getTaskLifeTime().setDoEndTime(taskTimeInfo.getDoEndTime());
        } else {
            attrTask.getTimeInfo().setNotUseAttrTime(true);
        }
    }

    public RunTask getTask(Integer id) {
        return tasks.getOrDefault(id, null);
    }

    public void updateLifeTime(int taskId, TaskLifeTime taskTimeInfo) {
        RunTask task = getTask(taskId);
        if (task == null) {
            return;
        }

        var attrTask = task.getAttrTask();
        if (null == attrTask) {
            LOGGER.error("player {} task {} got null attr", player.getUid(), taskId);
            return;
        }

        setAttrTaskTimeInfo(taskTimeInfo, attrTask);

        if (task.needTimeCheck()) {
            tasksNeedTimeCheck.add(taskId);
        } else {
            tasksNeedTimeCheck.remove(taskId);
        }
    }

    public void markDeleteRunningTask(int taskId) {
        if (runningTasksToDelete.contains(taskId)) {
            return;
        }
        // 在任务重置或删除之前跑一遍补领模块逻辑
        player.getTaskRewardManager().checkAndSetTaskReward(taskId);

        LOGGER.debug("player {} add running task {} to delete list", player.getUid(), taskId);
        runningTasksToDelete.add(taskId);
    }

    public Task getAttrTask(Integer id) {
        return player.getUserAttr().getTaskInfo().getRunningTask(id);
    }

    public TaskFinish getFinishTask(int id) {
        return player.getUserAttr().getTaskInfo().getFinishTask(id);
    }

    public boolean modifyTaskProgress(int taskId, long value) {
        RunTask task = getTask(taskId);
        if (task == null) {
            LOGGER.error("player {} modify task {} progress error: task not exist", player.getUid(), taskId);
            return false;
        }
        return task.modifyConditionProgress(0, value);
    }

    public boolean modifyTaskProgress(PlayerInteraction.PiiModifyTaskProgressParams req) {
        boolean supportTaskResetSwitch = PropertyFileReader.getRealTimeBooleanItem("idip_support_task_reset_switch", true);

        // 老的方式: 单个修改
        if (0 != req.getTaskId()) {
            // 当任务的待修改进度为0时, 重置任务
            if (supportTaskResetSwitch && 0 == req.getProgress()) {
                player.getTaskManager().resetTask(req.getTaskId());
                return true;
            }

            return modifyTaskProgress(req.getTaskId(), req.getProgress());
        }

        // 新的方式: 批量修改
        boolean ret = true;
        List<Integer> invalidTaskIds = new ArrayList<>();
        // 是否使用总值
        boolean useTotal = req.getProgressTotalListCount() > 0;
        for (int i = 0; i < req.getTaskIdListCount(); i++) {
            int taskId = req.getTaskIdList(i);
            RunTask task = getTask(taskId);
            if (Objects.isNull(task)) {
                invalidTaskIds.add(taskId);
                continue;
            }

            // 当任务的待修改进度为0时, 重置任务
            if (supportTaskResetSwitch && 0 == (useTotal ? req.getProgressTotalList(i) : req.getProgressDiffList(i))) {
                player.getTaskManager().resetTask(task.getTaskId());
            } else {
                boolean modifyRet = task.modifyConditionProgress(0,
                        useTotal ? req.getProgressTotalList(i) : req.getProgressDiffList(i), useTotal);
                ret = ret && modifyRet;
            }
        }

        if (!invalidTaskIds.isEmpty()) {
            LOGGER.error("TaskModule.modifyTaskProgress error. uid:{} invalidTaskIds:{}", player.getUid(), invalidTaskIds);
        }

        return ret;
    }

    /**
     * 生成一个任务
     *
     * @param taskId 任务id
     * @return 任务
     */
    private Task generateTask(int taskId) {
        Task task = new Task();
        task.setId(taskId);
        task.setStatus(TaskStatus.TS_Init);
        task.setRepeatNum(0);
        player.getUserAttr().getTaskInfo().putRunningTask(task.getId(), task);

        return task;
    }

    public boolean checkTaskValid(int taskId) {
        TaskConf taskConf = TaskConfData.getInstance().getTaskConf(taskId);
        if (taskConf == null) {
            return false;
        }
        if (!VersionUtil.checkClientVersion(taskConf.getLowVersion(), taskConf.getHighVersion(), player.getClientVersion64())) {
            return false;
        }
        // 检查是否要下架
        long lastUpdateMainVersionTime = player.getUserAttr().getLastUpdateMainVersionTimeMs();

        /* 策划要求奖杯征程版本切换特殊处理 Begin */
        // 客户端版本低于奖杯征程版本，保留
        if (player.getClientVersion64() < CupsConfigData.getInstance().getOpenClientVersion()) {
            return true;
        }

        // 奖杯征程版本之前注册的号，且更新到奖杯征程版本，从更新时间起保留一天或一周
        if (CupsConfigData.getInstance().getDailyRemoveTaskId().contains(taskId)) {
            // 奖杯征程版本新注册的号，不保留
            if (lastUpdateMainVersionTime <= 0) {
                return false;
            }
            long now = DateUtils.currentTimeMillis();
            return now < DateUtils.getDayEndTimeMs(lastUpdateMainVersionTime);
        } else if (CupsConfigData.getInstance().getWeeklyRemoveTaskId().contains(taskId)) {
            // 奖杯征程版本新注册的号，不保留
            if (lastUpdateMainVersionTime <= 0) {
                return false;
            }
            long now = DateUtils.currentTimeMillis();
            return now < DateUtils.getWeekEndTimeMs(lastUpdateMainVersionTime);
        }
        /* 策划要求奖杯征程版本切换特殊处理 End */

        return true;
    }

    /**
     * 加载所有任务的逻辑
     *
     * @return 是否成功
     */
    private NKErrorCode doLoadAllTask() {
        Monitor monitor = Monitor.getInstance();
        long startTime = System.nanoTime();

        try {
            // 初始化每类任务并统计各模块耗时(微秒级)
            Map<String, Long> moduleTimeMap = new HashMap<>();
            for (BaseTaskManager taskManager : taskManagers.values()) {
                long moduleStartTime = System.nanoTime();
                taskManager.initTaskIfNotYet();
                long moduleCostTime = (System.nanoTime() - moduleStartTime) / 1000; // 转换为微秒
                moduleTimeMap.put(taskManager.getClass().getSimpleName(), moduleCostTime);
            }

            // 清除不在配置表的任务
            for (Task task : player.getUserAttr().getTaskInfo().getRunningTask().values()) {
                if (!checkTaskValid(task.getId())) {
                    LOGGER.debug("player {} add task {} to tasksDeleteByConfig", player.getUid(), task.getId());
                    runningTasksToDelete.add(task.getId());
                }
            }

            // 统计任务类型分布
            Map<Integer, Integer> typeCounts = new HashMap<>();
            for (RunTask task : tasks.values()) {
                typeCounts.merge(task.getTaskType(), 1, Integer::sum);
            }

            // 上报各类型任务数量
            for (Map.Entry<Integer, Integer> entry : typeCounts.entrySet()) {
                monitor.observe(MonitorId.attr_task_type_count, entry.getValue(),
                        new String[]{"type=" + entry.getKey()});
                LOGGER.debug("Task type {} count: {}", entry.getKey(), entry.getValue());
            }

            // 上报各模块加载耗时(微秒级)
            for (Map.Entry<String, Long> entry : moduleTimeMap.entrySet()) {
                monitor.observe(MonitorId.attr_task_module_time, entry.getValue(),
                        new String[]{"module=" + entry.getKey()});
                LOGGER.debug("Task manager {} load time: {}μs", entry.getKey(), entry.getValue());
            }

            long costTime = (System.nanoTime() - startTime) / 1000; // 转换为微秒
            LOGGER.debug("Total task load time: {}μs", costTime);
            monitor.observe(MonitorId.attr_task_load_time, costTime);

            return NKErrorCode.OK;
        } catch (Exception e) {
            LOGGER.error("doLoadAllTask error", e);
            return NKErrorCode.TaskInitFail;
        } finally {
            refreshTaskManagers();
            deleteRunningTasks();
        }
    }

    private NKErrorCode loadAllTask() {
        try {
            return doLoadAllTask();
        } catch (Exception e) {
            LOGGER.error("player {} task init failed , exception {}\n", player.getUid(),
                    FunctionUtil.debugGetStackTrace(), e);
            return NKErrorCode.TaskInitFail;
        }
    }

    private void deleteFinishTask(int taskId) {
        TaskFinish taskFinish = getFinishTask(taskId);
        if (taskFinish != null) {
            taskInfo.removeFinishTask(taskId);
            if (isFullNtf) {
                tasksChangeMap.put(taskId, TaskStatusInfo.newBuilder()
                        .setId(taskId)
                        .setDelete(true).build());
            }
        }
    }

    public void deleteRunningTasks() {
        for (int id : runningTasksToDelete) {
            RunTask task = getTask(id);
            if (task != null) {
                getTaskManagerByType(task.getTaskType()).deleteTask(id);
                tasks.remove(id);
                taskInfo.removeRunningTask(id);
                tasksNeedTimeCheck.remove(id);
                task.onDelete();
                tasksChangeMap.put(id, task.getTaskChangeInfo(true));
            } else {
                taskInfo.removeRunningTask(id);
            }
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} delete task list {}", player.getUid(), runningTasksToDelete);
        }
        runningTasksToDelete.clear();
    }

    /**
     * Only delete the task in runningTasksToDelete
     * Please try not to use
     */
    public void deleteRunningTask(int id) {
        if (!runningTasksToDelete.contains(id)) {
            return;
        }
        RunTask task = getTask(id);
        if (task != null) {
            getTaskManagerByType(task.getTaskType()).deleteTask(id);
            tasks.remove(id);
            taskInfo.removeRunningTask(id);
            tasksNeedTimeCheck.remove(id);
            task.onDelete();
            tasksChangeMap.put(id, task.getTaskChangeInfo(true));
        } else {
            taskInfo.removeRunningTask(id);
        }
        LOGGER.debug("player {} delete task {}", player.getUid(), id);
        runningTasksToDelete.remove(id);
    }

    private void sendTaskFullNtf() {
        TaskChangeNtf.Builder ntfBuilder = TaskChangeNtf.newBuilder();
        for (BaseTaskManager taskManager : taskManagers.values()) {
            ntfBuilder.addAllAllTaskList(taskManager.getNtfTaskList());
        }
        player.sendNtfMsg(MsgTypes.MSG_TYPE_TASKCHANGENTF, ntfBuilder);
        tasksChangeMap.clear();
    }

    public void sendTaskChangeNtf(Collection<TaskStatusInfo> taskChangeList) {
        TaskChangeNtf.Builder ntfBuilder = TaskChangeNtf.newBuilder();
        ntfBuilder.addAllChangeTask(taskChangeList);
        player.sendNtfMsg(MsgTypes.MSG_TYPE_TASKCHANGENTF, ntfBuilder);
    }

    public void sendTaskChangeNtf() {
        if (tasksChangeMap.isEmpty() || !isFullNtf()) {
            return;
        }
        sendTaskChangeNtf(tasksChangeMap.values());
        tasksChangeMap.clear();
    }

    public boolean isFullNtf() {
        return isFullNtf;
    }

    private void refreshTaskManagers() {
        // 执行各个taskManager的刷新逻辑
        for (BaseTaskManager taskManager : taskManagers.values()) {
            long nextRefreshTimeMs = taskManager.getNextRefreshTimeMs();
            //LOGGER.debug("task manager {} nextRefreshTimeMs: {}", taskManager.getTaskType(), nextRefreshTimeMs);
            if (nextRefreshTimeMs != 0 && Framework.currentTimeMillis() > nextRefreshTimeMs
                    && taskManager.getLastRefreshTime() < nextRefreshTimeMs) {
                checkAllTaskTime();
                taskManager.onRefresh();
            }
        }
    }

    public void refresh() {
        long now = Framework.currentTimeMillis();
        if (now - lastRefreshTimeMs < 200L) {
            return;
        }
        lastRefreshTimeMs = now;

        refreshTaskManagers();
        sendTaskChangeNtf();
        if (player.getLoadingState() == Player.LoadingState.LS_Loading ||
                player.getLoadingState() == Player.LoadingState.LS_Loaded) {
            checkAllTaskTime();
        }
    }

    public void resetTask(int taskId) {
        // 在任务重置或删除之前跑一遍补领模块逻辑
        player.getTaskRewardManager().checkAndSetTaskReward(taskId);

        RunTask task = tasks.get(taskId);
        if (task == null) {
            // 重置已结束的
            if (taskInfo.getFinishTask().containsKey(taskId)) {
                taskInfo.removeFinishTask(taskId);
                taskInfo.removeRunningTask(taskId);
                registerTask(taskId, TaskConfData.getInstance().getTaskConf(taskId), null);
            } else {
                LOGGER.warn("reset task {} not exist", taskId);
                return;
            }
        } else {
            // 重置运行中的
            task.resetTask();
        }
        taskInfo.removeFinishTask(taskId);
    }

    public void resetAllTask() {
        for (RunTask task : tasks.values()) {
            task.resetTask();
        }
        for (TaskFinish finishTask : getFinishTaskList()) {
            resetTask(finishTask.getId());
        }
    }

    private void checkTaskLoadTime() {
        HashSet<BaseTaskManager> taskManagerSet = new HashSet<>();
        for (int taskId : tasksNeedLoadTimeCheck) {
            TaskConf taskConf = TaskConfData.getInstance().getTaskConf(taskId);
            if (taskConf == null) {
                LOGGER.error("check task need load time error, task conf {} not exist", taskId);
                continue;
            }
            BaseTaskManager taskManager = taskManagers.get(taskConf.getType().getNumber());
            if (taskManager == null) {
                LOGGER.error("check task need load time error, task manager {} not exist", taskConf.getType().getNumber());
                continue;
            }
            if (taskManager.hasTask(taskId) && !TaskTimeUtil.checkLoadTime(taskConf)) {
                // 不在加载时间内的，从内存中删除
                RunTask task = tasks.remove(taskId);
                if (task != null) {
                    if (task.getConditionGroup() == null) {
                        LOGGER.info("check task load time condition not set, taskId: {}, uid: {}", taskId, player.getUid());
                    } else {
                        task.getConditionGroup().unregisterConditionGroup();
                    }
                }
                tasksNeedTimeCheck.remove(taskId);
                taskManager.deleteTask(taskId);
                tasksChangeMap.put(taskId, TaskStatusInfo.newBuilder()
                        .setId(taskId)
                        .setDelete(true).build());
            } else if (!taskManager.hasTask(taskId) && TaskTimeUtil.checkLoadTime(taskConf)) {
                taskManagerSet.add(taskManager);
            }
        }

        // 刷新活动，让活动任务先加载
        player.getActivityManager().refreshTaskActivity();
        // 加载其他任务
        for (BaseTaskManager taskManager : taskManagerSet) {
            taskManager.initTask();
        }
    }

    public void checkAllTaskTime() {
        // 每分钟检查一次
        long now = DateUtils.currentTimeMillis();
        if (nextTimeCheckMin != 0 && now < nextTimeCheckMin * 60 * 1000) {
            return;
        }
        nextTimeCheckMin = DateUtils.currentTimeMillis() / 1000 / 60 + 1;
        LOGGER.debug("player {} refresh task need time check: {}", player.getUid(), tasksNeedTimeCheck);
        for (Integer taskId : tasksNeedTimeCheck) {
            RunTask task = tasks.get(taskId);
            if (task != null) {
                // 检查时间
                task.checkTaskTime();
            } else {
                TaskConf taskConf = TaskConfData.getInstance().getTaskConf(taskId);
                if (taskConf == null || now > taskConf.getShowTime().getEndTime()) {
                    deleteFinishTask(taskId);
                }
            }
        }
        checkTaskLoadTime();
        deleteRunningTasks();
    }

    public boolean checkExtraConf(int taskId, ResTask.TaskExtraConf taskExtraConf) {
        var validAccoutTypes = taskExtraConf.getAccountTypeList();
        if (!validAccoutTypes.isEmpty() && !validAccoutTypes.contains(player.getAccountType().getNumber())) {
            LOGGER.debug("player:{} task:{} extra require account types:{} but:{}"
                    , player.getUid(), taskId, validAccoutTypes, player.getAccountType());
            return false;
        }

        String regChannelDis = player.getUserAttr().getPlayerPublicBasicInfo().getRegChannelDis();

        // channel black list scene check
        if (taskExtraConf.hasDisbaleChannelScene() && taskExtraConf.getDisbaleChannelScene() > 0) {
            var disableScene = ChannelDisableSceneConf.getInstance().get(taskExtraConf.getDisbaleChannelScene());
            if (null == disableScene) {
                LOGGER.error("player:{} task:{} extra require check disable channel scene:{}, but lost"
                        , player.getUid(), taskId, taskExtraConf.getDisbaleChannelScene());
                return false;
            }

            if (disableScene.getChannelIdList().contains(regChannelDis)) {
                LOGGER.debug("player:{} task:{} extra require check disable channel scene:{} but:{} not in"
                        , player.getUid(), taskId, taskExtraConf.getDisbaleChannelScene(), regChannelDis);
                return false;
            }
        }

        // channel white list check
        var validRegChannels = taskExtraConf.getRegChannelList();
        if (!validRegChannels.isEmpty() && !validRegChannels.contains(regChannelDis)) {
            LOGGER.debug("player:{} task:{} extra require reg channels:{} but:{}"
                    , player.getUid(), taskId, validRegChannels, regChannelDis);
            return false;
        }

        String whitelistName = taskExtraConf.getWhilelist();
        if (StringUtils.isNotBlank(whitelistName)
                && !ResLoader.getResHolder().checkResTableContainsKey(whitelistName, player.getOpenId())) {
            LOGGER.debug("player:{} task:{} extra require in whitelist:{} but:{}"
                    , player.getUid(), taskId, whitelistName, player.getOpenId());
            return false;
        }
        return true;
    }

    public boolean checkClientVersion(ResTask.TaskConf taskConf) {
        if (player.isRobot()) {
            return true;
        }
        return VersionUtil.checkClientVersion(taskConf.getLowVersion(), taskConf.getHighVersion(), player.getClientVersion64());
    }

    public ArrayList<RunTask> getActiveTasks() {
        ArrayList<RunTask> taskList = new ArrayList<>();
        for (Task attrTask : taskInfo.getRunningTask().values()) {
            RunTask task = tasks.get(attrTask.getId());
            if (task != null) {
                taskList.add(task);
            }
        }

        return taskList;
    }

    public void completeTask(int taskId) {
        RunTask task = getTask(taskId);
        task.changeTaskStatus(TaskStatus.TS_Completed);
    }

    public void addFinishTask(int taskId, long completeTime) {
        TaskFinish taskFinish = new TaskFinish();
        taskFinish.setId(taskId).setCompleteTime(completeTime);
        taskInfo.putFinishTask(taskId, taskFinish);
    }

    /**
     * 发奖励
     */
    public NKErrorCode rewardTask(List<Integer> taskIdList) throws NKRuntimeException {
        return rewardTask(taskIdList, ItemChangeReason.ICR_TaskComplete);
    }

    public NKErrorCode rewardTask(List<Integer> taskIdList, ItemChangeReason reason) throws NKRuntimeException {
        return rewardTask(taskIdList, reason, true, null, "");
    }

    /**
     * 同上
     * @param withNtf 是否通知客户端道具变更
     * @param rewardedItems 实际获得的道具
     * @param busBillNo 订单号
     */
    public NKErrorCode rewardTask(List<Integer> taskIdList, ItemChangeReason reason, boolean withNtf, ItemChangeDetails rewardedItems, String busBillNo) throws NKRuntimeException {
        ItemChangeDetails realChanges = null;
        TaskChangeNtf.Builder ntfBuilder = TaskChangeNtf.newBuilder();
        for (int taskId : taskIdList) {
            RunTask task = getTask(taskId);
            if (task == null) {
                LOGGER.warn("player {} reward task {} not exist", player.getUid(), taskId);
                continue;
            }

            if (task.isRewarded()) {
                return NKErrorCode.TaskRewarded;
            }
            NKPair<ItemChangeDetails, TaskStatusInfo> rewardRes = task.receiveRewardWithoutTaskNtf(false, reason, busBillNo);
            if (rewardRes == null) {
                return NKErrorCode.TaskRewardFail;
            }
            ntfBuilder.addChangeTask(rewardRes.getValue());
            // 领奖发了通知，之前变化的不用再发了
            tasksChangeMap.remove(taskId);
            if (realChanges == null) {
                realChanges = rewardRes.getKey();
            } else {
                realChanges.merge(rewardRes.getKey());
            }
        }
        if (withNtf && realChanges != null) {
            realChanges.dry();
            player.getBagManager().sendGetItemsNtf(realChanges);
        }
        player.sendNtfMsg(MsgTypes.MSG_TYPE_TASKCHANGENTF, ntfBuilder);

        if (Objects.nonNull(rewardedItems) && Objects.nonNull(realChanges)) {
            rewardedItems.merge(realChanges);
        }
        return NKErrorCode.OK;
    }


    public NKErrorCode rewardTaskByUseItem(List<Integer> taskIdList, ItemChangeReason reason) throws NKRuntimeException {
        long needItemCount = 0;
        for (int taskId : taskIdList) {
            RunTask task = getTask(taskId);
            if (task == null) {
                LOGGER.error("player {} quit finish task  {} not exist", player.getUid(), taskId);
                return NKErrorCode.ActivcitySvrTaskQuitFinishError;
            }

            ResTask.TaskConf taskConf = TaskConfData.getInstance().getTaskConf(taskId);
            if (taskConf == null) {
                LOGGER.error("player {} quit finish task  {} config is null", player.getUid(), taskId);
                return NKErrorCode.ActivcitySvrTaskQuitFinishError;
            }
            if (task.getStatus() != TaskStatus.TS_Triggered) {
                LOGGER.error("player {} quit finish task  {}  is not triggered", player.getUid(), taskId);
                return NKErrorCode.ActivcitySvrTaskQuitFinishError;
            }
            if (!task.inDoPeriod()) {
                LOGGER.error("player {} quit finish task {} is time range", player.getUid(), taskId);
                return NKErrorCode.ActivcitySvrTaskQuitFinishError;
            }
            needItemCount += taskConf.getFinishNeedItemCount();
        }

        //扣除道具
        ResActivityFunPara.ActivityMianGanConfig mianganConfig = ActivityMianGanData.getInstance().getConfigByTime(DateUtils.currentTimeMillis());
        if (mianganConfig == null) {
            NKErrorCode.ActivcitySvrTaskMianGanExpireError.throwError(
                    "player[{}]  quit finish task error:time is no config:[{}]", player.getUid(), DateUtils.currentTimeMillis());
        }
        int costItemId = mianganConfig.getItemId();
        ChangedItems cost = new ChangedItems(ItemChangeReason.ICR_QUIT_FINISH_TASK.getNumber(), "");
        cost.mergeItemInfo(costItemId, needItemCount);
        NKErrorCode errorCode = player.getBagManager().MinItems(cost);
        if (!errorCode.isOk()) {
            LOGGER.error("player {} quit finish task  minItem  error  itemId:{} count:{}", player.getUid(), costItemId, needItemCount);
            return errorCode;
        }
        //修改任务进度为完成
        for (int taskId : taskIdList) {
            try {
                RunTask task = getTask(taskId);
                List<Long> changeReservedParams = new ArrayList<>();
                changeReservedParams.add(taskIdList.size() > 1 ? 1L : 0L);
                changeReservedParams.add(1L);
                task.setChangeReservedParams(changeReservedParams);
                task.quitCompeted();
            } catch (Exception e) {
                LOGGER.error("player:[{}] taskId:[{}]  quit finish task  error", player.getUid(), taskId);
                LOGGER.error(e);
                //TODO：flow上报
            }
        }
        //领奖
        return rewardTask(taskIdList, reason);
    }




    public ArrayList<Integer> getAttrTaskIdListByType(int taskType) {
        ArrayList<Integer> taskIdList = new ArrayList<>();
        for (Task attrTask : player.getUserAttr().getTaskInfo().getRunningTask().values()) {
            TaskType taskConfType = TaskConfData.getInstance().getTaskType(attrTask.getId());
            if (taskConfType.getNumber() == taskType) {
                taskIdList.add(attrTask.getId());
            }
        }
        return taskIdList;
    }

    public void setGuideTaskComplete(int taskId) {
        player.getUserAttr().getGuideTasks().add(taskId);
        try {
            player.getCocMgr().completeGuideTask(taskId);
        } catch (Exception e) {
            LOGGER.error("player({}:{}:{}) coc completeGuideTask error: ", player.getUid(), player.getOpenId(),
                player.getName());
        }
    }

    public boolean hasCompleteGuideTask(int taskId) {
        return player.getUserAttr().getGuideTasks().contains(taskId);
    }

    /**
     * 玩家注册时, 本模块内部的注册相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    /**
     * 玩家注册时, 本模块跨模块的注册相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void onRegister() throws NKCheckedException {

    }

    /**
     * 玩家注册后, 本模块后续的注册相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     */
    @Override
    public void afterRegister() throws NKCheckedException {

    }

    /**
     * 玩家加载时, 本模块内部的加载相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    /**
     * 玩家加载时, 本模块跨模块的加载相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void onLoad() throws NKCheckedException {

    }

    /**
     * 玩家加载后, 本模块后续的加载相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     */
    @Override
    public void afterLoad() {

    }

    /**
     * 玩家登录时, 本模块内部的登录相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void prepareLogin() throws NKCheckedException {
        this.isFullNtf = false;
    }

    /**
     * 玩家登录时, 本模块跨模块的登录相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void onLogin() throws NKCheckedException {

    }

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     *
     * @param todayFirstLogin
     */
    @Override
    public void afterLogin(boolean todayFirstLogin) {
        // 检查任务是否有效
        for (RunTask runTask : tasks.values()) {
            if (!checkTaskValid(runTask.getId())) {
                deleteRunningTask(runTask.getId());
            }
        }
        for (TaskFinish finishTask : getFinishTaskList()) {
            if (!checkTaskValid(finishTask.getId())) {
                deleteFinishTask(finishTask.getId());
            }
        }
        if (!isFullNtf) {
            isFullNtf = true;
            sendTaskFullNtf();
            LOGGER.debug("player {} send task full ntf", player.getUid());
        }
    }

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑, 在 afterlogin 后执行
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     *
     * @param todayFirstLogin
     */
    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {
        for (BaseTaskManager taskManager : taskManagers.values()) {
            taskManager.afterLoginFinish();
        }
    }

    @Override
    public void loadedWithLoginInfo() {
        loadAllTask();
        player.getQuickRewardMgr().registerQuickReward(this);
    }

    /**
     * 玩家登出时, 本模块的登出相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     */
    @Override
    public void onLogout() {

    }

    /**
     * 凌晨刷新逻辑
     * 只要上次刷新时间小于今天开始时间，就会产生一次刷新
     * 抛出异常不会影响调用其他模块的该接口
     */
    @Override
    public void onMidNight() {
        refreshTaskManagers();
        for (BaseTaskManager taskManager : taskManagers.values()) {
            taskManager.onMidnight();
        }
    }

    /**
     * 每周刷新逻辑
     * 只要上次刷新时间本周开始时间，就会产生一次刷新
     * 抛出异常不会影响调用其他模块的该接口
     */
    @Override
    public void onWeekStart() {
        LOGGER.debug("TaskManager onWeekStart");
        for (BaseTaskManager taskModule : taskManagers.values()) {
            taskModule.onWeekStart();
        }
    }

    @Override
    public void onReload() {
        // 新增任务
        for (BaseTaskManager taskManager : taskManagers.values()) {
            taskManager.initTask();
        }
        for (RunTask task : tasks.values()) {
            // 更新res task
            TaskConf taskConf = TaskConfData.getInstance().getTaskConf(task.getId());
            if (taskConf == null) {
                continue;
            }
            //LOGGER.debug("player {} reload task {} conf {}", player.getUid(), task.getId(),taskConf);
            updateTaskConf(task.getId(), taskConf);

            // 检查是否需要检查时间
            if (task.needTimeCheck()) {
                tasksNeedTimeCheck.add(task.getId());
            } else {
                tasksNeedTimeCheck.remove(task.getId());
            }
            task.refreshTask(TaskRefreshTriggerType.TRTT_Normal);
        }
        sendTaskChangeNtf();
    }

    /**
     * 根据任务组获取任务奖励
     * @param taskGroupList 任务组列表
     * @return List
     */
    public List<RewardItemInfo.Builder> getTaskListRewardItemInfoByTaskGroup(List<TaskGroup> taskGroupList) {
        List<RewardItemInfo.Builder> itemList = new ArrayList<>();
        for (TaskGroup taskGroup : taskGroupList) {
            for (int taskId : taskGroup.getTaskIdListList()) {
                RunTask task = getTask(taskId);
                if (task == null || !task.canReward()) {
                    continue;
                }
                ChangedItems changedItems = task.getRewardItem();
                for (ChangeItem changeItem : changedItems.getChangeItems()) {
                    RewardItemInfo.Builder rewardItemInfo = RewardItemInfo.newBuilder()
                            .setItemId(changeItem.getSrcItemId())
                            .setRewardItemNum(changeItem.getSrcItemNum());
                    itemList.add(rewardItemInfo);
                }
            }
        }
        return itemList;
    }

    @Override
    public QuickRewardType getQuickRewardType() {
        return QuickRewardType.QRT_TASK;
    }

    @Override
    public Map<Integer, List<RewardItemInfo.Builder>> getQuickRewardItemList() {
        Map<Integer, List<RewardItemInfo.Builder>> confItemMap = new HashMap<>();
        List<QuickRewardConf> confList = QuickRewardConfData.getInstance().getConfListByType(getQuickRewardType());
        for (QuickRewardConf conf : confList) {
            List<TaskGroup> taskGroupList = new ArrayList<>();
            for (int taskGroupId : conf.getIdListList()) {
                TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(taskGroupId);
                if (taskGroup == null) {
                    LOGGER.warn("get taskGroupId {} not exist", taskGroupId);
                    continue;
                }
                taskGroupList.add(taskGroup);
            }
            confItemMap.put(conf.getId(), getTaskListRewardItemInfoByTaskGroup(taskGroupList));
        }
        return confItemMap;
    }

    @Override
    public ItemChangeDetails receiveQuickReward() {
        return receiveQuickRewardByTaskGroup(getQuickRewardTaskGroupList());
    }

    public ItemChangeDetails receiveQuickRewardByTaskGroup(List<TaskGroup> taskGroupList) {
        ItemChangeDetails itemChangeDetails = null;
        for (TaskGroup taskGroup : taskGroupList) {
            for (int taskId : taskGroup.getTaskIdListList()) {
                RunTask task = player.getTaskManager().getTask(taskId);
                if (task == null || !task.canReward()) {
                    continue;
                }
                ItemChangeDetails rewardRes = task.receiveReward(false);
                if (rewardRes == null) {
                    continue;
                }
                if (itemChangeDetails == null) {
                    itemChangeDetails = rewardRes;
                } else {
                    itemChangeDetails.merge(rewardRes);
                }
            }
        }
        return itemChangeDetails;
    }

    private List<TaskGroup> getQuickRewardTaskGroupList() {
        List<TaskGroup> taskGroupList = new ArrayList<>();
        List<QuickRewardConf> confList = QuickRewardConfData.getInstance().getConfListByType(getQuickRewardType());
        for (QuickRewardConf conf : confList) {
            for (int taskGroupId : conf.getIdListList()) {
                TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(taskGroupId);
                if (taskGroup == null) {
                    LOGGER.warn("QuickRewardConfData conf {} taskGroupId {} not exist", conf.getId(), taskGroupId);
                    continue;
                }
                taskGroupList.add(taskGroup);
            }
        }
        return taskGroupList;
    }

    public void onFriendRemoved(long targetUid) {
        CurrentExecutorUtil.runJobNoException("CheckLuckyFriendTask", () -> {
            LuckyFriendTaskManager luckyFriendTaskManager = getTaskManagerByType(TaskType.TaskType_LuckyFriend_VALUE,
                    LuckyFriendTaskManager.class);
            if (null != luckyFriendTaskManager) {
                luckyFriendTaskManager.onFriendRemoved(targetUid);
            }
            return null;
        });
    }

    public void updateTaskByBurdenReduceConf(int taskId, ResTask.TaskConf taskConf,
                                             ResBurdenReduceTask.BurdenReduceTaskConf reduceConf) {
        RunTask task = getTask(taskId);
        if (task == null) {
            return;
        }
        ResTask.TaskConf.Builder taskConfBuilder = taskConf.toBuilder();
        // 修改主条件参数
        taskConfBuilder.getConditionBuilder().getResCompleteConditionGroupBuilder().getConditionBuilder(0)
                .setValue(reduceConf.getOverwriteConditionParams());
        task.setResTask(taskConfBuilder.build());
        task.updateBurdenReduceInfo(reduceConf.getOverwriteDesc());
        this.tasksChangeMap.put(taskId, task.getTaskChangeInfo(false));
        // 触发一次任务刷新
        task.refreshTask(TaskRefreshTriggerType.TRTT_Normal);
    }

    public void closeTaskBurdenReduce(int taskId, ResTask.TaskConf taskConf) {
        RunTask task = getTask(taskId);
        if (task == null) {
            return;
        }
        task.setResTask(taskConf);
        task.clearBurdenReduceInfo();
        this.tasksChangeMap.put(taskId, task.getTaskChangeInfo(false));
        // 触发一次任务刷新
        task.refreshTask(TaskRefreshTriggerType.TRTT_Normal);
    }

    /**
     * 口令码任务通过此接口获取口令码
     * @param taskId
     * @param activityId
     * @param builder
     * @throws NKRuntimeException
     */
    public void getTaskPassWordCode(int taskId, int activityId, GetTaskPassWordCode_S2C_Msg.Builder builder) throws NKRuntimeException {
        RunTask task = getTask(taskId);
        if (task == null) {
            NKErrorCode.TaskNotExist.throwError("player {} getTaskPassWordCode task {}  error: task not exist", player.getUid(), taskId);
        }

        // 先看下当前有没有生成过缓存
        PasswordCodeData passwordCodeData = player.getUserAttr().getPasswordCodeDataList(taskId);
        if (passwordCodeData != null){
            String key = CacheUtil.PasswordCode.getKeyShared(passwordCodeData.getPasswordCode());
            Cache.CacheResult<SrActivity.PasswordCodeInfoReq.Builder> res = CacheUtil.PasswordCode.getCachePb(key);
            if (res.errCode == CacheErrorCode.OK) {
                // redis 有值就取值
                if (res.val != null) {
                    SrActivity.PasswordCodeInfoReq.Builder req = res.val;
                    // 查看有效
                    if (checkPasswordCodeValid(req, taskId)){
                        builder.setPassWordCode(req.getPasswordCode());
                        return;
                    }
                }
            } else {
                NKErrorCode.RedisOpFail.throwError("player:{} get passwordCode info from redis:{} fail, ret={}", player.getUid(), key, res.errCode);
            }
        }
        // 如果现在没有何用的口令码 生成一个新的
        createNewPasswordCode(task, activityId, builder);

    }

    private void createNewPasswordCode(RunTask task, int activityId, GetTaskPassWordCode_S2C_Msg.Builder builder){
        long beginTime = Framework.currentTimeMillis();
        long nextRefreshTimeMs = getTaskManagerByType(task.getTaskType()).getNextRefreshTimeMs();
        // 如果不是定期刷新 则刷新时间是0，必须取和活动配置的时间 所以任务必须配置对应的结束时间
        long endTime = nextRefreshTimeMs > 0 ? nextRefreshTimeMs : task.getResTask().getDoTime().getEndTime();
        long expireTime = (endTime - beginTime)/1000;
        SrActivity.PasswordCodeInfoReq.Builder req = generateRandomPasswordCode(task.getTaskId(), activityId, beginTime, endTime);
        // 失败重试
        if (!trySavePasswordCode(req, expireTime)){
            String passwordCode = RandomCodeUtil.generateRandomString(10);
            req.setPasswordCode(passwordCode);
            if (!trySavePasswordCode(req, expireTime)){
                // 找不到对应助力信息说明缓存已经失效，或者参数非法，直接告知客户端即可
                NKErrorCode.CreatePasscodeCacheFail.throwError("player {} createNewPasswordCode fail, activityId {}, taskId{}, passwordCode{}",
                        player.getUid(), activityId, task.getTaskId(), req.getPasswordCode());
            }

        }
        builder.setPassWordCode(req.getPasswordCode());
    }



    /**
     * 尝试保存口令码
     * @param req 密码验证码请求信息
     * @param expireTime 过期时间
     * @return 保存是否成功
     */
    private boolean trySavePasswordCode(SrActivity.PasswordCodeInfoReq.Builder req, long expireTime) {
        if (req == null) {
            LOGGER.error("Password code request is null");
            return false;
        }

        try {
            // 生成缓存key
            String cacheKey = CacheUtil.PasswordCode.getKeyShared(req.getPasswordCode());

            // 尝试将密码验证码信息存入缓存
            Cache.CacheResult<String> cacheResult = CacheUtil.PasswordCode.setnxexCachePb(cacheKey, req, expireTime);

            // 如果缓存操作不成功，直接返回false
            if (cacheResult.errCode != CacheErrorCode.OK || !"OK".equals(cacheResult.val)) {
                if (!"OK".equals(cacheResult.val)){
                    Monitor.getInstance().add.succ(MonitorId.attr_passwordCode_repeat, 1);
                }
                LOGGER.error("Failed trySavePasswordCode, error code: {}, value: {}",
                        cacheResult.errCode, cacheResult.val);
                return false;
            }

            // 口令码指标
            Monitor.getInstance().add.succ(MonitorId.attr_passwordCode_create, 1);

            // 缓存成功后，更新玩家数据
            updatePlayerPasswordCodeData(req);
            TlogPassWordCodeFlow.hire(player)
                    .setFlowType(1)
                    .setActivityID(req.getActivityId())
                    .setPasswordCode(req.getPasswordCode())
                    .setGiveUid(req.getOwnerUid())
                    .setUsedNum(req.getUseCount())
                    .setRefreshCycle(1).logToTlogd();
            return true;

        } catch (Exception e) {
            LOGGER.error("Error while trySavePasswordCode", e);
            return false;
        }
    }

    /**
     * 更新玩家的密码验证码数据
     * @param req 密码验证码请求信息
     */
    private void updatePlayerPasswordCodeData(SrActivity.PasswordCodeInfoReq.Builder req) {
        // 移除旧的密码验证码数据
        player.getUserAttr().removePasswordCodeDataList(req.getTaskId());

        // 创建新的密码验证码数据
        PasswordCodeData passwordCodeData = new PasswordCodeData();
        passwordCodeData.setTaskId(req.getTaskId());
        passwordCodeData.setPasswordCode(req.getPasswordCode());
        passwordCodeData.setCreateTime(req.getCreateTime());

        player.getUserAttr().putPasswordCodeDataList(req.getTaskId(), passwordCodeData);
    }

    private SrActivity.PasswordCodeInfoReq.Builder generateRandomPasswordCode(int taskId, int activityId, long beginTime , long endTime){
        String passwordCode = RandomCodeUtil.generateRandomString(10);
        SrActivity.PasswordCodeInfoReq.Builder builder = SrActivity.PasswordCodeInfoReq.newBuilder();
        builder.setTaskId(taskId);
        builder.setActivityId(activityId);
        builder.setBeginTime(beginTime);
        builder.setEndTime(endTime);
        builder.setPasswordCode(passwordCode);
        builder.setOwnerUid(player.getUid());
        builder.setCreateTime(beginTime);
        builder.setUseCount(0);
        return builder;
    }

    private boolean checkPasswordCodeValid(SrActivity.PasswordCodeInfoReq.Builder req, int taskId){
        return req.getOwnerUid() == player.getUid() && req.getEndTime() > Framework.currentTimeMillis() && req.getTaskId() == taskId;
    }

    private boolean checkUsePasswordCodeValid(SrActivity.PasswordCodeInfoReq.Builder req, int taskId){
        return  req.getEndTime() > Framework.currentTimeMillis()
                && checkPasswordCodeUseLimit(req.getActivityId(), req.getUseCount());
    }

    private boolean checkPasswordCodeUseLimit(int activityId, long useCount){
        ResTask.PasswordCodeTaskConf conf = PasswordCodeTaskConfData.getInstance().getByActivityId(activityId);
        if (conf == null){
            NKErrorCode.ActivityPasscodeCacheExpire.throwError("player {} can not find  PasswordCodeTaskConf, activityId {}",
                    player.getUid(), activityId);
        }
        return conf.getMaxUse() <= 0 || conf.getMaxUse() > useCount;
    }

    /**
     * 口令码任务通过此接口获取使用口令码
     * @param reqMsg
     * @throws NKRuntimeException
     */
    public void useTaskPassWordCode(UseTaskPassWordCode_C2S_Msg reqMsg) throws NKRuntimeException {
        int taskId = reqMsg.getTaskId();
        String passwordCode = reqMsg.getPassWordCode();
        // 校验口令码格式防止攻击
        if (!RandomCodeUtil.validateUsingCharSet(passwordCode, 10)){
            NKErrorCode.PasscodeIllegalFormat.throwError("player {} passcodeIllegalFormat, activityId {}, taskId{}, passwordCode{}",
                    player.getUid(), reqMsg.getActivityId(), taskId, passwordCode);
        }

        RunTask task = getTask(taskId);
        if (task == null) {
            NKErrorCode.TaskNotExist.throwError("player {} useTaskPassWordCode task {}  error: task not exist", player.getUid(), taskId);
        }

        // 先看下当前有没有生成过缓存
        PasswordCodeData passwordCodeData = player.getUserAttr().getPasswordCodeDataList(taskId);
        if (passwordCodeData == null){
            passwordCodeData = new PasswordCodeData();
            player.getUserAttr().putPasswordCodeDataList(taskId, passwordCodeData);
        } else {
            if (passwordCodeData.getUsedPasswordCode().contains(passwordCode)){
                // 找不到对应助力信息说明缓存已经失效，或者参数非法，直接告知客户端即可
                NKErrorCode.PasscodeAlreadyUsed.throwError("player {} useTaskPassWordCode fail already used, activityId {}, taskId{}, passwordCode{}",
                        player.getUid(), reqMsg.getActivityId(), taskId, passwordCodeData.getPasswordCode());
            }
        }

        String key = CacheUtil.PasswordCode.getKeyShared(passwordCode);
        Cache.CacheResult<SrActivity.PasswordCodeInfoReq.Builder> res = CacheUtil.PasswordCode.getCachePb(key);
        if (res.errCode == CacheErrorCode.OK) {
            // redis 有值就取值
            if (res.val == null) {
                // 找不到对应助力信息说明缓存已经失效，或者参数非法，直接告知客户端即可
                NKErrorCode.ActivityPasscodeCacheExpire.throwError("player {} useTaskPassWordCode expire, activityId {}, taskId{}, passwordCode{}",
                        player.getUid(), reqMsg.getActivityId(), taskId, passwordCode);
            }
            SrActivity.PasswordCodeInfoReq.Builder req = res.val;
            // 查看有效
            if (checkUsePasswordCodeValid(req, taskId)){
                if (req.getOwnerUid() == player.getUid()){
                    NKErrorCode.ActivityPasscodeCanNotUseSelf.throwError("player {} useTaskPassWordCode can not use self code, activityId {}, taskId{}, passwordCode{}",
                            player.getUid(), reqMsg.getActivityId(), taskId, passwordCode);
                }
                // 如果口令码有效 则进入口令码使用流程
                req.setUseCount(req.getUseCount() + 1);
                // 尝试将密码验证码信息存入缓存
                Cache.CacheResult<String> cacheResult = CacheUtil.PasswordCode.setCachePb(key, req);

                if (res.errCode != CacheErrorCode.OK) {
                    LOGGER.error("Failed update PasswordCode, error code: {}, value: {}",
                            cacheResult.errCode, cacheResult.val);
                    NKErrorCode.RedisOpFail.throwError("player:{} get passwordCode info from redis:{} fail, ret={}", player.getUid(), key, res.errCode);
                }

                player.getPlayerEventManager().dispatch(new PlayerUsePasswordCodeEvent(player.getConditionMgr()).setParams(req.getActivityId()));

                PiiTaskPasswordCodeUseParams.Builder params = PiiTaskPasswordCodeUseParams.newBuilder()
                        .setActivityId(req.getActivityId())
                        .setTaskId(req.getTaskId())
                        .setUseUid(player.getUid())
                        .setGiveUid(req.getOwnerUid())
                        .setBizTime(Framework.currentTimeMillis())
                        .setBizType("use")
                        .setPasswordCode(req.getPasswordCode());
                PlayerInteractionData.Builder data = PlayerInteractionData.newBuilder();
                data.setInstruction(PlayerInteractionInstruction.PII_TASK_PASSWORD_CODE_USE).setTaskPasswordCodeUseParams(params);
                if (!PlayerInteractionInvoker.interact(req.getOwnerUid(), data)) {
                    LOGGER.error("player {} useTaskPassWordCode send PII_TASK_PASSWORD_CODE_USE  failed, data: {}",
                            player.getUid(), data);
                }

                saveUsedPassWord(req.getPasswordCode(), taskId);

                Monitor.getInstance().add.succ(MonitorId.attr_passwordCode_use, 1);

                TlogPassWordCodeFlow.hire(player)
                        .setFlowType(2)
                        .setActivityID(reqMsg.getActivityId())
                        .setPasswordCode(req.getPasswordCode())
                        .setGiveUid(req.getOwnerUid())
                        .setUsedNum(req.getUseCount())
                        .setRefreshCycle(1).logToTlogd();


            } else {
                // 找不到对应助力信息说明缓存已经失效，或者参数非法，直接告知客户端即可
                NKErrorCode.ActivityPasscodeCacheExpire.throwError("player {} useTaskPassWordCode expire, activityId {}, taskId{}, passwordCode{}",
                        player.getUid(), reqMsg.getActivityId(), taskId, passwordCode);
            }
        } else {
            NKErrorCode.RedisOpFail.throwError("player:{} get passwordCode info from redis:{} fail, ret={}", player.getUid(), key, res.errCode);
        }


    }

    public boolean acceptPasswordCodeBeUsed(PiiTaskPasswordCodeUseParams params){
        player.getPlayerEventManager().dispatch(new PlayerPasswordCodeBeUsedEvent(player.getConditionMgr()).setParams(params));
        return true;
    }

    private void saveUsedPassWord(String passwordCode, int taskId){
        PasswordCodeData passwordCodeData = player.getUserAttr().getPasswordCodeDataList(taskId);
        passwordCodeData.addUsedPasswordCode(passwordCode);
        player.getUserAttr().putPasswordCodeDataList(taskId, passwordCodeData);

    }

    public Map<Integer, Integer> getTaskTypeCounts() {
        Map<Integer, Integer> typeCounts = new HashMap<>();
        for (RunTask task : tasks.values()) {
            int type = task.getTaskType();
            typeCounts.put(type, typeCounts.getOrDefault(type, 0) + 1);
        }
        return typeCounts;
    }

}
