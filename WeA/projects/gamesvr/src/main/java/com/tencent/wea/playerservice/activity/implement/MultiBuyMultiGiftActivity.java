package com.tencent.wea.playerservice.activity.implement;

import com.tencent.wea.playerservice.activity.BaseActivity;
import com.tencent.wea.playerservice.activity.lifecycle.ActivityUnit;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.event.BaseEvent;
import com.tencent.wea.playerservice.event.EventConsumer;
import com.tencent.wea.playerservice.event.MallCommodityBuyEvent;
import com.tencent.wea.xlsRes.keywords.ActivityType;
import com.tencent.wea.xlsRes.ResMultiBuyActivity.*;
import com.tencent.wea.attr.MultiBuyActivityData;
import com.tencent.wea.common.NKErrorCode;
import com.tencent.wea.common.Framework;
import com.tencent.wea.common.util.TimeUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 多买多送运营活动
 * 
 * 功能特性：
 * 1. 每日不放回随机折扣池
 * 2. 商品展示及购买
 * 3. 多买特殊折扣（双倍折上折）
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public class MultiBuyMultiGiftActivity extends BaseActivity implements EventConsumer {
    
    private static final Logger LOGGER = LogManager.getLogger(MultiBuyMultiGiftActivity.class);
    
    // 默认折扣值（当折扣池为空时使用）
    private static final int DEFAULT_DISCOUNT = 95;
    
    public MultiBuyMultiGiftActivity(Player player, ActivityUnit activityUnit) {
        super(player, activityUnit);
    }

    @Override
    public ActivityType getType() {
        return ActivityType.AT_MultiBuyMultiGift;
    }

    @Override
    public void init(boolean isNew) {
        super.init(isNew);
        
        MultiBuyActivityData data = getActivityData();
        if (isNew) {
            // 初始化活动数据
            data.setDailyBuyCount(0);
            data.setHasDoubleDiscount(false);
            data.setLastResetTime(Framework.currentTimeSec());
            data.setTotalBuyCount(0);
            data.setTotalSavedMoney(0);
            data.setDoubleDiscountUsedToday(false);
            data.getUsedDiscountIds().clear();
        }
        
        // 检查每日重置
        checkAndResetDaily();
        
        LOGGER.info("MultiBuyMultiGiftActivity init, uid:{}, activityId:{}, isNew:{}", 
                   player.getUid(), activityId, isNew);
    }

    @Override
    public void onEvent(BaseEvent event) {
        if (event instanceof MallCommodityBuyEvent) {
            MallCommodityBuyEvent buyEvent = (MallCommodityBuyEvent) event;
            handleCommodityBuy(buyEvent.getCommodityId(), buyEvent.getBuyNum(), buyEvent.getFinalPrice());
        }
    }

    /**
     * 获取活动数据
     */
    public MultiBuyActivityData getActivityData() {
        return getActivityUnit().getDetailData().getMultiBuyActivityData();
    }

    /**
     * 获取活动配置
     */
    public MultiBuyActivityConf getActivityConf() {
        // 从配置表获取活动配置
        int confId = getActivityMainConfig().getActivityParam(0);
        MultiBuyActivityConf conf = MultiBuyActivityConfData.getInstance().get(confId);
        if (conf == null) {
            NKErrorCode.ActivityMainConfigNotExist.throwError("MultiBuyActivity config not found, confId:{}", confId);
        }
        return conf;
    }

    /**
     * 检查并执行每日重置
     */
    public void checkAndResetDaily() {
        MultiBuyActivityData data = getActivityData();
        long currentTime = Framework.currentTimeSec();
        long lastResetTime = data.getLastResetTime();
        
        if (TimeUtil.isDifferentDay(lastResetTime, currentTime)) {
            LOGGER.info("Daily reset for MultiBuyActivity, uid:{}, activityId:{}", player.getUid(), activityId);
            
            // 检查是否获得双倍折扣权益（基于昨日购买次数）
            MultiBuyActivityConf conf = getActivityConf();
            boolean earnedDoubleDiscount = data.getDailyBuyCount() >= conf.getDailyBuyThreshold();
            
            // 重置今日数据
            data.setDailyBuyCount(0);
            data.getUsedDiscountIds().clear();
            data.setDoubleDiscountUsedToday(false);
            data.setLastResetTime(currentTime);
            
            // 设置双倍折扣权益
            if (conf.getEnableDoubleDiscount()) {
                data.setHasDoubleDiscount(earnedDoubleDiscount);
                if (earnedDoubleDiscount) {
                    LOGGER.info("Player earned double discount privilege, uid:{}, activityId:{}", 
                               player.getUid(), activityId);
                }
            }
        }
    }

    /**
     * 计算商品折扣
     */
    public MultiBuyDiscountResult calculateDiscount(int commodityId) {
        checkAndResetDaily();
        
        MultiBuyActivityConf conf = getActivityConf();
        MultiBuyActivityData data = getActivityData();
        
        // 检查是否为活动商品
        if (!isActivityCommodity(commodityId)) {
            return createNoDiscountResult();
        }
        
        // 抽取基础折扣
        int baseDiscount = drawDailyDiscount();
        
        // 计算最终折扣（考虑双倍折扣）
        boolean useDoubleDiscount = data.getHasDoubleDiscount() && !data.getDoubleDiscountUsedToday();
        int finalDiscount = calculateFinalDiscount(baseDiscount, useDoubleDiscount);
        
        // 如果使用了双倍折扣，标记为已使用
        if (useDoubleDiscount) {
            data.setDoubleDiscountUsedToday(true);
        }
        
        return MultiBuyDiscountResult.newBuilder()
                .setBaseDiscount(baseDiscount)
                .setFinalDiscount(finalDiscount)
                .setHasDoubleDiscount(useDoubleDiscount)
                .build();
    }

    /**
     * 抽取每日折扣（不放回）
     */
    private int drawDailyDiscount() {
        MultiBuyActivityConf conf = getActivityConf();
        MultiBuyActivityData data = getActivityData();
        
        // 获取可用折扣池
        List<MultiBuyDiscountPoolConf> availableDiscounts = getAvailableDiscounts(conf, data);
        
        if (availableDiscounts.isEmpty()) {
            LOGGER.warn("No available discounts in pool, using default, uid:{}, activityId:{}", 
                       player.getUid(), activityId);
            return conf.getDefaultDiscount() > 0 ? conf.getDefaultDiscount() : DEFAULT_DISCOUNT;
        }
        
        // 权重随机抽取
        MultiBuyDiscountPoolConf selectedDiscount = weightedRandomSelect(availableDiscounts);
        
        // 记录已使用
        data.getUsedDiscountIds().add(selectedDiscount.getDiscountId());
        
        LOGGER.debug("Drew discount, uid:{}, activityId:{}, discountId:{}, value:{}", 
                    player.getUid(), activityId, selectedDiscount.getDiscountId(), selectedDiscount.getDiscountValue());
        
        return selectedDiscount.getDiscountValue();
    }

    /**
     * 获取可用折扣列表（排除已使用的）
     */
    private List<MultiBuyDiscountPoolConf> getAvailableDiscounts(MultiBuyActivityConf conf, MultiBuyActivityData data) {
        return conf.getDiscountPoolIdsList().stream()
                .map(poolId -> MultiBuyDiscountPoolConfData.getInstance().get(poolId))
                .filter(Objects::nonNull)
                .filter(discount -> !data.getUsedDiscountIds().contains(discount.getDiscountId()))
                .collect(Collectors.toList());
    }

    /**
     * 权重随机选择
     */
    private MultiBuyDiscountPoolConf weightedRandomSelect(List<MultiBuyDiscountPoolConf> discounts) {
        int totalWeight = discounts.stream().mapToInt(MultiBuyDiscountPoolConf::getWeight).sum();
        int randomValue = ThreadLocalRandom.current().nextInt(totalWeight);

        int currentWeight = 0;
        for (MultiBuyDiscountPoolConf discount : discounts) {
            currentWeight += discount.getWeight();
            if (randomValue < currentWeight) {
                return discount;
            }
        }

        // 兜底返回第一个
        return discounts.get(0);
    }

    /**
     * 计算最终折扣（考虑双倍折扣）
     */
    private int calculateFinalDiscount(int baseDiscount, boolean hasDoubleDiscount) {
        if (hasDoubleDiscount) {
            // 双倍折上折：实际折扣 = 抽取折扣 × 抽取折扣 / 100
            return (baseDiscount * baseDiscount) / 100;
        }
        return baseDiscount;
    }

    /**
     * 检查是否为活动商品
     */
    public boolean isActivityCommodity(int commodityId) {
        MultiBuyActivityConf conf = getActivityConf();
        return conf.getCommodityIdsList().contains(commodityId);
    }

    /**
     * 处理商品购买事件
     */
    private void handleCommodityBuy(int commodityId, int buyNum, int finalPrice) {
        if (!isActivityCommodity(commodityId)) {
            return;
        }

        MultiBuyActivityData data = getActivityData();
        MultiBuyActivityConf conf = getActivityConf();

        // 更新购买计数
        data.setDailyBuyCount(data.getDailyBuyCount() + buyNum);
        data.setTotalBuyCount(data.getTotalBuyCount() + buyNum);

        // 检查是否解锁明日双倍折扣
        boolean unlockDoubleDiscount = false;
        if (conf.getEnableDoubleDiscount() &&
            data.getDailyBuyCount() >= conf.getDailyBuyThreshold() &&
            !data.getHasDoubleDiscount()) {

            unlockDoubleDiscount = true;
            LOGGER.info("Player will unlock double discount tomorrow, uid:{}, activityId:{}, dailyBuyCount:{}",
                       player.getUid(), activityId, data.getDailyBuyCount());
        }

        // 发送状态通知
        sendActivityStatusNotification(unlockDoubleDiscount);

        LOGGER.debug("Handled commodity buy, uid:{}, commodityId:{}, buyNum:{}, dailyBuyCount:{}",
                    player.getUid(), commodityId, buyNum, data.getDailyBuyCount());
    }

    /**
     * 创建无折扣结果
     */
    private MultiBuyDiscountResult createNoDiscountResult() {
        return MultiBuyDiscountResult.newBuilder()
                .setBaseDiscount(100)
                .setFinalDiscount(100)
                .setHasDoubleDiscount(false)
                .build();
    }

    /**
     * 发送活动状态通知
     */
    private void sendActivityStatusNotification(boolean unlockDoubleDiscount) {
        MultiBuyActivityData data = getActivityData();

        CsMultiBuyActivity.MultiBuyActivityStatusNtf.Builder ntf =
            CsMultiBuyActivity.MultiBuyActivityStatusNtf.newBuilder()
                .setActivityId(activityId)
                .setDailyBuyCount(data.getDailyBuyCount())
                .setHasDoubleDiscount(data.getHasDoubleDiscount())
                .setUnlockDoubleDiscount(unlockDoubleDiscount)
                .addAllUsedDiscountIds(data.getUsedDiscountIds())
                .setTotalSavedMoney(data.getTotalSavedMoney())
                .setDoubleDiscountUsedToday(data.getDoubleDiscountUsedToday());

        player.sendNtfMsg(MsgTypes.MSG_TYPE_MULTIBUYACTIVITYSTATUSNTF, ntf);
    }

    /**
     * 获取活动商品信息列表
     */
    public List<ActivityCommodityInfo> getActivityCommodities() {
        MultiBuyActivityConf conf = getActivityConf();
        List<ActivityCommodityInfo> commodities = new ArrayList<>();

        for (int commodityId : conf.getCommodityIdsList()) {
            ActivityCommodityInfo info = buildCommodityInfo(commodityId);
            if (info != null) {
                commodities.add(info);
            }
        }

        return commodities;
    }

    /**
     * 构建商品信息
     */
    private ActivityCommodityInfo buildCommodityInfo(int commodityId) {
        // 获取商品配置
        ResMall.MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null) {
            return null;
        }

        MultiBuyActivityData data = getActivityData();
        int originalPrice = commodityConf.getPrice();

        // 预览折扣（不实际消耗）
        int previewDiscount = previewNextDiscount();
        boolean willUseDoubleDiscount = data.getHasDoubleDiscount() && !data.getDoubleDiscountUsedToday();
        int finalDiscount = calculateFinalDiscount(previewDiscount, willUseDoubleDiscount);
        int finalPrice = (originalPrice * finalDiscount) / 100;

        return ActivityCommodityInfo.newBuilder()
                .setCommodityId(commodityId)
                .setOriginalPrice(originalPrice)
                .setCurrentDiscount(finalDiscount)
                .setFinalPrice(finalPrice)
                .setIsDiscountUsed(data.getUsedDiscountIds().size() > 0)
                .build();
    }

    /**
     * 预览下一个折扣（不消耗）
     */
    private int previewNextDiscount() {
        MultiBuyActivityConf conf = getActivityConf();
        MultiBuyActivityData data = getActivityData();

        List<MultiBuyDiscountPoolConf> availableDiscounts = getAvailableDiscounts(conf, data);
        if (availableDiscounts.isEmpty()) {
            return conf.getDefaultDiscount() > 0 ? conf.getDefaultDiscount() : DEFAULT_DISCOUNT;
        }

        // 返回权重最高的折扣作为预览
        return availableDiscounts.stream()
                .max(Comparator.comparingInt(MultiBuyDiscountPoolConf::getWeight))
                .map(MultiBuyDiscountPoolConf::getDiscountValue)
                .orElse(DEFAULT_DISCOUNT);
    }

    /**
     * 获取剩余可用折扣数量
     */
    public int getRemainingDiscountCount() {
        MultiBuyActivityConf conf = getActivityConf();
        MultiBuyActivityData data = getActivityData();

        return (int) conf.getDiscountPoolIdsList().stream()
                .filter(poolId -> !data.getUsedDiscountIds().contains(poolId))
                .count();
    }
}
