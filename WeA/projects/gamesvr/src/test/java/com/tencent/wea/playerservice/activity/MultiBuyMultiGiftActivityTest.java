package com.tencent.wea.playerservice.activity;

import com.tencent.wea.playerservice.activity.implement.MultiBuyMultiGiftActivity;
import com.tencent.wea.playerservice.activity.lifecycle.ActivityUnit;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.attr.MultiBuyActivityData;
import com.tencent.wea.xlsRes.ResMultiBuyActivity.*;
import com.tencent.wea.common.Framework;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashSet;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 多买多送活动单元测试
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public class MultiBuyMultiGiftActivityTest {
    
    @Mock
    private Player mockPlayer;
    
    @Mock
    private ActivityUnit mockActivityUnit;
    
    @Mock
    private MultiBuyActivityData mockActivityData;
    
    private MultiBuyMultiGiftActivity activity;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 模拟活动数据
        when(mockActivityUnit.getDetailData().getMultiBuyActivityData()).thenReturn(mockActivityData);
        when(mockActivityData.getUsedDiscountIds()).thenReturn(new HashSet<>());
        when(mockActivityData.getDailyBuyCount()).thenReturn(0);
        when(mockActivityData.getHasDoubleDiscount()).thenReturn(false);
        when(mockActivityData.getLastResetTime()).thenReturn(Framework.currentTimeSec());
        
        activity = new MultiBuyMultiGiftActivity(mockPlayer, mockActivityUnit);
    }
    
    @Test
    void testCalculateFinalDiscount_NormalDiscount() {
        // 测试普通折扣计算
        int baseDiscount = 80; // 8折
        boolean hasDoubleDiscount = false;
        
        int result = activity.calculateFinalDiscount(baseDiscount, hasDoubleDiscount);
        
        assertEquals(80, result, "普通折扣应该返回原值");
    }
    
    @Test
    void testCalculateFinalDiscount_DoubleDiscount() {
        // 测试双倍折扣计算
        int baseDiscount = 80; // 8折
        boolean hasDoubleDiscount = true;
        
        int result = activity.calculateFinalDiscount(baseDiscount, hasDoubleDiscount);
        
        assertEquals(64, result, "双倍折扣应该是 80*80/100=64");
    }
    
    @Test
    void testCalculateFinalDiscount_EdgeCases() {
        // 测试边界情况
        assertEquals(100, activity.calculateFinalDiscount(100, false), "无折扣情况");
        assertEquals(100, activity.calculateFinalDiscount(100, true), "双倍无折扣情况");
        assertEquals(25, activity.calculateFinalDiscount(50, true), "5折双倍应该是25");
    }
    
    @Test
    void testWeightedRandomSelect() {
        // 创建测试折扣池
        MultiBuyDiscountPoolConf discount1 = MultiBuyDiscountPoolConf.newBuilder()
                .setDiscountId(1)
                .setDiscountValue(80)
                .setWeight(50)
                .build();
        
        MultiBuyDiscountPoolConf discount2 = MultiBuyDiscountPoolConf.newBuilder()
                .setDiscountId(2)
                .setDiscountValue(90)
                .setWeight(30)
                .build();
        
        MultiBuyDiscountPoolConf discount3 = MultiBuyDiscountPoolConf.newBuilder()
                .setDiscountId(3)
                .setDiscountValue(70)
                .setWeight(20)
                .build();
        
        // 多次测试权重随机选择
        int[] counts = new int[4]; // index 0 unused, 1-3 for discount1-3
        int testRounds = 10000;
        
        for (int i = 0; i < testRounds; i++) {
            MultiBuyDiscountPoolConf selected = activity.weightedRandomSelect(
                Arrays.asList(discount1, discount2, discount3));
            counts[selected.getDiscountId()]++;
        }
        
        // 验证权重分布大致正确（允许一定误差）
        double total = testRounds;
        double ratio1 = counts[1] / total;
        double ratio2 = counts[2] / total;
        double ratio3 = counts[3] / total;
        
        assertTrue(Math.abs(ratio1 - 0.5) < 0.05, "权重50%的折扣选中率应该接近50%");
        assertTrue(Math.abs(ratio2 - 0.3) < 0.05, "权重30%的折扣选中率应该接近30%");
        assertTrue(Math.abs(ratio3 - 0.2) < 0.05, "权重20%的折扣选中率应该接近20%");
    }
    
    @Test
    void testIsActivityCommodity() {
        // 模拟活动配置
        MultiBuyActivityConf mockConf = MultiBuyActivityConf.newBuilder()
                .addCommodityIds(1001)
                .addCommodityIds(1002)
                .addCommodityIds(1003)
                .build();
        
        when(activity.getActivityConf()).thenReturn(mockConf);
        
        assertTrue(activity.isActivityCommodity(1001), "商品1001应该是活动商品");
        assertTrue(activity.isActivityCommodity(1002), "商品1002应该是活动商品");
        assertTrue(activity.isActivityCommodity(1003), "商品1003应该是活动商品");
        assertFalse(activity.isActivityCommodity(2001), "商品2001不应该是活动商品");
    }
    
    @Test
    void testDailyReset() {
        // 模拟昨天的数据
        long yesterdayTime = Framework.currentTimeSec() - 24 * 3600;
        when(mockActivityData.getLastResetTime()).thenReturn(yesterdayTime);
        when(mockActivityData.getDailyBuyCount()).thenReturn(5);
        
        // 模拟活动配置
        MultiBuyActivityConf mockConf = MultiBuyActivityConf.newBuilder()
                .setDailyBuyThreshold(3)
                .setEnableDoubleDiscount(true)
                .build();
        when(activity.getActivityConf()).thenReturn(mockConf);
        
        // 执行每日重置
        activity.checkAndResetDaily();
        
        // 验证重置操作
        verify(mockActivityData).setDailyBuyCount(0);
        verify(mockActivityData).setDoubleDiscountUsedToday(false);
        verify(mockActivityData).setLastResetTime(anyLong());
        verify(mockActivityData).setHasDoubleDiscount(true); // 昨日购买5次 >= 阈值3，应该获得双倍折扣
        verify(mockActivityData.getUsedDiscountIds()).clear();
    }
    
    @Test
    void testDiscountPoolExhaustion() {
        // 模拟折扣池已用完的情况
        HashSet<Integer> usedDiscounts = new HashSet<>();
        usedDiscounts.add(1);
        usedDiscounts.add(2);
        usedDiscounts.add(3);
        when(mockActivityData.getUsedDiscountIds()).thenReturn(usedDiscounts);
        
        MultiBuyActivityConf mockConf = MultiBuyActivityConf.newBuilder()
                .addDiscountPoolIds(1)
                .addDiscountPoolIds(2)
                .addDiscountPoolIds(3)
                .setDefaultDiscount(95)
                .build();
        when(activity.getActivityConf()).thenReturn(mockConf);
        
        // 模拟空的可用折扣列表
        when(activity.getAvailableDiscounts(any(), any())).thenReturn(Arrays.asList());
        
        int result = activity.drawDailyDiscount();
        
        assertEquals(95, result, "折扣池用完时应该返回默认折扣");
    }
    
    @Test
    void testActivityDataInitialization() {
        // 测试新活动数据初始化
        activity.init(true);
        
        verify(mockActivityData).setDailyBuyCount(0);
        verify(mockActivityData).setHasDoubleDiscount(false);
        verify(mockActivityData).setLastResetTime(anyLong());
        verify(mockActivityData).setTotalBuyCount(0);
        verify(mockActivityData).setTotalSavedMoney(0);
        verify(mockActivityData).setDoubleDiscountUsedToday(false);
        verify(mockActivityData.getUsedDiscountIds()).clear();
    }
    
    @Test
    void testCommodityBuyHandling() {
        // 模拟购买前状态
        when(mockActivityData.getDailyBuyCount()).thenReturn(2);
        when(mockActivityData.getTotalBuyCount()).thenReturn(10);
        
        MultiBuyActivityConf mockConf = MultiBuyActivityConf.newBuilder()
                .setDailyBuyThreshold(3)
                .setEnableDoubleDiscount(true)
                .addCommodityIds(1001)
                .build();
        when(activity.getActivityConf()).thenReturn(mockConf);
        
        // 模拟购买事件
        activity.handleCommodityBuy(1001, 2, 800);
        
        // 验证购买计数更新
        verify(mockActivityData).setDailyBuyCount(4); // 2 + 2
        verify(mockActivityData).setTotalBuyCount(12); // 10 + 2
    }
}
