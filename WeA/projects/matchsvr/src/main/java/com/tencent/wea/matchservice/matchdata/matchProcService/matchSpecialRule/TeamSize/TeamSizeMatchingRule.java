package com.tencent.wea.matchservice.matchdata.matchProcService.matchSpecialRule.TeamSize;

import com.tencent.match.matchConfigProxyMgr.ConfigProxyMgr;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKPair;
import com.tencent.resourceloader.resclass.MatchTeamSizeMatchingRuleData;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.wea.matchservice.matchdata.MatchTeamInfo;
import com.tencent.wea.matchservice.matchdata.fillBack.MatchFillBackUtils;
import com.tencent.wea.matchservice.matchdata.matchProcService.matchProcCommon.MatchAlgorithmUtils.MatchParams;
import com.tencent.wea.matchservice.matchdata.matchProcService.matchSpecialRule.Filter.MatchSpecialRuleFilterProcessor;
import com.tencent.wea.xlsRes.ResMatch;
import com.tencent.wea.xlsRes.ResMatch.MatchSideInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Unmodifiable;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class TeamSizeMatchingRule {
    private static final Logger LOGGER = LogManager.getLogger(TeamSizeMatchingRule.class);
    /**
     * 处理队伍人数大于所有阵营剩余真人人数, 如果出现大于就挤占AI坑位 并且需要把其他阵营最大真人数也增多
     * @param teamInfo 队伍
     * @param roomInfoCfg 配置
     * @param tmpResultBuilderList 匹配撮合真人阵营信息
     */
    public static boolean procMultiPlayerTeamOccupyAIPos(MatchTeamInfo teamInfo,
            ResMatch.MatchRoomInfo roomInfoCfg, List<MatchSideInfo.Builder> tmpResultBuilderList) {

        if (!checkSideSymmetryBuilder(tmpResultBuilderList)) {
            LOGGER.warn("procMultiPlayerTeamOccupyAIPos checkSideSymmetryBuilder " +
                            "return false roomID:{} roomInfoID: {} tmpResultBuilderList:{}",
                    teamInfo.getRoomID(), roomInfoCfg.getId(), tmpResultBuilderList);
            return false;
        }
        int sidePlayers = ConfigProxyMgr.getInstance().getPlayerSideList(teamInfo.getDynamicConfigData(), roomInfoCfg).get(0).getTeamPlayers();
        //队伍人数大于配置的最大人数, 没有可扩充的阵营直接失败
        if (teamInfo.getMemberCnt() > sidePlayers) {
            LOGGER.warn("procMultiPlayerTeamOccupyAIPos teamInfo.getMemberCnt() > lastTeamPlayers " +
                            "roomID:{} roomInfoID: {} lastTeamPlayers: {} getTeamPlayers:{}",
                    teamInfo.getRoomID(), roomInfoCfg.getId(), sidePlayers, teamInfo.getMemberCnt());
            return false;
        }

        for (MatchSideInfo.Builder tmpResultBuilder : tmpResultBuilderList) {
            if (tmpResultBuilder.getTeamPlayers() < teamInfo.getHumanMemberCnt()) {
                LOGGER.info("procMultiPlayerTeamOccupyAIPos succ roomID:{} roomInfoID: {} memCnt: {} "+
                                "humanMemberCnt: {} originPlayers:{} matchSideID:{}",
                        teamInfo.getRoomID(), roomInfoCfg.getId(), teamInfo.getMemberCnt(),
                        teamInfo.getHumanMemberCnt(), tmpResultBuilder.getTeamPlayers(), tmpResultBuilder.getSideID());
                //找到这个阵营并且扩展设置成队伍人数
                tmpResultBuilder.setTeamPlayers(teamInfo.getHumanMemberCnt());
            }
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("procMultiPlayerTeamOccupyAIPos end roomID:{} roomInfoID: {} memCnt:{} humanMemberCnt: {} tmpResultBuilderList:{}",
                    teamInfo.getRoomID(), roomInfoCfg.getId(), teamInfo.getMemberCnt(),  teamInfo.getHumanMemberCnt(), tmpResultBuilderList);
        }

        //返回是否成功挤占AI坑位
        return true;
    }

    //检查阵营必须对称
    private static boolean checkSideSymmetryBuilder(List<MatchSideInfo.Builder> matchSideList) {
        List<Integer> intList = new ArrayList<>();
        for (MatchSideInfo.Builder matchSideInfo : matchSideList) {
            intList.add(matchSideInfo.getTeamPlayers());
        }
        return checkIntSymmetry(intList);
    }

    //检测阵营信息对称, 并且不能为空
    private static boolean checkSideSymmetry(List<MatchSideInfo> matchSideList) {
        List<Integer> intList = new ArrayList<>();
        for (MatchSideInfo matchSideInfo : matchSideList) {
            intList.add(matchSideInfo.getTeamPlayers());
        }
        return checkIntSymmetry(intList);
    }

    //检测阵营信息对称, 并且不能为空
    private static boolean checkIntSymmetry(List<Integer> matchSideList) {
        if (matchSideList.isEmpty()) {
            LOGGER.warn("checkIntSymmetry fail matchSideList is empty");
            return false;
        }
        int lastTeamPlayers = -1;
        //必须是对称阵营
        for (int sideCnt : matchSideList) {
            if (-1 == lastTeamPlayers) {
                lastTeamPlayers = sideCnt;
            } else if (lastTeamPlayers != sideCnt) {
                //不对称返回失败
                LOGGER.warn("checkIntSymmetry lastTeamPlayers != matchSideInfo.getTeamPlayers " +
                                "lastTeamPlayers: {} sideCnt:{} matchSideList:{}",
                        lastTeamPlayers, sideCnt, matchSideList);
                return false;
            }
        }
        return true;
    }

    //检查阵营必须对称
    private static boolean procBaseTeam(List<MatchSideInfo> matchSideList,
                                        ResMatch.MatchRoomInfo roomCfg,
                                        MatchParams matchParams,
                                        Set<MatchTeamInfo> teamSetBak,
                                        List<Integer> numResult,
                                        ResMatch.MatchRule rule) {
        int baseSideID = -1;
        MatchSideInfo baseMatchSideInfo = null;
        //先处理baseTeam
        MatchTeamInfo baseTeamInfo = matchParams.baseTeamInfo;
        Iterator<MatchSideInfo> iterator = matchSideList.iterator();
        while (iterator.hasNext()) {
            MatchSideInfo matchSideInfo = iterator.next();
            if (matchSideInfo.getTeamPlayers() >= baseTeamInfo.getHumanMemberCnt()) {
                baseTeamInfo.setSide(matchSideInfo.getSideID());
                iterator.remove();
                baseMatchSideInfo = MatchSideInfo.newBuilder(matchSideInfo).setTeamPlayers
                                        (matchSideInfo.getTeamPlayers() - baseTeamInfo.getHumanMemberCnt()).build();
                baseSideID = matchSideInfo.getSideID();
                numResult.add(baseTeamInfo.getHumanMemberCnt());
                matchParams.matchResult.add(baseTeamInfo);
                break;
            }
        }

        //baseTeam没有找到可以使用的阵营
        if (baseSideID < 0 || null == baseMatchSideInfo) {
            LOGGER.warn("procBaseTeam not found baseTeam SideID  roomID:{} roomInfoID: {} "+
                            "getHumanMemberCnt:{} matchSideList:{}", baseTeamInfo.getRoomID(),
                    roomCfg.getId(), baseTeamInfo.getHumanMemberCnt(), matchSideList);
            return false;
        }

        int sideTotalPlayers = -1;
        for (var sideInfo : roomCfg.getBaseRule().getTeamPlayersList()) {
            if (sideInfo.getSideID() == baseSideID) {
                sideTotalPlayers = sideInfo.getTeamPlayers();
            }
        }

        //baseTeam没有找到可以使用的阵营
        if (sideTotalPlayers < 0) {
            LOGGER.warn("procBaseTeam sideTotalPlayers < 0  roomID:{} roomInfoID: {} sideTotalPlayers:{}",
                                    baseTeamInfo.getRoomID(), roomCfg.getId(), sideTotalPlayers);
            return false;
        }

        //处理baseTeam阵营
        Set<MatchTeamInfo> result = new HashSet<>();
        if (baseMatchSideInfo.getTeamPlayers() > 0) {
            //baseTeam阵营失败了 直接返回失败
            MatchSpecialRuleFilterProcessor ruleFilterProcessor = MatchSpecialRuleFilterProcessor.createRuleFilerProcessor(baseTeamInfo, rule);

            // 先尝试用新的基于excel配置的规则去匹配
            FindTeamCombinationResult findBaseTeamExcelCfgCombination = FindTeamExcelCfgCombination.findBaseTeamExcelCfgCombination(teamSetBak, baseTeamInfo,
                    baseMatchSideInfo.getTeamPlayers(), ruleFilterProcessor, sideTotalPlayers);
            switch (findBaseTeamExcelCfgCombination.getErrorCode()) {
                // 找不到 返回
                case NotFound: {
                    LOGGER.info("team {} leader {} memberCnt {} roomInfoId {} findBaseTeamExcelCfgCombination ret not found",
                            baseTeamInfo.getRoomID(), baseTeamInfo.getLeaderID(), baseTeamInfo.getMemberCnt(), baseTeamInfo.getRoomInfoID());
                    return false;
                }

                // 使用旧规则
                case UseOldStrategy: {
                    //尽量撮合和自己一样人数的队伍
                    result = TeamSizeBaseTeamMatching.findCombination(teamSetBak, baseTeamInfo, baseMatchSideInfo.getTeamPlayers(), ruleFilterProcessor, sideTotalPlayers);
                    break;
                }

                // 成功
                case OK: {
                    result = findBaseTeamExcelCfgCombination.getFoundTeams();

                    break;
                }
            }

            if (null == result) {
                LOGGER.info("procBaseTeam baseTeam not match + roomID:{} roomInfoID: {} getTeamPlayers:{} "+
                                "SideID:{} matchSideList:{} baseMatchSideInfo.getTeamPlayers():{} sideTotalPlayers:{}",
                                baseTeamInfo.getRoomID(), roomCfg.getId(), baseTeamInfo.getMemberCnt(),
                        baseMatchSideInfo.getSideID(), matchSideList, baseMatchSideInfo.getTeamPlayers(), sideTotalPlayers);
                return false;
            }
            // 这里的result包括baseTeam
            for (MatchTeamInfo matchTeamInfo : result) {
                matchTeamInfo.setSide(baseSideID);
            }
            matchParams.matchResult.addAll(result);
            teamSetBak.removeAll(result);
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("procBaseTeam baseTeam succ roomID:{} roomInfoID: {} getTeamPlayers:{} getHumanMemberCnt: "
                            +"{} SideID:{} matchSideList:{} baseMatchSideInfo.getTeamPlayers():{} sideTotalPlayers:{} result：{}",
                    baseTeamInfo.getRoomID(), roomCfg.getId(), baseTeamInfo.getMemberCnt(), baseTeamInfo.getHumanMemberCnt(),
                    baseMatchSideInfo.getSideID(), NKStringFormater.formatShortPbCollections(matchSideList), baseMatchSideInfo.getTeamPlayers(),
                    sideTotalPlayers, NKStringFormater.formatCollectionLambdaToSizeLimitStr(result, 1024,
                            team -> String.valueOf(team.getRoomID())));
        }
        result.forEach(matchTeamInfo -> {
            numResult.add(matchTeamInfo.getHumanMemberCnt());
        });
        return true;
    }

    // 检查最后的匹配结果
    private static boolean checkMatchResult(ResMatch.MatchRoomInfo roomCfg, MatchParams matchParams) {
        boolean isValid = true;

        long baseRoomId = matchParams.baseTeamInfo.getRoomID();

        Map<Integer, Integer> sideToTotalPlayerCntMap = new HashMap<>();
        for (MatchSideInfo sideInfo: roomCfg.getBaseRule().getTeamPlayersList()) {
            sideToTotalPlayerCntMap.put(sideInfo.getSideID(),
                    sideToTotalPlayerCntMap.getOrDefault(sideInfo.getSideID(), 0) + sideInfo.getTeamPlayers());
        }

        boolean isFoundBaseTeam = false;
        Map<Integer, Integer> matchResultSideToPlayerCntMap = new HashMap<>();
        for (MatchTeamInfo team: matchParams.matchResult) {
            if (!isFoundBaseTeam && baseRoomId == team.getRoomID()) {
                isFoundBaseTeam = true;
            }

            matchResultSideToPlayerCntMap.put(team.getSide(),
                    matchResultSideToPlayerCntMap.getOrDefault(team.getSide(), 0) + team.getHumanMemberCnt());
        }

        // 检查是否包含baseTeam
        if (!isFoundBaseTeam) {
            isValid = false;
            LOGGER.error("baseTeam {} not found roomInfoId {}",
                    baseRoomId, roomCfg.getId());
            Monitor.getInstance().add.total(MonitorId.attr_matchsvr_team_size_match_rule_result_err, 1, new String[]{
                    String.valueOf(roomCfg.getId()),
            });
        }

        // 分阵营检查人数
        for (Map.Entry<Integer, Integer> cfgEntry: sideToTotalPlayerCntMap.entrySet()) {
            int cfgTotalPlayerCnt = cfgEntry.getValue();
            int resultPlayerCnt = matchResultSideToPlayerCntMap.getOrDefault(cfgEntry.getKey(), 0);
            // 这里还没有填充ai 所以要检查的是目前的人数不能超过配置的总人数 但是可以少于
            if (resultPlayerCnt > cfgTotalPlayerCnt) {
                isValid = false;

                LOGGER.error("roomID:{} resultPlayerCnt {} > cfgTotalPlayerCnt {}",
                        roomCfg.getId(), resultPlayerCnt, cfgTotalPlayerCnt);

                Monitor.getInstance().add.total(MonitorId.attr_matchsvr_team_size_match_rule_result_err, 1, new String[]{
                        String.valueOf(roomCfg.getId()),
                });
            }
        }

        return isValid;
    }

    //[left, right]
    private static NKPair<Integer, Integer> getScoreScope(int teamScore, MatchTeamInfo matchTeamInfo, int specialRuleID) {
        long duration = Framework.currentTimeMillis() - matchTeamInfo.getMatchTime();
        if (duration < 0) {
            LOGGER.warn("getScoreScope duration < 0 roomID:{} currentTimeMillis: {} getMatchTime:{}",
                    matchTeamInfo.getRoomID(), Framework.currentTimeMillis(), matchTeamInfo.getMatchTime());
            return new NKPair<>(teamScore, teamScore);
        }

        ResMatch.TeamSizeMatchingRelax matchingRelax = MatchTeamSizeMatchingRuleData.getInstance().
                                                                    getMatchingRelax(specialRuleID, duration);
        if (null == matchingRelax) {
            LOGGER.warn("getScoreScope getMatchingRelax return null roomID:{} specialRuleID:{}",
                    matchTeamInfo.getRoomID(), specialRuleID);
            return new NKPair<>(teamScore, teamScore);
        }

        double leftDivide = matchingRelax.getScopeLeftDivide() / 100.0;
        double rightMutiply = matchingRelax.getScopeRightMultiply() / 100.0;
        if (0.0f == leftDivide || 0.0f == rightMutiply) {
            LOGGER.warn("getScoreScope getMatchingRelax 0.0f == leftDivide || 0.0f == rightMutiply "+
                            "roomID:{} specialRuleID:{} leftDivide:{} rightMutiply:{} matchingRelax:{}",
                    matchTeamInfo.getRoomID(), specialRuleID, leftDivide, rightMutiply, matchingRelax);
            return new NKPair<>(teamScore, teamScore);
        }
        double leftScope = teamScore / leftDivide;
        double rightScope = teamScore * rightMutiply;
        LOGGER.info("getScoreScope getMatchingRelax succ  roomID:{} specialRuleID:{} leftDivide:{} "+
                        "rightMutiply:{} matchingRelax:{} teamScore:{} leftScope:{} rightScope:{}",
                matchTeamInfo.getRoomID(), specialRuleID, leftDivide, rightMutiply, matchingRelax,
                teamScore, leftScope, rightScope);
        return new NKPair<>((int)leftScope, (int)rightScope);
    }

    //按照队伍人数规则撮合队伍
    private static boolean matchTeamCombine(MatchParams matchParams, List<List<Integer>> resList,
                                            MatchSideInfo matchSideInfo, Set<MatchTeamInfo> teamSetBak,
                                            MatchSpecialRuleFilterProcessor ruleFilterProcessor,
                                            ResMatch.MatchRoomInfo roomCfg) {
        // 将 teamSetBak 转换为 List 并按 memberCnt 排序
        List<MatchTeamInfo> sortedTeams = new ArrayList<>(teamSetBak);
        sortedTeams.sort(Comparator.comparingInt(MatchTeamInfo::getHumanMemberCnt));
        int sideTotalPlayers = -1;
        for (var sideInfo : roomCfg.getBaseRule().getTeamPlayersList()) {
            if (sideInfo.getSideID() == matchSideInfo.getSideID()) {
                sideTotalPlayers = sideInfo.getTeamPlayers();
            }
        }
        if (sideTotalPlayers < 0) {
            LOGGER.error("matchTeamCombine sideTotalPlayers < 0 roomID:{} list:{}",
                    matchParams.baseTeamInfo.getRoomID(), matchSideInfo.getSideID());
            return false;
        }
        Set<MatchTeamInfo> currRoundMatchTeam = new HashSet<>();
        boolean fail = false;
        for (List<Integer> list : resList) {
            currRoundMatchTeam.clear();
            // 使用 ArrayList 的构造函数进行浅拷贝
            Set<Integer> delIndexSet = new HashSet<>();
            fail = false;
            int alreadyTotalMemCnt = 0;
            for (int cnt : list) {
                //getFillBackResultByExact 会删除copiedList已经
                int index = MatchFillBackUtils.getFillBackResultByExact(sortedTeams, delIndexSet, currRoundMatchTeam,
                                                    cnt, ruleFilterProcessor, alreadyTotalMemCnt, sideTotalPlayers);
                if (-1 != index) {
                    MatchTeamInfo result = sortedTeams.get(index);
                    alreadyTotalMemCnt += result.getMemberCnt();
                    delIndexSet.add(index);
                    currRoundMatchTeam.add(result);
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("getFillBackResultByExact succ roomID:{} list:{} cnt:{} delIndexSet:{} result:{}",
                                matchParams.baseTeamInfo.getRoomID(), list, cnt, delIndexSet, result);
                    } else {
                        LOGGER.info("getFillBackResultByExact succ roomID:{} list:{} cnt:{}",
                                matchParams.baseTeamInfo.getRoomID(), list, cnt);
                    }
                } else {
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("getFillBackResultByExact fail roomID:{} list:{} cnt:{} matchParams.matchResult:{}",
                                matchParams.baseTeamInfo.getRoomID(), list, cnt, matchParams.matchResult);
                    } else {
                        LOGGER.info("getFillBackResultByExact fail roomID:{} list:{} cnt:{} matchParams.matchResult.size:{}",
                                matchParams.baseTeamInfo.getRoomID(), list, cnt, matchParams.matchResult.size());
                    }
                    fail = true;
                    break;
                }
            }
            //如果没有失败这个组合就成功了
            if (!fail) {
                teamSetBak.removeAll(currRoundMatchTeam);
                //把结果加到最终集合里面
                currRoundMatchTeam.forEach(matchTeamInfo -> {
                    matchTeamInfo.setSide(matchSideInfo.getSideID());
                    matchParams.matchResult.add(matchTeamInfo);
                });

                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("now resList succ roomID:{} list:{}  matchParams.matchResult:{} currRoundMatchTeam:{}",
                            matchParams.baseTeamInfo.getRoomID(), list, matchParams.matchResult, currRoundMatchTeam);
                } else {
                    LOGGER.info("now resList succ roomID:{} list:{} matchParams.matchResult.size:{} currRoundMatchTeam.size:{}",
                            matchParams.baseTeamInfo.getRoomID(), list, matchParams.matchResult.size(), currRoundMatchTeam.size());
                }
                break;
            }
        }
        return !fail;
    }

    //获取所有队伍组合情况
    private static List<List<Integer>> getTeamSizeList(MatchParams matchParams, int sideNum,
                                                        List<Integer> numResult, int specialRuleID) {
        int teamScore = TeamSizeMatchingRuleDataMgr.getTeamScore(numResult);
        LOGGER.debug("getTeamScore roomID:{} teamScore:{} numResult:{}",
                matchParams.baseTeamInfo.getRoomID(), teamScore, numResult);

        NKPair<Integer, Integer> scoreScope = getScoreScope(teamScore, matchParams.baseTeamInfo, specialRuleID);
        LOGGER.info("getScoreScope roomID:{} specialRuleID:{} teamScore:{} scoreScope:{}",
                matchParams.baseTeamInfo.getRoomID(), specialRuleID, teamScore, scoreScope);

        List<List<Integer>> resList = TeamSizeMatchingRuleDataMgr.getPartitionResult(sideNum, teamScore, scoreScope);
        if (null == resList) {
            LOGGER.warn("getPartitionResult resList is null roomID:{} specialRuleID:{} teamScore:{} scoreScope:{}",
                    matchParams.baseTeamInfo.getRoomID(), specialRuleID, teamScore, scoreScope);
            return null;
        }
        LOGGER.info("getTeamSizeList roomID:{} specialRuleID:{} teamScore:{} resList:{}",
                matchParams.baseTeamInfo.getRoomID(), specialRuleID, teamScore, resList);
        return resList;
    }

    // 根据MatchSideInfo 填充matchParams的结果
    public static boolean fillResultWithMatchSideList(@Unmodifiable List<MatchSideInfo> matchSideList,
                                                      ResMatch.MatchRoomInfo roomCfg,
                                                      MatchParams matchParams,
                                                      ResMatch.MatchRule rule) {
        //检测阵营信息对称, 并且不能为空
        if (!checkSideSymmetry(matchSideList)) {
            //不对称返回失败
            LOGGER.warn("fillResultWithMatchSideList checkSideSymmetry failed roomID:{} roomInfoID:{}",
                    matchParams.baseTeamInfo.getRoomID(), roomCfg.getId());
            return false;
        }
        List<MatchSideInfo> sideSetBak = new ArrayList<>(matchSideList);
        Set<MatchTeamInfo> teamSetBak = new LinkedHashSet<>(matchParams.preResult);
        List<Integer> numResult = new ArrayList<>();
        if (!procBaseTeam(sideSetBak, roomCfg, matchParams, teamSetBak, numResult, rule)) {
            //baseTeam处理失败 直接返回失败
            LOGGER.info("fillResultWithMatchSideList procBaseTeam failed roomID:{} roomInfoID:{}",
                    matchParams.baseTeamInfo.getRoomID(), roomCfg.getId());
            return false;
        }
        //单阵营情况直接成功
        if (sideSetBak.isEmpty()) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.info("fillResultWithMatchSideList succ roomID:{} roomInfoID:{} matchResult:{} matchSideList:{}",
                        matchParams.baseTeamInfo.getRoomID(), roomCfg.getId(), matchParams.matchResult, matchSideList);
            } else {
                LOGGER.info("fillResultWithMatchSideList succ roomID:{} roomInfoID:{} matchResult.size:{} matchSideList:{}",
                        matchParams.baseTeamInfo.getRoomID(), roomCfg.getId(), matchParams.matchResult.size(), matchSideList);
            }
            return true;
        }

        //阵营人数, 因为是对称阵营所以取第一个就行了
        int sideNum = matchSideList.get(0).getTeamPlayers();
        //按照阵营挨个找, 而且上面确保是对称阵营所以人数都一样
        MatchSpecialRuleFilterProcessor ruleFilterProcessor = MatchSpecialRuleFilterProcessor.
                createRuleFilerProcessor(matchParams.baseTeamInfo, rule);

        // 先尝试用基于excel配置的组合规则
        FindTeamCombinationResult.ErrorCode findOpponentCombinationRet = FindTeamExcelCfgCombination.findOpponentExcelCfgCombination(teamSetBak,
                sideSetBak, matchParams, ruleFilterProcessor);
        if (findOpponentCombinationRet != FindTeamCombinationResult.ErrorCode.UseOldStrategy) {
            boolean isSucc = findOpponentCombinationRet == FindTeamCombinationResult.ErrorCode.OK;
            if (isSucc) {
                // 尝试优化下两边的队伍组合
                try {
                    OptimizeSideTeamAfterMatchUtils.optimizeSideTeamAfterMatchWhenNeed(roomCfg, matchParams);

                    // 检查匹配结果 内部有log
                    checkMatchResult(roomCfg, matchParams);
                } catch (Exception e) {
                    LOGGER.error("team {} leader {} optimizeSideTeamAfterMatchWhenNeed catch",
                            matchParams.baseTeamInfo.getRoomID(), matchParams.baseTeamInfo.getLeaderID(), e);
                }
            }

            return isSucc;
        }

        // 回落到老的匹配规则
        List<List<Integer>> resList = getTeamSizeList(matchParams, sideNum, numResult, rule.getSpecialRuleCfg().getSpecialRuleID());
        if (null == resList || resList.isEmpty()) {
            LOGGER.warn("getTeamSizeList resList is null or isEmpty roomID:{} specialRuleID:{} resList:{}",
                    matchParams.baseTeamInfo.getRoomID(), rule.getSpecialRuleCfg().getSpecialRuleID(), resList);
            return false;
        }
        LOGGER.info("getTeamSizeList roomID:{} roomInfoID:{} specialRuleID:{} resList:{}",
                matchParams.baseTeamInfo.getRoomID(), roomCfg.getId(), rule.getSpecialRuleCfg().getSpecialRuleID(), resList);

        boolean succ = true;
        for (MatchSideInfo matchSideInfo : sideSetBak) {
            //按照队伍人数规则撮合队伍
            if (teamSetBak.isEmpty() || !matchTeamCombine(matchParams, resList, matchSideInfo,
                                                            teamSetBak, ruleFilterProcessor, roomCfg)) {
                succ = false;
                break;
            }
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("fillResultWithMatchSideList roomID:{} roomInfoID:{} matchParams.matchResult:{} " +
                            "teamSetBak:{} succ:{}", matchParams.baseTeamInfo.getRoomID(), roomCfg.getId(),
                                matchParams.matchResult, teamSetBak, succ);
        } else {
            LOGGER.info("fillResultWithMatchSideList roomID:{} roomInfoID:{} matchParams." +
                            "matchResult.size:{} teamSetBak.size:{} succ:{}", matchParams.baseTeamInfo.getRoomID(),
                    roomCfg.getId(), matchParams.matchResult.size(), teamSetBak.size(), succ);
        }
        return succ;
    }
}