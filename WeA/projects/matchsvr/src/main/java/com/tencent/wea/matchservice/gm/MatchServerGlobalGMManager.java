package com.tencent.wea.matchservice.gm;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.framework.MSEngine;
import com.tencent.wea.matchservice.matchdata.MatchMgr;
import com.tencent.wea.protocol.SsMatchsvr;
import com.tencent.wea.protocol.common.MemberBaseInfo;
import com.tencent.wea.protocol.common.TeamData;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 匹配全局类型gm管理器
 */
public class MatchServerGlobalGMManager {
    private static final Logger LOGGER = LogManager.getLogger(MatchServerGlobalGMManager.class);

    // 记录的最近一次匹配请求的消息体
    private volatile SsMatchsvr.RpcMatchReq lastMatchReq;

    /**
     * 单例
     * @return 单例
     */
    public static MatchServerGlobalGMManager getInstance() {
        return MatchServerGlobalGMManager.InstanceHolder.instance;
    }

    // 单例holder
    private static class InstanceHolder {
        private static final MatchServerGlobalGMManager instance = new MatchServerGlobalGMManager();
    }

    // 禁止外面实例化
    private MatchServerGlobalGMManager() {
    }

    /**
     * 当有匹配请求时 记录下
     * @param req 匹配请求的消息体
     */
    public void recordLastMatchReq(SsMatchsvr.RpcMatchReq.Builder req) {
        if (!MSEngine.getInstance().canUseGmCmd()) {
            return;
        }

        if (MSEngine.getInstance().isBusiness()) {
            return;
        }
        if (MSEngine.getInstance().isPress()) {
            return;
        }

        lastMatchReq = req.build();
        LOGGER.info("set lastMatchReq room {} leader {}",
                lastMatchReq.getTeamData().getRoomID(), lastMatchReq.getTeamData().getLeaderID());
    }

    /**
     * 执行gm
     * @param gmCmd gm指令参数
     */
    public void onGmCmd(String gmCmd) {
        if (!MSEngine.getInstance().canUseGmCmd()) {
            return;
        }

        List<String> cmdArgs = new ArrayList<>();
        String subCmd = "";
        // 按空格分割
        final String cmdSplitChar = " ";
        for (String cmd : gmCmd.strip().split(cmdSplitChar)) {
            if (cmd.isEmpty()) {
                continue;
            }

            if (subCmd.isEmpty()) {
                subCmd = cmd;
            } else {
                cmdArgs.add(cmd);
            }
        }

        LOGGER.info("on subCmd: {} args: {}",
                subCmd, cmdArgs);

        switch (subCmd) {
            // 模拟匹配
            case "sim_match": {
                simulationMatchGmCmd(cmdArgs);

                break;
            }

            default: {
                LOGGER.warn("unknown subCmd: {}",
                        subCmd);
            }
        }
    }

    // 模拟匹配
    private void simulationMatchGmCmd(List<String> cmdArgs) {
        if (null == lastMatchReq) {
            LOGGER.error("lastMatchReq is null");

            return;
        }
        // 第一个参数 表示模拟多少个成员
        int simulationMemberCnt = Integer.parseInt(cmdArgs.get(0));
        // 第二个参数 模拟请求多少次
        int simulationReqCnt = Integer.parseInt(cmdArgs.get(1));

        boolean isReAssignSide = false;
        int reAssignSideId = 0;
        // 如果有第三个参数 则指定匹配申请的阵营
        if (cmdArgs.size() > 2) {
            isReAssignSide = true;
            reAssignSideId = Integer.parseInt(cmdArgs.get(2));
        }

        if (simulationMemberCnt <= 0 || simulationReqCnt <= 0) {
            LOGGER.error("simulationMemberCnt or simulationReqCnt is invalid: {} {}",
                    simulationMemberCnt, simulationReqCnt);
            return;
        }

        // 选uid最大的玩家作为要模拟的起步uid
        MemberBaseInfo simulationBaseMemberInfo = null;
        for (MemberBaseInfo memberInfo : lastMatchReq.getTeamData().getMemberInfosMap().values()) {
            if (null == simulationBaseMemberInfo) {
                simulationBaseMemberInfo = memberInfo;
                continue;
            }
            if (memberInfo.getUid() > simulationBaseMemberInfo.getUid()) {
                simulationBaseMemberInfo = memberInfo;
            }
        }

        if (null == simulationBaseMemberInfo) {
            LOGGER.error("simulationBaseMemberInfo is null");
            return;
        }

        long baseRoomId = lastMatchReq.getTeamData().getRoomID() + 1;
        long baseUid = simulationBaseMemberInfo.getUid() + 1;
        long baseMatchId = simulationBaseMemberInfo.getMatchID() + 1;

        // 模拟发送的最后一个请求
        SsMatchsvr.RpcMatchReq.Builder tmpSentMatchReq = null;
        for (int sim_idx = 0; sim_idx < simulationReqCnt; sim_idx++) {
            SsMatchsvr.RpcMatchReq.Builder reqBuilder = lastMatchReq.toBuilder();

            long curRoomId = baseRoomId + sim_idx;
            long curMatchId = baseMatchId + sim_idx;

            reqBuilder.setRoomId(curRoomId);

            // 创建新的TeamData
            TeamData.Builder teamDataBuilder = reqBuilder.getTeamData().toBuilder();
            teamDataBuilder.setRoomID(curRoomId);
            // 选第一个队员作为队长
            teamDataBuilder.setLeaderID(baseUid + ((long) sim_idx * simulationMemberCnt));
            teamDataBuilder.setMatchTime(Framework.currentTimeMillis() + sim_idx);

            // 清空原有队员
            teamDataBuilder.clearMemberInfos();

            // 添加新队员并设置matchId
            for (int member_idx = 0; member_idx < simulationMemberCnt; member_idx++) {
                long curUid = baseUid + ((long) sim_idx * simulationMemberCnt) + member_idx;
                MemberBaseInfo.Builder newMember = simulationBaseMemberInfo.toBuilder()
                        // 设置队员的uid
                        .setUid(curUid)
                        // 设置队员的openId
                        .setOpenId(String.valueOf(curUid))
                        // 设置队员的matchId
                        .setMatchID(curMatchId)
                        // 设置队员的roomID
                        .setRoomID(curRoomId);

                /*
                MMRScoresInfo.Builder mmrScoreBuilder = newMember.getMmrScoresInfoBuilder();
                for (MMRScoreType mmrKey : MMRScoreType.values()) {
                    MMRScore mmrScore = mmrScoreBuilder.getScoresMap().get(mmrKey.getNumber());
                    if (null != mmrScore) {
                        int sim_score = mmrScore.getScore() + RandomUtil.randomBetween(1, 100);
                        mmrScoreBuilder.putScores(mmrKey.getNumber(), mmrScore.toBuilder().setScore(sim_score).build());

                    } else {
                        int sim_score = RandomUtil.randomBetween(1, 100);
                        mmrScoreBuilder.putScores(mmrKey.getNumber(), MMRScore.newBuilder().setScore(sim_score).build());
                    }
                }
                 */

                teamDataBuilder.putMemberInfos(newMember.getUid(), newMember.build());
            }

            // 如果设置了要模拟的阵营
            if (isReAssignSide) {
                teamDataBuilder.setSideID(reAssignSideId);
                LOGGER.info("curRoomId {} set side {}",
                        curRoomId, reAssignSideId);
            }

            reqBuilder.setTeamData(teamDataBuilder);

            // 将构建的请求传递给处理函数
            doMatchReq(reqBuilder);
            tmpSentMatchReq = reqBuilder;
        }

        if (null != tmpSentMatchReq) {
            // 更新 下次用这个作为基准
            lastMatchReq = tmpSentMatchReq.build();
        }
    }

    private void doMatchReq(SsMatchsvr.RpcMatchReq.Builder req) {
        LOGGER.warn("Gm sim req {}",
                req.getRoomId());

        switch (req.getMatchOperateType()) {
            // 正常匹配
            case MOT_Normal: {
                int ret = MatchMgr.getInstance().matchReq(req);
                if (NKErrorCode.OK.getValue() != ret) {
                    LOGGER.error("matchReq ret {}",
                            ret);
                }

                break;
            }

            // 其他的暂时不支持
            default: {
                LOGGER.error("not supported yet");
            }
        }
    }
}
