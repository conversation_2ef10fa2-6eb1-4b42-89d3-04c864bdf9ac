package com.tencent.wea.framework;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.property.PropertyFileReader;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * MatchSvr配置管理器
 */
public class MSConfig {
    private static final Logger LOGGER = LogManager.getLogger(MSConfig.class);

    // AI剧本Ab实验分组 b2组的uid范围
    private static volatile NKPair<Integer, Integer> aiScriptB2GrpUidRangePair = new NKPair<>(20, 29);
    private static Set<String> dscRegionDisableSet = new HashSet<>();
    private static final Map<Integer, NKPair<Integer, Integer>> gmOverriderRoomInfoIdSideRobotCntMap = new HashMap<>();

    private static final Set<Integer> matchCandidateTeamSortRoomInfoID = new HashSet<>();

    private static final Set<Integer> closeBlackListBlockingRoomInfoID = new HashSet<>();

    private static final Map<Integer/*room id*/, Integer /*second*/> matchTimerIntervalConfig = new HashMap<>();

    private static boolean closeMatchBlack = false;

    public static int onInit() {
        int ret = MsMetaAiConfig.onInit();
        if (ret != NKErrorCode.OK.getValue()) {
            LOGGER.error("MetaAiConfig err ret {}",
                    ret);
            //return ret;
        }

        ret = MsAiDifficultyTestConfig.onInit();
        if (ret != NKErrorCode.OK.getValue()) {
            LOGGER.error("AiDifficultyTestConfig err ret {}",
                    ret);
        }

        ret = loadAiScriptTestB2GrpUidRangePair();
        if (ret != NKErrorCode.OK.getValue()) {
            LOGGER.error("loadAiScriptTestB2GrpUidRangePair err ret {}",
                    ret);
            //return ret;
        }
        closeMatchBlack = PropertyFileReader.getRealTimeBooleanItem("close_match_black", false);
        parseGmOverriderRoomInfoIdSideRobotCntMap();
        parseMatchCandidateTeamSortRoomInfoID();
        parseCloseBlackListBlockingRoomInfoID();
        parseMatchTimerIntervalConfig();
        LOGGER.info("MSConfig init succ");
        return ret;
    }

    public static int onReload() {
        int ret = NKErrorCode.OK.getValue();

        int metaAiRet = MsMetaAiConfig.onReload();
        if (metaAiRet != NKErrorCode.OK.getValue()) {
            LOGGER.error("MetaAiConfig err ret {}", ret);
            ret = metaAiRet;
        }

        int aiDiffRet = MsAiDifficultyTestConfig.onReload();
        if (aiDiffRet != NKErrorCode.OK.getValue()) {
            LOGGER.error("AiDifficultyTestConfig err ret {}",
                    aiDiffRet);
            ret = aiDiffRet;
        }

        int loadAiScriptRet = loadAiScriptTestB2GrpUidRangePair();
        if (loadAiScriptRet != NKErrorCode.OK.getValue()) {
            LOGGER.error("loadAiScriptTestB2GrpUidRangePair err ret {}", loadAiScriptRet);
            ret = loadAiScriptRet;
        }
        closeMatchBlack = PropertyFileReader.getRealTimeBooleanItem("close_match_black", false);
        parseGmOverriderRoomInfoIdSideRobotCntMap();
        parseMatchCandidateTeamSortRoomInfoID();
        parseCloseBlackListBlockingRoomInfoID();
        parseMatchTimerIntervalConfig();
        String dscRegionDisableString = PropertyFileReader.getRealTimeItem("dscalloc_dsc_region_disable", "");
        String[] regionFileds = StringUtils.split(dscRegionDisableString, ",");
        dscRegionDisableSet.clear();
        Collections.addAll(dscRegionDisableSet, regionFileds);

        if (NKErrorCode.OK.getValue() == ret) {
            LOGGER.info("MSConfig onReload succ");
        }

        return ret;
    }

    public static Set<String> getDscRegionDisableSet() {
        return dscRegionDisableSet;
    }

    public static boolean isMultiIdcDsEnable() {
        return PropertyFileReader.getRealTimeBooleanItem("multi_idc_ds_enable", false);
    }

    public static NKPair<String, Integer> getGameAiLabVip() {
        String host = PropertyFileReader.getRealTimeItem("game_ai_lab_match_polaris_vip", "*************");
        int port = PropertyFileReader.getRealTimeIntItem("game_ai_lab_match_polaris_vip_port", 55101);
        return new NKPair<>(host, port);
    }

    public static boolean isGameAiEnable() {
        return PropertyFileReader.getRealTimeBooleanItem("game_ai_enable", false);
    }

    public static int getGameAiLabRpcQueueSize() {
        return PropertyFileReader.getRealTimeIntItem("game_ai_lab_rpc_queue_sz", 1024);
    }

    public static int getGameAiLabRpcTickProcMsgSize() {
        return PropertyFileReader.getRealTimeIntItem("game_ai_lab_rpc_tick_proc_msg_sz", 1024);
    }

    public static int getGameAiLabHeartbeatReqType() {
        return PropertyFileReader.getRealTimeIntItem("game_ai_lab_heartbeat_req_type", 101);
    }

    public static int getGameAiMaxHeartBeatOfflineTimes() {
        return PropertyFileReader.getRealTimeIntItem("game_ai_max_heart_beat_offline_times", 3);
    }

    public static int getGameAiGuoneiGameType() {
        return PropertyFileReader.getRealTimeIntItem("game_ai_guonei_gametype", 7500040);
    }

    public static int getGameAiYunyouGameType() {
        return PropertyFileReader.getRealTimeIntItem("game_ai_yunyou_gametype", 7500000);
    }

    public static int getGameAiServerMaxAiUseRate() {
        return PropertyFileReader.getRealTimeIntItem("game_ai_server_max_ai_use_rate", 90);
    }

    public static float getSplitSideEvenlyTotalPlayerMulFactor() {
        return PropertyFileReader.getRealTimeFloatItem("split_side_evenly_total_player_mul_factor", 2.0f);
    }

    public static String getGameAiLabMatchPolarisTestSid() {
        return PropertyFileReader.getRealTimeItem("game_ai_lab_match_polaris_test_sid", "");
    }

    public static String getGameAiLabMatchPolarisTestNS() {
        return PropertyFileReader.getRealTimeItem("game_ai_lab_match_polaris_test_ns", "");
    }

    public static String getGameAiLabMatchPolarisBusinessSid() {
        return PropertyFileReader.getRealTimeItem("game_ai_lab_match_polaris_business_sid", "");
    }

    public static String getGameAiLabMatchPolarisBusinessNS() {
        return PropertyFileReader.getRealTimeItem("game_ai_lab_match_polaris_business_ns", "");
    }

    public static boolean isMatchDynamicPlayersOpen() {
        return PropertyFileReader.getRealTimeBooleanItem("match_dynamic_players_switcher", false);
    }

    public static boolean isMatchDynamicPlayersMaxTimeoutFail() {
        return PropertyFileReader.getRealTimeBooleanItem("match_dynamic_players_maxtimeout_fail", false);
    }

    public static int getMatchDynamicPlayersLoadValidPeriod() {
        return PropertyFileReader.getRealTimeIntItem("match_dynamic_players_loadvalid_period", 10000);
    }

    public static int getMatchDynamicPlayersLoadFactor() {
        return PropertyFileReader.getRealTimeIntItem("match_dynamic_players_load_factor", 100);
    }

    public static int getMatchDynamicPlayersInvalidCPUDefault() {
        return PropertyFileReader.getRealTimeIntItem("match_dynamic_players_invalid_cpu_default", 51);
    }

    public static int getCPUDscVeryBusy() {
        return PropertyFileReader.getRealTimeIntItem("dsCpuVeryBusy", 80);
    }

    public static int getMatchDynamicPlayersMaxCpu() {
        return PropertyFileReader.getRealTimeIntItem("match_dynamic_players_max_cpu", 90);
    }

    public static boolean isTriggerMaxCpuDenyMatch(int cpuLoad) {
        if (!MSConfig.getMatchDynamicPlayersMaxCpuSwitcher()) {
            return false;
        }
        return cpuLoad >= MSConfig.getMatchDynamicPlayersMaxCpu();
    }

    public static boolean getMatchDynamicPlayersMaxCpuSwitcher() {
        return PropertyFileReader.getRealTimeBooleanItem("match_dynamic_players_max_cpu_switcher", true);
    }

    public static boolean isMatchAllinPlayers() {
        return PropertyFileReader.getRealTimeBooleanItem("match_allin_players", false);
    }

    public static boolean isMatchServicesSwitcher() {
        return PropertyFileReader.getRealTimeBooleanItem("match_services_switcher", true);
    }

    public static boolean isOpenMatchTimerIntervalSwitcher() {
        return PropertyFileReader.getRealTimeBooleanItem("match_timer_interval_switcher", false);
    }

    public static boolean isOpenMatchSortOptimizeSwitcher() {
        return PropertyFileReader.getRealTimeBooleanItem("match_sort_optimize_switcher", false);
    }

    public static NKPair<Integer, Integer> getAiScriptTestB2GrpUidRangePair() {
        return aiScriptB2GrpUidRangePair;
    }

    private static int loadAiScriptTestB2GrpUidRangePair() {
        try {
            String groupStr = PropertyFileReader.getRealTimeItem("ai_script_abtest_b2_grp", "0:5");
            String[] groupStrList = groupStr.split(":");
            if (2 != groupStrList.length) {
                LOGGER.error("ai_script_abtest_b2_grp {} length {} != 2",
                        groupStr, groupStrList.length);
                return NKErrorCode.InvalidParams.getValue();
            } else {
                int begin = Integer.parseInt(groupStrList[0]);
                int end = Integer.parseInt(groupStrList[1]);
                aiScriptB2GrpUidRangePair = new NKPair<>(begin, end);
            }

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("ai_script_abtest_b2_grp {}",
                        aiScriptB2GrpUidRangePair);
            }

            return NKErrorCode.OK.getValue();
        } catch (Exception e) {
            LOGGER.error("catch ", e);

            return NKErrorCode.UnknownError.getValue();
        }
    }

    public static boolean isGrayMatchTogetherOpen() {
        return PropertyFileReader.getRealTimeBooleanItem("is_gray_match_together_open", true);
    }

    public static boolean isMatchIgnorePlatDimeCheck() {
        return PropertyFileReader.getRealTimeBooleanItem("is_match_ignore_plat_dime_check", false);
    }

    /**
     * 是否根据匹配房间设置了固定ai数量
     * @param roomInfoId 匹配房间id
     * @return sideId, 人数
     */
    @Nullable
    public static NKPair<Integer, Integer> getRoomInfoOverrideSideRobotCnt(int roomInfoId) {
        if (!MSEngine.getInstance().canUseGmCmd()) {
            return null;
        }

        return gmOverriderRoomInfoIdSideRobotCntMap.get(roomInfoId);
    }

    /**
     * 是否设置了根据匹配房间设置了固定ai数量
     * @param roomInfoId 匹配房间id
     * @return true表示匹配房间id开启了固定ai人数
     */
    public static boolean isRoomInfoIdInOverrideSideRobotCnt(int roomInfoId) {
        if (!MSEngine.getInstance().canUseGmCmd()) {
            return false;
        }

        return gmOverriderRoomInfoIdSideRobotCntMap.containsKey(roomInfoId);
    }

    private static void parseGmOverriderRoomInfoIdSideRobotCntMap() {
        String v = PropertyFileReader.getRealTimeItem("gm_overrider_room_info_side_robot_cnt", "");
        gmOverriderRoomInfoIdSideRobotCntMap.clear();

        if (v.isEmpty()) {
            return;
        }

        try {
            String[] vLst = v.strip().split(";");
            for (String kv : vLst) {
                String[] kvList = kv.strip().split(",");
                if (kvList.length != 3) {
                    throw new RuntimeException(NKStringFormater.format("kv {} len != 3", kv));
                }

                int roomInfoId = Integer.parseInt(kvList[0]);
                int sideId = Integer.parseInt(kvList[1]);
                int cnt = Integer.parseInt(kvList[2]);
                gmOverriderRoomInfoIdSideRobotCntMap.put(roomInfoId, new NKPair<>(sideId, cnt));
            }

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("parseGmOverriderRoomInfoIdRobotMap result {}", gmOverriderRoomInfoIdSideRobotCntMap);
            }
        } catch (Exception e) {
            LOGGER.error("parseGmOverriderRoomInfoIdRobotMap v {} failed",
                    v, e);
            gmOverriderRoomInfoIdSideRobotCntMap.clear();
        }
    }

    private static boolean isMatchCandidateTeamSortEnable() {
        return PropertyFileReader.getRealTimeBooleanItem("match_candidate_team_sort_enable", false);
    }

    public static boolean isMatchCandidateTeamSortEnable(int roomInfoID) {
        return isMatchCandidateTeamSortEnable() && matchCandidateTeamSortRoomInfoID.contains(roomInfoID);
    }

    private static void parseMatchCandidateTeamSortRoomInfoID() {
        String v = PropertyFileReader.getRealTimeItem("match_candidate_team_sort_room_info_id", "");
        matchCandidateTeamSortRoomInfoID.clear();

        if (v.isEmpty()) {
            return;
        }

        try {
            String[] vLst = v.strip().split(",");
            for (String kv : vLst) {
                int roomInfoId = Integer.parseInt(kv);
                matchCandidateTeamSortRoomInfoID.add(roomInfoId);
            }

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("parseMatchCandidateTeamSortRoomInfoID result {}", matchCandidateTeamSortRoomInfoID);
            }
        } catch (Exception e) {
            LOGGER.error("parseMatchCandidateTeamSortRoomInfoID v {} failed",
                    v, e);
            matchCandidateTeamSortRoomInfoID.clear();
        }
    }

    private static Set<Integer> parseDelimiterCommaStrToSet(String str) {
        if (str.isEmpty()) {
            return Collections.emptySet();
        }

        Set<Integer> resSet = new HashSet<>();

        try {
            String[] vLst = str.strip().split(",");
            for (String kv : vLst) {
                int roomInfoId = Integer.parseInt(kv);
                resSet.add(roomInfoId);
            }

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("parseDelimiterCommaStrToSet str{}, result {}", str, resSet);
            }
        } catch (Exception e) {
            LOGGER.error("parseDelimiterCommaStrToSet v {} failed",
                    str, e);
            resSet.clear();
        }
        return resSet;
    }

    private static void parseCloseBlackListBlockingRoomInfoID() {
        String v = PropertyFileReader.getRealTimeItem("match_close_black_list_blocking_room_info_ids", "");
        LOGGER.info("parseCloseBlackListBlockingRoomInfoID before {}", closeBlackListBlockingRoomInfoID);
        closeBlackListBlockingRoomInfoID.clear();
        closeBlackListBlockingRoomInfoID.addAll(parseDelimiterCommaStrToSet(v));
        LOGGER.info("parseCloseBlackListBlockingRoomInfoID after {}", closeBlackListBlockingRoomInfoID);
    }

    private static void parseMatchTimerIntervalConfig() {
        String v = PropertyFileReader.getRealTimeItem("match_timer_interval_config", "");
        LOGGER.info("parseMatchTimerIntervalConfig before {}", closeBlackListBlockingRoomInfoID);
        try {
            String[] vLst = v.strip().split(";");
            for (String kv : vLst) {
                String[] kvList = kv.strip().split(",");
                if (kvList.length != 2) {
                    throw new RuntimeException(NKStringFormater.format("kv {} len != 2", kv));
                }

                int roomInfoId = Integer.parseInt(kvList[0]);
                int interval = Integer.parseInt(kvList[1]);
                matchTimerIntervalConfig.put(roomInfoId, interval);
            }
        } catch (Exception e) {
            LOGGER.error("parseMatchTimerIntervalConfig v {} failed",
                    v, e);
            matchTimerIntervalConfig.clear();
        }
        LOGGER.info("parseMatchTimerIntervalConfig after {}", closeBlackListBlockingRoomInfoID);
    }

    public static int getMatchTimerInterval(int roomInfoID) {
        Integer interval = matchTimerIntervalConfig.get(roomInfoID);
        return interval != null ? interval : 0; //
    }

    private static boolean isMatchResultSideBlackCheckEnable () {
        return PropertyFileReader.getRealTimeBooleanItem("match_result_side_black_check", true);
    }

    public static boolean isNeedBlackListBlocking(int roomInfoID) {
        return isMatchResultSideBlackCheckEnable() && !closeBlackListBlockingRoomInfoID.contains(roomInfoID);
    }

    /**
     * 修改匹配中房间功能开关
     * @return true for开启
     */
    public static boolean isEnableModifyMatchingTeamInfo() {
        return PropertyFileReader.getRealTimeBooleanItem("enable_modify_matching_team_info", true);
    }

    public static boolean isCloseMatchBlack() { return closeMatchBlack; }

    /**
     * 是否启用维度扩散基础区间按照匹配房间id配置
     * @return true for开启
     */
    public static boolean isEnableMatchDimQueryScopeData() {
        return PropertyFileReader.getRealTimeBooleanItem("enable_match_dim_query_scope_data", true);
    }

    /**
     * 是否在按队伍人数匹配规则中使用新的基于excel配置的规则
     * @return true for开启
     */
    public static boolean isEnableNewExcelCfgTeamSizeMatchingRule() {
        return PropertyFileReader.getRealTimeBooleanItem("enable_new_excel_cfg_team_size_matching_rule", true);
    }

    /**
     * 是否启用基于玩家人数匹配策略下 匹配完成后是否优化队伍的功能
     * @return true for开启
     */
    public static boolean isEnableOptimizeSideTeamAfterMatchInTeamSizeStrategy() {
        return PropertyFileReader.getRealTimeBooleanItem("enable_optimize_side_team_after_match_in_team_size_strategy", true);
    }

    /**
     * aiLab 匹配相关的额外配置
     */
    public static class MsMetaAiConfig {

        // ailab a组 uid 范围
        @Getter
        private static volatile NKPair<Integer, Integer> metaAiDyeAGrpUidRangePair = new NKPair<>(0, 5);
        // 同上 对照组
        @Getter
        private static volatile NKPair<Integer, Integer> metaAiDyeControlGrpUidRangePair = new NKPair<>(-1, -1);

        public static int onInit() {
            return loadMetaAiDyeUidRangePair();
        }

        public static int onReload() {
            return loadMetaAiDyeUidRangePair();
        }

        private static int loadMetaAiDyeUidRangePair() {
            try {
                metaAiDyeAGrpUidRangePair = loadIntegerPairFromRainbowRealTime("meta_ai_lab_dye_a_grp", new NKPair<>(0, 5));
                metaAiDyeControlGrpUidRangePair = loadIntegerPairFromRainbowRealTime("meta_ai_lab_dye_control_grp", new NKPair<>(90, 99));

                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("AGroup {} ControlGrp {}",
                            metaAiDyeAGrpUidRangePair, metaAiDyeControlGrpUidRangePair);
                }

                return NKErrorCode.OK.getValue();
            } catch (Exception e) {
                LOGGER.error("catch ", e);

                return NKErrorCode.UnknownError.getValue();
            }
        }

        public static boolean isMetaAiLabMatchOptimizeEnable() {
            return PropertyFileReader.getRealTimeBooleanItem("meta_ai_lab_match_optimize_enable", false);
        }

        public static boolean isMetaAiLabMatchSvrGetParamEnable() {
            return PropertyFileReader.getRealTimeBooleanItem("meta_ai_matchsvr_get_param_enable", false);
        }

        public static boolean isMetaAiLabMatchSvrGetMatchEnable() {
            return PropertyFileReader.getRealTimeBooleanItem("meta_ai_matchsvr_get_match_enable", false);
        }

        public static float getMetaAiLabMinOpenPlayerCntTimes() {
            return PropertyFileReader.getRealTimeFloatItem("meta_ai_lab_min_open_player_cnt_times", 1.f);
        }

        public static int getMetaAiNioThreadNum() {
            return PropertyFileReader.getRealTimeIntItem("meta_ai_lab_nio_thread_num", 2);
        }

        public static boolean getMetaAiSetInfoDifficultyEnable() {
            return PropertyFileReader.getRealTimeBooleanItem("meta_ai_set_ai_info_difficulty_enable", true);
        }

        public static int getUnMatchReMatchAddedTimeoutMillSec() {
            return PropertyFileReader.getRealTimeIntItem("meta_ai_unmatched_added_extra_ms", 3000);
        }
    }

    public static class MsAiDifficultyTestConfig {
        // 功能总开关
        public static boolean isEnableAiDifficultyTest() {
            return PropertyFileReader.getRealTimeBooleanItem("ai_difficulty_test_enable", true);
        }

        // 实验组 uid 范围
        @Getter
        private static volatile NKPair<Integer, Integer> testGrpUidRangePair = new NKPair<>(-1, -1);
        // 对照组 对照组
        @Getter
        private static volatile NKPair<Integer, Integer> controlGrpUidRangePair = new NKPair<>(-1, -1);

        private static int onInit() {
            return loadGrpUidRangePair();
        }

        private static int onReload() {
            return loadGrpUidRangePair();
        }

        private static int loadGrpUidRangePair() {
            try {
                testGrpUidRangePair = loadIntegerPairFromRainbowRealTime("ai_difficulty_test_grp", new NKPair<>(-1, -1));
                controlGrpUidRangePair = loadIntegerPairFromRainbowRealTime("ai_difficulty_control_grp", new NKPair<>(-1, -1));

                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("testGrpUidRangePair {} controlGrpUidRangePair {}",
                            testGrpUidRangePair, controlGrpUidRangePair);
                }

                return NKErrorCode.OK.getValue();
            } catch (NKRuntimeException e) {
                LOGGER.error("catch NKRuntimeException {}", e.getEnumErrCode(), e);

                return e.getErrCode();
            } catch (Exception e) {
                LOGGER.error("catch ", e);

                return NKErrorCode.UnknownError.getValue();
            }
        }
    }

    private static NKPair<Integer, Integer> loadIntegerPairFromRainbowRealTime(String confName,
                                                                               NKPair<Integer, Integer> defaultVal) {
        String confStrVal = PropertyFileReader.getRealTimeItem(confName, "");
        if (confStrVal.isEmpty()) {
            return defaultVal;
        }

        String[] controlGroupStrList = confStrVal.split(":");
        if (2 != controlGroupStrList.length) {
            NKErrorCode.InvalidParams.throwError("confName {} val {} length {} != 2",
                    confName, confStrVal, controlGroupStrList.length);

            // throwError会抛出异常, 下面的return不会执行
            return defaultVal;
        }

        int begin = Integer.parseInt(controlGroupStrList[0]);
        int end = Integer.parseInt(controlGroupStrList[1]);
        return new NKPair<>(begin, end);
    }
}
