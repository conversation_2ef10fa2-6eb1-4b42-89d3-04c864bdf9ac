package com.tencent.wea.matchservice.matchdata.matchProcService.matchSpecialRule.TeamSize;

import com.tencent.match.qualify.QualifyingUtils;
import com.tencent.resourceloader.resclass.AfterMatchOptimizeTeamsEvaluationData;
import com.tencent.resourceloader.resclass.MatchTypeData;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.math.Constants;
import com.tencent.wea.framework.MSConfig;
import com.tencent.wea.matchservice.matchdata.MatchTeamInfo;
import com.tencent.wea.matchservice.matchdata.matchProcService.matchProcCommon.MatchAlgorithmUtils;
import com.tencent.wea.xlsRes.ResMatch;
import com.tencent.wea.xlsRes.ResMatchOptimizeTeamsAfterMatch;
import com.tencent.wea.xlsRes.keywords.MatchRuleDimen;
import com.tencent.wea.xlsRes.keywords.QualifyType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Unmodifiable;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 在匹配后优化优化两边队伍人数
 */
public class OptimizeSideTeamAfterMatchUtils {
    private static final Logger LOGGER = LogManager.getLogger(OptimizeSideTeamAfterMatchUtils.class);

    private static final Map<MatchRuleDimen, CalTeamDimScoreFunc> dimToCalTeamScoreFuncMap = new HashMap<>();
    // 最大可优化的队伍真人数量 超过这个复杂度太高 可能拖慢匹配效率 不优化
    private static final int MAX_CAL_SIDE_HUMAN_CNT = 6;

    static {
        // 加载队伍分数计算函数
        loadDimToCalTeamScoreFuncMap();
    }

    /**
     * 加载队伍分数计算函数
     */
    private static void loadDimToCalTeamScoreFuncMap() {
        dimToCalTeamScoreFuncMap.put(MatchRuleDimen.MRD_TEAMSCORE, OptimizeSideTeamAfterMatchUtils::getTeamQualifyScore);
        dimToCalTeamScoreFuncMap.put(MatchRuleDimen.MRD_MMR, OptimizeSideTeamAfterMatchUtils::getTeamMmrScore);
        dimToCalTeamScoreFuncMap.put(MatchRuleDimen.MRD_AILAB_SKILL_SCORE, OptimizeSideTeamAfterMatchUtils::getTeamAiLabScore);
    }

    /**
     * 如有必要 优化两边队伍人数
     * @param roomCfg 匹配房间配置
     * @param matchParams 匹配参数
     */
    public static void optimizeSideTeamAfterMatchWhenNeed(ResMatch.MatchRoomInfo roomCfg,
                                                          MatchAlgorithmUtils.MatchParams matchParams) {
        if (!MSConfig.isEnableOptimizeSideTeamAfterMatchInTeamSizeStrategy()) {
            // 开关关闭
            return;
        }

        Optional<ResMatchOptimizeTeamsAfterMatch.AfterMatchOptimizeTeamsEvaluationInfo> evaluationInfoOpt =
                AfterMatchOptimizeTeamsEvaluationData.getInstance().get(roomCfg.getId());
        if (evaluationInfoOpt.isEmpty()) {
            // 没有配置 不启用
            return;
        }
        if (evaluationInfoOpt.get().getEnableDiffPercentage() <= 0) {
            // 配置值太小
            LOGGER.debug("roomInfoId {} enableDiffPercentage {} <= 0",
                    roomCfg.getId(), evaluationInfoOpt.get().getEnableDiffPercentage());
            return;
        }
        if (evaluationInfoOpt.get().getEnableEvaluationDimCount() == 0) {
            LOGGER.error("roomInfoId {} enableEvaluationDimCount 0",
                    roomCfg.getId());
            return;
        }

        ResMatch.MatchType matchType = MatchTypeData.getInstance().get(matchParams.baseTeamInfo.getMatchType());
        if (null == matchType) {
            LOGGER.error("roomInfoId {} matchType {} not exists",
                    roomCfg.getId(), matchParams.baseTeamInfo.getMatchType());
            return;
        }

        MatchRuleDimen evaluationDim = null;
        for (MatchRuleDimen dim : evaluationInfoOpt.get().getEnableEvaluationDimList()) {
            evaluationDim = dim;
            break;
        }
        if (null == evaluationDim || (!dimToCalTeamScoreFuncMap.containsKey(evaluationDim))) {
            LOGGER.error("roomInfoId {} evaluationDim {} null",
                    roomCfg.getId(), evaluationDim);
            return;
        }

        // 获取两个阵营的队伍列表
        GetTwoSideTeamListResult twoSideTeamListResult = getTwoSideTeamsList(matchParams.baseTeamInfo.getRoomID(),
                matchParams.matchResult);
        if (!twoSideTeamListResult.isValid()) {
            // isValid有log
            return;
        }

        Map<MatchRuleDimen, Map<Long, Integer>> teamDimToScoreCacheMap = new HashMap<>();
        // 计算两边队伍维度分数
        int lftSideTeamsDimScore = getTeamsDimScoreSum(evaluationDim, teamDimToScoreCacheMap, matchType, twoSideTeamListResult.getLeftSideTeamList());
        int rhtSideTeamsDimScore = getTeamsDimScoreSum(evaluationDim, teamDimToScoreCacheMap, matchType, twoSideTeamListResult.getRightSideTeamList());
        int oriMinScoreDiff = Math.abs(lftSideTeamsDimScore - rhtSideTeamsDimScore);

        // 获取两边队伍分数最大值 作为百分比计算的除数
        Optional<Integer> maxSideDimScore = getSideMaxDimScore(matchParams.baseTeamInfo.getRoomID(), lftSideTeamsDimScore, rhtSideTeamsDimScore);
        if (maxSideDimScore.isEmpty()) {
            return;
        }

        // 如果分数差异百分比小于配置 就不用交换了
        double diffFactorPercentage = (double) oriMinScoreDiff / (double) maxSideDimScore.get() * Constants.HUNDRED;
        if (diffFactorPercentage <= evaluationInfoOpt.get().getEnableDiffPercentage()) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("room {} leader {} lftTeamScore {} rhtTeamScore {} diffFactorPercentage {} cfg percent {}",
                        matchParams.baseTeamInfo.getRoomID(), matchParams.baseTeamInfo.getLeaderID(),
                        lftSideTeamsDimScore, rhtSideTeamsDimScore, diffFactorPercentage, evaluationInfoOpt.get().getEnableDiffPercentage());
            }
            return;
        }

        // 计算两边队伍最佳交换子集(实现最小分数差距)
        TwoSideMinScoreDiffExchangeTeamsSubsetResult subsetResult = getTwoSideMinScoreDiffExchangeTeamsSubsets(
                matchParams.baseTeamInfo.getRoomID(), matchType, lftSideTeamsDimScore, rhtSideTeamsDimScore, twoSideTeamListResult,
                evaluationDim, teamDimToScoreCacheMap);

        if (!subsetResult.isValid()) {
            // isValid有log
            return;
        }

        // 真正交换阵营
        changeTwoSideTeamSideId(matchParams, twoSideTeamListResult, subsetResult);

        // 打点
        sendOptimizeTlog(matchParams.baseTeamInfo, oriMinScoreDiff, subsetResult);
    }

    // 计算队伍列表某个维度的分数总和
    private static int getTeamsDimScoreSum(MatchRuleDimen evaluationDim,
                                           Map<MatchRuleDimen, Map<Long, Integer>> teamDimToScoreCacheMap,
                                           ResMatch.MatchType matchType,
                                           @Unmodifiable List<MatchTeamInfo> teamList) {
        CalTeamDimScoreFunc calTeamsDimScoreFunc = dimToCalTeamScoreFuncMap.get(evaluationDim);
        if (null == calTeamsDimScoreFunc) {
            // 外围检查过 应该不可能
            LOGGER.error("matchType {} evaluationDim {} dimToCalTeamScoreFuncMap is null",
                    matchType.getId(), evaluationDim);
            return 0;
        }

        Map<Long, Integer> scoreCacheMap = teamDimToScoreCacheMap.computeIfAbsent(evaluationDim,
                k -> new HashMap<>());

        int ret = 0;
        for (MatchTeamInfo teamInfo : teamList) {
            Integer cacheScore = scoreCacheMap.get(teamInfo.getRoomID());
            if (null != cacheScore) {
                // 有cache
                ret += cacheScore;
            } else {
                int score = calTeamsDimScoreFunc.calculate(matchType, teamInfo);

                scoreCacheMap.put(teamInfo.getRoomID(), score);
                ret += score;
            }
        }

        return ret;
    }

    // 获取两个阵营的最大分数
    private static Optional<Integer> getSideMaxDimScore(long roomIdForLog,
                                                        int lftSideTeamsDimScore,
                                                        int rhtSideTeamsDimScore) {
        int oriMinScoreDiff = Math.abs(lftSideTeamsDimScore - rhtSideTeamsDimScore);
        if (oriMinScoreDiff <= 0) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("room {} lftTeamScore {} rhtTeamScore {} oriMinScoreDiff {} is 0, no need to optimize",
                        roomIdForLog, lftSideTeamsDimScore, rhtSideTeamsDimScore, oriMinScoreDiff);
            }
            return Optional.empty();
        }

        // 检查下除数不能为0
        int maxSideDimScore = Math.max(lftSideTeamsDimScore, rhtSideTeamsDimScore);
        if (maxSideDimScore == 0) {
            LOGGER.info("room {} lftTeamScore {} rhtTeamScore {} maxSideDimScore {} is 0, can not optimize",
                    roomIdForLog, lftSideTeamsDimScore, rhtSideTeamsDimScore, maxSideDimScore);
            return Optional.empty();
        }
        return Optional.of(maxSideDimScore);
    }

    // 实际交换阵营队伍子集的阵营id
    private static void changeTwoSideTeamSideId(MatchAlgorithmUtils.MatchParams matchParams,
                                                GetTwoSideTeamListResult twoSideTeamListResult,
                                                TwoSideMinScoreDiffExchangeTeamsSubsetResult subsetResult) {
        for (MatchTeamInfo teamInfo : subsetResult.getLeftSideSubset()) {
            teamInfo.setSide(twoSideTeamListResult.getRightSideOriSideId());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("base {} leader {} candidate {} leader {} set side {}",
                        matchParams.baseTeamInfo.getRoomID(), matchParams.baseTeamInfo.getLeaderID(),
                        teamInfo.getRoomID(), teamInfo.getLeaderID(), teamInfo.getSide());
            }
        }
        for (MatchTeamInfo teamInfo : subsetResult.getRightSideSubset()) {
            teamInfo.setSide(twoSideTeamListResult.getLeftSideOriSideId());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("base {} leader {} candidate {} leader {} set side {}",
                        matchParams.baseTeamInfo.getRoomID(), matchParams.baseTeamInfo.getLeaderID(),
                        teamInfo.getRoomID(), teamInfo.getLeaderID(), teamInfo.getSide());
            }
        }
    }

    /**
     * 获取两个阵营的队伍列表结果
     */
    private static class GetTwoSideTeamListResult {
        // log用的房间id
        private final long roomIdForLog;
        // A(左, id1)的阵营的队伍列表
        @lombok.Getter
        private final @Unmodifiable List<MatchTeamInfo> leftSideTeamList;
        // B(右, id2)的阵营的队伍列表
        @lombok.Getter
        private final @Unmodifiable List<MatchTeamInfo> rightSideTeamList;

        // 每个阵营真人数量
        @lombok.Getter
        private final int eachSideTeamHumanCnt;

        private final int leftSideTeamHumanCnt;
        private final int rightSideTeamHumanCnt;

        // A(左, id1)的阵营的原始阵营id
        @lombok.Getter
        private int leftSideOriSideId;
        // B(右, id2)的阵营的原始阵营id
        @lombok.Getter
        private int rightSideOriSideId;


        public GetTwoSideTeamListResult(long roomIdForLog) {
            this.roomIdForLog = roomIdForLog;
            this.leftSideTeamList = Collections.emptyList();
            this.rightSideTeamList = Collections.emptyList();
            this.leftSideTeamHumanCnt = 0;
            this.rightSideTeamHumanCnt = 0;
            this.eachSideTeamHumanCnt = 0;
            this.leftSideOriSideId = 0;
            this.rightSideOriSideId = 0;
        }

        public GetTwoSideTeamListResult(long roomIdForLog,
                                        List<MatchTeamInfo> leftSideTeamList,
                                        List<MatchTeamInfo> rightSideTeamList) {
            this.roomIdForLog = roomIdForLog;
            this.leftSideTeamList = Collections.unmodifiableList(leftSideTeamList);
            this.rightSideTeamList = Collections.unmodifiableList(rightSideTeamList);

            int lftTeamHumanCnt = 0;
            int rhtTeamHumanCnt = 0;
            for (MatchTeamInfo team : leftSideTeamList) {
                lftTeamHumanCnt += team.getHumanMemberCnt();
                leftSideOriSideId = team.getSide();
            }
            for (MatchTeamInfo team : rightSideTeamList) {
                rhtTeamHumanCnt += team.getHumanMemberCnt();
                rightSideOriSideId = team.getSide();
            }

            this.leftSideTeamHumanCnt = lftTeamHumanCnt;
            this.rightSideTeamHumanCnt = rhtTeamHumanCnt;

            this.eachSideTeamHumanCnt = lftTeamHumanCnt;
        }

       public boolean isValid() {
            if (leftSideTeamHumanCnt != rightSideTeamHumanCnt) {
                LOGGER.error("room {} leftTeamHumanCnt {} != rightTeamHumanCnt {}",
                        roomIdForLog, leftSideTeamHumanCnt, rightSideTeamHumanCnt);
                return false;
            }

            if (leftSideTeamHumanCnt <= 0) {
                LOGGER.error("roomId {} leftTeamHumanCnt <= 0",
                        roomIdForLog);
                return false;
            }

           // 限定一下队伍最大人数 防止后面的递归太深
           if (leftSideTeamHumanCnt >= MAX_CAL_SIDE_HUMAN_CNT) {
               LOGGER.warn("room {} sideTeamHumanCnt {} >= MAX_CAL_SIDE_HUMAN_CNT {}",
                       roomIdForLog, leftSideTeamHumanCnt, MAX_CAL_SIDE_HUMAN_CNT);
               return false;
           }

            return true;
       }
   }

   // 获取两个阵营的队伍列表结果
   private static GetTwoSideTeamListResult getTwoSideTeamsList(long roomIdForLog, Collection<MatchTeamInfo> matchResult) {
       Map<Integer, List<MatchTeamInfo>> sideIdToMatchTeamList = new HashMap<>();
       for (MatchTeamInfo teamInfo : matchResult) {
           sideIdToMatchTeamList.computeIfAbsent(teamInfo.getSide(),
                   k -> new ArrayList<>()).add(teamInfo);
       }

       if (sideIdToMatchTeamList.size() != 2) {
           LOGGER.error("roomInfoId {} sideIdToMatchTeamList.size() {} != 2",
                   roomIdForLog, sideIdToMatchTeamList.size());
           return new GetTwoSideTeamListResult(roomIdForLog);
       }

       List<MatchTeamInfo> lftSideTeamList = null;
       List<MatchTeamInfo> rhtSideTeamList = null;
       for (List<MatchTeamInfo> teamList : sideIdToMatchTeamList.values()) {
           if (lftSideTeamList == null) {
               lftSideTeamList = teamList;
           } else {
               rhtSideTeamList = teamList;
           }
       }

       return new GetTwoSideTeamListResult(roomIdForLog, lftSideTeamList, rhtSideTeamList);
   }

    /**
     * 获取两个阵营真正交换队伍(使得分数相差最小)的子集
     */
   @lombok.Getter
   private static class TwoSideMinScoreDiffExchangeTeamsSubsetResult {
        // 阵营A(左, id1)的阵营的队伍列表
        private final List<MatchTeamInfo> leftSideSubset;
        // 阵营B(右, id2)的阵营的队伍列表
        private final List<MatchTeamInfo> rightSideSubset;
        // 最小分数差
        private final int minScoreDiff;
        // log用的房间id
        private final long roomIdForLog;

        public TwoSideMinScoreDiffExchangeTeamsSubsetResult(long roomIdForLog,
                                                            List<MatchTeamInfo> leftSideSubset,
                                                            List<MatchTeamInfo> rightSideSubset,
                                                            int minScoreDiff) {
            this.roomIdForLog = roomIdForLog;
            this.leftSideSubset = leftSideSubset;
            this.rightSideSubset = rightSideSubset;
            this.minScoreDiff = minScoreDiff;
        }

        public TwoSideMinScoreDiffExchangeTeamsSubsetResult(long roomIdForLog) {
            this.roomIdForLog = roomIdForLog;
            this.leftSideSubset = Collections.emptyList();
            this.rightSideSubset = Collections.emptyList();
            this.minScoreDiff = 0;
        }

        public boolean isValid() {
            if (leftSideSubset.isEmpty() || rightSideSubset.isEmpty()) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("room {} cur diff {} is best",
                            roomIdForLog, minScoreDiff);
                }

                return false;
            }

            return true;
        }
    }

   // 获取两个阵营真正交换队伍(使得分数相差最小)的子集
   private static TwoSideMinScoreDiffExchangeTeamsSubsetResult getTwoSideMinScoreDiffExchangeTeamsSubsets(long roomIdForLog,
                                                                                                          ResMatch.MatchType matchType,
                                                                                                          int lftSideTeamsDimScore,
                                                                                                          int rhtSideTeamsDimScore,
                                                                                                          GetTwoSideTeamListResult twoSideTeamListResult,
                                                                                                          MatchRuleDimen evaluationDim,
                                                                                                          Map<MatchRuleDimen, Map<Long, Integer>> teamDimToScoreCacheMap) {
       List<MatchTeamInfo> bestSubLft = null;
       List<MatchTeamInfo> bestSubRht = null;
       int curMinScoreDiff = Math.abs(lftSideTeamsDimScore - rhtSideTeamsDimScore);
       int oriMinDiff = curMinScoreDiff;

       // 从人数1到每个阵营最大真人数量-1 生成每个阵营能组成这样人数的队伍的子集 尝试交换 计算新的分数差值
       TeamsListSubsetDpResults leftSideSubsetDpResult = findTeamListSubsetsDpResult(roomIdForLog, twoSideTeamListResult.getLeftSideTeamList(),
               twoSideTeamListResult.getEachSideTeamHumanCnt() - 1);
       TeamsListSubsetDpResults rightSideSubsetDpResult = findTeamListSubsetsDpResult(roomIdForLog, twoSideTeamListResult.getRightSideTeamList(),
               twoSideTeamListResult.getEachSideTeamHumanCnt() - 1);

       for (int changeGrpHumanCnt = 1; changeGrpHumanCnt < twoSideTeamListResult.getEachSideTeamHumanCnt(); ++changeGrpHumanCnt) {
           List<List<MatchTeamInfo>> subLftTeamListList = leftSideSubsetDpResult.querySubsets(changeGrpHumanCnt);
           List<List<MatchTeamInfo>> subRhtTeamListList = rightSideSubsetDpResult.querySubsets(changeGrpHumanCnt);

           if (LOGGER.isDebugEnabled()) {
               LOGGER.debug("room {} changeGrpHumanCnt {} subLftTeamListList {} subRhtTeamListList {}",
                       roomIdForLog, changeGrpHumanCnt, subLftTeamListList.size(), subRhtTeamListList.size());
           }

           if (subLftTeamListList.isEmpty() || subRhtTeamListList.isEmpty()) {
               // 没有这样的组合
               continue;
           }

           for (List<MatchTeamInfo> subLftTeams : subLftTeamListList) {
               int scoreLft = getTeamsDimScoreSum(evaluationDim, teamDimToScoreCacheMap, matchType, subLftTeams);
               for (List<MatchTeamInfo> subRhtTeams : subRhtTeamListList) {
                   int scoreRht = getTeamsDimScoreSum(evaluationDim, teamDimToScoreCacheMap, matchType, subRhtTeams);
                   // 计算新的分数差值
                   int newSumLft = lftSideTeamsDimScore - scoreLft + scoreRht;
                   int newSumRht = rhtSideTeamsDimScore - scoreRht + scoreLft;
                   int diff = Math.abs(newSumLft - newSumRht);
                   if (diff < curMinScoreDiff) {
                       bestSubLft = new ArrayList<>(subLftTeams);
                       bestSubRht = new ArrayList<>(subRhtTeams);
                       curMinScoreDiff = diff;
                   }
               }
           }
       }

       if (bestSubLft == null || bestSubRht == null) {
           // 没找到
           return new TwoSideMinScoreDiffExchangeTeamsSubsetResult(roomIdForLog);
       }

       if (LOGGER.isDebugEnabled()) {
           LOGGER.debug("room {} oriDiff {} minDiff {} bestSubLft {} bestSubRht {}",
                   roomIdForLog, oriMinDiff, curMinScoreDiff,
                   NKStringFormater.formatCollectionLambdaToSizeLimitStr(bestSubLft, 1024,
                           teamInfo -> String.valueOf(teamInfo.getRoomID())),
                   NKStringFormater.formatCollectionLambdaToSizeLimitStr(bestSubRht, 1024,
                           teamInfo -> String.valueOf(teamInfo.getRoomID())));
       }

       return new TwoSideMinScoreDiffExchangeTeamsSubsetResult(roomIdForLog, bestSubLft, bestSubRht, curMinScoreDiff);
   }

    private static void sendOptimizeTlog(MatchTeamInfo baseTeamInfo,
                                         int oriBestDiff,
                                         TwoSideMinScoreDiffExchangeTeamsSubsetResult subsetResult) {
    }

    /**
     * dp备忘录
     */
    private static class TeamsListSubsetDpResults {
       private final int maxHumanCnt;
       private final List<List<List<MatchTeamInfo>>> dp;
       private final long roomIdForLog;

        public TeamsListSubsetDpResults(long roomIdForLog, int maxHumanCnt, List<List<List<MatchTeamInfo>>> dp) {
            if (dp.size() < (maxHumanCnt + 1)) {
                LOGGER.warn("room {} maxHumanCnt {} dp.size() {}",
                        roomIdForLog, maxHumanCnt, dp.size());

                this.roomIdForLog = roomIdForLog;
                this.maxHumanCnt = 0;
                this.dp = Collections.emptyList();
            } else {
                this.roomIdForLog = roomIdForLog;
                this.maxHumanCnt = maxHumanCnt;
                this.dp = dp;
            }
        }

        public TeamsListSubsetDpResults(long roomIdForLog) {
            this.roomIdForLog = roomIdForLog;
            this.maxHumanCnt = 0;
            this.dp = Collections.emptyList();
        }

        public List<List<MatchTeamInfo>> querySubsets(int humanSizeCnt) {
            if (humanSizeCnt > maxHumanCnt) {
                LOGGER.warn("room {} humanSizeCnt {} > maxHumanCnt {}",
                        roomIdForLog, humanSizeCnt, maxHumanCnt);
                return Collections.emptyList();
            }

            return dp.get(humanSizeCnt) != null ? dp.get(humanSizeCnt) : Collections.emptyList();
        }
    }

    /**
     * 查找给定的list中，指定人数的子集
     * @param teamList 队伍列表
     * @param targetHumanCnt 要求的子集人数
     * @return 符合条件的子集列表
     */
    private static TeamsListSubsetDpResults findTeamListSubsetsDpResult(long roomIdForLog,
                                                                        List<MatchTeamInfo> teamList,
                                                                        int targetHumanCnt) {
        if (teamList.size() >= MAX_CAL_SIDE_HUMAN_CNT) {
            // 队伍的数量太多
            LOGGER.warn("too many team size to find subsets: {}",
                    teamList.size());
            return new TeamsListSubsetDpResults(roomIdForLog);
        }
        if (targetHumanCnt >= MAX_CAL_SIDE_HUMAN_CNT || targetHumanCnt <= 0) {
            // 目标人数太多
            LOGGER.warn("too many targetHumanCnt to find subsets: {}",
                    targetHumanCnt);
            return new TeamsListSubsetDpResults(roomIdForLog);
        }

        // 初始化dp备忘录
        List<List<List<MatchTeamInfo>>> dp = new ArrayList<>(targetHumanCnt + 1);
        for (int idx = 0; idx <= targetHumanCnt; idx++) {
            dp.add(null);
        }
        dp.set(0, new ArrayList<>());
        dp.get(0).add(new ArrayList<>());

        for (MatchTeamInfo team : teamList) {
            int curTeamHumanCnt = team.getHumanMemberCnt();
            // 从目标targetHumanSizeCnt查找到只有这个队伍
            for (int idx = targetHumanCnt; idx >= curTeamHumanCnt; idx--) {
                if (dp.get(idx - curTeamHumanCnt) != null) {
                    if (dp.get(idx) == null) {
                        dp.set(idx, new ArrayList<>());
                    }
                    for (List<MatchTeamInfo> subset : dp.get(idx - curTeamHumanCnt)) {
                        List<MatchTeamInfo> newSubset = new ArrayList<>(subset);
                        // 加入当前队伍
                        newSubset.add(team);
                        dp.get(idx).add(newSubset);
                    }
                }
            }
        }

        return new TeamsListSubsetDpResults(roomIdForLog, targetHumanCnt, dp);
    }


    // 获取队伍的积分
    private static int getTeamQualifyScore(ResMatch.MatchType matchType,
                                           MatchTeamInfo teamInfo) {

        QualifyType qualifyType = QualifyingUtils.getQualifyType(matchType.getId(), teamInfo.getSide());;
        int score = 0;
        for (MatchTeamInfo.Member member : teamInfo.getMemberPlayerMap().values()) {
            if (member.getIsRobot()) {
                continue;
            }

            int playerQualifyScore = member.getQualifyingDegreeInfo(qualifyType).getIntegral();
            score += playerQualifyScore;
        }
        return score;
    }

    // 获取队伍的mmr积分
    private static int getTeamMmrScore(ResMatch.MatchType matchType, MatchTeamInfo teamInfo) {

        int score = 0;
        for (MatchTeamInfo.Member member : teamInfo.getMemberPlayerMap().values()) {
            if (member.getIsRobot()) {
                continue;
            }

            int playerQualifyScore = member.getMMR(false);
            score += playerQualifyScore;
        }
        return score;
    }


    // 获取队伍的实力分(ailab下发的)
    private static int getTeamAiLabScore(ResMatch.MatchType matchType,
                                         MatchTeamInfo teamInfo) {
        int score = 0;
        for (MatchTeamInfo.Member member : teamInfo.getMemberPlayerMap().values()) {
            if (member.getIsRobot()) {
                continue;
            }

            int playerQualifyScore = member.getAiLabSkillScore();
            score += playerQualifyScore;
        }
        return score;
    }


    /**
     * 计算队伍维度得分的函数
     */
    @FunctionalInterface
    public interface CalTeamDimScoreFunc {
        int calculate(ResMatch.MatchType matchType, MatchTeamInfo teamInfo);
    }
}


