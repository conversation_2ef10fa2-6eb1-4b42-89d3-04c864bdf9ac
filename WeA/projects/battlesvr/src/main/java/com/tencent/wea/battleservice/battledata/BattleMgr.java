package com.tencent.wea.battleservice.battledata;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.protobuf.ByteString;
import com.google.protobuf.TextFormat;
import com.tencent.cl5.PolarisUtil;
import com.tencent.match.MatchRouteMgr;
import com.tencent.match.matchConfigProxyMgr.ConfigProxyMgr;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.timer.TimerType;
import com.tencent.nk.timer.TimerUtil;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.BattleOperateRetryException;
import com.tencent.nk.util.exception.IEnumedException;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.guid.BattleIdGenerator;
import com.tencent.resourceloader.ResHolder;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.resclass.*;
import com.tencent.rpc.RpcResult;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.timiutil.time.NKStopWatch;
import com.tencent.timiutil.time.TxStopWatch;
import com.tencent.timiutil.tool.FunctionUtil;
import com.tencent.util.Pb2JsonUtil;
import com.tencent.util.ReputationScoreUtil;
import com.tencent.wea.battlehistory.BattleHistoryUtil;
import com.tencent.wea.battleresult.LevelBattleResult;
import com.tencent.wea.battleservice.battledata.BattleInfo.BattleState;
import com.tencent.wea.battleservice.battledata.BattleStatisticsHelper.DataType;
import com.tencent.wea.battleservice.battledata.arena.ArenaBattleDataHelper;
import com.tencent.wea.battleservice.battledata.robot.BaseRobot;
import com.tencent.wea.battleservice.battledata.scene.BattleSceneAdapter;
import com.tencent.wea.battleservice.battledata.scene.BattleSceneInfo;
import com.tencent.wea.battleservice.battlestatistics.BattleDSCLoadMgr;
import com.tencent.wea.battleservice.battlestatistics.BattleStatisticsMgr;
import com.tencent.wea.battleservice.level.RoundLevelChooser;
import com.tencent.wea.battleservice.level.randevent.RandEventMgr;
import com.tencent.wea.battleservice.level.randevent.RandEventMgr.RandEventPair;
import com.tencent.wea.battleservice.level.randevent.RandEventMgr.RandResEnum;
import com.tencent.wea.battleservice.metaai.BattleInfoMetaAiComp;
import com.tencent.wea.battleservice.play.OMDGamePlay;
import com.tencent.wea.battleservice.rank.RankMgr;
import com.tencent.wea.framework.BSConfig;
import com.tencent.wea.g6.irpc.proto.ds_common.DsCommon;
import com.tencent.wea.g6.irpc.proto.ds_event_subscriber.DsEventSubscriberOuterClass;
import com.tencent.wea.g6.irpc.proto.ds_event_subscriber.DsEventSubscriberOuterClass.GameSessionEndCode;
import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr;
import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr.DSMonitorInfo;
import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr.GetNextLevelByRoundIndexReply;
import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr.GetNextLevelByRoundIndexRequest;
import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr.GetResTableRowsReply;
import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr.GetResTableRowsRequest;
import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr.MobaAiLabBattleInfoReply;
import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr.RandEvent;
import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr.ReportDSInfo;
import com.tencent.wea.g6.irpc.proto.dsa_public.DsaPublicOuterClass;
import com.tencent.wea.g6.irpc.proto.dsa_public.DsaPublicOuterClass.GameSessionStatus;
import com.tencent.wea.g6.irpc.proto.sd_battle.SdBattleOuterClass;
import com.tencent.wea.g6.irpc.proto.sd_common.SdCommonOuterClass;
import com.tencent.wea.g6.irpc.proto.sd_common.SdCommonOuterClass.PlayerExitDsReply;
import com.tencent.wea.g6.irpc.proto.sd_common.SdCommonOuterClass.PlayerExitDsRequest;
import com.tencent.wea.g6.irpc.proto.sd_competition.SdCompetitionOuterClass;
import com.tencent.wea.g6.irpc.service.DsaPublicService;
import com.tencent.wea.g6.irpc.service.SdBattleService;
import com.tencent.wea.g6.irpc.service.SdCommonService;
import com.tencent.wea.g6.irpc.service.SdCompetitionService;
import com.tencent.wea.interaction.player.DoReportReputationScoreBehaviorInteraction;
import com.tencent.wea.interaction.player.SendBroadcastNoticeInteraction;
import com.tencent.wea.matchbattle.BattlePublicDao;
import com.tencent.wea.matchbattle.BattleQuitDeleteEnum;
import com.tencent.wea.matchbattle.MatchBattleMgr;
import com.tencent.wea.midjoin.HokConfs;
import com.tencent.wea.midjoin.TycConfs;
import com.tencent.wea.namedenum.servertype.WeAServerType;
import com.tencent.wea.protocol.CsPlayer.ABTestType;
import com.tencent.wea.protocol.SsBattlesvr;
import com.tencent.wea.protocol.SsGamesvr;
import com.tencent.wea.protocol.SsMatchsvr;
import com.tencent.wea.protocol.SsUgcsvr;
import com.tencent.wea.protocol.common.BattleAiTakeOverPlayerInfo;
import com.tencent.wea.protocol.common.BattleMidJoinExtraInfo;
import com.tencent.wea.protocol.common.G6Common;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.protocol.common.ChangeBattleSceneResult;
import com.tencent.wea.protocol.common.G6Common.BattleDsMonitorType;
import com.tencent.wea.protocol.common.G6Common.BattleDsTlogType;
import com.tencent.wea.protocol.common.G6Common.QuitBattleCode;
import com.tencent.wea.protocol.common.G6Common.ResTableKeys;
import com.tencent.wea.protocol.common.G6Common.ResTableRow;
import com.tencent.wea.protocol.common.GameModeType;
import com.tencent.wea.protocol.common.MatchCancelReason;
import com.tencent.wea.protocol.common.MatchFillBackData;
import com.tencent.wea.protocol.common.MatchFillBackSideInfo;
import com.tencent.wea.protocol.common.MatchFillBackType;
import com.tencent.wea.protocol.common.MatchOperateType;
import com.tencent.wea.protocol.common.MatchRuleInfo;
import com.tencent.wea.protocol.common.MatchSuccData;
import com.tencent.wea.protocol.common.MatchTeamData;
import com.tencent.wea.protocol.common.MatchTypeABTestInfo;
import com.tencent.wea.protocol.common.MemberBaseInfo;
import com.tencent.wea.protocol.common.PlayerReputationScoreBehaviorInfo;
import com.tencent.wea.protocol.common.PlayerReputationScoreBehaviorReportInfo;
import com.tencent.wea.protocol.common.ServerType;
import com.tencent.wea.protocol.common.TeamData;
import com.tencent.wea.protocol.common.UgcAiTakeOverPlayerInfo;
import com.tencent.wea.protocol.common.UgcLevelSettlementInfo;
import com.tencent.wea.protocol.common.UniversalRoom;
import com.tencent.wea.robot.RobotUtil;
import com.tencent.wea.rpc.service.GameService;
import com.tencent.wea.rpc.service.MatchService;
import com.tencent.wea.rpc.service.UgcService;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import com.tencent.wea.tlog.TlogFlowMgr;
import com.tencent.wea.tlog.flow.TlogMacros;
import com.tencent.wea.tlog.flow.TlogMacros.MATCH_STEP_ENUM;
import com.tencent.wea.tlog.letsgods.LetsGoDsTlogTypes;
import com.tencent.wea.xlsRes.*;
import com.tencent.wea.xlsRes.ResActivity.ActivityPrayerCardCollectionConfig;
import com.tencent.wea.xlsRes.ResActivity.CollectionSceneType;
import com.tencent.wea.xlsRes.ResLevelInfo.T_LevelInfoData;
import com.tencent.wea.xlsRes.keywords.BattleEventType;
import com.tencent.wea.xlsRes.keywords.BroadcastType;
import com.tencent.wea.xlsRes.keywords.MatchRuleDimen;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 房间管理
 *
 * <AUTHOR>
 * @date 2021/07/27
 */
public class BattleMgr {

    private static final Logger LOGGER = LogManager.getLogger(BattleMgr.class);
    private static final HashMap<String, AIServerInfo> aiServerInfoMap = new HashMap<String, AIServerInfo>();
    private static final HashMap<String, String> regionToAiServerEnvMap = new HashMap<String, String>();
    private static final Long ONE_MINUTE_MS = 60L * 1000;
    // 以下为配置项
    public static int tcaplustDBretry;
    public static long localMaxFreeTime = 3600 * 1000;
    public static long localFrameTime;
    public static long localDelayTime;
    public static int localSupplementNote;
    public static int localSupplementGameTime;
    public static long ugcMaxFreeTime = 2 * 3600 * 1000;
    public static long tycMaxFreeTime;
    public static long battleMemberLeaveListRecordMax = 32;  // 最多纪录32个玩家
    // 塔防战斗DS最大存在时间
    public static long tycoonBattleMaxFreeTime;
    private static long localMaxBattleEndTime = 3000 * 1000;
    private static long battleReadyDealyTimeForFirst;
    private static long battleReadyDealyTimeForNone;
    private static long localSupplementSongStartTime;
    private static long localCanNotConnectTime;
    private static long localOfflineMaxTime = 30 * 1000;
    private static long localWaitReadyMaxTime;
    private static long localSupplementRobotStartTime;
    private static int battleOperateRetryTimes;
    private static int battleStartAddTime;
    private static int battleFirstEndAddTime;
    private static long showtimeDelay;
    private static int battleRobotEmojiSendLimit;
    private static int isCheckFrameModEnd;
    private static int gmRobotDiffiulty = 0;
    private static int pveStartAddEndTime;
    private static int aiServerAddrReuseTimesMax;
    private static int aiServerAddrKeepSec;
    private static String defaultAIServerEnv = "";
    private final Map<Long, BattleInfo> battleMap;
    private final HashMap<Long, Long> replayBattleInfoMap = new HashMap<>();
    private final HashMap<Long, HashSet<Long>> dsaReplayBattleSetMap = new HashMap<>();
    private final MatchBattleMgr matchBattleMgr;
    /* ds game sessionId -> battleId, 用于sessionId 与 battleId的解绑*/
    private final ConcurrentHashMap<Long, Long> sessionIdToBattleIdMap = new ConcurrentHashMap<>();
    /* battleId -> ds game sessionId*/
    private final ConcurrentHashMap<Long, Long> battleIdToSessionIdMap = new ConcurrentHashMap<>();
    private int saveAIDsReplayCount = 0;
    private long saveAIDsReplayStartTime = 0;
    private int saveLocalAIFileCount = 0;
    private long saveLocalAIFileStartTime = 0;
    private int saveSecurityDsReplayCount = 0;
    private long saveSecurityDsReplayStartTime = 0;
    private String dsAuthSecret = "test";
    private volatile int warmGuideBattleCnt = 0;
    private volatile int normalBattleCnt = 0;
    private OMDGamePlay omdGamePlay = new OMDGamePlay();
    private BattleInfoMetaAiComp battleMetaAiComp;
    /* battleId -> battleSceneInfo 用于单局多ds管理 */
    private final ConcurrentHashMap<Long, BattleSceneInfo> battleSceneInfoMap = new ConcurrentHashMap<>();

    private BattleMgr() {
        battleMap = new HashMap<>();
        matchBattleMgr = new MatchBattleMgr(this);
    }

    private static void loadCfg() {
        tcaplustDBretry = PropertyFileReader.getRealTimeIntItem("tcaplustDBretryNaxNum", 3);
        localMaxFreeTime = PropertyFileReader.getRealTimeLongItem("battleMaxFreeTime", 3600) * 1000L;
        tycoonBattleMaxFreeTime = PropertyFileReader.getRealTimeLongItem("tycoonBattleMaxFreeTime", 90000) * 1000L;
        localFrameTime = PropertyFileReader.getRealTimeLongItem("battleFrameTime", 1) * 1000L;
        localDelayTime = PropertyFileReader.getRealTimeLongItem("battleFrameDelayTime", 100);
        localSupplementNote = PropertyFileReader.getRealTimeIntItem("SupplementNote", 5);
        localSupplementGameTime = PropertyFileReader.getRealTimeIntItem("SupplementGameTime", 100) * 1000;//ms
        localMaxBattleEndTime = PropertyFileReader.getRealTimeLongItem("MaxBattleEndTime", 3000) * 1000L;//ms
        battleReadyDealyTimeForFirst =
                PropertyFileReader.getRealTimeIntItem("BattleReadyDealyTimeForFirst", 15) * 1000L;
        battleReadyDealyTimeForNone = PropertyFileReader.getRealTimeIntItem("BattleReadyDealyTimeForNone", 20) * 1000L;
        localSupplementSongStartTime = PropertyFileReader.getRealTimeIntItem("SupplementSongStartTime", 17) * 1000L;//ms
        localCanNotConnectTime = PropertyFileReader.getRealTimeIntItem("SupplementSongStartTime", 15) * 1000L;//ms
        localOfflineMaxTime = PropertyFileReader.getRealTimeIntItem("OfflineMaxTime", 30) * 1000L;
        localWaitReadyMaxTime = PropertyFileReader.getRealTimeIntItem("WaitReadyMaxTime", 30) * 1000L;
        localSupplementRobotStartTime =
                PropertyFileReader.getRealTimeIntItem("SupplementRobotStartTime", 17) * 1000L;//ms
        battleOperateRetryTimes = PropertyFileReader.getRealTimeIntItem("battleOperateRetryTimes", 1);
        battleStartAddTime = PropertyFileReader.getRealTimeIntItem("battleStartAddTime", 5) * 1000;
        battleFirstEndAddTime = PropertyFileReader.getRealTimeIntItem("battleFirstEndAddTime", 2) * 1000;
        showtimeDelay = PropertyFileReader.getRealTimeIntItem("showtimeDelayMs", 1000);
        battleRobotEmojiSendLimit = PropertyFileReader.getRealTimeIntItem("robotEmojiSendLimit", 2);
        isCheckFrameModEnd = PropertyFileReader.getRealTimeIntItem("isCheckFrameModEnd", 0);
        pveStartAddEndTime = PropertyFileReader.getRealTimeIntItem("pveStartAddEndTime", 15) * 1000;

        aiServerAddrReuseTimesMax = PropertyFileReader.getRealTimeIntItem("AiServerAddrReuseTimesMax", 5);
        aiServerAddrKeepSec = PropertyFileReader.getRealTimeIntItem("AiServerAddrKeepSec", 10);

        ugcMaxFreeTime = PropertyFileReader.getRealTimeLongItem("ugcMaxFreeTime", 3 * 3600) * 1000L;  // 3个小时
        tycMaxFreeTime = PropertyFileReader.getRealTimeLongItem("tycMaxFreeTime", 36000) * 1000L;

        // 在此时纪录的个数大小为 32 * 24
        battleMemberLeaveListRecordMax =PropertyFileReader.getRealTimeLongItem("battleMemberLeaveListRecordMax", 32);

        reloadAIServerEnvList();
        reloadRegionAiInfoServerList();
    }

    private static void reloadAIServerEnvList() {
        aiServerInfoMap.clear();
        List<ResThirdPartyEnv.AIServerEnvConfig> listCfg = AIServerEnvConfData.getInstance().getArrayList();
        for (ResThirdPartyEnv.AIServerEnvConfig cfg : listCfg) {
            AIServerInfo defaultAIServerInfo = new AIServerInfo();
            defaultAIServerInfo.aiServerCnPolarisSid = cfg.getPolarisSid();
            defaultAIServerInfo.aiServerCnPolarisNs = cfg.getPolarisNs();
            defaultAIServerInfo.aiServerAddrSafe.key = cfg.getSafeIp();
            defaultAIServerInfo.aiServerAddrSafe.value = cfg.getSafePort();
            aiServerInfoMap.put(cfg.getEnv(), defaultAIServerInfo);
            LOGGER.info("reloadAIServerEnvList env:{} polarisSid:{} polarisNs:{} safeIp:{} safePort:{}", cfg.getEnv(),
                    cfg.getPolarisSid(), cfg.getPolarisNs(), cfg.getSafeIp(), cfg.getSafePort());
        }
    }

    private static void reloadRegionAiInfoServerList() {
        defaultAIServerEnv = PropertyFileReader.getRealTimeItem("AiServerEnv", "");
        LOGGER.info("reloadRegionAiInfoServerList, defaultEnv:{}", defaultAIServerEnv);
        String list = PropertyFileReader.getRealTimeItem("RegionAiServerEnvList", "");
        String[] regionList = StringUtils.split(list, ",");
        regionToAiServerEnvMap.clear();
        if (regionList != null) {
            for (String regionToAiServerEnv : regionList) {
                String[] regionToAiServerEnvArray = StringUtils.split(regionToAiServerEnv, ":");
                if (regionToAiServerEnvArray != null && regionToAiServerEnvArray.length == 2) {
                    String regionName = regionToAiServerEnvArray[0];
                    String aiServerEnv = regionToAiServerEnvArray[1];
                    ResThirdPartyEnv.AIServerEnvConfig aiServerEnvConfig = AIServerEnvConfData.getInstance()
                            .get(aiServerEnv);
                    if (aiServerEnvConfig == null) {
                        LOGGER.error("reloadRegionAiInfoServerList cfg error list:{} regionCfg:{}", list,
                                regionToAiServerEnv);
                    }
                    regionToAiServerEnvMap.put(regionName, aiServerEnv);
                    LOGGER.info("reloadRegionAiInfoServerList regionName:{} aiServerEnv:{}", regionName, aiServerEnv);
                }
            }
        }
    }

    public static int getGmRobotDiffiulty() {
        return gmRobotDiffiulty;
    }

    public static void setGmRobotDiffiulty(int diff) {
        gmRobotDiffiulty = diff;
    }

    public static boolean isCheckFrameModEnd() {
        return isCheckFrameModEnd == 1;
    }

    public static int getPveStartAddEndTime() {
        return pveStartAddEndTime;
    }

    public static int getBattleRobotEmojiSendLimit() {
        return battleRobotEmojiSendLimit;
    }

    public static long getShowtimeDelay() {
        return showtimeDelay;
    }

    public static long getOfflineMaxTime() {
        return localOfflineMaxTime;
    }

    public static long getCanNotConnectTime() {
        return localCanNotConnectTime;
    }

    public static long getLocalSupplementSongStartTime() {
        return localSupplementSongStartTime;
    }

    public static int getLocalSupplementNote() {
        return localSupplementNote;
    }

    public static BattleMgr getInstance() {
        return InstanceHolder.INSTANCE;
    }

    public static int getTcaplustDBretry() {
        return tcaplustDBretry;
    }

    public static long getLocalMaxBattleEndTime() {
        return localMaxBattleEndTime;
    }

    public static long getLocalMaxFreeTime() {
        return localMaxFreeTime;
    }

    public static long getBattleReadyDealyTimeForFirst() {
        return battleReadyDealyTimeForFirst;
    }

    public static int getBattleStartAddTime() {
        return battleStartAddTime;
    }

    public static int getBattleFirstEndAddTime() {
        return battleFirstEndAddTime;
    }

    public static long getBattleReadyDealyTimeForNone() {
        return battleReadyDealyTimeForNone;
    }

    public static long getLocalFrameTime() {
        return localFrameTime;
    }

    public static long getLocalWaitReadyMaxTime() {
        return localWaitReadyMaxTime;
    }

    public static long getRobotStartTimeCfg() {
        return localSupplementRobotStartTime;
    }

    public static long getTycMaxFreeTime() {
        return tycMaxFreeTime;
    }

    public static long getUgcMaxFreeTime() {
        return ugcMaxFreeTime;
    }

    public static long getTycoonBattleMaxFreeTime() {
        return tycoonBattleMaxFreeTime;
    }

    public static long getBattleMemberLeaveListRecordMax() { return battleMemberLeaveListRecordMax; }

    public static boolean battleCanJoinMidway(BattleInfo battleInfo) {
        return battleInfo != null && battleInfo.canJoinMidway();
    }

    public static boolean battlePriorityDsJoin(BattleInfo battleInfo) {
        return battleInfo != null && battleInfo.priorityDsJoin();
    }

    public static void sendTycDsBattleTlog(BattleInfo battle, int reason, String extraStr) {
        try {
            if (battle == null || !TycConfs.isTYCGroup(battle.getMatchRuleInfo().getMatchTypeId())) {
                return;
            }
            Map<Long, BattlePlayerInfo> reCampPlay = battle.getBattlePlayerMap();
            if (reCampPlay != null) {
                long curPass = (Framework.currentTimeMillis() - battle.getCreateTime()) / 1000L;
                long migratePass = (battle.getMigrateSuccessTime() - battle.getBeginMigrateTime());
                for (BattlePlayerInfo battlePlayerInfo : reCampPlay.values()) {
                    TlogFlowMgr.sendTycDsFlow(battlePlayerInfo.getMemberBaseInfo(false), -1,
                            battle.getBattleID(), battle.getDsSessionId(), battle.getDsSessionId(),
                            battle.getCreateTime(), battle.getMigrateSuccessTime(), reason, battlePlayerInfo.getSide(),
                            -1, String.valueOf(battle.getMatchRuleInfo().getMatchTypeId()),
                            battle.getMigrateCount(), battle.getPlayerSize(), battle.getPlayerUids(),
                            -1, (int) curPass, (int) migratePass, extraStr, battle.getState().getValue());
                }
            }
        } catch (Throwable t) {
            LOGGER.error("Error in sendTycDsFlow: ", t);
        }
    }

    public OMDGamePlay getOmdGamePlay() {
        return omdGamePlay;
    }

    public String getDsAuthSecret() {
        return dsAuthSecret;
    }

    private void incrWarmGuideBattleCnt() {
        ++warmGuideBattleCnt;
    }

    private void decrWarmGuideBattleCnt() {
        --warmGuideBattleCnt;
        if (warmGuideBattleCnt < 0) {
            warmGuideBattleCnt = 0;
        }
    }

    public int getWarmGuideBattleCnt() {
        return warmGuideBattleCnt;
    }

    private void incrNormalBattleCnt() {
        ++normalBattleCnt;
    }

    private void decrNormalBattleCnt() {
        --normalBattleCnt;
        if (normalBattleCnt < 0) {
            normalBattleCnt = 0;
        }
    }

    public int getNormalBattleCnt() {
        return normalBattleCnt;
    }

    public NKPair<String, Integer> getAiServerAddr(String dscRegion) {
        String aiServerEnv = defaultAIServerEnv;
        if (regionToAiServerEnvMap.containsKey(dscRegion)) {
            aiServerEnv = regionToAiServerEnvMap.get(dscRegion);
        }
        AIServerInfo aiServerInfo = aiServerInfoMap.get(aiServerEnv);
        if (aiServerInfo == null) {
            LOGGER.error("BattleMgr getAiServerAddr ai server env not exist, dscRegion:{} aiServerEnv:{}", dscRegion,
                    aiServerEnv);
            aiServerEnv = defaultAIServerEnv;
            aiServerInfo = aiServerInfoMap.get(aiServerEnv);
        }
        if (aiServerInfo == null) {
            LOGGER.error("BattleMgr getAiServerAddr error, dscRegion:{} aiServerEnv:{}", dscRegion, aiServerEnv);
            return new NKPair<>("", 0);
        }
        boolean needGet = false;
        if (aiServerInfo.aiServerAddr == null) {
            needGet = true;
        } else if (aiServerInfo.aiServerAddr.getKey().equals("")) {
            needGet = true;
        }
        long curTimeMs = Framework.currentTimeMillis();
        if (curTimeMs > aiServerInfo.aiServerAddrExpireTimeMs) {
            needGet = true;
        }
        if (aiServerInfo.aiServerAddrResuseTimes > aiServerAddrReuseTimesMax) {
            needGet = true;
        }
        if (needGet) {
            NKPair<String, Integer> instance = PolarisUtil.discover(aiServerInfo.aiServerCnPolarisSid,
                    aiServerInfo.aiServerCnPolarisNs);
            if (instance == null || instance.key == null) {
                //LOGGER.warn("BattleMgr getAiServerAddr use safe addr, dscRegion:{} aiServerEnv:{} polarisSid:{} PolarisNs:{} safeAddr:{}",
                //dscRegion, aiServerEnv, aiServerInfo.aiServerCnPolarisSid, aiServerInfo.aiServerCnPolarisNs, aiServerInfo.aiServerAddrSafe.toString());
                //instance = aiServerInfo.aiServerAddrSafe;
                // 为了安全，上面的保底规则屏蔽掉了
                LOGGER.warn("failed to get aiproxy from polaris {}|{}", aiServerInfo.aiServerCnPolarisSid,
                        aiServerInfo.aiServerCnPolarisNs);
                return new NKPair<>("", 0);
            }
            if (!aiServerInfo.aiServerAddr.key.equals(instance.key)
                    || !aiServerInfo.aiServerAddr.value.equals(instance.value)) {
                LOGGER.info(
                        "BattleMgr getAiServerAddr changed dscRegion:{} polarisSid:{} PolarisNs:{} oldAddr:{} oldPort:{} newAddr:{} newPort:{}",
                        dscRegion, aiServerInfo.aiServerCnPolarisSid, aiServerInfo.aiServerCnPolarisNs,
                        aiServerInfo.aiServerAddr.key, aiServerInfo.aiServerAddr.value, instance.key, instance.value);
            }
            aiServerInfo.aiServerAddr = instance;
            aiServerInfo.aiServerAddrExpireTimeMs = curTimeMs + aiServerAddrKeepSec * 1000L;
            aiServerInfo.aiServerAddrResuseTimes = 0;
        }
        aiServerInfo.aiServerAddrResuseTimes += 1;
        LOGGER.info("BattleMgr getAiServerAddr {}|{}|{}|{}|{}|{}", dscRegion, aiServerEnv,
                aiServerInfo.aiServerAddr.toString(), aiServerInfo.aiServerAddrExpireTimeMs,
                curTimeMs, aiServerInfo.aiServerAddrResuseTimes);
        return aiServerInfo.aiServerAddr;
    }

    public MatchBattleMgr getMatchBattleMgr() {
        return matchBattleMgr;
    }

    public Map<Long, BattleInfo> getBattleMap() {
        return battleMap;
    }

    public boolean containBattle(long battleId) {
        return battleMap.containsKey(battleId);
    }

    @Nullable
    public BattleInfo getBattleInfo(long battleID) {
        BattleInfo battleInfo = battleMap.get(battleID);
        if (null == battleInfo) {
            battleInfo = BattleDBMgr.getInstance().load(battleID);
        }
        return battleInfo;
    }

    public BattleInfo tryGetBattleInfo(long gameSessionId) {
        return battleMap.get(gameSessionId);
    }

    public BattleSceneInfo getBattleSceneInfo(long battleId) {
        BattleSceneInfo battleSceneInfo = battleSceneInfoMap.get(battleId);
        if (null == battleSceneInfo) {
            battleSceneInfo = BattleDBMgr.getInstance().loadBattleScene(battleId);
        }
        return battleSceneInfo;
    }

    public BattleInfo getBattleInfoBySessionId(long battleId, long gameSessionId) {
        // 如果gameSessionId != 0 并且 battleId != gameSessionId 说明是多DS的 从scene里面拉
        if (gameSessionId != 0 && battleId != gameSessionId) {
            var battleSceneInfo = getBattleSceneInfo(battleId);
            if (battleSceneInfo == null) {
                return null;
            }
            return battleSceneInfo.getBattleInfo(gameSessionId);
        }
        return getBattleInfo(battleId);
    }
    
    public BattleSceneInfo tryGetBattleSceneInfo(long battleId) {
        // 这里只加载缓存
        return battleSceneInfoMap.get(battleId);
    }

    // 使用玩家作为校验
    public BattleInfo getBattleInfoByUid(long battleId, long uid) {
        if (uid == 0L) return getBattleInfo(battleId);
        // 这里是为了减少一次dbload 因为大部分都没有BattleSceneInfo
        var battleSceneInfo = battleSceneInfoMap.get(battleId);
        // 如果是多DS并且记载过了 去加载这个battleInfo
        if (battleSceneInfo != null) {
            return battleSceneInfo.getBattleInfoByUid(uid);
        }
        // 好了尝试去加载这个battleInfo
        var battleInfo = getBattleInfo(battleId);
        if (battleInfo != null) {
            return battleInfo;
        }
        // 加载不到这个battleInfo就尝试去加载管理器
        battleSceneInfo = getBattleSceneInfo(battleId);
        if (battleSceneInfo != null) {
            return battleSceneInfo.getBattleInfoByUid(uid);
        }
        return null;
    }

    public BattleInfo getBattleInfoOrMainScene(long battleId) {
        // 这里是为了减少一次dbload 因为大部分都没有BattleSceneInfo
        var battleSceneInfo = battleSceneInfoMap.get(battleId);
        // 如果是多DS并且记载过了 去加载这个battleInfo
        if (battleSceneInfo != null) {
            return battleSceneInfo.getMainSceneBattleInfo();
        }
        // 好了尝试去加载这个battleInfo
        var battleInfo = getBattleInfo(battleId);
        if (battleInfo != null) {
            return battleInfo;
        }
        // 加载不到这个battleInfo就尝试去加载管理器
        battleSceneInfo = getBattleSceneInfo(battleId);
        if (battleSceneInfo != null) {
            return battleSceneInfo.getMainSceneBattleInfo();
        }
        return null;
    }

    public BattleInfo getBattleInfoByFirstScene(long battleId) {
        // 这里流程上需要拿第一个scene用于确认什么之类的 这里就加个逻辑吧
        var battleSceneInfo = battleSceneInfoMap.get(battleId);
        // 如果是多DS并且记载过了 去加载这个battleInfo
        if (battleSceneInfo != null) {
            return battleSceneInfo.getFirstSceneBattleInfo();
        }
        // 好了尝试去加载这个battleInfo
        var battleInfo = getBattleInfo(battleId);
        if (battleInfo != null) {
            return battleInfo;
        }
        // 加载不到这个battleInfo就尝试去加载管理器
        battleSceneInfo = getBattleSceneInfo(battleId);
        if (battleSceneInfo != null) {
            return battleSceneInfo.getFirstSceneBattleInfo();
        }
        return null;
    }

    public Collection<BattleInfo> getAllBattleInfo() {
        return battleMap.values();
    }

    @Nullable
    public BattleInfo getBattleInfoFromCache(long battleID) {
        BattleInfo battleInfo = battleMap.get(battleID);
        return battleInfo;
    }

    @Nullable
    public SsBattlesvr.RpcGetBattleInfoRes.Builder getRoomBattleInfo(long battleId) {
        BattleInfo battleInfo = getBattleInfo(battleId);
        if (battleInfo != null) {
            return battleInfo.getRoomBattleInfo();
        }
        BattleSceneInfo battleSceneInfo = getBattleSceneInfo(battleId);
        if (battleSceneInfo != null) {
            return battleSceneInfo.getRoomBattleInfo();
        }
        LOGGER.warn("BattleMgr getRoomBattleInfo battleInfo is null, battleId:{}", battleId);
        return null;
    }

    private BattleInfoMetaAiComp getBattleInfoMetaAiComp(){
        if(null == battleMetaAiComp){
            battleMetaAiComp = new BattleInfoMetaAiComp();
        }
        return battleMetaAiComp;
    }

    public int getBattleSceneSize(Map<Integer, Integer> matchTypeSizeMap, Map<Integer, Integer> matchTypeSubSceneSizeMap) {
        for (BattleSceneInfo battleSceneInfo : battleSceneInfoMap.values()) {
            Integer matchType = battleSceneInfo.getMatchRuleInfo().getMatchTypeId();
            Integer matchTypeSize = matchTypeSizeMap.getOrDefault(matchType, 0);
            matchTypeSizeMap.put(matchType, matchTypeSize + 1);
            Integer matchTypeSubSceneSize = matchTypeSubSceneSizeMap.getOrDefault(matchType, 0);
            matchTypeSubSceneSizeMap.put(matchType, battleSceneInfo.getSceneSize() + matchTypeSubSceneSize);
        }
        return battleSceneInfoMap.size();
    }

    public int getBattleSize(Map<Integer, Integer> matchTypeSizeMap, Set<Integer> currentMatchTypeSet) {
        for (BattleInfo battleInfo : battleMap.values()) {
            Integer matchType = battleInfo.getMatchRuleInfo().getMatchTypeId();
            Integer matchTypeSize = matchTypeSizeMap.getOrDefault(matchType, 0);
            matchTypeSizeMap.put(matchType, matchTypeSize + 1);
            currentMatchTypeSet.add(matchType);
        }
        return battleMap.size();
    }

    public int getBattleMemeberSize(Map<Integer, Integer> matchTypePlayerSizeMap) {
        int size = 0;
        for (BattleInfo battleInfo : battleMap.values()) {
            Integer matchTypePlayerSize = battleInfo.getPlayerSize();
            Integer matchType = battleInfo.getMatchRuleInfo().getMatchTypeId();
            Integer matchTypeTotalPlayerSize = matchTypePlayerSizeMap.getOrDefault(matchType, 0);
            matchTypePlayerSizeMap.put(matchType, matchTypeTotalPlayerSize + matchTypePlayerSize);
            size += matchTypePlayerSize;
        }
        return size;
    }

    public int getBattleAlivePlayerSize(Map<Integer, Integer> matchTypeAlivePlayerSizeMap) {
        int size = 0;
        for (BattleInfo battleInfo : battleMap.values()) {
            Integer matchTypePlayerSize = battleInfo.getAlivePlayerSize();
            Integer matchType = battleInfo.getMatchRuleInfo().getMatchTypeId();
            Integer matchTypeTotalPlayerSize = matchTypeAlivePlayerSizeMap.getOrDefault(matchType, 0);
            matchTypeAlivePlayerSizeMap.put(matchType, matchTypeTotalPlayerSize + matchTypePlayerSize);
            size += matchTypePlayerSize;
        }
        return size;
    }
    
    public int getBattleRobotSize(Map<Integer, Integer> matchTypeRobotSizeMap) {
        int size = 0;
        for (BattleInfo battleInfo : battleMap.values()) {
            Integer matchTypeRobotSize = battleInfo.getBattleRobotMap().size();
            Integer matchType = battleInfo.getMatchRuleInfo().getMatchTypeId();
            Integer matchTypeTotalRobotSize = matchTypeRobotSizeMap.getOrDefault(matchType, 0);
            matchTypeRobotSizeMap.put(matchType, matchTypeTotalRobotSize + matchTypeRobotSize);
            size += matchTypeRobotSize;
        }
        return size;
    }

    public int getMatchTypeRobotNum(Map<Integer, Integer> matchTypeRobotNumMap) {
        int size = 0;
        for (BattleInfo battleInfo : battleMap.values()) {
            if(battleInfo.getReportDSInfo() == null) {
                continue;
            }
            Integer matchTypeRobotNum = battleInfo.getReportDSInfo().getRobotInfo().getRobotNum();
            Integer matchType = battleInfo.getMatchRuleInfo().getMatchTypeId();
            Integer matchTypeTotalRobotNum = matchTypeRobotNumMap.getOrDefault(matchType, 0);
            matchTypeRobotNumMap.put(matchType, matchTypeTotalRobotNum + matchTypeRobotNum);
            size += matchTypeRobotNum;
        }
        return size;
    }
    public int getLevelTypeRobotNum(Map<Integer, Integer> levelTypeRobotNumMap, Set<Integer> currentLevelTypeSet) {
        int size = 0;
        for (BattleInfo battleInfo : battleMap.values()) {
            if(battleInfo.getReportDSInfo() == null) {
                continue;
            }
            Integer levelId = battleInfo.getReportDSInfo().getLevelId();
            int levelType = -1;
            T_LevelInfoData levelInfo = LevelInfoData.getInstance().get(levelId);
            if(levelInfo != null) {
                levelType = levelInfo.getLevelType().getNumber();
            }
            Integer robotNum = battleInfo.getReportDSInfo().getRobotInfo().getRobotNum();
        
            Integer levelTypeTotalRobotNum = levelTypeRobotNumMap.getOrDefault(levelType, 0);
            levelTypeRobotNumMap.put(levelType, levelTypeTotalRobotNum + robotNum);
            currentLevelTypeSet.add(levelType);
            size += robotNum;
        }
        return size;
    }
    public int getLevelIdRobotNum(Map<Integer, Integer> levelIdRobotNumMap, Set<Integer> currentLevelIdSet) {
        int size = 0;
        for (BattleInfo battleInfo : battleMap.values()) {
            if(battleInfo.getReportDSInfo() == null) {
                continue;
            }
            Integer levelId = battleInfo.getReportDSInfo().getLevelId();
            Integer robotNum = battleInfo.getReportDSInfo().getRobotInfo().getRobotNum();
        
            Integer levelIdTotalRobotNum = levelIdRobotNumMap.getOrDefault(levelId, 0);
            levelIdRobotNumMap.put(levelId, levelIdTotalRobotNum + robotNum);
            currentLevelIdSet.add(levelId);
            size += robotNum;
        }
        return size;
    }

    public void addBattleInfo(BattleInfo battleInfo) {
        BattleInfo battleInfoTmp = getBattleInfoFromCache(battleInfo.getBattleID());
        if (null != battleInfoTmp) {
            removeBattle(battleInfoTmp);
        }

        if (battleInfo.getIsGuideWarmRound()) {
            incrWarmGuideBattleCnt();
        } else {
            incrNormalBattleCnt();
        }

        if (battleInfo.battleWithScene()) {
            battleMap.put(battleInfo.getDsSessionId(), battleInfo);
        } else {
            battleMap.put(battleInfo.getBattleID(), battleInfo);
            registerBattleIdToSessionId(battleInfo.getDsSessionId(), battleInfo.getBattleID());
            registerGameSessionId(battleInfo.getDsSessionId(), battleInfo.getBattleID());
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("addBattleInfo battleId:{}, session:{}, battleMapSize:{}",
                    battleInfo.getBattleID(), battleInfo.getDsSessionId(), battleMap.size());
        }
        battleInfo.setTimerHandle(CurrentExecutorUtil.addRepeatTimer(TimerType.ExecutorLocalProcTimer,
                1, TimerUtil.msToTick(500), true, () -> {
                    TxStopWatch stopWatch = NKStopWatch.SW_BattleLoclProc.getStopWatch();
                    try {
                        battleInfo.procTimer(Framework.currentTimeMillis());
                    } catch (BattleOperateRetryException retryE) {
                        LOGGER.warn("battleInfo procTimer meets BattleOperateRetryException battleID:{}, err:{}",
                                battleInfo.getBattleID(), retryE);
                        BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                        //reload add new timer so here return;
                    } catch (Exception e) {
                        LOGGER.error("battleInfo timeout proc exception battleID:{}, err:",
                                battleInfo.getBattleID(), e);
                    }
                }));

        DSMigrateMgr.getInstance().tryRefreshGameSessionDscRegion(battleInfoTmp, battleInfo);
    }

    public void removeBattle(BattleInfo battleInfo) {
        try {
            if (battleInfo.isUgcCustomRoomWithSinglePlayer()) {
                BattleDSCLoadMgr.getInstance().decrUgcSingleDS(battleInfo.getBattleID());
            }
            battleInfo.onFini();
            if (battleInfo.getIsGuideWarmRound()) {
                decrWarmGuideBattleCnt();
            } else {
                decrNormalBattleCnt();
            }
            endBattleOnJoinMidwayMode(battleInfo);
            long battleId = battleInfo.getBattleID();
            BattleInfo preBattleInfo = null;
            if (battleInfo.battleWithScene()) {
                preBattleInfo = battleMap.remove(battleInfo.getDsSessionId());
            } else {
                preBattleInfo = battleMap.remove(battleInfo.getBattleID());
            }
            if (replayBattleInfoMap.containsKey(battleId)) {
                long replayStartTime = replayBattleInfoMap.get(battleId);
                LOGGER.info("remove replay battle info, battleId:{} startTime:{}", battleId,
                        DateUtils.unixTime2String(replayStartTime));
                replayBattleInfoMap.remove(battleId);
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("removeBattle battleMapSize:{}", battleMap.size());
            }
            if (!battleInfo.battleWithScene()) {
                unregisterGameSession(battleInfo.getDsSessionId(), battleId);
            }
            if (preBattleInfo != null && TycConfs.isTYCGroup(preBattleInfo.getMatchRuleInfo().getMatchTypeId())) {
                Monitor.getInstance().add.succ(MonitorId.attr_tyc_ds_end_count, 1);
                if (preBattleInfo.getMatchBattleInfo() != null) {
                    Monitor.getInstance().add.succ(MonitorId.attr_tyc_tf_ds_enter_player_count,
                            preBattleInfo.getMatchBattleInfo().getEnterPlayerNum());
                    Monitor.getInstance().add.succ(MonitorId.attr_tyc_tf_ds_migrate_count,
                            preBattleInfo.getMatchBattleInfo().getMigrateCount());
                }

                long pass = (Framework.currentTimeMillis() - preBattleInfo.getCreateTime()) / 1000L;
                String[] liveParams = new String[]{
                        preBattleInfo.getMatchRuleInfo().getMatchTypeId() + "_" + pass / 300};
                Monitor.getInstance().add.succ(MonitorId.attr_tyc_tf_ds_alive, pass);
                Monitor.getInstance().add.succ(MonitorId.attr_tyc_tf_ds_alive_range, 1, liveParams);
            }
        } catch (Throwable t) {
            LOGGER.error("Error in removeBattle: ", t);
        }
    }

    public void addBattleScene(BattleSceneInfo battleSceneInfo) {
        if (battleSceneInfo == null) {
            return;
        }
        battleSceneInfoMap.put(battleSceneInfo.getBattleId(), battleSceneInfo);
        battleSceneInfo.setTimerHandle(CurrentExecutorUtil.addRepeatTimer(TimerType.ExecutorLocalProcTimer,
                1, TimerUtil.msToTick(500), true, () -> {
                    try {
                        battleSceneInfo.procTimer(Framework.currentTimeMillis());
                    } catch (Exception e) {
                        LOGGER.error("battleSceneInfo timeout proc exception battleID:{}, err:",
                                battleSceneInfo.getBattleId(), e);
                    }
                }));
    }

    public void removeBattleScene(BattleSceneInfo battleSceneInfo) {
        if (battleSceneInfo == null) {
            return;
        }
        battleSceneInfoMap.remove(battleSceneInfo.getBattleId());
        battleSceneInfo.onRemove();
    }

    public int onInit() {
        this.dsAuthSecret = PropertyFileReader.getRealTimeItem("ds_auth_secret", "test");
        loadCfg();
        MatchRouteMgr.getInstance().onInit();
        return NKErrorCode.OK.getValue();
    }

    public int onReload() {
        this.dsAuthSecret = PropertyFileReader.getRealTimeItem("ds_auth_secret", "test");
        loadCfg();
        TycConfs.loadCfg();
        MatchRouteMgr.getInstance().onReload();
        return NKErrorCode.OK.getValue();
    }



    public void recordFullLinkConsume(long uid, long matchID, int matchType, int roomInfoID,
            long battleID, long triggerTime, int step) {
        if (!PropertyFileReader.getRealTimeBooleanItem("open_record_match_fulllink_consume", true)) {
            return;
        }
        TlogFlowMgr.sendMatchFullLinkTimeConsumptionFlow(uid, matchID, matchType, roomInfoID,
                battleID, triggerTime, step);
    }

    public void createBattle(SsBattlesvr.RpcCreateBattleReq.Builder req,
            SsBattlesvr.RpcCreateBattleRes.Builder resBuilder) {
        BattleInfo battle = new BattleInfo(req.getBattleId(), req.getBattleId(), null, BattleInfo.BattleState.Battle_Wait);

        // 先填充matchData
        battle.fetchByPBData(req.getRoomData(), req.getGameModeType(), req.getExtraInfo());
        battle.fillOfflineHostingAI();
        battle.fillRobotInfo();
        battle.reportWarmRoundStatics();
        battle.fillPakDetailID();
        battle.setUgcInfo(req.getUgcInfo());
        battle.setUgcId(req.getUgcId());
        battle.setUgcMapVersion(req.getUgcMapVersion());
        battle.setMapPoolId(req.getExtraInfo().getMapPoolId());
        battle.setMapSource(req.getMapSource());
        battle.setLogicMapSource(req.getLogicMapSource());
        battle.setFromCollectionId(req.getFromCollectionId());
        battle.fillUgcExtraInfo();
        // battle.setLevelSettings(req.getLevelSettingList()); // 废弃
        battle.setRoomSetting(req.getRoomSetting());

        battle.setRecommendMapAlgoInfo(req.getRecommendMapAlgoInfo()); //结伴同游专用
        battle.setCustomRoomInfo(req.getCustomRoomInfo());
        // battle.setWolfKillAni(); //修复狼人随机动画
        battle.setBattleCampSize(req.getBattleCampSize());
        battle.updateEndTime();
        if (TycConfs.isOMDMultipleGame(battle.getMatchRuleInfo().getMatchTypeId()) && TycConfs.openOMDRoomNo()) {
            battle.initRoomMidJoinInfo(req);
        }
        battle.generateArenaRandomEventJson(req);
        battle.setArenaClientParams(req);
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("createBattle {}", battle.getShortDebugString(false));
        }
        battle.setArenaClientParams(req);
        boolean competitionOpen = PropertyFileReader.getRealTimeBooleanItem("competition_open", false);
        if (req.getCompetitionData().getSeason() > 0) {
            LOGGER.info("createBattle competition debug, battleId:{} season:{} type:{} roomId:{} roomNo:{} groupId:{}",
                    req.getBattleId(),req.getCompetitionData().getSeason(),req.getCompetitionData().getGameType(),
                    req.getCompetitionData().getRoom().getRoomId(),req.getCompetitionData().getRoom().getRoomNo(),
                    req.getCompetitionData().getRoom().getGroupId());
            // 赛事-淘汰赛/巅峰赛房间
            battle.setCompetitionData(req.getCompetitionData());
        } else if (ResMatch.MatchTypeSettleProc.MTSC_Rank.equals(battle.getMatchTypeCfg().getSettleProc())
                && competitionOpen) {
            // 排位赛，在赛事海选阶段
            battle.fillRankCompetitionBattleData();
        }
        // 初始化多场景信息
        battle.fillBattleSceneInfo();

        if (battle.isUgcCustomRoomWithSinglePlayer() && !BattleDSCLoadMgr.getInstance().checkUGCSingleDSLoadValid()) {
            resBuilder.setResult(NKErrorCode.BattleUGCSingleDSLoadHighBusy.getValue());
            LOGGER.info("createBattle checkUGCSingleDSLoadValid failed battleId:{} matchTypeID:{} gameMode:{}",
                    req.getBattleId(), battle.getMatchRuleInfoBuilder().getMatchTypeId(), battle.getMatchRuleInfo().getGameModeType());
            return;
        }

        // 在上面batleSceneInfo信息已经创建完毕 这里检查是否需要创建BattleSceneInfo
        NKErrorCode ec = createBattleSceneInfo(battle);
        if (ec != NKErrorCode.OK) {
            resBuilder.setResult(ec.getValue());
            LOGGER.info("createBattle createBattleSceneInfo fail battleId:{}", req.getBattleId());
            return;
        }

        // 插入数据
        NKErrorCode res = BattleDBMgr.getInstance().insertDBBattleRoomInfo(battle);
        if (res == NKErrorCode.OK) {
            addBattleInfo(battle);
            asyncRecordRobotTlog(battle);
            if (battle.isUgcCustomRoomWithSinglePlayer()) {
                BattleDSCLoadMgr.getInstance().incrUgcSingleDS(battle.getBattleID());
            }
        }

        if (LOGGER.isDebugEnabled()) {
            battle.getBattlePlayerMap().forEach((uid, playerInfo) -> {
                if (!playerInfo.isRobot()) {
                    LOGGER.debug("uid {} player {} size:{}", uid, playerInfo.memberBaseInfo,
                            playerInfo.memberBaseInfo.build().getSerializedSize());
                }
            });
        }
        resBuilder.setResult(res.getValue());
        resBuilder.setCompetitionData(battle.getCompetitionData());
        resBuilder.addAllBattlePlayerClientInfos(battle.getAllBattlePlayerClientInfoList());
        battle.updateIsManualPauseOpen();
        this.omdGamePlay.proFillRoomCreateInfo(battle);
    }

    private NKErrorCode createBattleSceneInfo(BattleInfo battleInfo) {
        var battleSceneBrief = battleInfo.getBattleSceneBrief();
        if (battleSceneBrief == null || !battleSceneBrief.needMultiSceneDS()) {
            LOGGER.debug("battleId:{} not need create sceneInfo", battleInfo.getBattleID());
            return NKErrorCode.OK;
        }
        // 创建一个battleScene
        var sceneBaseInfo = battleInfo.getBattleSceneBaseInfo();
        var memberList = battleInfo.getMemberListNotRobot().getMemberInfoList();
        BattleSceneInfo battleSceneInfo = new BattleSceneInfo(battleInfo.getBattleID(), battleSceneBrief, sceneBaseInfo, memberList);
        NKErrorCode res = BattleDBMgr.getInstance().insertDBBattleSceneInfo(battleSceneInfo);
        if (res == NKErrorCode.OK) {
            addBattleScene(battleSceneInfo);
            return NKErrorCode.OK;
        }
        // 修改下错误码
        return NKErrorCode.DBOpFailed;
    }

    private void asyncRecordRobotTlog(BattleInfo battleInfo) {
        try {
            CurrentExecutorUtil.runJob(() -> {
                battleInfo.getBattlePlayerMap().forEach((uid, battlePlayerInfo) -> {
                    if (battlePlayerInfo.isRobot()) {
                        int roomMemberNum = 0;
                        if (battleInfo.getBattleTeamPlayerMap().containsKey(battlePlayerInfo.getRoomId())) {
                            roomMemberNum = battleInfo.getBattleTeamPlayerMap().get(battlePlayerInfo.getRoomId())
                                    .size();
                        }
                        battleInfo.getBattleTeamPlayerMap().get(battlePlayerInfo.getRoomId());
                        TlogFlowMgr.sendRobotBattleBeginFlow(battlePlayerInfo.getMemberBaseInfo(false),
                                battleInfo.getBattleID(), battleInfo.getMatchRuleInfo().getMatchTypeId(),
                                battleInfo.getWarmRoundType(), battleInfo.getCompetitionData());
                    }
                });
                return null;
            }, "asyncRecordRobotTlog", false);
        } catch (NKCheckedException e) {
            LOGGER.error("NKCheckedException: ", e);
        }
    }

    public int applyUgcData(long ugcId, long battleId) {
        if (ugcId == 0) {
            LOGGER.debug("allocUgcData ugcId {} battleId {}", ugcId, battleId);
            return 0;
        }

        SsUgcsvr.RpcApplyMapDataReq.Builder builder = SsUgcsvr.RpcApplyMapDataReq.newBuilder();
        builder.setUgcId(ugcId);
        try {
            RpcResult<SsUgcsvr.RpcApplyMapDataRes.Builder> rpcResult = UgcService.get().rpcApplyMapData(builder);
            if (0 != rpcResult.getData().getResult()) {
                LOGGER.error("allocUgcData result != 0, {}", rpcResult.getData().getResult());
                return rpcResult.getData().getResult();
            } else {
                BattleInfo battleInfo = battleMap.get(battleId);
                if (battleInfo != null) {
                    battleInfo.setUgcData(rpcResult.getData().getMapData().toByteArray());
                    battleInfo.setUseUgc(true);
                    battleInfo.setUgcId(ugcId);
                    battleInfo.setUgcMapMd5(rpcResult.getData().getMd5());
                    battleInfo.setKeyInfo(rpcResult.getData().getKeyInfo());
                } else {
                    LOGGER.error("applyUgcData result == 0, battleInfo == null {}", battleId);
                }
            }
        } catch (NKCheckedException exception) {
            LOGGER.error("applyUgcData catch exception {}", exception);
        }
        return NKErrorCode.OK.getValue();
    }

    private NKPair<Integer, String> createGameSession(BattleInfo battle, AiServerData aiServerData, long customRoomLeaderId) throws BattleOperateRetryException {
        // pve模式下不需要初始化聊天
        if (!BattleChatMgr.getInstance().initChatGroup(battle)) {
            LOGGER.error("init chat group for battle fail : battle {}", battle.getBattleID());
        }
        battle.setAiServerData(aiServerData);
        battle.setCreateCustomRoomLeaderId(customRoomLeaderId);

        boolean enableDsReplay = false;
        String dsReplayDir = "";
        if (needSecurityReplay(battle)) { //如果开了安全的录像，就不开AI录像了，空闲的时候再开
            enableDsReplay = true;
            dsReplayDir = BSConfig.getSaveSecurityBattleDSDir();
        } else if (randomEnableAIReplay(battle)) {
            enableDsReplay = true;
            dsReplayDir = BSConfig.getSaveAIBattleDSDir();
        }

//        if (enableDsReplay){
//            battle.setDsReplayFlag();
//        }

        if (battle.isUgcCustomRoomWithSinglePlayer() && !BattleDSCLoadMgr.getInstance().checkUGCSingleDSLoadValid()) {
            LOGGER.info("confirmBattle checkUGCSingleDSLoadValid failed battleId:{} matchTypeID:{} gameMode:{}",
                    battle.getBattleID(), battle.getMatchRuleInfoBuilder().getMatchTypeId(), battle.getMatchRuleInfo().getGameModeType());
            return new NKPair<>(NKErrorCode.BattleUGCSingleDSLoadHighBusy.getValue(), "ugc single load busy");
        }

        LOGGER.debug("create game session battleId:{} gameSessionId:{}", battle.getBattleID(), battle.getDsSessionId());
        NKPair<Integer, String> createResult = battle.createGameSession(this.dsAuthSecret, enableDsReplay,
                dsReplayDir);
        if (createResult.getKey() != NKErrorCode.OK.getValue()) {
            LOGGER.error("startBattle createGameSession failed battleId:{} ret:{}",
                    battle.getBattleID(), createResult.getKey());
            Monitor.getInstance().add.fail(MonitorId.attr_battlesvr_create_battle, 1);
            battle.cleanBattleProc(false);
            return new NKPair<>(NKErrorCode.BattleCreateModSessionFailure.getValue(), createResult.getValue());
        }
        return new NKPair<>(createResult.getKey(), "ok");
    }

    public NKPair<Integer, String> confirmBattle(SsBattlesvr.RpcConfirmBattleReq.Builder req) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battle = getBattleInfoByFirstScene(req.getBattleId());
            if (null == battle) {
                LOGGER.error("startBattle failed null == battle {}", req.getBattleId());
                return new NKPair<>(NKErrorCode.BattleNotExist.getValue(), "invalid battle");
            }
            try {
                return createGameSession(battle, req.getAiServerData(), req.getCustomRoomLeaderId());
            } catch (BattleOperateRetryException e) {
                LOGGER.warn("confirmBattle caught BattleOperateRetryException, battleId:{}, err:{}",
                        battle.getBattleID(), e.getMessage());
                BattleDBMgr.getInstance().reloadBattleInfo(battle);
                continue;
            }
        }
        return new NKPair<>(NKErrorCode.BattleRetryTimeLimit.getValue(), "retry failed");
    }

    private boolean isInHour(String hourStr) {
        long curTime = DateUtils.currentTimeMillis();
        long curHour = DateUtils.getHourOfDay(curTime);
        String[] hours = hourStr.split(":");
        for (int i = 0; i < hours.length; i += 2) {
            if (i + 1 < hours.length) {
                int startHour = Integer.parseInt(hours[i]);
                int endHour = Integer.parseInt(hours[i + 1]);
                if (curHour >= startHour && curHour < endHour) {
                    return true;
                }
            }
        }
        return false;
    }

    public NKErrorCode sceneChangeBattleDS(BattleSceneInfo sceneInfo, BattleInfo battleInfo, long gameSessionId, BattleSceneExtraInfo sceneExtraInfo, KVArray kvArray,
            List<MemberBaseInfo> memberBaseInfoList) {
        // 拷贝一份数据出来
        BattleInfo newBattleInfo = battleInfo.createReplica(this.dsAuthSecret, false);
        newBattleInfo.setState(BattleState.Battle_Scene_Change);
        newBattleInfo.setVersion(0);
        for (var member : memberBaseInfoList) {
            BattlePlayerInfo replicaBattlePlayer = new BattlePlayerInfo(newBattleInfo, member);
            if (!replicaBattlePlayer.isRobot()) {
                replicaBattlePlayer.generateToken(dsAuthSecret);
            }
            newBattleInfo.addBattlePlayer(replicaBattlePlayer);
        }
        newBattleInfo.fillUgcExtraInfo();
        // 这里更新当前场景的scene信息
        newBattleInfo.setCreateTime(battleInfo.getCreateTime());
        newBattleInfo.setDsSessionId(gameSessionId);
        newBattleInfo.setBattleSceneExtraInfo(sceneExtraInfo);
        newBattleInfo.setKvArray(kvArray);
        newBattleInfo.updateEndTime();

        // 将这个插入到数据库里面
        NKErrorCode res = BattleDBMgr.getInstance().insertDBBattleRoomInfo(newBattleInfo);
        if (res != NKErrorCode.OK) {
            return res;
        }

        // 将这个插回到SceneInfo里面 同时若更新失败则删除这个battleInfo
        if (!sceneInfo.addBattleSceneBaseInfo(newBattleInfo.getBattleSceneBaseInfo())) {
            BattleDBMgr.getInstance().delWithVersionComparison(newBattleInfo);
            LOGGER.error("add battle scene base info fail battleId:{}", newBattleInfo.getBattleID());
            return NKErrorCode.DBOpFailed;
        }

        // 开始执行数据的拉起
        addBattleInfo(newBattleInfo);
        asyncRecordRobotTlog(newBattleInfo);
        if (newBattleInfo.isUgcCustomRoomWithSinglePlayer()) {
            BattleDSCLoadMgr.getInstance().incrUgcSingleDS(newBattleInfo.getBattleID());
        }
        LOGGER.debug("Scene battleId:{} try create game session:{}", sceneInfo.getBattleId(), gameSessionId);

        // 开始尝试创建对局
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            try {
                // 拉起gameSession
                var ret = createGameSession(newBattleInfo, newBattleInfo.getAiServerData(), newBattleInfo.getCreateCustomRoomLeaderId());
                return NKErrorCode.forNumber(ret.getKey());
            } catch (BattleOperateRetryException e) {
                LOGGER.warn("sceneChangeBattleDS caught BattleOperateRetryException, battleId:{}, err:{}",
                        newBattleInfo.getBattleID(), e.getMessage());
                BattleDBMgr.getInstance().reloadBattleInfo(newBattleInfo);
                continue;
            }
        }

        return NKErrorCode.BattleRetryTimeLimit;
    }

    public boolean migrateBattleDS(long battleId) {
        try {
            BattleInfo battleInfo = getBattleInfo(battleId);
            if (battleInfo == null) {
                LOGGER.error("battleInfo is null: {}", battleId);
                return false;
            }
            if (!TycConfs.isOpenDSMigrate(battleInfo.getMatchRuleInfo().getRoomInfoID())) {
                return false;
            }
            BattleInfo.BattleState preState = battleInfo.getState();
            /*if(!battleInfo.checkCanMigrate()) {
                LOGGER.error("battle can not migrate now:{}, state:{}", battleId, battleInfo.getState());
                return false;
            }*/
            LOGGER.info("Begin to migrateBattleDS, id:{}", battleId);
            // 1. 创建一个备份 battleInfo
            BattleInfo newBattleInfo = battleInfo.createReplica(this.dsAuthSecret);
            newBattleInfo.setCreateTime(Framework.currentTimeMillis());
            newBattleInfo.setBeginMigrateTime(Framework.currentTimeMillis());
            newBattleInfo.setState(BattleInfo.BattleState.Battle_Migrate);
            newBattleInfo.setMigrateCount(battleInfo.getMigrateCount() + 1);

            battleInfo.setState(BattleInfo.BattleState.Battle_Migrate);
            battleInfo.setMigrateProcessStatus(DSMigrateMgr.MigrateProcessStatus.MPS_Begin);
            try {
                boolean enableDsReplay = false;
                String dsReplayDir = "";
                if (needSecurityReplay(battleInfo)) { //如果开了安全的录像，就不开AI录像了，空闲的时候再开
                    enableDsReplay = true;
                    dsReplayDir = BSConfig.getSaveSecurityBattleDSDir();
                } else if (randomEnableAIReplay(battleInfo)) {
                    enableDsReplay = true;
                    dsReplayDir = BSConfig.getSaveAIBattleDSDir();
                }

                NKPair<Integer, String> ret = newBattleInfo.createGameSessionForMigrate(battleInfo, this.dsAuthSecret,
                        false, dsReplayDir);
                if (ret != null && ret.getKey() != NKErrorCode.OK.getValue()) {
                    battleInfo.setState(preState);
                    battleInfo.updateStateToDb();
                    battleInfo.setMigrateProcessStatus(DSMigrateMgr.MigrateProcessStatus.MPS_Finish);
                    LOGGER.error("Error in createGameSessionForMigrate:{}, errCode:{}, err:{}",
                            battleId, ret.getKey(), ret.getValue());
                } else {
                    battleInfo.setMigrateProcessStatus(DSMigrateMgr.MigrateProcessStatus.MPS_Wait_Ds_Create);
                    BattleMgr.sendTycDsBattleTlog(battleInfo, TlogMacros.TYCDSFlOWTYPE.TDFT_DS_MIGRATE_BEGIN,
                            "migrate:" + String.valueOf(battleInfo.getMigrateProcessStatus().getNumber()));
                }
                Monitor.getInstance().add.succ(MonitorId.attr_tyc_tf_migrate_req, 1);
                LOGGER.error("Success in migrateBattleDS, battleId:{}, ret:{}", battleId, ret);
            } catch (Throwable t) {
                Monitor.getInstance().add.fail(MonitorId.attr_tyc_tf_migrate_req, 1);
                LOGGER.error("Error in createGameSessionForMigrate:{}, ", battleId, t);
                battleInfo.setState(preState);
                battleInfo.setMigrateProcessStatus(DSMigrateMgr.MigrateProcessStatus.MPS_None);
                battleInfo.updateStateToDb();
            }
            return true;
        } catch (Throwable t) {
            LOGGER.error("Error in migrateBattleDS:{}, ", battleId, t);
        }
        return false;
    }

    private boolean randomEnableAIReplay(BattleInfo battleInfo) {
        long battleId = battleInfo.getBattleID();
        if (!BSConfig.getEnableSaveAIBattleDSReplay()) {
            LOGGER.debug("randomEnableReplay disable replay. battleId:{}", battleId);
            return false;
        }
        if (BSConfig.getSaveAIBattleDSReplayServiceInstanceId() != Framework.getInstance().getInstanceId()) {
            LOGGER.debug(
                    "randomEnableReplay disable service instance, battleId:{} enableServiceIntanceId:{} serviceInstanceId:{}",
                    battleId, BSConfig.getSaveAIBattleDSReplayServiceInstanceId(),
                    Framework.getInstance().getInstanceId());
            return false;
        }
        boolean inHour = isInHour(BSConfig.getSaveAIBattleDSReplayHour());
        if (!inHour) {
            LOGGER.debug("randomEnableAIReplay not in hour, battleId:{} hourStr:{}", battleId,
                    BSConfig.getSaveAIBattleDSReplayHour());
            return false;
        }
        long curTime = DateUtils.currentTimeMillis();
        if (!DateUtils.isSameDay(curTime, saveAIDsReplayStartTime)) {
            LOGGER.info("reset save ds replay count, battleId:{} lastCount:{} lastStartTime:{}",
                    battleId, saveAIDsReplayCount, DateUtils.unixTime2String(saveAIDsReplayStartTime));
            saveAIDsReplayStartTime = curTime;
            saveAIDsReplayCount = 0;
        }
        if (saveAIDsReplayCount >= BSConfig.getSaveAIBattleDSReplayDayCount()) {
            LOGGER.debug("randomEnableReplay day count limit, battleId:{} saveCount:{} dayCount:{}",
                    battleId, saveAIDsReplayCount, BSConfig.getSaveAIBattleDSReplayDayCount());
            return false;
        }
        ArrayList<Long> outTimeList = null;
        for (Map.Entry<Long, Long> item : replayBattleInfoMap.entrySet()) {
            long replayBattleId = item.getKey();
            long replayStartTime = item.getValue();
            if (curTime - replayStartTime > BSConfig.getAIBattleDsReplayOutTime() * 1000L) {
                if (outTimeList == null) {
                    outTimeList = new ArrayList<>();
                    outTimeList.add(replayBattleId);
                }
            }
        }
        if (outTimeList != null) {
            for (Long removeBattleId : outTimeList) {
                Long replayStartTime = replayBattleInfoMap.get(removeBattleId);
                replayBattleInfoMap.remove(removeBattleId);
                LOGGER.info("remove replay battle info timeout, battleId:{} startTime:{}", removeBattleId,
                        DateUtils.unixTime2String(replayStartTime));
            }
        }
        if (replayBattleInfoMap.size() >= BSConfig.getAIBattleDSReplayConcurrencyCount()) {
            LOGGER.info("randomEnableReplay concurrency count limit, battleId:{} curCount:{} limitCount:{}",
                    battleId, replayBattleInfoMap.size(), BSConfig.getAIBattleDSReplayConcurrencyCount());
            return false;
        }
        float randomRate = RandomUtils.nextFloat(0f, 1f);
        if (randomRate > BSConfig.getAIBattleDSReplayRandomRate()) {
            LOGGER.debug("randomEnableReplay not in random rate, battleId:{} randomRate:{} needRate:{}",
                    battleId, randomRate, BSConfig.getAIBattleDSReplayRandomRate());
            return false;
        }
        replayBattleInfoMap.put(battleId, curTime);
        LOGGER.info("add replay battle info, battleId:{} startTime:{}  randomRate:{} needRate:{}",
                battleId, DateUtils.unixTime2String(curTime), randomRate, BSConfig.getAIBattleDSReplayRandomRate());
        saveAIDsReplayCount += 1;
        return true;
    }

    public boolean randomEnableLevelSaveLocalAIFile(BattleInfo battleInfo, int levelId) {
        ResAIInfo.AIInfoDifficulty aiDifficultyCfg = AIInfoDifficultyData.getInstance().get(levelId);
        if (aiDifficultyCfg != null && aiDifficultyCfg.getUseLocalAI()) {
            return randomEnableSaveLocalAIFile(battleInfo);
        }
        LOGGER.debug("randomEnableLevelSaveLocalAIFile level is not open local ai, battleId:{} levelId:{}",
                battleInfo.getBattleID(), levelId);
        return false;
    }

    public boolean randomEnableSaveLocalAIFile(BattleInfo battleInfo) {
        long battleId = battleInfo.getBattleID();
        if (!BSConfig.getEnableSaveLocalAIFile()) {
            LOGGER.debug("randomEnableSaveLocalAIFile disable. battleId:{}", battleId);
            return false;
        }
        boolean inHour = isInHour(BSConfig.getSaveLocalAIFileHour());
        if (!inHour) {
            LOGGER.debug("randomEnableSaveLocalAIFile not in hour, battleId:{} hourStr:{}", battleId,
                    BSConfig.getSaveLocalAIFileHour());
            return false;
        }

        long curTime = DateUtils.currentTimeMillis();
        if (!DateUtils.isSameDay(curTime, saveLocalAIFileStartTime)) {
            LOGGER.info("randomEnableSaveLocalAIFile reset save count, battleId:{} lastCount:{} lastStartTime:{}",
                    battleId, saveLocalAIFileCount, DateUtils.unixTime2String(saveLocalAIFileStartTime));
            saveLocalAIFileStartTime = curTime;
            saveLocalAIFileCount = 0;
        }
        if (saveLocalAIFileCount >= BSConfig.getSaveLocalAIFileDayCount()) {
            LOGGER.debug("randomEnableSaveLocalAIFile day count limit, battleId:{} saveCount:{} dayCount:{}",
                    battleId, saveLocalAIFileCount, BSConfig.getSaveLocalAIFileDayCount());
            return false;
        }
        float randomRate = RandomUtils.nextFloat(0f, 1f);
        if (randomRate > BSConfig.getLocalAIFileRandomRate()) {
            LOGGER.debug("randomEnableSaveLocalAIFile not in random rate, battleId:{} randomRate:{} needRate:{}",
                    battleId, randomRate, BSConfig.getLocalAIFileRandomRate());
            return false;
        }
        LOGGER.info("randomEnableSaveLocalAIFile success, battleId:{} startTime:{}  randomRate:{} needRate:{}",
                battleId, DateUtils.unixTime2String(curTime), randomRate, BSConfig.getLocalAIFileRandomRate());
        saveLocalAIFileCount += 1;
        return true;
    }

    private boolean needSecurityReplay(BattleInfo battleInfo) {
        long battleId = battleInfo.getBattleID();
        if (!BSConfig.getEnableSaveSecurityBattleDSReplay()) {
            LOGGER.debug("needSecurityReplay disable replay, battleId:{}", battleId);
            return false;
        }
        if (!battleInfo.getOpenSecurityDsReplayRecord()) {
            LOGGER.debug("needSecurityReplay getOpenSecurityDsReplayRecord false, battleId:{}", battleId);
            return false;
        }
        long curTime = DateUtils.currentTimeMillis();
        if (!DateUtils.isSameDay(curTime, saveSecurityDsReplayStartTime)) {
            LOGGER.info("needSecurityReplay reset save ds replay count, battleId:{} lastCount:{} lastStartTime:{}",
                    battleId, saveSecurityDsReplayCount, DateUtils.unixTime2String(saveSecurityDsReplayStartTime));
            saveSecurityDsReplayStartTime = curTime;
            saveSecurityDsReplayCount = 0;
        }
        if (saveSecurityDsReplayCount >= BSConfig.getSaveSecurityBattleDSReplayDayCount()) {
            LOGGER.debug("needSecurityReplay day count limit, battleId:{} saveCount:{} dayCount:{}",
                    battleId, saveSecurityDsReplayCount, BSConfig.getSaveSecurityBattleDSReplayDayCount());
            return false;
        }

        LOGGER.debug("needSecurityReplay add replay battle info, battleId:{}", battleId);
        saveSecurityDsReplayCount += 1;
        return true;
    }

    public int onCreateGameSessionNtf(DsEventSubscriberOuterClass.CreateGameSessionResultEventRequest request) {
        long dsSessionId = Long.parseLong(request.getGameSessionId());
        long battleID = getBattleIdBySessionId(dsSessionId);
        if (request.getResult() == DsEventSubscriberOuterClass.CreateGameSessionResult.CREATE_GAME_SESSION_RESULT_SUCCESS_VALUE) {
            LOGGER.info("In onCreateGameSessionNtf, sessionId:{}, battleId:{} req dsa_service_name:{}, dsa_service_id:{}, ds_listen_addr:{}, ds_log:{}, game_session_id:{}, result:{}",
                    request.getGameSessionId(), battleID, request.getDsaServiceName(), request.getDsaServiceId(),
                    request.getDsListenAddr(), request.getDsLog(), request.getGameSessionId(), request.getResult());
        } else {
            LOGGER.info("In onCreateGameSessionNtf, sessionId:{}, battleId:{} req game_session_id:{}, result:{}",
                    request.getGameSessionId(), battleID, request.getGameSessionId(), request.getResult());
        }
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battle = getBattleInfo(battleID);
            if (battle == null && dsSessionId != battleID) {
               battle = getBattleInfo(dsSessionId);
            }
            if (null == battle) {
                LOGGER.error("startBattle failed null == battle {}", battleID);
                return NKErrorCode.BattleNotExist.getValue();
            }
            try {
                if (battle.getState() == BattleInfo.BattleState.Battle_Migrate) {
                    LOGGER.info("Is ds migrate:{}", battleID);
                    registerBattleIdToSessionId(dsSessionId, battleID);
                    return battle.onMigrateDSCreateGameSessionNtf(request.getDsaServiceId(), dsSessionId,
                            request.getDsListenAddr(), request.getResult(), request.getDsUserDataMap());
                } else if (battle.getState() == BattleInfo.BattleState.Battle_Scene_Change) {
                    LOGGER.info("is scene change:{}", battleID);
                    return battle.onSceneChangeDSCreateGameSessionNtf(request.getDsaServiceId(), dsSessionId,
                            request.getDsListenAddr(), request.getResult(), request.getDsUserDataMap(), request.getDsLog());
                } else {
                    return battle.onCreateGameSessionNtf(request.getDsaServiceId(), dsSessionId,
                            request.getDsListenAddr(), request.getResult(), request.getDsUserDataMap(), request.getDsLog());
                }
            } catch (BattleOperateRetryException e) {
                LOGGER.warn("confirmBattle caught BattleOperateRetryException, battleId:{}, err:{}",
                        battleID, e.getMessage());
                BattleDBMgr.getInstance().reloadBattleInfo(battle);
                continue;
            }
        }
        return NKErrorCode.BattleRetryTimeLimit.getValue();
    }

    public int onGameSessionEndEventNtf(DsEventSubscriberOuterClass.GameSessionEndEventRequest request) {
        long dsSessionId = Long.parseLong(request.getGameSessionId());
        long battleID = getBattleIdBySessionId(Long.parseLong(request.getGameSessionId()));
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfo(battleID);
            if (battleInfo == null && dsSessionId != battleID) {
                battleInfo = getBattleInfo(dsSessionId);
            }
            if (battleInfo == null) {
                return NKErrorCode.BattleNotExist.getValue();
            }
            LOGGER.info("In onGameSessionEndEventNtf battleID:{}, sessionId:{}, matchType:{}, status:{}",
                    battleID, request.getGameSessionId(), battleInfo.getMatchRuleInfo().getMatchTypeId(),
                    battleInfo.getMigrateProcessStatus());
            if (battleInfo.getMigrateProcessStatus() == DSMigrateMgr.MigrateProcessStatus.MPS_Wait_Ds_Terminate
                    && request.getEndCode()
                    == GameSessionEndCode.GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED_VALUE) {
                battleInfo.setMigrateProcessStatus(DSMigrateMgr.MigrateProcessStatus.MPS_Finish);
                LOGGER.info("Migrated ds success to close:{}, {}", battleID, battleInfo.getMigrateProcessStatus());
                BattleMgr.sendTycDsBattleTlog(battleInfo, TlogMacros.TYCDSFlOWTYPE.TDFT_DS_MIGRATE_SUCCESS,
                        "onGameSessionEndEventNtfMigrate:" + String.valueOf(DSMigrateMgr.MigrateProcessStatus.MPS_Finish.getNumber()));
                return NKErrorCode.OK.getValue();
            }
            if (request.getEndCode() == GameSessionEndCode.GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED_VALUE) {
                LOGGER.error("In onGameSessionEndEventNtf force terminate ds:{}, battleId:{}",
                        request, battleID);
            }
            if (!battleInfo.battleWithScene()) {
                RankMgr.getInstance().removeBattleRankData(battleID);
            }
            if (request.getEndCode() != GameSessionEndCode.GAME_SESSION_ENDCODE_NORMAL_END_VALUE) {
                // DS进程销毁，只处理异常情况才通知
                battleInfo.battleExitNtf(request.getEndCode());
                String[] monitorParams = new String[]{
                        String.valueOf(battleInfo.getMatchRuleInfo().getMatchTypeId()),
                };
                switch (request.getEndCode()) {
                    case GameSessionEndCode.GAME_SESSION_ENDCODE_ABNORMAL_VALUE:
                        Monitor.getInstance().add.total(MonitorId.attr_battlesvr_battle_end_abnormal, 1, monitorParams);
                        break;
                    case GameSessionEndCode.GAME_SESSION_ENDCODE_DS_PROCESS_FORCE_TERMINATED_VALUE:
                        Monitor.getInstance().add.total(MonitorId.attr_battlesvr_battle_end_ds_process_force_terminated,
                                1, monitorParams);
                        break;
                    case GameSessionEndCode.GAME_SESSION_ENDCODE_FLEET_FORCE_TERMINATED_VALUE:
                        Monitor.getInstance().add.total(MonitorId.attr_battlesvr_battle_end_fleet_force_terminated, 1,
                                monitorParams);
                        break;
                    case GameSessionEndCode.GAME_SESSION_ENDCODE_GAME_SESSION_TOO_LONG_VALUE:
                        Monitor.getInstance().add.total(MonitorId.attr_battlesvr_battle_end_game_seesion_too_long, 1,
                                monitorParams);
                        break;
                    case GameSessionEndCode.GAME_SESSION_ENDCODE_PROCESS_ENDING_WITHOUT_TERMINATE_GAME_SESSION_VALUE:
                        Monitor.getInstance().add.total(
                                MonitorId.attr_battlesvr_battle_end_process_ending_without_terminate_game_session, 1,
                                monitorParams);
                        break;
                    case GameSessionEndCode.GAME_SESSION_ENDCODE_CHECK_PID_NOT_EXIST_VALUE:
                        Monitor.getInstance().add.total(
                                MonitorId.attr_battlesvr_battle_game_session_endcode_check_pid_not_exist, 1,
                                monitorParams);
                        break;
                    case GameSessionEndCode.GAME_SESSION_ENDCODE_HEALTH_CHECK_TIMEOUT_VALUE:
                        Monitor.getInstance().add.total(
                                MonitorId.attr_battlesvr_battle_game_session_endcode_health_check_timeout, 1,
                                monitorParams);
                        break;
                    case GameSessionEndCode.GAME_SESSION_ENDCODE_STOP_FOR_RECOVERY_VALUE:
                        Monitor.getInstance().add.total(
                                MonitorId.attr_battlesvr_battle_game_session_endcode_stop_for_recovery, 1,
                                monitorParams);
                        break;
                }
            }
            // 只要战场还存在，就去销毁战场
            NKErrorCode res;
            try {
                res = battleInfo.cleanBattleProc(true);
                // 清理兽人ds附加的信息
                battleInfo.cancelRecruit();
                BattleRoomNoHelper.freeRoomNo(battleInfo);

                BattleMgr.sendTycDsBattleTlog(battleInfo, TlogMacros.TYCDSFlOWTYPE.TDFT_DS_DESTROY,
                        "onGameSessionEndEventNtf:" + String.valueOf(request.getEndCode()));
            } catch (BattleOperateRetryException e) {
                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                LOGGER.error("onGameSessionEndEventNtf meets reload, battleId:{}", battleInfo.getBattleID());
                continue;
            }

            return res.getValue();
        }
        return NKErrorCode.BattleRetryTimeLimit.getValue();
    }

    public long allocBattleID() {
        return BattleIdGenerator.getInstance().allocGuid();
    }

    /**
     * 退出战斗
     */
    public int onQuitBattle(DsRoomsvr.QuitBattleRequest request) {
        boolean needReload = true;
        StringBuilder sb = new StringBuilder("quitBattle:");
        for (DsRoomsvr.QuitBattleInfo playerQuitInfo : request.getQuitListList()) {
            //battleInfo.processPlayerGiveUpBattle(playerQuitInfo.getUid(), playerQuitInfo.getQuitCode());
            int ret = NKErrorCode.OK.getValue();
            BattleInfo battleInfo = null;
            sb.append(playerQuitInfo.getUid()).append(",");
            for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
                if (needReload) {
                    battleInfo = getBattleInfoBySessionId(request.getBattleId(), request.getDsSessionId());
                    needReload = false;
                }
                if (null == battleInfo) {
                    LOGGER.error("QuitBattle failed null == battle {}", request.getBattleId());
                    return NKErrorCode.BattleNotExist.getValue();
                }
                try {
                    ret = battleInfo.processPlayerGiveUpBattleBroadCastRoom(
                            playerQuitInfo.getUid(), playerQuitInfo.getQuitCode(),
                            retried < 1 && ret != NKErrorCode.BattleQuitRpcError.getValue());
                } catch (BattleOperateRetryException e) {
                    BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                    needReload = true;
                    LOGGER.error("endBattle meets reload, battleId:{}", battleInfo.getBattleID());
                    continue;
                }
                break;
            }
        }

        // 结算后处理
        List<Long> quitPlayers = new ArrayList<>();
        BattleInfo battleInfo = getBattleInfoBySessionId(request.getBattleId(), request.getDsSessionId());
        request.getQuitListList().forEach(o -> quitPlayers.add(o.getUid()));
        quitBattleOnJoinMidwayMode(battleInfo, quitPlayers, BattleQuitDeleteEnum.BQDE_Quit_Battle);

        removePlayerWithEndBattle(battleInfo, quitPlayers, BattleQuitDeleteEnum.BQDE_Quit_Battle);

        // 中途退出
        sendTycDsBattleTlog(battleInfo, TlogMacros.TYCDSFlOWTYPE.TDFT_MID_LEAVE, sb.toString());

        // 处理多场景有玩家退出后的逻辑
        BattleSceneInfo battleSceneInfo = battleInfo.getBattleSceneInfo();
        if (battleSceneInfo != null) {
            battleSceneInfo.removeSceneMemberAfterCheck(battleInfo);
        }
        return NKErrorCode.OK.getValue();
    }

    /**
     * 战斗结算
     */
    public int onEndBattle(DsRoomsvr.EndBattleRequest request) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfoBySessionId(request.getBattleId(), request.getDsSessionId());
            if (null == battleInfo) {
                LOGGER.error("onEndBattle failed null == battle {}", request.getBattleId());
                return NKErrorCode.BattleNotExist.getValue();
            }
            if (battleInfo.getGameModeType() == GameModeType.GMT_UgcTest && BSConfig.enableUgcMultiTestSettlement() == false) {
                LOGGER.error("onEndBattle unsupported null == battle {}, gameModeType {}", request.getBattleId(),
                        battleInfo.getGameModeType());
                return NKErrorCode.Unsupported.getValue();
            }
            LOGGER.info("onEndBattle battleId:{} matchTypeId:{} dsSessionId:{} version:{} isBattleFinished:{} " +
                            "observerSize:{} retried:{} requestSize:{}", request.getBattleId(),
                    battleInfo.getMatchRuleInfo().getMatchTypeId(), request.getDsSessionId(), request.getVersion(),
                    request.getBattleFinishedExResult().getIsBattleFinished(), request.getObserversInfoList().size(),
                    retried, request.getSerializedSize());
            battleInfo.battleSettlement(request);
            LOGGER.info("onEndBattle reply battleId {}", battleInfo.getBattleID());
            sendTycDsBattleTlog(battleInfo, TlogMacros.TYCDSFlOWTYPE.TDFT_LEAVE, "onEndBattle");

            try {
                ArenaBattleDataHelper.monitorBossMatch(battleInfo, request);
            }catch (Exception e){
                LOGGER.info("monitorBossMatch exception {}", battleInfo.getBattleID(), e);
            }

            return NKErrorCode.OK.getValue();
        }
        return NKErrorCode.BattleRetryTimeLimit.getValue();
    }

    private void changeBattleSceneMonitor(List<ChangeBattleSceneResult> resultList) {
        // 次数监控
        Monitor.getInstance().add.total(MonitorId.attr_battle_multi_scene_change_scene_req, 1);
        // 结果监控
        Map<Integer, Integer> monitorMap = new HashMap<>();
        for (var resultOne : resultList) {
            var beforeCnt = monitorMap.get(resultOne.getErrCode());
            if (beforeCnt == null) {
                beforeCnt = 0;
            }
            monitorMap.put(resultOne.getErrCode(), beforeCnt + 1);
        }
        for (var monitorOne : monitorMap.entrySet()) {
            int ec = monitorOne.getKey();
            int cnt = monitorOne.getValue();
            Monitor.getInstance().add.total(MonitorId.attr_battle_multi_scene_change_scene_ec, cnt, new String[]{String.valueOf(ec)});
        }
        // 打个日志
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("monitorMap:{}", monitorMap);
        }
    }

    public List<ChangeBattleSceneResult> changeBattleScene(DsRoomsvr.ChangeBattleSceneRequest req) {
        var resultList = BattleSceneAdapter.ChangeBattleScene(req);
        changeBattleSceneMonitor(resultList);
        return resultList;
    }

    public int changeBattleSideInGame(DsRoomsvr.ChangeBattleSideInGameRequest req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            LOGGER.error("battleId:{} not exist", req.getBattleId());
            return NKErrorCode.BattleNotExist.getValue();
        }
        var battlePlayer = battleInfo.getBattlePlayerInfo(req.getUid());
        if (battlePlayer == null) {
            LOGGER.error("battleId:{} uid:{} not in game", req.getBattleId(), req.getUid());
            return NKErrorCode.BattlePlayerNotExist.getValue();
        }
        LOGGER.info("battleId:{} uid:{} change side in game from:{} to:{}", req.getBattleId(), req.getBattleId(),
                req.getFromSide(), req.getToSide());
        battlePlayer.setSideInGame(req.getToSide());
        return NKErrorCode.OK.getValue();
    }

    public void onInsertBattleRecord(DsRoomsvr.BattleRecordRequest request) { //已废弃函数
        if (request.hasGlobalRecord()) {
            long battleId = request.getGlobalRecord().getBattleId();
            BattleInfo battleInfo = getBattleInfo(battleId);
            if (battleInfo != null) {
                if (battleInfo.getGameModeType() == GameModeType.GMT_UgcTest) {
                    LOGGER.error("onInsertBattleRecord unsupported null == battle {}, gameModeType {}", battleId,
                            battleInfo.getGameModeType());
                    return;
                }
            }
            boolean ret = BattleHistoryUtil.addRecord(request.getGlobalRecord().getBattleId(),
                    request.getGlobalRecord().getRecord());
            if (!ret) {
                LOGGER.error("insert to GlobalBattleRecord failed:{}", request);
            }
        }
    }

    public int onEndLevel(DsRoomsvr.EndLevelRequest req) {
        BattleInfo battleInfo = getBattleInfoBySessionId(req.getBattleId(), req.getDsSessionId());
        if (null == battleInfo) {
            LOGGER.error("onEndLevel failed null == battle {}", req.getBattleId());
            return NKErrorCode.BattleNotExist.getValue();
        }

        UgcLevelSettlementInfo.Builder ugcSettlement = UgcLevelSettlementInfo.newBuilder();
        // 如果对局需要ugc结算
        if (req.getUgcSettlement()) {
            battleInfo.fillUgcLevelSettlementInfo(ugcSettlement);
        }

        var abtest = battleInfo.getLevelRoundABTestInfo(req.getAtRound());
        List<Integer> enabledABTestIds = abtest == null ? Lists.newArrayList() : abtest.getEnabledTestIdsList();
        if (LOGGER.isDebugEnabled()) {
            var abTestInfos = battleInfo.getLevelRoundABTestInfos();
            Set<Integer> enabledLevels = Sets.newHashSet();
            for (var abtestInfo : abTestInfos) {
                if (abtestInfo.getEnabledTestIdsCount() > 0) {
                    enabledLevels.add(abtestInfo.getRound());
                }
            }

            LOGGER.debug("prepare level round abtest fields, battleId:{} enabled:{} current:{} testIds:{}",
                    battleInfo.getBattleID(), enabledLevels, req.getAtRound(), enabledABTestIds);
        }

        req.getResultsList().forEach(
                res -> battleInfo.ntfPlayerLevelFinish(req.getLevelId(), req.getAtRound() - 1, enabledABTestIds,
                        res, ugcSettlement, req.getRandEvent()));

        battleInfo.endLevel(req.getLevelId());
        battleInfo.prepareLevel(0);

        // 完成level时通知赛事侧
        if (battleInfo.getCompetitionData().getSeason() > 0) {
            LOGGER.debug("battle {} ntfCompetitionOnLevelEnd, season: {}", req.getBattleId(),
                    battleInfo.getCompetitionData().getSeason());
            try {
                CurrentExecutorUtil.runJob(() -> {
                    battleInfo.ntfCompetitionOnLevelEnd(req);
                    return null;
                }, "ntfCompetitionOnLevelEnd", false);
            } catch (NKCheckedException e) {
                LOGGER.error("battle {} ntfCompetitionOnLevelEnd call job catch {}", req.getBattleId(),
                        e.getEnumErrCode());
            }
        }
        // 同步数据给room
        DataType.LevelStatisticsData.broadcastLevelDataToRoom(battleInfo, req.getResultsList(), req.getLevelId(), req.getAtRound());
        LOGGER.debug("onEndLevel reply");
        return NKErrorCode.OK.getValue();
    }

    public int onBeginLevel(DsRoomsvr.BeginLevelRequest req) {
        BattleInfo battleInfo = getBattleInfoBySessionId(req.getBattleId(), req.getDsSessionId());
        if (null == battleInfo) {
            LOGGER.error("onEndLevel failed null == battle {}", req.getBattleId());
            return NKErrorCode.BattleNotExist.getValue();
        }
        if (battleInfo.getGameModeType() == GameModeType.GMT_UgcTest) {
            LOGGER.debug("onBeginLevel, battleId:{} gameModeType:{}", battleInfo.getBattleID(),
                    battleInfo.getGameModeType());
            return NKErrorCode.OK.getValue();
        }
        battleInfo.runLevel(req.getLevelId(), req.getLevelDuration());
        var levelInfoData = LevelInfoData.getInstance().get(req.getLevelId());
        int ruleID = 0;
        if (levelInfoData == null) {
            LOGGER.error("levelInfoData is null {}", req.getLevelId());
        } else {
            ruleID = levelInfoData.getRandEventRuleID();
        }
        LOGGER.debug("onEndLevel reply : {},{} {} {}", battleInfo.getBattleID(),
                                            req.getLevelId(), ruleID, req.getRankEventStr());
        TlogFlowMgr.sendMapBeginFlow(req.getLevelId(),req.getAtRound(),battleInfo.getMatchRuleInfo().getMatchTypeId(),
                        battleInfo.getBattleID(),battleInfo.getMemberList(false), ruleID, req.getRankEventStr());
        return NKErrorCode.OK.getValue();
    }

    public NKErrorCode onBattleHeartbeat(long battleId, long gameSessionId) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfoBySessionId(battleId, gameSessionId);
            if (null == battleInfo) {
                LOGGER.error("onBattleHeartbeat failed null == battle {}", battleId);
                return NKErrorCode.BattleNotExist;
            }
            NKErrorCode res;
            try {
                res = battleInfo.updateHeartbeatTime(Framework.currentTimeMillis(), false);
                if (res == NKErrorCode.OK) {
                    battleInfo.setHeartTimeOut(false);
                }
            } catch (BattleOperateRetryException e) {
                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                LOGGER.error("onBattleHeartbeat meets reload, battleId:{}", battleInfo.getBattleID());
                continue;
            }
            return res;
        }
        return NKErrorCode.BattleRetryTimeLimit;
    }

    public void onPlayerDsEnter(DsCommon.PlayerDsEnterRequest req) {
        long battleId = req.getDsId();
        long gameSessionId = req.getDsSessionId();
        BattleInfo battleInfo = getBattleInfoBySessionId(battleId, gameSessionId);
        if (null == battleInfo) {
            LOGGER.error("onPlayerDsEnter failed null == battle {} {}", battleId, req.getUuid());
            return;
        }
        recordFullLinkConsume(req.getUuid(), battleInfo.getMatchID(), battleInfo.getMatchRuleInfo().getMatchTypeId(),
                battleInfo.getMatchRuleInfo().getRoomInfoID(), battleInfo.getBattleID(),
                Framework.currentTimeMillis(), MATCH_STEP_ENUM.MATCHSTEP_ENTER_DS);
    }

    public NKErrorCode onDsPassBackParamNtf(DsCommon.DsPassBackParamRequest req) {
        long battleId = req.getDsId();
        long gameSessionId = 0L;
        try {
            gameSessionId = Long.parseLong(req.getDsSessionId());
        } catch (NumberFormatException e) {
            LOGGER.error("onDsPassBackParamNtf failed NumberFormatException", e);
        }
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfoBySessionId(battleId, gameSessionId);
            if (null == battleInfo) {
                LOGGER.error("onDsPassBackParamNtf failed null == battle {}", battleId);
                return NKErrorCode.BattleNotExist;
            }
            NKErrorCode res;
            try {
                res = battleInfo.dsPassBackParamNtf(req.getDsPassBackDataMap());
            } catch (BattleOperateRetryException e) {
                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                LOGGER.error("onDsPassBackParamNtf meets reload, battleId:{}", battleInfo.getBattleID());
                continue;
            }
            return res;
        }
        return NKErrorCode.BattleRetryTimeLimit;
    }

    public NKErrorCode onSendMonitor(DsRoomsvr.SendBattleMonitorRequest req) {
        BattleInfo battleInfo = getBattleInfoBySessionId(req.getBattleId(), req.getDsSessionId());
        if (null == battleInfo) {
            LOGGER.error("onSendMonitor failed null == battle {}", req.getBattleId());
            return NKErrorCode.BattleNotExist;
        }
        String[] monitorParams = new String[]{};
        if (req.getLevelId() > 0) {
            monitorParams = new String[]{String.format("%d", req.getLevelId())};
        }
        int monitID = 0;
        int monitValue = 0;
        for (DSMonitorInfo info : req.getMonitorInfoList()) {
            monitID = info.getKey();
            monitValue = info.getValue();
            switch (monitID) {
                case BattleDsMonitorType.BDMT_LOCALDS_RESULT_VALUE: {
                    LOGGER.warn("onSendMonitor battle {} BDMT_LOCALDS_RESULT_VALUE monitValue {}", req.getBattleId(),
                            monitValue);
                    if (1 == monitValue) {
                        Monitor.getInstance().add.succ(MonitorId.attr_battlesvr_ds_localai_result, 1, monitorParams);
                    } else {
                        Monitor.getInstance().add.fail(MonitorId.attr_battlesvr_ds_localai_result, 1, monitorParams);
                    }
                }
                break;
                case BattleDsMonitorType.BDMT_LOCALDS_ROBOT_VALUE: {
                    LOGGER.debug("onSendMonitor battle {} BDMT_LOCALDS_ROBOT_VALUE monitValue {}", req.getBattleId(),
                            monitValue);
                    Monitor.getInstance().add.total(MonitorId.attr_battlesvr_ds_localai_robot, monitValue,
                            monitorParams);
                }
                break;
                case BattleDsMonitorType.BDMT_LOCALDS_CONCURRENCY_VALUE: {
                    LOGGER.debug("onSendMonitor battle {} BDMT_LOCALDS_CONCURRENCY_VALUE monitValue {}",
                            req.getBattleId(), monitValue);
                    Monitor.getInstance().add.total(MonitorId.attr_battlesvr_ds_localai_concurrency, monitValue,
                            monitorParams);
                }
                break;
                case BattleDsMonitorType.BDMT_LOCALDS_REQUESTTIME_VALUE: {
                    LOGGER.debug("onSendMonitor battle {} BDMT_LOCALDS_REQUESTTIME_VALUE monitValue {}",
                            req.getBattleId(), monitValue);
                    Monitor.getInstance().add.total(MonitorId.attr_battlesvr_ds_localai_requesttime, monitValue,
                            monitorParams);
                }
                break;
                default: {
                    LOGGER.error("onSendMonitor monitorID error {} {}", req.getBattleId(), monitID);
                }
                break;
            }
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode onReportDSReplayResult(DsRoomsvr.ReportDSReplayResultRequest req) {
        BattleInfo battleInfo = getBattleInfoBySessionId(req.getBattleId(), req.getDsSessionId());
        if (null == battleInfo) {
            LOGGER.error("onSendMonitor failed null == battle {}", req.getBattleId());
            return NKErrorCode.BattleNotExist;
        }
        battleInfo.onReportDSReplayResult(req.getAtRound(), req.getLevelId(), req.getGameId(), req.getReplayFileName(),
                req.getReplayFileSize());
        return NKErrorCode.OK;
    }

    public NKErrorCode onUpdateBattleLevelRoundInfo(BattleInfo battleInfo, boolean retry) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            NKErrorCode res;
            try {
                res = battleInfo.updateBattleLevelRoundInfo();
            } catch (BattleOperateRetryException e) {
                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                LOGGER.error("onUpdateBattleLevelRoundInfo meets reload, battleId:{}", battleInfo.getBattleID());
                if (!retry) {
                    return NKErrorCode.BattleRetryTimeLimit;
                }
                continue;
            }
            return res;
        }
        return NKErrorCode.BattleRetryTimeLimit;
    }

    public int getBattleOperateRetryTimes() {
        return battleOperateRetryTimes;
    }

    public NKErrorCode onRpcRoomMidJoinBattleReq(SsBattlesvr.RpcRoomMidJoinBattleReq.Builder req,
            SsBattlesvr.RpcRoomMidJoinBattleRes.Builder res) {
        return BattleMidJoinHelper.onRpcRoomMidJoinBattleReq(req, res);
    }

    public NKErrorCode onMidJoinPullPlayerRequest(DsRoomsvr.MidJoinPullPlayerRequest req) {
        return BattleMidJoinHelper.onMidJoinPullPlayerRequest(req);
    }

    public void onTeamJoinDsResult(DsRoomsvr.TeamJoinDsResultRequest req) {
        BattleMidJoinHelper.onTeamJoinDsResult(req);
    }

    public NKErrorCode onMidJoinStatusChange(DsRoomsvr.MidJoinStatusChangeRequest req) {
        return BattleMidJoinHelper.onMidJoinStatusChange(req);
    }

    public void onRoomMidJoinStatusChangeSync(SsBattlesvr.RpcRoomMidJoinStatusChangeSyncReq.Builder req) {
        BattleMidJoinHelper.onRoomMidJoinStatusChangeSync(req);
    }

    /**
     * 战场销毁 from ds
     */
    public int onDestroyBattle(DsRoomsvr.DestroyBattleRequest request) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfoBySessionId(request.getBattleId(), request.getDsSessionId());
            if (null == battleInfo) {
                LOGGER.error("startBattle failed null == battle {} gameSessionId:{}", request.getBattleId(), request.getDsSessionId());
                return NKErrorCode.BattleNotExist.getValue();
            }
            if (!battleInfo.battleWithScene()) {  // 非多场景多ds的这里处理rank
                RankMgr.getInstance().removeBattleRankData(request.getBattleId());
            }
            battleInfo.battleExitNtf(GameSessionEndCode.GAME_SESSION_ENDCODE_NORMAL_END_VALUE);
            // 只要战场还存在，就去销毁战场
            NKErrorCode res;
            try {
                res = battleInfo.cleanBattleProc(true);
                endBattleDisbandRoomOnJoinMidwayMode(battleInfo);
            } catch (BattleOperateRetryException e) {
                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                LOGGER.error("onDestroyBattle meets reload, battleId:{}", battleInfo.getBattleID());
                continue;
            }

            return res.getValue();
        }
        return NKErrorCode.BattleRetryTimeLimit.getValue();
    }

    /**
     * 玩家在线
     *
     * @param request 请求
     * @return int
     *         BattleNotExist 对局已经销毁，或者不存在（异常参数输入）
     *         BattleModAlreadyEnd 对局已经结束
     *         BattleReOnlineError 对局ds异常，无法重连
     *         BattleMemberNotExist 玩家不存在此对局中（异常参数输入）玩家在对局生命周期中不存在从map中移除的场景
     *         BattleUserModOver 玩家已经结算退出
     *         BattlePlayerOnlineInInvalidVersion 客户端版本不匹配
     */
    public void onPlayerOnline(SsBattlesvr.RpcBattlePlayerOnlineReq.Builder request,
            SsBattlesvr.RpcBattlePlayerOnlineRes.Builder resBuilder) {
        BattleInfo battleInfo = getBattleInfoByUid(request.getBallteId(), request.getUid());
        // 檢查玩家是否在跳轉中
        var battleSceneInfo = tryGetBattleSceneInfo(request.getBallteId());
        if (battleSceneInfo != null) {
            var onlinePreCheck = battleSceneInfo.memberOnline(request.getUid(), battleInfo, resBuilder.getBattleInfoBuilder());
            if (onlinePreCheck != NKErrorCode.OK) {
                resBuilder.setResult(onlinePreCheck.getValue());
                return;
            }
        }
        if (null == battleInfo) {
            LOGGER.info("onPlayerOnline failed null == battleId:{} uid:{}",
                    request.getBallteId(), request.getUid());
            resBuilder.setResult(NKErrorCode.BattleNotExist.getValue());
            return;
        }
        if (battleInfo.checkIsBattleEnd()) {
            LOGGER.info("onPlayerOnline checkIsBattleEnd true {} {}",
                    battleInfo.getBattleID(), request.getUid());
            resBuilder.setResult(NKErrorCode.BattleModAlreadyEnd.getValue());
            return;
        }
        NKErrorCode checkDsErrorCode = BattleMgr.getInstance().checkBattleDSStatus(battleInfo);
        if (checkDsErrorCode != NKErrorCode.OK) {
            LOGGER.error("onPlayerOnline checkIsBattleCannotEnter true {} {} {}",
                    battleInfo.getBattleID(), request.getUid(), checkDsErrorCode);
            if (checkDsErrorCode != NKErrorCode.BattleDsInCreating) {
                BattlePlayerInfo battlePlayer = battleInfo.getBattlePlayerInfo(request.getUid());
                if (battlePlayer != null) {
                    Set<BattlePlayerInfo> teamPlayers = battleInfo.getBattleTeamPlayerMap()
                            .get(battlePlayer.getRoomId());
                    if (teamPlayers != null) {
                        for (BattlePlayerInfo player : teamPlayers) {
                            if (!player.isRobot()) {
                                player.setBattleState(BattleInfo.BattleState.Battle_End);
                                LOGGER.error("onBattleCannotEnter debug, uid:{} battleId:{}", player.getUid(),
                                        request.getBallteId());
                            }
                        }
                    } else {
                        battlePlayer.setBattleState(BattleInfo.BattleState.Battle_End);
                        LOGGER.error("onBattleCannotEnter debug, uid:{} battleId:{}", battlePlayer.getUid(),
                                request.getBallteId());
                    }
                }
                // 说明还有其他场景 对局还没有结束
                if (battleSceneInfo != null && battleSceneInfo.hasOtherSceneAndWithOtherMember(request.getUid())) {
                    LOGGER.error("onPlayerOnline fail scene battleId:{} exists but player:{} battle online fail", request.getBallteId(), request.getUid());
                    resBuilder.setResult(NKErrorCode.BattleMemberNotExist.getValue());
                } else {
                    resBuilder.setResult(NKErrorCode.BattleReOnlineError.getValue());
                }
            } else {
                int onlineRet = battleInfo.playerOnline(request.getUid(), request.getVersionGroupId(), resBuilder);
                if (onlineRet != NKErrorCode.OK.getValue()) {
                    resBuilder.setResult(onlineRet);
                } else {
                    resBuilder.setResult(NKErrorCode.BattleDsInCreating.getValue());
                }
            }
            return;
        }

        resBuilder.setResult(battleInfo.playerOnline(request.getUid(), request.getVersionGroupId(), resBuilder));
    }

    /**
     * 在玩家离线
     *
     * @param request 请求
     * @return int
     */
    public int onPlayerOffline(SsBattlesvr.RpcBattlePlayerOfflineReq request) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfoByUid(request.getBallteId(), request.getUid());
            if (null == battleInfo) {
                LOGGER.debug("onPlayerOffline failed null == battle {},{}", request.getBallteId(), request.getUid());
                return NKErrorCode.BattleNotExist.getValue();
            }
            try {
                return battleInfo.playerOffline(request.getUid());
            } catch (BattleOperateRetryException e) {
                LOGGER.warn("confirmBattle caught BattleOperateRetryException, battleId:{}, err:{}",
                        battleInfo.getBattleID(), e.getMessage());
                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                continue;
            }
        }
        return NKErrorCode.BattleRetryTimeLimit.getValue();
    }

    public SsBattlesvr.RpcBattlePlayerLoginRes.Builder onPlayerLoginData(SsBattlesvr.RpcBattlePlayerLoginReq request) {
        BattleInfo battleInfo = getBattleInfoByUid(request.getBallteId(), request.getUid());
        if (null == battleInfo) {
            LOGGER.error("onPlayerOffline failed null == battle {}", request.getBallteId());
            return SsBattlesvr.RpcBattlePlayerLoginRes.newBuilder().setResult(NKErrorCode.BattleNotExist.getValue());
        }
        if (battleInfo.checkIsBattleCannotEnter()) {
            return SsBattlesvr.RpcBattlePlayerLoginRes.newBuilder()
                    .setResult(NKErrorCode.BattleReOnlineError.getValue());
        }

        return battleInfo.onPlayerLoginData(request);
    }

    /**
     * 上报游戏数据
     *
     * @param request 请求
     * @return int
     */
    public int onReportGameDate(SsBattlesvr.RpcBattleReportGameDataReq request) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfoByUid(request.getBallteId(), request.getUid());
            if (null == battleInfo) {
                LOGGER.error("onReportGameDate battleInfo failed null == battle {}", request.getBallteId());
                return NKErrorCode.BattleNotExist.getValue();
            }
            try {
                return battleInfo.onReportGameData(request.getUid(), request);
            } catch (BattleOperateRetryException e) {
                LOGGER.warn("onReportGameDate caught BattleOperateRetryException, battleId:{}, err:{}",
                        battleInfo.getBattleID(), e.getMessage());
                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                continue;
            }
        }
        return NKErrorCode.BattleRetryTimeLimit.getValue();
    }

    public int onPlayerEndGame(SsBattlesvr.RpcBattleReportGameEndReq request) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfoByUid(request.getBallteId(), request.getPlayerId());
            if (null == battleInfo) {
                LOGGER.error("onPlayerEndGame failed null == battle {}", request.getBallteId());
                return NKErrorCode.BattleNotExist.getValue();
            }
            try {
                return battleInfo.onPlayerEndGame(request).getValue();
            } catch (BattleOperateRetryException e) {
                LOGGER.warn("confirmBattle caught BattleOperateRetryException, battleId:{}, err:{}",
                        battleInfo.getBattleID(), e.getMessage());
                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                continue;
            }
        }
        return NKErrorCode.BattleRetryTimeLimit.getValue();
    }

    public SsBattlesvr.RpcSyncGameTimeRes.Builder onPlayerSyncTime(SsBattlesvr.RpcSyncGameTimeReq request) {
        BattleInfo battleInfo = getBattleInfoByUid(request.getBallteId(), request.getUid());
        SsBattlesvr.RpcSyncGameTimeRes.Builder syncGameTime = SsBattlesvr.RpcSyncGameTimeRes.newBuilder();
        if (null == battleInfo) {
            LOGGER.error("onPlayerEndGame failed null == battle {}", request.getBallteId());
            return syncGameTime.setResult(NKErrorCode.BattleNotExist.getValue());
        }
        syncGameTime.setCltTime(request.getCltTime());
        syncGameTime.setSvrTime(Framework.currentTimeMillis());

        syncGameTime.setResult(NKErrorCode.OK.getValue());
        return syncGameTime;
    }

    /**
     * 玩家准备
     *
     * @param request 请求
     * @return {@link SsBattlesvr.RpcBattlePlayerReadyRes.Builder}
     */
    public SsBattlesvr.RpcBattlePlayerReadyRes.Builder onPlayerReady(SsBattlesvr.RpcBattlePlayerReadyReq request) {
        SsBattlesvr.RpcBattlePlayerReadyRes.Builder readyRes = SsBattlesvr.RpcBattlePlayerReadyRes.newBuilder();
//        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
//            BattleInfo battleInfo = getBattleInfo(request.getBallteId());
//            if (null == battleInfo) {
//                LOGGER.error("onPlayerEndGame failed null == battle {}", request.getBallteId());
//                return readyRes.setResult(NKErrorCode.BattleNotExist.getValue());
//            }
//
//            if (!battleInfo.isFrameGame()) {
//                LOGGER.error("onPlayerEndGame checkIsFrameGame return false {}", request.getBallteId());
//                return readyRes.setResult(NKErrorCode.BattleNotFrameMod.getValue());
//            }
//
//            BattleFrame battleFrame = (BattleFrame) battleInfo;
//            readyRes.setCltTime(request.getCltTime());
//            readyRes.setSvrTime(battleFrame.getReadyTime());
//            try {
//                NKErrorCode eCode = battleFrame.battlePlayerReady(request);
//                readyRes.setResult(eCode.getValue());
//                return readyRes;
//            } catch (BattleOperateRetryException e) {
//                LOGGER.warn("confirmBattle caught BattleOperateRetryException, battleId:{}, err:{}",
//                        battleInfo.getBattleID(), e.getMessage());
//                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
//                continue;
//            }
//        }
//        readyRes.setResult(NKErrorCode.BattleRetryTimeLimit.getValue());
        return readyRes;
    }

    // 向ds发退出协议
    public SsBattlesvr.RpcBattleQuitRes.Builder onPlayerBattleQuit(SsBattlesvr.RpcBattleQuitReq request) {
        SsBattlesvr.RpcBattleQuitRes.Builder quitRes = SsBattlesvr.RpcBattleQuitRes.newBuilder();
        boolean rpcSent = false;
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfoByUid(request.getBallteId(), request.getPlayerId());
            if (null == battleInfo) {
                LOGGER.error("onPlayerBattleQuit failed null == battle {}", request.getBallteId());
                return quitRes.setResult(NKErrorCode.BattleNotExist.getValue());
            }
            if (!rpcSent) {
                PlayerExitDsRequest.Builder reqBuilder =
                        PlayerExitDsRequest.newBuilder()
                                .setGameSessionId(String.valueOf(getSessionIdByBattleId(request.getBallteId())))
                                .setDsId(battleInfo.getDsSessionId())
                                .setUuid(request.getPlayerId())
                                .setQuitCode(request.getQuitCode())
                                .setDsaInstId(battleInfo.getDsaInstanceID());
                if (battleInfo.battleWithScene()) {
                    reqBuilder.setGameSessionId(String.valueOf(battleInfo.getDsSessionId()))
                            .setDsId(battleInfo.getBattleID());
                }

                try {
                    RpcResult<PlayerExitDsReply.Builder> resBuilder = SdBattleService.get()
                            .irpcPlayerExitDs(reqBuilder.build());

                    rpcSent = true;
                    if (!resBuilder.isOK()) {
                        LOGGER.error("procQuitBattle !resBuilder.isOK ret {}", resBuilder.getRet());
                        return quitRes.setResult(NKErrorCode.BattleNotExist.getValue());
                    }
                } catch (Exception ex) {
                    LOGGER.error("procQuitBattle fail", ex);
                    return quitRes.setResult(NKErrorCode.BattleNotExist.getValue());
                }
            }
            try {
                int ret = battleInfo.playerOffline(request.getPlayerId());
                return quitRes.setResult(ret);
            } catch (BattleOperateRetryException e) {
                LOGGER.warn("confirmBattle caught BattleOperateRetryException, battleId:{}, err:{}",
                        battleInfo.getBattleID(), e.getMessage());
                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
                continue;
            }
        }

        return quitRes.setResult(NKErrorCode.BattleRetryTimeLimit.getValue());
    }

    public NKErrorCode delBattleDS(BattleInfo battleInfo, long battleId, int delCode) {
        LOGGER.info("onDelBattleDs battleId:{} sessionId:{} DsSessionId:{} DsaInstanceId:{} delCode:{}",
                battleId, getSessionIdByBattleId(battleId), battleInfo.getDsSessionId(),
                battleInfo.getDsaInstanceID(), delCode);
        SdCommonOuterClass.DelDsRequest.Builder reqBuilder =
                SdCommonOuterClass.DelDsRequest.newBuilder()
                        .setDsId(battleId)
                        .setGameSessionId(String.valueOf(getSessionIdByBattleId(battleId)))
                        .setDsaInstId(battleInfo.getDsaInstanceID())
                        .setDelCode(delCode);
        if (battleInfo.battleWithScene()) {
            reqBuilder.setGameSessionId(String.valueOf(battleInfo.getDsSessionId()));
        }

        try {
            SdCommonService.get()
                    .irpcDelDs(reqBuilder.build());
        } catch (Exception ex) {
            LOGGER.error("onDelBattleDs fail", ex);
            return NKErrorCode.BattleNotExist;
        }
        return NKErrorCode.OK;
    }

    public NKErrorCode delBattleSceneDS(BattleSceneInfo battleSceneInfo, int delCode) {
        battleSceneInfo.onDelDs();
        List<BattleInfo> allSceneBattleInfo = battleSceneInfo.getAllSceneBattleInfo();
        for (var battleInfo : allSceneBattleInfo) {
            // 删除所有的ds
            delBattleDS(battleInfo, battleSceneInfo.getBattleId(), delCode);
        }
        return NKErrorCode.OK;
    }

    public SsBattlesvr.RpcBattleDelDsRes.Builder onDelBattleDs(SsBattlesvr.RpcBattleDelDsReq request) {
        SsBattlesvr.RpcBattleDelDsRes.Builder delRes = SsBattlesvr.RpcBattleDelDsRes.newBuilder();
        BattleInfo battleInfo = getBattleInfo(request.getBattleId());
        if (null == battleInfo) {
            BattleSceneInfo battleSceneInfo = getBattleSceneInfo(request.getBattleId());
            if (battleSceneInfo != null) {
                LOGGER.error("onDelBattleDS battle scene battleId:{}", request.getBattleId());
                return delRes.setResult(delBattleSceneDS(battleSceneInfo, request.getDelCode().getNumber()).getValue());
            } else {
                LOGGER.error("onDelBattleDs failed battleId:{} sessionId:{} delCode:{}",
                        request.getBattleId(), getSessionIdByBattleId(request.getBattleId()),
                        request.getDelCode());
                return delRes.setResult(NKErrorCode.BattleNotExist.getValue());
            }
        }
        return delRes.setResult(delBattleDS(battleInfo, request.getBattleId(), request.getDelCode().getNumber()).getValue());
    }

    public SsBattlesvr.RpcDsGuideRes.Builder onDsGuideInfo(SsBattlesvr.RpcDsGuideReq request) {
        SsBattlesvr.RpcDsGuideRes.Builder dsGuidRes = SsBattlesvr.RpcDsGuideRes.newBuilder();
        BattleInfo tmpGuideBattleInfo = new BattleInfo(request.getBattleId(), request.getBattleId(), null, BattleInfo.BattleState.Battle_Wait);
        List<MemberBaseInfo> robotList = new ArrayList<>();
        this.generateRobot(request.getRobotCnt(), robotList);
        tmpGuideBattleInfo.parseDsGuideRobotInfo(robotList);
        tmpGuideBattleInfo.fillCommonRobotInfo();
        Set<BaseRobot> robotSet = tmpGuideBattleInfo.getBattleRobotMap();
        for (BaseRobot guideRobot : robotSet) {
            dsGuidRes.addAiInfo(guideRobot.serializedToPb());
        }
        dsGuidRes.setBattleId(request.getBattleId());
        return dsGuidRes.setResult(NKErrorCode.OK.getValue());
    }

    public SsBattlesvr.RpcCheckDsStatusRes.Builder checkDSStatus(SsBattlesvr.RpcCheckDsStatusReq request) {
        SsBattlesvr.RpcCheckDsStatusRes.Builder resBuilder = SsBattlesvr.RpcCheckDsStatusRes.newBuilder();
        BattleInfo battleInfo = BattleMgr.getInstance().getBattleInfoByUid(request.getBattleId(), request.getUid());
        if (battleInfo == null) {
            return resBuilder.setResult(NKErrorCode.BattleNotExist.getValue());
        }

        if (null == battleInfo.getBattlePlayerInfo(request.getUid())) {
            return resBuilder.setResult(NKErrorCode.BattleMemberNotExist.getValue());
        }
        NKErrorCode checkDsErrorCode = checkBattleDSStatus(battleInfo);
        return resBuilder.setResult(checkDsErrorCode.getValue());
    }

    public NKErrorCode checkBattleDSStatus(BattleInfo battleInfo) {
        if (battleInfo.getDsaInstanceID() == 0) {
            if (battleInfo.isReady()) {
                LOGGER.warn("checkBattleDSStatus battle in ready {}", battleInfo.getBattleID());
                return NKErrorCode.BattleDsInCreating;
            } else {
                // 对局ds是拉起成功之后, 才会修改玩家属性的, 所以当前对局对应的dsa不存在的话, 就认为非法
                LOGGER.warn("checkBattleDSStatus failed {},{}",
                        battleInfo.getBattleID(), battleInfo.getDsaInstanceID());
                return NKErrorCode.BattleCheckDsStatusError;
            }
        }
        DsaPublicOuterClass.GetGameSessionStatusRequest.Builder reqBuilder =
                DsaPublicOuterClass.GetGameSessionStatusRequest.newBuilder()
                        .setGameSessionId(String.valueOf(getSessionIdByBattleId(battleInfo.getBattleID())))
                        .setDsaInstId(battleInfo.getDsaInstanceID());
        if (battleInfo.battleWithScene()) {
            reqBuilder.setGameSessionId(String.valueOf(battleInfo.getDsSessionId()));
        }
        try {
            RpcResult<DsaPublicOuterClass.GetGameSessionStatusReply.Builder> rpcResult =
                    DsaPublicService.get().irpcGetGameSessionStatus(reqBuilder.build());
            LOGGER.debug("req:{} res:{}", reqBuilder, rpcResult.getData());
            if (0 != rpcResult.getRet()) {
                LOGGER.error("checkBattleDSStatus fail 0 != rpcResult.getRet(), {} {}",
                        battleInfo.getBattleID(), rpcResult.getRet());

                return NKErrorCode.BattleCheckDsStatusError;
            }
            if (GameSessionStatus.GAME_SESSION_STATUS_NOT_FOUND_VALUE == rpcResult.getData().getGameSessionStatus()) {
                return NKErrorCode.BattleEnd;
            }
            return NKErrorCode.OK;
        } catch (Exception ex) {
            LOGGER.error("checkBattleDSStatus fail", ex);
        }
        return NKErrorCode.BattleCheckDsStatusError;
    }

    public int playerSendExpression(SsBattlesvr.RpcBattleSendExpressionReq.Builder req) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfoByUid(req.getBallteId(), req.getPlayerId());
            if (null == battleInfo) {
                LOGGER.error("onReportGameDate battleInfo failed null == battle {}", req.getBallteId());
                return NKErrorCode.BattleNotExist.getValue();
            }
            try {
                return battleInfo.onSendExpressionData(req.build());
            } catch (BattleOperateRetryException e) {
                LOGGER.warn("onReportGameDate caught BattleOperateRetryException, battleId:{}, err:{}",
                        battleInfo.getBattleID(), e.getMessage());
                BattleDBMgr.getInstance().reloadBattleInfo(battleInfo);
            }
        }
        return NKErrorCode.BattleRetryTimeLimit.getValue();
    }

    public int battleDsLogCtrl(SsBattlesvr.RpcBattleDsLogCtrlReq.Builder req) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            BattleInfo battleInfo = getBattleInfoByUid(req.getBallteId(), req.getPlayerId());
            if (null == battleInfo) {
                LOGGER.error("battleDsLogCtrl battleInfo failed null == battle {}", req.getBallteId());
                return NKErrorCode.BattleNotExist.getValue();
            }
            G6Common.DSLogCtrlInfo.Builder dsLogCtrlInfoBuilder = G6Common.DSLogCtrlInfo.newBuilder();
            // ds log ctrl
            if (req.getPlayerId() != 0) {
                G6Common.DSLogCtrlPlayerInfo.Builder ctrlPlayerBuilder = G6Common.DSLogCtrlPlayerInfo.newBuilder()
                        .setPlayerUid(req.getPlayerId())
                        .setDsLogLevel(req.getDsLogLevel());
                dsLogCtrlInfoBuilder.addPlayerDsLogList(ctrlPlayerBuilder);
            } else {
                dsLogCtrlInfoBuilder.setGameDsLogLevel(req.getDsLogLevel());
            }
            SdCommonOuterClass.DsLogCtrlRequest.Builder dsLogCtrlReqBuilder =
                    SdCommonOuterClass.DsLogCtrlRequest.newBuilder()
                            .setDsId(battleInfo.getDsSessionId())
                            .setDsaInstId(battleInfo.getDsaInstanceID())
                            .setDsLogCtrlInfo(dsLogCtrlInfoBuilder);
            LOGGER.debug("battleDsLogCtrl battleId: {}, playerUid:{}, DsLogCtrl: {}",
                    req.getBallteId(), req.getPlayerId(), dsLogCtrlInfoBuilder);
            try {
                SdCommonService.get().irpcDSLogCtrl(dsLogCtrlReqBuilder.build());
            } catch (Exception ex) {
                LOGGER.error("ntfDsPlayerEnter DsLogCtrl fail", ex);
            }
        }
        return NKErrorCode.BattleRetryTimeLimit.getValue();
    }

    private List<DsRoomsvr.RandEvent> randEvent(BattleInfo battleInfo, int round, int levelID) {

        boolean randEventABTest = false;
        //随机事件实验一期
        MatchTypeABTestInfo matchTypeABTestInfo = battleInfo.getMatchTypeABTestInfoMap()
                .get(ABTestType.ABTT_RANDOM_EVENT_VALUE);
        if (matchTypeABTestInfo != null && matchTypeABTestInfo.getRandEventRoundList().contains(round)) {
            LOGGER.debug("BattleInfo randEvent randEventABTest enable, battleID:{} matchType:{} levelID:{} round:{} RandEventRound:{}",
                    battleInfo.getBattleID(), battleInfo.getMatchTypeCfg().getId(), levelID, round, matchTypeABTestInfo.getRandEventRoundList());
            randEventABTest = true;
        }

        //随机事件实验二期
        MatchTypeABTestInfo matchTypeABTestInfoV2 = battleInfo.getMatchTypeABTestInfoMap()
                .get(ABTestType.ABTT_RANDOM_EVENT_V2_VALUE);
        if (matchTypeABTestInfoV2 != null && matchTypeABTestInfoV2.getLevelRoundList().contains(round)) {
            LOGGER.debug("BattleInfo randEvent randEventABTestV2 enable, battleID:{} matchType:{} levelID:{} round:{} RandEventRound:{}",
                    battleInfo.getBattleID(), battleInfo.getMatchTypeCfg().getId(), levelID, round, matchTypeABTestInfoV2.getLevelRoundList());
            randEventABTest = true;
        }

        List<RandEventPair> resEvents = new ArrayList<>();
        RandEventMgr.RandResEnum randRes = RandEventMgr.getInstance().randEvent(
                                            battleInfo.getMatchTypeCfg(), levelID, resEvents, randEventABTest);
        if (RandResEnum.SUCCESS != randRes) {
            if (RandResEnum.NO_NEED_RAND != randRes) {
                LOGGER.error("randEvent failed {} {} {}", battleInfo.getBattleID(), levelID, randRes);
            }
            return Collections.emptyList();
        }
        List<DsRoomsvr.RandEvent> builderList = new ArrayList<>();
        for (var randEventPair : resEvents) {
            builderList.add(RandEvent.newBuilder().setEventID(
                                        randEventPair.id).setEventType(randEventPair.type.getNumber()).build());
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("randEvent battleID {} levelID {} builderList {}",
                                    battleInfo.getBattleID(), levelID, builderList);
        }
        return builderList;
    }

    public void onSaveDSPlayerCustomData(DsRoomsvr.SaveDSPlayerCustomDataRequest req) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("onSaveDSPlayerCustomData msg is {}", Pb2JsonUtil.getPbMsg(req));
        }

        BattleInfo battleInfo = getBattleInfoByUid(req.getBattleId(), req.getPlayerUid());
        if (null == battleInfo) {
            LOGGER.error("onSaveDSPlayerCustomData battleInfo is null battleid {}", req.getBattleId());
            return;
        }

        // 检查player存盘CD是否满足
        BattlePlayerInfo battlePlayer = battleInfo.getBattlePlayerMap().get(req.getPlayerUid());
        if (null == battlePlayer) {
            LOGGER.error("onSaveDSPlayerCustomData playerinfo not found battleid {} playerid {}", req.getBattleId(), req.getPlayerUid());
            return;
        }

        if (!battlePlayer.canSavePlayerCustomData()) {
            LOGGER.error("onSaveDSPlayerCustomData player save custom data cd");
            return;
        }

        // 存储DB数据
        try {
            BattleDBMgr.getInstance().saveDSPlayerCustomData(req.getBattleId(),
                    req.getPlayerUid(),
                    req.getCustomUid(),
                    req.getCustomData());
        }catch (Exception e) {
            LOGGER.error("failed save ds player custom data battleid {} playerid {} customid {}",
                    req.getBattleId(), req.getPlayerUid(), req.getCustomUid());
        }

        // 更新时间戳
        battlePlayer.updateLastSavePlayerCustomDataTime();
    }

    public GetNextLevelByRoundIndexReply.Builder onGetNextLevelByRoundIndex(GetNextLevelByRoundIndexRequest req) {
        GetNextLevelByRoundIndexReply.Builder resBuilder = GetNextLevelByRoundIndexReply.newBuilder();
        try {
            BattleInfo battleInfo = getBattleInfoBySessionId(req.getBattleId(), req.getDsSessionId());
            if (null == battleInfo) {
                LOGGER.error("onGetNextLevelByRoundIndex failed null == battle {}", req.getBattleId());
                return resBuilder.setResult(NKErrorCode.BattleNotExist.getValue());
            }

            ResHolder resHolder = ResLoader.getResHolder();

            NKPair<NKPair<Integer, Integer>, NKErrorCode> res = RoundLevelChooser.getInstance()
                    .choose(resHolder, battleInfo, req.getSurvivalPlayerNum(), req.getSurvivalPlayerUidList(),
                            req.getSurvivalSideNum(), req.getCurrentRound(), req.getHistoryLevelIdsList());

            if (!res.value.isOk()) {
                LOGGER.error("failed to get next level for {}: {}", req.getBattleId(), res.value);
                resBuilder.setResult(NKErrorCode.UnknownError.getValue());
                return resBuilder;
            }

            T_LevelInfoData data = LevelInfoData.getInLoadingInstance(resHolder).get(res.key.value);
            if (data == null) {
                LOGGER.error("failed to get next level cfg for {}: {}", req.getBattleId(), res);
                resBuilder.setResult(NKErrorCode.ResNotFound.getValue());
                return resBuilder;
            }

            resBuilder.setResult(NKErrorCode.OK.getValue());
            resBuilder.setNextRound(res.key.key);
            resBuilder.setNextLevelId(res.key.value);
//            resBuilder.setNextLevelData(BattleResMgr.BYTE_FORMAT.apply(data));
            if (randomEnableLevelSaveLocalAIFile(battleInfo, res.key.key)) {
                resBuilder.setSaveLocalAiFile(true);
                resBuilder.setSaveLocalAiFileDir(BSConfig.getSaveLocalAIFileDir());
            }

            // 如有 填充ailab指定的ai强度信息
            battleInfo.findMetaAiGivenAiInfoDifficulty(res.getKey().getValue()).ifPresent(v -> {
                DsRoomsvr.GetNextLevelMetaAiInfo.Builder builder = DsRoomsvr.GetNextLevelMetaAiInfo.newBuilder();
                builder.setAiInfoDifficulty(v);
                resBuilder.setLevelMetaAiInfo(builder);
            });

            List<DsRoomsvr.RandEvent> resRandEventList = randEvent(battleInfo, res.key.getKey(), res.key.getValue());
            if (!resRandEventList.isEmpty()) {
                for (var randEvent : resRandEventList) {
                    resBuilder.addRandEvent(randEvent);
                }
            }

            LOGGER.info("onGetNextLevelByRoundIndex, battleId:{} nextRound:{} nextLevelId:{}", req.getBattleId(),
                    res.key.key, res.key.value);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("onGetNextLevelByRoundIndex battleId:{} level_meta_ai_info {}",
                        req.getBattleId(), TextFormat.shortDebugString(resBuilder.getLevelMetaAiInfo()));
            }
            return resBuilder;
        } catch (Exception e) {
            LOGGER.error("failed to get next level for {}: {}", req.getBattleId(), e);
            resBuilder.setResult(NKErrorCode.UnknownError.getValue());
            return resBuilder;
        }
    }

    public GetResTableRowsReply.Builder onGetResTableRow(GetResTableRowsRequest request) {
        GetResTableRowsReply.Builder resBuilder = GetResTableRowsReply.newBuilder();

        List<NKPair<String, List<String>>> reqList = Lists.newArrayList();
        if (!request.getTable().isBlank() && !request.getKeysList().isEmpty()) {
            reqList.add(new NKPair<>(request.getTable(), request.getKeysList()));
        }

        for (ResTableKeys req : request.getExtraReqsList()) {
            if (!req.getTable().isBlank() && !req.getKeysList().isEmpty()) {
                reqList.add(new NKPair<>(req.getTable(), req.getKeysList()));
            }
        }

        LOGGER.info("start res table row, battle:{} reqList:{}", request.getBattleId(), reqList);
        NKErrorCode errorCode = NKErrorCode.OK;
        ResHolder resHolder = ResLoader.getResHolder();

        for (NKPair<String, List<String>> req : reqList) {
            try {
                NKPair<Map<String, ByteString>, NKErrorCode> res = BattleResMgr.getInstance()
                        .getResRowByKeyStrings(resHolder, req.key, req.value, BattleResMgr.BYTE_FORMAT);
                if (!res.value.isOk()) {
                    LOGGER.error("failed to get res rows, uid:{} table:{} keys:{} err:{}", request.getBattleId(),
                            req.key, req.value, res.value);
                    errorCode = res.value;
                    continue;
                }

                res.key.forEach((k, v) -> {
                    ResTableRow.Builder row = ResTableRow.newBuilder().setTable(req.key).setKey(k).setRow(v);
                    resBuilder.addRows(row);
                });
            } catch (Exception e) {
                LOGGER.error("failed to get res table row, uid:{} table:{} row:{} err:{}", request.getBattleId(),
                        req.key, req.value, e);
                errorCode = (e instanceof IEnumedException) ? (NKErrorCode) ((IEnumedException) e).getEnumErrCode()
                        : NKErrorCode.UnknownError;
            }
        }

        LOGGER.info("finish res table row, battle:{} reqList:{} res:{}", request.getBattleId(), reqList, errorCode);
        resBuilder.setResult(errorCode.getValue());
        return resBuilder;
    }

    private void handleDsSecTlog(BattleInfo battleInfo, BattlePlayerInfo battlePlayerInfo, int tlogType,
            String rawData) {
        if (!BSConfig.getEnableSecTlog()) {
            return;
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("playerUid: {} tlogType: {} rawData: {}", battlePlayerInfo.getUid(), tlogType, rawData);
        }
        SsGamesvr.RpcReportTlogDataNtfReq.Builder builder = SsGamesvr.RpcReportTlogDataNtfReq.newBuilder();
        builder.setPlayerId(battlePlayerInfo.getUid())
                .setBattleId(battleInfo.getBattleID())
                .setTlogType(tlogType)
                .setData(rawData);
        try {
            if (tlogType == BattleDsTlogType.BDTT_SEC_ROUND_START_FLOW_VALUE) {
                Map<Integer, Set<BattlePlayerInfo>> sideMap = battleInfo.getSidePlayerMap();
                SsGamesvr.RoundStartData.Builder startDataBuilder = builder.getRoundStartDataBuilder();
                final Set<BattlePlayerInfo> teammates = sideMap != null ? sideMap.get(battlePlayerInfo.getSide()) : null;
                final int maxTeammateNum = 3;
                if (teammates != null) {
                    List<SsGamesvr.RoundStartTeammateData.Builder> teammateBuilderList = new ArrayList<>();
                    teammateBuilderList.add(startDataBuilder.getTeammate1Builder());
                    teammateBuilderList.add(startDataBuilder.getTeammate2Builder());
                    teammateBuilderList.add(startDataBuilder.getTeammate3Builder());
                    int idx = 0;
                    for (BattlePlayerInfo teammate : teammates) {
                        if (teammate.getUid() == battlePlayerInfo.getUid()) {   // 这里先保持不变，机器人队友也填
                            continue;
                        }
                        if (idx < maxTeammateNum) {
                            var teammateBuilder = teammateBuilderList.get(idx);
                            teammateBuilder.setOpenid(teammate.getOpenId())
                                    .setTeammateType(teammate.getRoomId() == battlePlayerInfo.getRoomId() ? 1 : 2)
                                    .setUid(teammate.getUid());
                        } else {
                            startDataBuilder.addOtherPlayers(SsGamesvr.RoundStartTeammateData.newBuilder()
                                    .setOpenid(teammate.getOpenId())
                                    .setTeammateType(teammate.getRoomId() == battlePlayerInfo.getRoomId() ? 1 : 2)
                                    .setUid(teammate.getUid()));
                        }
                        ++idx;
                    }
                }
                Map<Long, BattlePlayerInfo> playerInfoMap = battleInfo.getBattlePlayerMap();
                if (playerInfoMap != null) {
                    for (BattlePlayerInfo playerInfo : playerInfoMap.values()) {
                        if (playerInfo.isRobot() ||
                                playerInfo.getUid() == battlePlayerInfo.getUid() ||
                                (teammates != null && teammates.contains(playerInfo))) {
                            continue;
                        }
                        startDataBuilder.addOtherPlayers(SsGamesvr.RoundStartTeammateData.newBuilder()
                                .setOpenid(playerInfo.getOpenId())
                                .setTeammateType(0) // 非队友
                                .setUid(playerInfo.getUid()));
                    }
                }
                builder.setRoundStartData(startDataBuilder);
            }

            GameService.get().rpcReportTlogDataNtf(builder);
        } catch (Exception e) {
            LOGGER.error("handleDsSecTlog failed, battleId {} playerUid {} tlogType {} content {}",
                    battleInfo.getBattleID(), battlePlayerInfo.getUid(), tlogType, rawData, e);
        }
    }

    public int onSendBattleTlog(DsRoomsvr.SendBattleTlogRequest request) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("SendBattleTlogRequest msg is {}", Pb2JsonUtil.getPbMsg(request));
        }
        BattleInfo battleInfo = null;
        if (request.getDsSessionId() == 0L && request.getUuid() != 0L) {
            battleInfo = getBattleInfoByUid(request.getBattleId(), request.getUuid());
        } else {
            battleInfo = getBattleInfoBySessionId(request.getBattleId(), request.getDsSessionId());
        }
        if (null == battleInfo) {
            LOGGER.error("onSendBattleTlog failed null == battle {}", request.getBattleId());
            return NKErrorCode.BattleNotExist.getValue();
        }
        BattlePlayerInfo battlePlayerInfo = battleInfo.getBattlePlayerInfo(request.getUuid());
        if (battlePlayerInfo != null) {
            var tlogFunction = LetsGoDsTlogTypes.getTlogFunction(request.getTlogType());
            if (tlogFunction != null) {
                tlogFunction.apply(battlePlayerInfo.getMemberBaseInfo(false), request.getTlogContent());
                if (request.getTlogType() == BattleDsTlogType.BDTT_TYC_ROUND_DETAIL_FLOW_VALUE) {
                    BattleStatisticsMgr.getInstance()
                            .tycPlayerSettlementAddMonitor(battlePlayerInfo.getMemberBaseInfo(false),
                                    request.getTlogContent());
                }else if (request.getTlogType() == BattleDsTlogType.BDTT_TYCTDS_ROUND_DETAIL_FLOW_VALUE) {
                    BattleStatisticsMgr.getInstance()
                            .tycTDSPlayerSettlementAddMonitor(battlePlayerInfo.getMemberBaseInfo(false),
                                    request.getTlogContent());
                } else if (request.getTlogType() == BattleDsTlogType.BDTT_OMD_ROUND_DETAIL_FLOW_VALUE) {
                    BattleStatisticsMgr.getInstance()
                            .tycOMDPlayerSettlementAddMonitor(battlePlayerInfo.getMemberBaseInfo(false),
                                    request.getTlogContent());
                }
            } else {
                // 包含机器人
                switch (request.getTlogType()) {
                    case BattleDsTlogType.BDTT_SEC_ROUND_START_FLOW_VALUE:
                    case BattleDsTlogType.BDTT_SEC_ROUND_END_FLOW_VALUE:
                    case BattleDsTlogType.BDTT_SEC_ITEM_GET_FLOW_VALUE:
                    case BattleDsTlogType.BDTT_SEC_ROUND_DETAIL_VALUE:
                    case BattleDsTlogType.BDTT_SEC_GAME_SAFE_DATA_VALUE:
                    case BattleDsTlogType.BDTT_SEC_VERIFY_FLOW_VALUE:
                    case BattleDsTlogType.BDTT_SEC_ROUND_DETAIL_START_VALUE:
                    case BattleDsTlogType.BDTT_SEC_CANYON_EAT_CHICKEN_ROUND_DETAIL_FLOW_VALUE:
                    case BattleDsTlogType.BDTT_SEC_CANYON_3V3_ROUND_DETAIL_FLOW_VALUE:
                    case BattleDsTlogType.BDTT_SEC_AFK_FLOW_VALUE:
                    case BattleDsTlogType.BDTT_SEC_CANYON_3V3OP_ROUND_DETAIL_FLOW_VALUE:
                        handleDsSecTlog(battleInfo, battlePlayerInfo, request.getTlogType(), request.getTlogContent());
                        break;
                    default:
                        LOGGER.warn("onSendBattleTlog invalid type player: {},{},{},{}",
                                request.getBattleId(), request.getUuid(), request.getTlogType(), request.getTlogContent());
                        return NKErrorCode.InvalidParams.getValue();
                }
            }
            String[] monitorParams = new String[]{
                    BattleDsTlogType.forNumber(request.getTlogType()).name()
            };
            Monitor.getInstance().add.succ(MonitorId.attr_battlesvr_tlog_ds_flow, 1, monitorParams);
        } else {
            // 有部分类型日志 不需要uid
            var tlogFunction = LetsGoDsTlogTypes.getTlogFunction(request.getTlogType());
            if (tlogFunction != null) {
                switch (request.getTlogType()) {
                    case BattleDsTlogType.BDTT_UGC_CODING_FLOW_VALUE:
                        tlogFunction.apply(MemberBaseInfo.newBuilder() , request.getTlogContent());
                        break;
                    case BattleDsTlogType.BDTT_DS_PERFORMANCE_FLOW_VALUE:
                        tlogFunction.apply(MemberBaseInfo.newBuilder() , request.getTlogContent());
                        break;
                    case BattleDsTlogType.BDTT_UGC_SCRIPT_FLOW_VALUE:
                        tlogFunction.apply(MemberBaseInfo.newBuilder() , request.getTlogContent());
                        break;
                }
            }

            LOGGER.warn("onSendBattleTlog invalid player: {},{},{},{}",
                    request.getBattleId(), request.getUuid(), request.getTlogType(), request.getTlogContent());
        }
        return NKErrorCode.OK.getValue();
    }

    /**
     * 将战场添加到匹配池，支持中途加入。可重入
     *
     * @param battleInfo
     */
    public void battleJoinMatchPoolOnJoinMidwayMode(BattleInfo battleInfo) {
        // 必须这种玩法模式开启了中途加入选项
        if (battleCanJoinMidway(battleInfo)) {
            battleInfo.createMatchInfoIfAbsent();
        }

        // 如果开启了匹配池，在用匹配池
        if (battlePriorityDsJoin(battleInfo)) {

            //加入匹配池
            if (matchBattleMgr.addBattleInfo(battleInfo)) {
                Set<Long> uids = battleInfo.getBattlePlayerMap().keySet();
                // 加入redis
                BattlePublicDao.batchSetPlayPublicBattleInfo(battleInfo, uids);
            }

            LOGGER.info("teamJoinBattle log, battleJoinMatchPoolOnJoinMidwayMode battle {} ", battleInfo.getBattleID());
        }
    }

    public void removePlayerInRoom(BattleInfo battleInfo, long uid, boolean kickPlayer, boolean removeTeam, boolean leaveRoom, int quitCode) {
        LOGGER.info("removePlayerInRoom battleId:{} uid:{} kickPlayer:{} removeTeam:{} leaveRoom:{} quitCode:{}", 
                battleInfo.getBattleID(), uid, kickPlayer, removeTeam, leaveRoom, quitCode);

        if (leaveRoom) {
            // 先离开房间
            battleInfo.playerLeaveRoom(uid);
        }

        // 移除玩家
        battleInfo.removeBattlePlayer(uid, removeTeam);

        // 发退出请求到ds
        if (kickPlayer) {
            // 这边需要发，roomsvr那边因为拦截而不会向battle发玩家退出
            tryDoPlayerExitDs(battleInfo, uid, quitCode);
        }

        if (battlePriorityDsJoin(battleInfo)) {
            // 删除redis
            BattlePublicDao.delPlayPublicBattleInfoFromRedis(uid);
        }
    }

    public void playerLeaveUgcCustomRoom(BattleInfo battleInfo, long uid) {
        battleInfo.playerLeaveRoom(uid);
    }
    
    private List<Long> removePlayerInRoom(BattleInfo battleInfo, List<Long> uids, BattleQuitDeleteEnum deleteEnum,
            boolean kickPlayer, boolean leaveRoom) {
        List<Long> removes = new ArrayList<>();
        for (long uid : uids) {
            BattlePlayerInfo battlePlayerInfo = battleInfo.getBattlePlayerInfo(uid);
            // 如果是多场景 结算后就让玩家离开
            if (battlePlayerInfo != null && (battlePlayerInfo.tryDelete(deleteEnum) || battleInfo.battleWithScene())) {
                removePlayerInRoom(battleInfo, uid, kickPlayer, false, leaveRoom, QuitBattleCode.QUIT_BATTLE_CODE_QUIT_WHEN_ENABLE_MID_JOIN_VALUE);
                removes.add(uid);
            }
        }
        return removes;
    }

    public void removePlayerInRoomAndSyncDs(BattleInfo battleInfo, Set<Long> uids) {
        if (!uids.isEmpty()) {
            for (long uid : uids) {
                removePlayerInRoom(battleInfo, uid, true, true, false, QuitBattleCode.QUIT_BATTLE_CODE_QUIT_WHEN_ENABLE_MID_JOIN_VALUE);
            }
            battleInfo.trySyncBattlePlayerInfo();
        }
    }

    public void removePlayerSceneChange(BattleInfo battleInfo, Map<Long, Integer> kickPlayerMap) {
        for (var entry : kickPlayerMap.entrySet()) {
            long uid = entry.getKey();
            BattlePlayerInfo battlePlayerInfo = battleInfo.getBattlePlayerInfo(uid);
            if (battlePlayerInfo != null) {
                int quitCode = entry.getValue();
                removePlayerInRoom(battleInfo, uid, true, true, false, quitCode);
            }
        }
    }

    public void removePlayerQuitBattle(BattleInfo battleInfo, long uid) {
        removePlayerInRoom(battleInfo, uid, true, true, false, QuitBattleCode.QUIT_BATTLE_CODE_LEAVE_VALUE);
    }
    public void removePlayerOfflineTooLong(BattleInfo battleInfo, long uid) {
        // 先离开房间
        battleInfo.playerLeaveRoom(uid);
    
        tryDoPlayerExitDs(battleInfo, uid, QuitBattleCode.QUIT_BATTLE_CODE_OFFLINE_KICK_VALUE); //主动退出DS

    }
    /**
     * 退出战场
     *
     * @param battleInfo
     * @param uids
     */
    public void quitBattleOnJoinMidwayMode(BattleInfo battleInfo, List<Long> uids, BattleQuitDeleteEnum deleteEnum) {
        if (battleInfo == null || uids == null || uids.isEmpty()) {
            return;
        }

        // 必须这种玩法模式开启了中途加入选项
        if (battleCanJoinMidway(battleInfo)) {
            int matchTypeId = battleInfo.getMatchRuleInfo().getMatchTypeId();
            List<Long> removes = removePlayerInRoom(battleInfo, uids, deleteEnum, false,
                    TycConfs.isTYCGame(matchTypeId) ||
                            (TycConfs.isOMDGame(matchTypeId) && battleInfo.isComCustom(uids.get(0))));

            if (!removes.isEmpty()) {
                matchBattleMgr.addBattleInfo(battleInfo);
                try {
                    battleInfo.updateMembersToDb(dsAuthSecret);
                } catch (BattleOperateRetryException e) {
                    LOGGER.error("teamJoinBattle log, quitBattleOnJoinMidwayMode battle {} updateMembersToDb failed",
                            battleInfo.getBattleID(), e);
                }
            }

            LOGGER.info(
                    "teamJoinBattle log, quitBattleOnJoinMidwayMode battle {} deleteEnum: {}  uids: {} remove uid: {} ",
                    battleInfo.getBattleID(), deleteEnum, uids, removes);

        }
    }

    public void removePlayerWithEndBattle(BattleInfo battleInfo, List<Long> uids, BattleQuitDeleteEnum deleteEnum) {
        if (battleInfo == null || uids == null || uids.isEmpty()) {
            return;
        }

        // 允许结束后把玩家移除
        if (BattleMidJoinHelper.battleEndNeedRemovePlayer(battleInfo)) {
            List<Long> removes = removePlayerInRoom(battleInfo, uids, deleteEnum, true, false);
            if (!removes.isEmpty()) {
                // 如果是多场景 就尝试将玩家移除掉
                var battleSceneInfo = battleInfo.getBattleSceneInfo();
                if (battleSceneInfo != null) {
                    battleSceneInfo.removeSceneMemberUpdateDb(battleInfo, removes);
                    battleSceneInfo.removeSceneMemberAfterCheck(battleInfo);
                }
            }
            LOGGER.info(
                    "teamJoinBattle log, deletePlayerWithEndBattle battle {} deleteEnum: {}  uids: {} remove uid: {} ",
                    battleInfo.getBattleID(), deleteEnum, uids, removes);
        } else if(deleteEnum == BattleQuitDeleteEnum.BQDE_Quit_Battle && battleInfo.isUgcMatch()) {
            // 合计匹配的需要先退出房间去
            for (long uid : uids) {
                if (battleInfo.isUgcMatchWithCustomRoom(uid)) {
                    LOGGER.info("player:{} quit battle:{} ugc match with custom room", uid, battleInfo.getBattleID());
                    playerLeaveUgcCustomRoom(battleInfo, uid);
                }
            }
        }
    }

    public void endBattleOnJoinMidwayMode(BattleInfo battleInfo) {
        if (battlePriorityDsJoin(battleInfo)) {
            Set<Long> uids = battleInfo.getBattlePlayerMap().keySet();
            // 移除
            matchBattleMgr.removeBattleInfo(battleInfo);
            //移除redis
            uids.forEach(BattlePublicDao::delPlayPublicBattleInfoFromRedis);

            LOGGER.info("teamJoinBattle log, endBattleOnJoinMidwayMode battle {} ", battleInfo.getBattleID());

        }
    }

    public void endBattleDisbandRoomOnJoinMidwayMode(BattleInfo battleInfo) {
        if (battleCanJoinMidway(battleInfo)) {

            if (TycConfs.isTYCGame(battleInfo.getMatchRuleInfo().getMatchTypeId())) {
                // 解散房间
                battleInfo.disbandAllRoom();
            }
            Monitor.getInstance().avg.succ(MonitorId.attr_battlesvr_midJoin_avg_time,
                    (Framework.currentTimeMillis() - battleInfo.getCreateTime()) / 1000.0);

            LOGGER.info("teamJoinBattle log, endBattleDisbandRoomOnJoinMidwayMode battle {} ",
                    battleInfo.getBattleID());
        }
    }

    public boolean registerGameSessionId(long gameSessionId, long battleId) {
        Long preVal = sessionIdToBattleIdMap.putIfAbsent(gameSessionId, battleId);
        if (preVal != null) {
            LOGGER.error("Has register gamesessionId:{}, battleId:{}, newGameSessionId:{}",
                    preVal, battleId, gameSessionId);
            return false;
        }
        return true;
    }

    /**
     * TODO devinyou
     *
     * @param gameSessionId
     * @return
     */
    public boolean unregisterGameSession(long gameSessionId, long battleId) {
        Long preBattleId = sessionIdToBattleIdMap.remove(gameSessionId);
        Long preSessionId = battleIdToSessionIdMap.remove(battleId);
        LOGGER.info("Success to unregisterGameSession, gameSessionId:{},{}, battleId:{}, {}",
                gameSessionId, preSessionId, battleId, preBattleId);
        return preBattleId != null;
    }

    public long getBattleIdBySessionId(long gameSessionId) {
        Long battleId = sessionIdToBattleIdMap.get(gameSessionId);
        return battleId == null ? gameSessionId : battleId;
    }

    public boolean registerBattleIdToSessionId(long gameSessionId, long battleId) {
        Long preVal = battleIdToSessionIdMap.put(battleId, gameSessionId);
        if (preVal != null) {
            LOGGER.error("Has register registerBattleIdToSessionId:{}, battleId:{}, newGameSessionId:{}",
                    preVal, battleId, gameSessionId);
        }
        return true;
    }


    public long getSessionIdByBattleId(long battleId) {
        Long sessionId = battleIdToSessionIdMap.get(battleId);
        return sessionId == null ? battleId : sessionId;
    }

    public SsBattlesvr.RpcJoinBattleRes.Builder searchDsJoin(SsBattlesvr.RpcJoinBattleReq.Builder req) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("MatchBattleMgr log, teamJoinBattle start room: {} match: {} ruleDime: {}",
                    req.getTeamData().getRoomID(), req.getTeamData().getMatchType(),
                    req.getTeamData().getRuleDimInfosMap());
        }

        if (req.getTeamData().getMemberInfosMap().isEmpty()) {
            return matchBattleMgr.createFailedBattleRes();
        }

        if (matchBattleMgr.openGmJoin()) {
            long gmBattle = matchBattleMgr.getGmBattle(req);
            //如果单纯是localds方式，直接返回
            if (gmBattle < 0) {
                return matchBattleMgr.createFailedBattleRes();
            }

            BattleInfo gmBattleInfo = getBattleInfo(gmBattle);
            //如果gm指定的battle，则加入
            if (gmBattleInfo != null) {
                LOGGER.debug("MatchBattleMgr log, gmBattle start uid: {} battle: {} ",
                        req.getTeamData().getMemberInfosMap().keySet(), gmBattleInfo.getBattleID());
                return updateBattleMatchAfterTeamJoin(gmBattleInfo, req.getTeamData());
            }
        }

        // 先考虑指定的id
        for (long battleId : req.getBattleIdsList()) {
            BattleInfo battle = getBattleInfo(battleId);
            if (null == battle) {
                continue;
            }

            if (matchBattleMgr.canJoinBattle(battle, req)) {
                return updateBattleMatchAfterTeamJoin(battle, req.getTeamData());
            }
        }

        // 如果可以加检查其他的战场，检查其他战场
        if (req.getCheckOther()) {
            BattleInfo battle = matchBattleMgr.getCanJoinBattle(req);
            if (battle != null) {
                Monitor.getInstance().add.succ(MonitorId.attr_battlesvr_midJoin_search_num, 1);
                return updateBattleMatchAfterTeamJoin(battle, req.getTeamData());
            } else {
                Monitor.getInstance().add.fail(MonitorId.attr_battlesvr_midJoin_search_num, 1);
            }
        }

        return SsBattlesvr.RpcJoinBattleRes.newBuilder()
                .setResult(NKErrorCode.BattleJoinBattleSearchFail.getValue())
                .setErrMsg("search failed");
    }


    /**
     * 中途加入后更新匹配池信息
     * @param battle 指定的battle
     * @param teamData
     * @return
     */
    public SsBattlesvr.RpcJoinBattleRes.Builder updateBattleMatchAfterTeamJoin(BattleInfo battle,
            TeamData teamData) {
        SsBattlesvr.RpcJoinBattleRes.Builder resBuilder = SsBattlesvr.RpcJoinBattleRes.newBuilder();
        NKPair<Integer, String> joinResult = teamJoinBattle(battle, teamData);
        if (joinResult.getKey() == NKErrorCode.OK.getValue()) {
            List<Long> uids = new ArrayList<>(teamData.getMemberInfosMap().keySet());
            matchBattleMgr.updateBattleMatchInfoAfterTeamJoin(battle, uids);
        }

        resBuilder.setResult(joinResult.getKey()).setErrMsg(joinResult.getValue());

        return resBuilder;
    }


    /**
     * teamData检查人数和battle状态是否可以加入
     * @param battle   指定的battle
     * @param teamData
     * @return
     */
    public NKPair<Integer, String> teamCheckAndJoinBattle(BattleInfo battle, TeamData teamData, MatchFillBackType fillBackType, BattleMidJoinExtraInfo extraInfo) {
        int res = matchBattleMgr.canJoinBattleOnFillBack(battle, teamData, fillBackType, extraInfo);
        if (res == NKErrorCode.OK.getValue()) {
            Monitor.getInstance().add.succ(MonitorId.attr_battlesvr_midJoin_check_num, 1,
                    new String[]{String.valueOf(battle.getMatchRuleInfo().getMatchTypeId())});
            return teamJoinBattle(battle, teamData);
        } else {
            Monitor.getInstance().add.fail(MonitorId.attr_battlesvr_midJoin_check_num, 1,
                    new String[]{String.valueOf(battle.getMatchRuleInfo().getMatchTypeId())});
        }

        return new NKPair<>(res, "check failed");
    }

    /**
     * 请求加入到battle里，
     * @param battle   指定的battle
     * @param teamData
     * @return
     */
    public NKPair<Integer, String> teamJoinBattle(BattleInfo battle, TeamData teamData) {
        for (int retried = 0; retried <= battleOperateRetryTimes; retried++) {
            List<BattlePlayerInfo> reCampPlay;
            try {
                // 重新分配阵营
                reCampPlay = matchBattleMgr.reCamp(battle, teamData);
                if (reCampPlay == null) {
                    return new NKPair<>(NKErrorCode.BattleJoinBattleFail.getValue(), "join failed");
                }

                NKPair<Integer, String> joinResult = battle.teamJoin(this.dsAuthSecret, teamData, reCampPlay);
                if (joinResult.getKey() != NKErrorCode.OK.getValue()) {
                    LOGGER.error("teamJoinBattle log, team: {} battle: {} failed, key: {} value: {}",
                            teamData, battle.getBattleID(), joinResult.getKey(), joinResult.getValue());
                } else {
                    try {
                        //播报涉及到roomsvr的rpc，有可能导致死锁，需要异步
                        CurrentExecutorUtil.runJob(() -> {
                            matchBattleMgr.teamJoinBattleSucc(dsAuthSecret, teamData, battle, reCampPlay, false, true);
                            return null;
                        }, "teamJoinBattleSucc", false);
                    } catch (Exception e){
                        LOGGER.error("teamJoinBattle log, teamJoinBattleSucc error, battle:{} ", battle.getBattleID(), e);
                    }
                }

                try {
                    StringBuilder sb = new StringBuilder("teamJoinBattle:");
                    for (BattlePlayerInfo battlePlayerInfo : reCampPlay) {
                        sb.append(battlePlayerInfo.getUid()).append(",");
                    }
                    sendTycDsBattleTlog(battle, TlogMacros.TYCDSFlOWTYPE.TDFT_MID_JOIN, "teamJoinBattle:" + sb);
                } catch (Throwable t) {
                    LOGGER.error("Error in sendTycDsFlow: ", t);
                }
                return joinResult;
            } catch (BattleOperateRetryException e) {
                LOGGER.warn("teamJoinBattle log, caught BattleOperateRetryException, battleId:{}, err:{}",
                        battle.getBattleID(), e.getMessage());
            }
        }

        return new NKPair<>(NKErrorCode.BattleJoinBattleFail.getValue(), "join failed");
    }


    private void generateRobot(int robotCnt, List<MemberBaseInfo> robotList) {
        for (int index = 0; index < robotCnt; ++index) {
            MemberBaseInfo.Builder robotMemberBaseInfoBuiler = MemberBaseInfo.newBuilder();
            int side = 0;
            robotMemberBaseInfoBuiler.setSide(0);
            robotMemberBaseInfoBuiler.setRoomID(-1000 - side);
            robotMemberBaseInfoBuiler.setUid(RobotUtil.generateRobotUid(side, index));
            robotMemberBaseInfoBuiler.setIsRobot(true);
            robotList.add(robotMemberBaseInfoBuiler.build());
        }
    }

    public RpcResult<SsBattlesvr.RpcDirectorJoinBattleRes.Builder>
    directorJoinBattle(SsBattlesvr.RpcDirectorJoinBattleReq.Builder req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (null == battleInfo) {
            LOGGER.error("directorJoinBattle battleInfo failed null == battle {}", req.getBattleId());
            return RpcResult.create(NKErrorCode.BattleNotExist.getValue(),
                    SsBattlesvr.RpcDirectorJoinBattleRes.newBuilder());
        }

        try {
            //限制4个导播
            int directorCnt = 0;
            for (Map.Entry<Long, BattlePlayerInfo> entry : battleInfo.getBattlePlayerMap().entrySet()) {
                Long uid = entry.getKey();
                BattlePlayerInfo playerInfo = entry.getValue();

//                LOGGER.debug("directorJoinBattle battleId:{},  uid:{}, playerInfo {}", battleInfo.getBattleID(), uid,
//                        playerInfo);

                // 导播
                if (playerInfo.getRobotType() == -1) {
                    LOGGER.debug("directorJoinBattle battleId:{}, uid:{}, isDirector", battleInfo.getBattleID(), uid);
                    directorCnt++;
                }
            }

            int maxDirectorCnt = 4;
            if (req.getMaxDirectorCnt() > 0) {
                maxDirectorCnt = req.getMaxDirectorCnt();
            }
            LOGGER.debug("directorJoinBattle maxDirectorCnt:{}", maxDirectorCnt);

            if (directorCnt >= maxDirectorCnt) {
                return RpcResult.create(NKErrorCode.BattleJoinBattlePlayNumIsFull.getValue(),
                        SsBattlesvr.RpcDirectorJoinBattleRes.newBuilder());
            }

            SsBattlesvr.RpcGetBattleInfoRes.Builder roomBattleInfo = battleInfo.getRoomBattleInfo();
            String dsAddr = roomBattleInfo.getDsAddr();

            String desModInfo = roomBattleInfo.getDesGameInfo();
            int matchType = battleInfo.getMatchRuleInfo().getMatchTypeId();

            SdCompetitionOuterClass.DirectorJoinRoomRequest.Builder reqSdDirectorJoinRoom = SdCompetitionOuterClass.
                    DirectorJoinRoomRequest.newBuilder().setDsId(req.getBattleId())
                    .setDsaInstId(battleInfo.getDsaInstanceID()).addAllUuids(req.getUuidsList());

            // 增加对应导播的关注列表
            for (SsBattlesvr.DirectorFollowPlayerInfo directorInfo : req.getDirectorUuidsList()) {
                SdCompetitionOuterClass.DirectorFollowPlayerInfo.Builder sdDirectorInfo = SdCompetitionOuterClass.DirectorFollowPlayerInfo.newBuilder().setUuids(directorInfo.getUuids());
                for (long followUid : directorInfo.getFollowPlayerUuidList()) {
                    sdDirectorInfo.addFollowPlayerUuid(followUid);
                }
                reqSdDirectorJoinRoom.addDirectorUuids(sdDirectorInfo);

            }

            // 处理每个导播
            for (long uuid : req.getUuidsList()) {
                try {
                    MemberBaseInfo.Builder memberBaseInfo = MemberBaseInfo.newBuilder()
                            .setUid(uuid)
                            .setRobotType(-1);
                    BattlePlayerInfo battlePlayerInfo = new BattlePlayerInfo(battleInfo, memberBaseInfo.build());
                    battleInfo.addBattlePlayer(battlePlayerInfo);

                    // 加入聊天频道
                    List<BattlePlayerInfo> joinPlayers = new ArrayList<>();
                    joinPlayers.add(battlePlayerInfo);
                    battleInfo.addCampPlayToChatGroup(joinPlayers, false);

                    // 同时发送到gamesvr，以便断网重连进这局游戏
                    SsGamesvr.RpcObserverDsInfoNtfReq.Builder obReq = SsGamesvr.RpcObserverDsInfoNtfReq.newBuilder();
                    obReq.setUid(uuid).setDesModInfo(desModInfo).setDsAddr(dsAddr).setMatchType(matchType).setBattleId(req.getBattleId());
                    GameService service = GameService.get();
                    service.rpcObserverDsInfoNtf(obReq);
                }catch (Exception e1){
                    LOGGER.error("rpcObserverDsInfoNtf fail", e1);
                }
            }

            SdCompetitionService.get()
                    .irpcDirectorJoinRoom(reqSdDirectorJoinRoom.build());



            return RpcResult.create(NKErrorCode.OK.getValue(),
                    SsBattlesvr.RpcDirectorJoinBattleRes.newBuilder().setResult(0).setDesModInfo(desModInfo).
                            setMatchType(matchType).setDsAddr(dsAddr));

        } catch (Exception ex) {
            LOGGER.error("rpcDirectorJoinBattle fail", ex);
        }

        return RpcResult.create(NKErrorCode.BattleJoinBattleFail.getValue());


    }

    public void executeOperateOnAllDs(long uid, int matchType, String key, String value) {
        for (BattleInfo battleInfo : battleMap.values()) {
            if (battleInfo.getMatchRuleInfo().getMatchTypeId() == matchType) {
                executeOperateOnDs(uid, battleInfo.getDsaInstanceID(), battleInfo.getDsSessionId(), key, value);
            }
        }
    }

    public int executeOperateOnDs(long uid, long dsInstanceId, long dsGameSessionId, String key, String value) {
        try {
            SdBattleOuterClass.ExecuteOperateOnDsRequest.Builder builder = SdBattleOuterClass.ExecuteOperateOnDsRequest.newBuilder()
                    .setDsSessionId(dsGameSessionId)
                    .setDsaInstId(dsInstanceId)
                    .setKey(key)
                    .setValue(value)
                    .setUid(uid);
            RpcResult<SdBattleOuterClass.ExecuteOperateOnDsReply.Builder> resBuilder = SdBattleService.get()
                    .irpcExecuteOperateOnDs(builder.build());

            if (LOGGER.isDebugEnabled()) {
                LOGGER.info(
                        "executeOperateOnDs log, end: req: dsInstanceId: {} dsGameSessionId: {} key: {} value: {} res: {} ",
                        dsInstanceId, dsGameSessionId, key, value, resBuilder.getRet());
            }

            return resBuilder.getRet();
        } catch (Exception ex) {
            LOGGER.error("executeOperateOnDs log, fail", ex);
        }

        return NKErrorCode.UnknownError.getValue();
    }


    public int searchOtherPlayerJoin(DsRoomsvr.SearchOtherPlayerJoinRequest req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist.getValue();
        }

        try {
            CurrentExecutorUtil.runJob(() -> {
                int num = req.getSearchNum();
                // 先计算需要的人数
                ResMatch.MatchRoomInfo roomInfoCfg = battleInfo.getMatchRoomInfo();
                int maxNum = 0;

                for (ResMatch.MatchSideInfo matchSideInfo : ConfigProxyMgr.getInstance().
                                    getPlayerSideList(battleInfo.getDynamicConfigData(), roomInfoCfg)) {
                    maxNum = matchSideInfo.getTeamPlayers();
                }
                int curNum = battleInfo.getBattlePlayerMap().size();
                int fillNum = Math.min(maxNum - curNum, num);
                if (fillNum <= 0) {
                    LOGGER.error("doSearchOtherPlayerJoin log,battle: {} uid: {} maxNum: {} {} {} num zero",
                            battleInfo.getBattleID(), req.getUid(), maxNum, curNum, num);
                    return null;
                }

                // 先取消招募
                battleInfo.cancelRecruit();
                List<MatchFillBackSideInfo> sideInfos = new ArrayList<>();
                MatchFillBackSideInfo sideInfo = MatchFillBackSideInfo.newBuilder()
                        .setSide(1)
                        .setCnt(fillNum)
                        .build();
                sideInfos.add(sideInfo);
                SsMatchsvr.RpcMatchRes.Builder res = doFillBackOtherPlayer(battleInfo, req.getUid(), fillNum, sideInfos, MatchFillBackType.MFT_OMD_Mid_Join);
                if (res != null && res.getResult() == NKErrorCode.OK.getValue()) {
                    battleInfo.setMatchSvrId(res.getSvrId());
                    battleInfo.ntfSearchPlayerJoinResult(null, true, 1);
                    LOGGER.info("searchOtherPlayerJoin log, battle: {} uid: {} sucess, svrId: {} ",
                            battleInfo.getBattleID(), req.getUid(), res.getSvrId());
                }
                return null;
            }, "doFillBackOtherPlayer", false);
        } catch (Exception t) {
            LOGGER.error("doSearchOtherPlayerJoin log: error ", t);
        }

        return NKErrorCode.OK.getValue();
    }

    /**
     * 请求到matchsvr做回填
     *
     * @param battleInfo
     * @param uid
     * @param sideInfos,
     * @param fillBackType
     */
    public SsMatchsvr.RpcMatchRes.Builder doFillBackOtherPlayer(BattleInfo battleInfo, long uid, int num,List<MatchFillBackSideInfo> sideInfos, MatchFillBackType fillBackType) {
        try {
            // 发到matchsbvr 匹配
            SsMatchsvr.RpcMatchReq.Builder reqBuilder = SsMatchsvr.RpcMatchReq.newBuilder()
                    .setRoomId(battleInfo.getBattleID())
                    .setMatchOperateType(MatchOperateType.MOT_FillBack);
            MatchRuleInfo.Builder matchRuleInfo = battleInfo.getMatchRuleInfoBuilder();
            MatchFillBackData.Builder fillBack = MatchFillBackData.newBuilder()
                    .setUuID(battleInfo.getBattleID())
                    .setUid(uid)
                    .setFillBackCnt(num)
                    .setReqTime(Framework.currentTimeMillis())
                    .setMatchType(matchRuleInfo.getMatchTypeId())
                    .setRuleId(matchRuleInfo.getRuleId())
                    .setRoomInfoId(matchRuleInfo.getRoomInfoID())
                    .setSrcServerTypeInt(WeAServerType.ST_BattleServer.getNumber())
                    // 兼容性
                    .setSrcServerType(ServerType.ST_BattleServer)
                    .setFillBackType(fillBackType)
                    .addAllSideInfo(sideInfos);
            matchRuleInfo.getDimensionListList().forEach(o -> fillBack.putRuleDimInfos(o.getId(), o.getValue()));
            reqBuilder.setMatchFillBackData(fillBack);

            // 回填
            if (TycConfs.isOMDGame(matchRuleInfo.getMatchTypeId())) {
                if(!fillBack.containsRuleDimInfos(MatchRuleDimen.MRD_MMR.getNumber())) {
                    fillBack.putRuleDimInfos(MatchRuleDimen.MRD_MMR.getNumber(), "0");
                }
                if (PropertyFileReader.getRealTimeBooleanItem("OMDMmrMatchCloseSwitch", false)) {
                    fillBack.putRuleDimInfos(MatchRuleDimen.MRD_MMR.getNumber(), "0");
                }
            }

            MatchRouteMgr.getInstance().procMatchRoute(reqBuilder, fillBack.getRoomInfoId());

            RpcResult<SsMatchsvr.RpcMatchRes.Builder> response = MatchService.get().rpcMatch(reqBuilder);
            LOGGER.info("doSearchOtherPlayerJoin log, battle: {} uid: {} num: {} sideInfos: {} fill: {} res: {} ",
                    battleInfo.getBattleID(), uid, num, sideInfos, fillBack, response.getData());
            return response.getData();
        } catch (Exception t) {
            LOGGER.error("doSearchOtherPlayerJoin log: error ", t);
        }

        return null;
    }


    public void cancelSearchPlayerJoin(DsRoomsvr.CancelSearchPlayerJoinRequest req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            return;
        }

        try {
            CurrentExecutorUtil.runJob(() -> {
                MatchFillBackType fillBackType = MatchFillBackType.MFT_OMD_Mid_Join;
                SsMatchsvr.RpcCancelMatchRes.Builder res = battleInfo.cancelFillBack(req.getUid(), fillBackType, MatchCancelReason.MCR_Click);
//                if (res != null && res.getResult() == NKErrorCode.OK.value) {
//                    battleInfo.setMatchSvrId(0);
//                    battleInfo.ntfSearchPlayerJoinResult(null, true, 2);
//                }

                LOGGER.info("cancelSearchPlayerJoin log, battle: {} res: {}", req.getBattleId(), res);
                return null;
            }, "cancelSearchPlayerJoin(", false);
        } catch (Exception t) {
            LOGGER.error("cancelSearchPlayerJoin( log: error ", t);
        }

    }


    public SsBattlesvr.RpcTeamsJoinBattleRes.Builder teamsJoinBattle(SsBattlesvr.RpcTeamsJoinBattleReq.Builder req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        SsBattlesvr.RpcTeamsJoinBattleRes.Builder res = SsBattlesvr.RpcTeamsJoinBattleRes.newBuilder();
        if (battleInfo == null) {
            res.setResult(NKErrorCode.BattleNotExist.getValue());
            return res;
        }

        LOGGER.info("teamsJoinBattle log, roomId: {} start", req.getBattleId());
        for (TeamData teamData : req.getTeamDataList()) {
            NKPair<Integer, String> teamResult = teamCheckAndJoinBattle(battleInfo, teamData, req.getFillBackType(), req.getExtraInfo());

            if (teamResult.getKey() != NKErrorCode.OK.value) {
                LOGGER.info("teamsnnnJoinBattle log, req: {} uid: {} res: {} {} fail.",
                        req, teamData.getMemberInfosMap().keySet(), teamResult.getKey(), teamResult.getValue());
                try {
                    CurrentExecutorUtil.runJob(() -> {
                        matchBattleMgr.teamJoinBattleFail(teamData, battleInfo);
                        return null;
                    }, "teamJoinBattleFail", false);
                } catch (Exception e){
                    LOGGER.error("teamsnnnJoinBattle log, req: {} uid: {} res: {} {} ntf fail.",
                            req, teamData.getMemberInfosMap().keySet(), teamResult.getKey(), teamResult.getValue());
                }
            }

            LOGGER.info("teamsJoinBattle log, req: {} uid: {} res: {} {} ",
                    req, teamData.getMemberInfosMap().keySet(), teamResult.getKey(), teamResult.getValue());
            res.addTeamResults(SsBattlesvr.TeamsJoinBattleResUnit.newBuilder()
                    .setResult(teamResult.getKey())
                    .setErrMsg(teamResult.getValue()));
        }

        LOGGER.info("teamsJoinBattle log, req: {} end", req.getBattleId());
        return res;
    }

    public NKErrorCode matchFillBackResult(SsBattlesvr.RpcMatchFillBackResultReq.Builder req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist;
        }

        MatchFillBackType fillBackType = req.getSuccData().getMatchFillBackData().getFillBackType();
        switch (fillBackType){
            case MFT_OMD_Mid_Join:
                battleMidJoinFillBack(battleInfo, req);
                break;
            case MFT_UgcMatch_Mid_Join: {
                return BattleMidJoinHelper.onMidJoinPullCallback(req.getBattleId(), req.getSuccData());
            }
            default:
                LOGGER.warn("matchFillBackResult invalid type: {},{},{}",
                        req.getBattleId(),fillBackType,  req.getResult());

        }

        LOGGER.info("searchPlayerResult log, req: {} ", req.getBattleId());
        return NKErrorCode.OK;
    }

    public NKErrorCode MatchFillBackCancelResult(SsBattlesvr.RpcMatchFillBackCancelResultReq.Builder req) {
        int fillBackType = req.getMatchFillBackData().getFillBackTypeInt();
        switch (fillBackType) {
            case MatchFillBackType.MFT_OMD_Mid_Join_VALUE:
                OMDMatchFillBackCancelResult(req);
                break;
            default:
                LOGGER.info("MatchFillBackCancelResult invalid type: {},{},{}",
                        req.getBattleId(), fillBackType, req.getResult());

        }

        LOGGER.info("MatchFillBackCancelResult log, req: {} ", req.getBattleId());
        return NKErrorCode.OK;
    }

    public NKErrorCode OMDMatchFillBackCancelResult(SsBattlesvr.RpcMatchFillBackCancelResultReq.Builder req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist;
        }

        if (req.getResult() == NKErrorCode.OK.value) {
            battleInfo.setMatchSvrId(0);
            battleInfo.ntfSearchPlayerJoinResult(null, true, 2);
        }

        LOGGER.info("OMDMatchFillBackCancelResult log, req: {} {}", req.getBattleId(), req.getResult());
        return NKErrorCode.OK;
    }

    public NKErrorCode partnerCoMatchPropose(SsBattlesvr.RpcBattlePartnerCoMatchProposeReq.Builder req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist;
        }
        return battleInfo.coMatchPropose(req.getProposalUid());
    }

    public NKErrorCode partnerCoMatchConfirm(SsBattlesvr.RpcBattlePartnerCoMatchConfirmReq.Builder req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist;
        }
        return battleInfo.coMatchConfirm(req.getUid(), req.getAgreeFlag());
    }

    public void partnerCoMatchSync(SsBattlesvr.RpcBattlePartnerCoMatchResultSyncReq.Builder req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            LOGGER.info("battle already not exit, battleId:{}", req.getBattleId());
            return;
        }
        battleInfo.coMatchTeamSync(req.getRoomId(), req.getPlayerListList());
    }

    public void eliminationBattle(DsRoomsvr.EliminationBattleRequest req) {
        BattleInfo battleInfo = getBattleInfoBySessionId(req.getBattleId(), req.getDsSessionId());
        if (battleInfo == null) {
            return;
        }
        battleInfo.eliminationBattle(req);
    }

    public NKErrorCode setBattlePlayerClientInfo(SsBattlesvr.RpcSetBattlePlayerClientInfoReq.Builder req) {
        BattleInfo battleInfo = getBattleInfoByUid(req.getBattleId(), req.getBattlePlayerClientInfo().getUid());
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist;
        }
        return battleInfo.setBattlePlayerClientInfo(req.getBattlePlayerClientInfo());
    }

    public void battleMidJoinFillBack(BattleInfo battleInfo, SsBattlesvr.RpcMatchFillBackResultReq.Builder req) {
        LOGGER.info("battleMidJoinFillBack log, battleId: {} start", req.getBattleId());
        battleInfo.setMatchSvrId(0);
        if (req.getResult() == NKErrorCode.OK.value) {
            MatchSuccData matchSuccData = req.getSuccData();
            for (MatchTeamData matchTeamData : matchSuccData.getTeamDatasMap().values()) {
                TeamData teamData = matchBattleMgr.matchTeamDataToTeamData(matchTeamData);
                NKPair<Integer, String> teamResult = teamCheckAndJoinBattle(battleInfo, teamData, MatchFillBackType.MFT_OMD_Mid_Join, null);
                if (teamResult.getKey() != NKErrorCode.OK.value) {
                    LOGGER.info("battleMidJoinFillBack log, req: {} uid: {} res: {} {} fail.",
                            req.getBattleId(), matchTeamData.getMemberInfosMap().keySet(), teamResult.getKey(), teamResult.getValue());
                    try {
                        CurrentExecutorUtil.runJob(() -> {
                            matchBattleMgr.teamJoinBattleFail(teamData, battleInfo);
                            return null;
                        }, "teamJoinBattleFail", false);
                    } catch (Exception e){
                        LOGGER.error("battleMidJoinFillBack log, req: {} uid: {} res: {} {} ntf fail.",
                                req.getBattleId(), matchTeamData.getMemberInfosMap().keySet(), teamResult.getKey(), teamResult.getValue());
                    }
                }

                LOGGER.info("battleMidJoinFillBack log, req: {} uid: {} res: {} {} ",
                        req.getBattleId(), matchTeamData.getMemberInfosMap().keySet(), teamResult.getKey(), teamResult.getValue());

            }

            Monitor.getInstance().add.succ(MonitorId.attr_battlesvr_fillback_result, 1,
                    new String[]{String.valueOf(battleInfo.getMatchRuleInfo().getMatchTypeId())});
        } else {
            Monitor.getInstance().add.fail(MonitorId.attr_battlesvr_fillback_result, 1,
                    new String[]{String.valueOf(battleInfo.getMatchRuleInfo().getMatchTypeId())});
        }

        // 通知结束
        battleInfo.ntfSearchPlayerJoinResult(null, true, 2);
        LOGGER.info("battleMidJoinFillBack log, req: {} end", req.getBattleId());
    }

    private void tryDoPlayerExitDsInnerCall(PlayerExitDsRequest.Builder reqBuilder) {
        try {
            RpcResult<PlayerExitDsReply.Builder> resBuilder = SdBattleService.get()
                    .irpcPlayerExitDs(reqBuilder.build());

            if (!resBuilder.isOK()) {
                LOGGER.error("tryDoPlayerExitDs !resBuilder.isOK ret {}", resBuilder.getRet());
            }
        } catch (Exception ex) {
            LOGGER.error("tryDoPlayerExitDs fail", ex);
        }
    }

    public void tryDoPlayerExitDs(BattleInfo battleInfo, long uid, int quitCode) {
        LOGGER.info("tryDoPlayerExitDs, battleId:{} uid:{} quitCode:{}", battleInfo.getBattleID(), uid, quitCode);
        try {
            PlayerExitDsRequest.Builder reqBuilder =
                    PlayerExitDsRequest.newBuilder()
                            .setGameSessionId(String.valueOf(battleInfo.getDsSessionId()))
                            .setDsId(battleInfo.getBattleID())
                            .setUuid(uid)
                            .setQuitCode(quitCode)
                            .setDsaInstId(battleInfo.getDsaInstanceID());
            CurrentExecutorUtil.runJob(() -> {
                tryDoPlayerExitDsInnerCall(reqBuilder);
                return null;
            }, "tryDoPlayerExitDs", false);
        } catch (Exception e) {
            LOGGER.error("e:", e);
        }
    }

    public NKErrorCode syncPlayerInfo(SsBattlesvr.RpcBattleSyncPlayerInfoReq.Builder req) {
        // mark-glueli 废弃
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (null == battleInfo) {
            LOGGER.error("syncPlayerInfo failed null == battle {}", req.getBattleId());
            return NKErrorCode.BattleNotExist;
        }
        BattlePlayerInfo battlePlayer = battleInfo.getBattlePlayerInfo(req.getPlayerUid());
        if (battlePlayer == null) {
            LOGGER.error("syncPlayerInfo getBattlePlayerInfo failed null == battle {} uid {}", req.getBattleId(), req.getPlayerUid());
            return NKErrorCode.BattlePlayerNotExist;
        }
        return battleInfo.syncPlayerInfo(req);
    }

    public void playerReputationScoreReportCheck(SsBattlesvr.RpcPlayerReputationScoreReportCheckReq.Builder req) {

        Monitor.getInstance().add.total(MonitorId.attr_reputation_score_battle_report, 1, new String[]{
                ReputationScoreUtil.REPUTATION_SCORE_OPERATE_SVR_BATTLESVR
        });

        int checkResult = 0;
        BattleInfo battleInfo = BattleMgr.getInstance().getBattleInfoByUid(req.getBattleId(), req.getReporterUid());

        do {
            if (battleInfo == null) {
                break;
            }

            // 判断当前时间是否超时对局结束时间一定的阈值
            int reportEndTime =
                    ReputationSysCommonConfig.getInstance().getConfig("ReputationScoreReportEndTime", 10);
            long reportEndTimeMs = ONE_MINUTE_MS * reportEndTime;
            if (Framework.currentTimeMillis() > battleInfo.getEndtime() + reportEndTimeMs) {
                checkResult = 1;
                break;
            }

            // 判断对局中是否包含举报者和被举报者
            if (!battleInfo.getBattlePlayerMap().containsKey(req.getReporterUid()) ||
                    !battleInfo.getBattlePlayerMap().containsKey(req.getBeReporterUid())) {
                checkResult = 2;
                break;
            }
        } while (false);

        SsGamesvr.RpcPlayerReputationScoreBeReportedReq.Builder reqBuilder =
                SsGamesvr.RpcPlayerReputationScoreBeReportedReq.newBuilder()
                        .setUid(req.getBeReporterUid())
                        .setBattleId(req.getBattleId())
                        .setReporterUid(req.getReporterUid())
                        .setReportBehaviorId(req.getReportBehaviorId())
                        .setCheckResult(checkResult)
                        .setNickName(req.getBeReporterNickName());
        if (req.hasModeId() && req.getModeId() != 0) {
            reqBuilder.setModeId(req.getModeId());
        } else if (battleInfo != null) {
            reqBuilder.setModeId(battleInfo.getMatchRuleInfo().getMatchTypeId());
        }

        try {
            GameService service = GameService.get();
            service.rpcPlayerReputationScoreBeReported(reqBuilder);
        } catch (Exception ex) {
            LOGGER.error("rpcPlayerReputationScoreBeReported failed, reporterUid:{}, beReporterUid:{}, ",
                    req.getReporterUid(), req.getBeReporterUid(), ex);
        }
    }

    /**
     * 批量上报玩家信誉分行为数据
     * @param req
     * @param uidList
     * @return
     */
    public int batchPlayerReputationScoreBehaviorReport(DsRoomsvr.BatchReputationScoreBehaviorReportRequest req,
                                                        List<Long> uidList) {

        // 检查对局是否存在, 不存在直接返回
        BattleInfo battleInfo = BattleMgr.getInstance().getBattleInfoBySessionId(req.getBattleId(), req.getDsSessionId());
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist.getValue();
        }

        long currentTimeMs = Framework.currentTimeMillis();

        // 遍历玩家信誉分行为上报信息列表
        for (PlayerReputationScoreBehaviorReportInfo info : req.getInfosList()) {
            // 过滤掉不合法的行为数据
            if (info.getUid() == 0L || info.getModeId() == 0 || info.getBehaviorId() == 0 || info.getScoreId() == 0) {
                continue;
            }
            // 针对机器人账号不做处理
            if (RobotUtil.isRobot(info.getUid())) {
                continue;
            }
            // 从对局信息中获取该玩家的对局玩家信息, 若不存在则跳过
            BattlePlayerInfo battlePlayer = battleInfo.getBattlePlayerInfo(info.getUid());
            if (battlePlayer == null) {
                continue;
            }

            // 记录该玩家在本局的所有违规行为id moba在使用的临时数据
            for (PlayerReputationScoreBehaviorInfo playerReputationScoreBehaviorInfo : info.getInfosList()) {
                battlePlayer.addReputationBehaviorId(playerReputationScoreBehaviorInfo.getBehaviorId());
                LOGGER.info("battleId({}) player({}) add reputation behaviorId({})",
                        req.getBattleId(), info.getUid(), playerReputationScoreBehaviorInfo.getBehaviorId());
            }

            // 获取该玩家所在队伍的其他队友uid列表(不包含机器人账号)
            List<Long> teamMemberUidList = new ArrayList<>();
            Set<BattlePlayerInfo> teamMemberSet = battleInfo.getBattleTeamPlayerMap().get(battlePlayer.getRoomId());
            if (teamMemberSet != null) {
                for (BattlePlayerInfo playerInfo : teamMemberSet) {
                    if (playerInfo.getUid() != info.getUid() && !RobotUtil.isRobot(playerInfo.getUid())) {
                        teamMemberUidList.add(playerInfo.getUid());
                    }
                }
            }

            try {
                // 拼接上报玩家信誉分行为数据interaction结构体数据
                PlayerInteraction.PiiReportReputationScoreBehaviorParams.Builder params =
                        PlayerInteraction.PiiReportReputationScoreBehaviorParams.newBuilder()
                                .setInfo(info)
                                .setReportTime(currentTimeMs);
                if (params.getInfoBuilder().getBattleId().isEmpty()) {
                    params.getInfoBuilder().setBattleId(String.valueOf(req.getBattleId()));
                }
                if (!teamMemberUidList.isEmpty()) {
                    params.addAllTeamMemberUidList(teamMemberUidList);
                }
                PlayerInteraction.PlayerInteractionData.Builder builder =
                        PlayerInteraction.PlayerInteractionData.newBuilder()
                                .setInstruction(PlayerInteraction.PlayerInteractionInstruction.
                                        PII_REPORT_REPUTATION_SCORE_BEHAVIOR)
                                .setPiiReportReputationScoreBehaviroParams(params);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("report player reputation score behavior, uid:{}, param:{}",
                            info.getUid(), params);
                }

                // 发起上报玩家信誉分行为数据, 先存储数据到db, 在线则转发到gamesvr处理
                DoReportReputationScoreBehaviorInteraction.sendDoReportReputationScoreBehavior(info.getUid(), builder);
                Monitor.getInstance().add.total(MonitorId.attr_reputation_score_behavior_report, 1, new String[]{
                        ReputationScoreUtil.REPUTATION_SCORE_OPERATE_INTERACTION
                });

                uidList.add(info.getUid());
            } catch (Exception ex) {
                LOGGER.error("sendDoReportReputationScoreBehavior catch exception, battleId:{}, uid:{}, ",
                        req.getBattleId(), info.getUid(), ex);
            }
        }

        Monitor.getInstance().add.total(MonitorId.attr_reputation_score_behavior_report, 1, new String[]{
                ReputationScoreUtil.REPUTATION_SCORE_OPERATE_SVR_BATTLESVR
        });

        return NKErrorCode.OK.getValue();
    }

    public void mapMultiSceneSwitchReport(DsRoomsvr.MapMultiSceneSwitchReportRequest req) {
        // 检查对局是否存在, 不存在直接返回
        BattleInfo battleInfo = BattleMgr.getInstance().getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            return;
        }

        // 开始打印信息
        LOGGER.info("battleId:{} ugcId:{} oldLevelId:{} newLevelId:{} uids:{}", req.getBattleId(), req.getUgcId(),
                req.getOldLevelId(), req.getNewLevelId(), req.getUidsList());
    }

    public int batchPlayerNoticeBroadcast(DsRoomsvr.BatchBroadcastRequest req,
                                                        List<Long> succUids) {

        // 检查对局是否存在, 不存在直接返回
        BattleInfo battleInfo = BattleMgr.getInstance().getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist.getValue();
        }
        // 校验template_id 的有效性
        ResNotice.BroadcastNoticeConfig templateConfig = BroadcastNoticeConfig.getInstance().get(req.getTemplateId());
        if (templateConfig == null) {
            LOGGER.error("templateConfig {} not found", req.getTemplateId());
            return NKErrorCode.InvalidParams.getValue();
        }

        // 校验type
        BroadcastType type = BroadcastType.forNumber(req.getType());
        if (type == null) {
            LOGGER.error("type {} invalid", req.getType());
            return NKErrorCode.InvalidParams.getValue();
        }

        // 遍历玩家列表
        for (long uid : req.getUidsList()) {
            // 过滤掉不合法的行为数据
            if (uid == 0L) {
                continue;
            }
            if (succUids.contains(uid)) {
                continue;
            }
            try {
                PlayerInteraction.PiiSendBroadcastNoticeParams.Builder params =
                        PlayerInteraction.PiiSendBroadcastNoticeParams.newBuilder()
                                .setTemplateId(req.getTemplateId())
                                .addAllParams(req.getParamsList())
                                .setType(req.getType())
                                .setCanReplace(req.getCanReplace())
                                .setCanQueue(req.getCanQueue());

                PlayerInteraction.PlayerInteractionData.Builder builder =
                        PlayerInteraction.PlayerInteractionData.newBuilder()
                                .setInstruction(PlayerInteraction.PlayerInteractionInstruction.
                                        PII_SEND_BROADCAST_NOTICE)
                                .setSendBroadcastNoticeParams(params);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("batchPlayerNoticeBroadcast, uid:{}, param:{}", uid, params);
                }

                //  在线则转发到gamesvr处理
                SendBroadcastNoticeInteraction.sendBroadcastNotice(uid, builder);

                succUids.add(uid);
            } catch (Exception ex) {
                LOGGER.error("batchPlayerNoticeBroadcast catch exception, battleId:{}, uid:{} ",
                        req.getBattleId(), uid, ex);
            }
        }


        return NKErrorCode.OK.getValue();
    }

    public NKErrorCode battleRecruitPublish(SsBattlesvr.RpcBattleRecruitPublishReq.Builder req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (null == battleInfo) {
            LOGGER.error("battleRecruitPublish failed null == battle {}", req.getBattleId());
            return NKErrorCode.BattleNotExist;
        }

        if (!TycConfs.openOMDRecruit()) {
            LOGGER.error("battleRecruitPublish failed {}, not open", req.getBattleId());
            return NKErrorCode.UnknownError;
        }

        LOGGER.info("battleRecruitPublish, battleId:{}, Uid:{} time: {} {}",
                req.getBattleId(), req.getOperateUid(), req.getAvailableTime(), req.getTopicId());
        battleInfo.publishRecruit(req.getOperateUid(),req.getQualifyingLowLimit(), req.getTopicId());

        return NKErrorCode.OK;
    }

    public NKErrorCode battleRecruitUnPublish(DsRoomsvr.BattleRecruitUnPublishRequest req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (null == battleInfo) {
            LOGGER.error("battleRecruitUnPublish failed null == battle {}", req.getBattleId());
            return NKErrorCode.BattleNotExist;
        }

        battleInfo.cancelRecruit();
        return NKErrorCode.OK;
    }

    public SsBattlesvr.RpcGetBattleMatchInfoRes.Builder getBattleMatchInfo(SsBattlesvr.RpcGetBattleMatchInfoReq.Builder req) {
        SsBattlesvr.RpcGetBattleMatchInfoRes.Builder res = SsBattlesvr.RpcGetBattleMatchInfoRes.newBuilder();
        BattleInfo battleInfo = getBattleInfoByUid(req.getBattleId(), req.getUid());
        if (null == battleInfo) {
            LOGGER.error("battleRecruitUnPublish failed null == battle {}", req.getBattleId());
            res.setResult(NKErrorCode.BattleNotExist.getValue());
            return res;
        }

        res.setResult(NKErrorCode.OK.getValue());
        res.setBattleId(battleInfo.getBattleID());
        res.setRuleInfo(battleInfo.getMatchRuleInfo());
        res.setStage(battleInfo.getState().getValue());
        LOGGER.info("getBattleMatchInfo, battleId:{}, match: {} ", req.getBattleId(), res.getRuleInfo().getMatchTypeId());
        return res;
    }

    public SsBattlesvr.RpcGetBattlePublicRoomInfoRes.Builder getBattlePublicRoomInfo(SsBattlesvr.RpcGetBattlePublicRoomInfoReq.Builder req) {
        SsBattlesvr.RpcGetBattlePublicRoomInfoRes.Builder res = SsBattlesvr.RpcGetBattlePublicRoomInfoRes.newBuilder();
        BattleInfo battleInfo = getBattleInfoByUid(req.getBattleId(), req.getUid());
        if (null == battleInfo) {
            LOGGER.error(" getBattlePublicRoomInfo failed null == battle {}", req.getBattleId());
            res.setResult(NKErrorCode.BattleNotExist.getValue());
            return res;
        }

        res.setBattleId(battleInfo.getBattleID());
        UniversalRoom.Builder roomInfo = battleInfo.getBattlePublicRoomInfo();
        if (roomInfo == null) {
            res.setResult(NKErrorCode.InvalidParams.getValue());
        } else {
            res.setResult(NKErrorCode.OK.getValue());
            res.setRoomInfo(roomInfo);
        }

        LOGGER.info("getBattleMatchInfo, battleId:{}, match: {} room: {}", req.getBattleId(),
                battleInfo.getMatchRuleInfoBuilder().getMatchTypeId(), roomInfo);
        return res;
    }

    public int battleRoomMidJoinInfoChange(DsRoomsvr.BattleRoomMidJoinInfoChangeRequest req) {
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (null == battleInfo) {
            LOGGER.error("battleRoomMidJoinInfoChange failed null == battle {}", req.getBattleId());
            return NKErrorCode.BattleNotExist.getValue();
        }

        int res = battleInfo.battleRoomMidJoinInfoChange(req);
        LOGGER.info(" battleRoomMidJoinInfoChange, battleId:{}, info: {} ", req.getBattleId(), req);
        return res;
    }

    public int battleCommonBroadcastInfo(SsBattlesvr.RpcBattleCommonBroadcastInfoReq.Builder req) {
        BattleInfo battleInfo = getBattleInfoByUid(req.getBattleId(), req.getSender());
        if (null == battleInfo) {
            LOGGER.error("battleCommonBroadcastInfo failed null == battle {}", req.getBattleId());
            return NKErrorCode.BattleNotExist.getValue();
        }
        if (battleInfo.getBattlePlayerInfo(req.getSender()) == null) {
            return NKErrorCode.BattleMemberNotExist.getValue();
        }
        // 处理数据广播
        return battleInfo.battleCommonBroadcastInfo(req);
    }

    public int battleMemberListGet(SsBattlesvr.RpcBattleMemberListGetReq.Builder req,
            SsBattlesvr.RpcBattleMemberListGetRes.Builder res) {
        BattleInfo battleInfo = getBattleInfoByUid(req.getBattleId(), req.getUid());
        if (null == battleInfo) {
            LOGGER.error("battleCommonBroadcastInfo failed null == battle {}", req.getBattleId());
            return NKErrorCode.BattleNotExist.getValue();
        }
        for (BattlePlayerInfo battlePlayerInfo : battleInfo.getBattlePlayerMap().values()) {
            MemberBaseInfo.Builder playerBaseInfo = battlePlayerInfo.getMemberBaseInfo(false);
            MemberBaseInfo.Builder memberBaseInfo = MemberBaseInfo.newBuilder()
                    .setUid(playerBaseInfo.getUid())
                    .setGender(playerBaseInfo.getGender())
                    .setName(playerBaseInfo.getName())
                    .setFace(playerBaseInfo.getFace())
                    .setSide(playerBaseInfo.getSide())
                    .setFashionValue(playerBaseInfo.getFashionValue())
                    .addAllDressUpItems(playerBaseInfo.getDressUpItemsList())
                    .setOrnamentCustomData(playerBaseInfo.getOrnamentCustomData())
                    .setIsMidJoin(playerBaseInfo.getIsMidJoin())
                    .setAddInBattleProcess(playerBaseInfo.getAddInBattleProcess());
            if (battlePlayerInfo.isRobot()) {
                memberBaseInfo.setAddInBattleProcess(100);
            }
            res.addMemberList(memberBaseInfo);
        }
        res.addAllUgcCamps(battleInfo.getUgcBattleCampsReal());
        return NKErrorCode.OK.getValue();
    }

    /**
     * 信誉分违规行为消息发送
     * @param battleId
     * @param uid
     * @return
     */
    public int reputationScoreBehaviorMessageSend(long battleId, long gameSessionId, long uid) {

        // 检查信誉分功能开关和信誉分违规行为消息发送开关是否开启
        boolean serverSwitch =
                PropertyFileReader.getRealTimeBooleanItem("player_reputation_score_function_switch", false);
        boolean reputationScoreBehaviorMessageSendSwitch =
                PropertyFileReader.getRealTimeBooleanItem("reputation_score_behavior_message_send_switch", true);
        if (!(serverSwitch && reputationScoreBehaviorMessageSendSwitch)) {
            return NKErrorCode.OK.getValue();
        }

        // 检查对局是否存在, 不存在直接返回
        BattleInfo battleInfo = BattleMgr.getInstance().getBattleInfoBySessionId(battleId, gameSessionId);
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist.getValue();
        }

        // 从对局信息中获取该玩家的对局玩家信息
        BattlePlayerInfo battlePlayer = battleInfo.getBattlePlayerInfo(uid);
        if (battlePlayer == null) {
            return NKErrorCode.BattlePlayerNotExist.getValue();
        }

        // 开启单独的协程job来执行信誉分违规行为消息发送逻辑
        try {
            CurrentExecutorUtil.runJob(() -> {
                // 给违规者发送信誉分违规行为消息
                battleInfo.sendPlayerReputationScoreIllegalSystemMsg(battlePlayer, battleId);
                // 给违规者队友发送信誉分违规行为队友消息
                battleInfo.sendPlayerReputationScoreTeammateIllegalSystemMsg(battlePlayer, battleId);

                // LOGGER.info("reputationScoreBehaviorMessageSend end, battleId:{}, uid:{}", battleId, uid);
                return null;
            }, "reputationScoreBehaviorMessageSend", false);
        } catch (Exception ex) {
            LOGGER.error("reputationScoreBehaviorMessageSend catch exception, battleId:{}, uid:{}, ",
                    battleId, uid, ex);
        }

        return NKErrorCode.OK.getValue();
    }

    /**
     * 通知收集物刷新池配置
     * @param battleId 战场id
     * @return NKErrorCode
     */
    public NKErrorCode ntfSceneCollectionPoolInfo(long battleId, long gameSessionId) {
        BattleInfo battleInfo = BattleMgr.getInstance().getBattleInfoBySessionId(battleId, gameSessionId);
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist;
        }

        CollectionSceneType sceneType;
        long sceneTypeId;
        if (battleInfo.getUgcId() > 0) {
            sceneType = CollectionSceneType.CST_UgcBattle;
            sceneTypeId = battleInfo.getUgcId();
        } else {
            sceneType = CollectionSceneType.CST_CommonBattle;
            sceneTypeId = battleInfo.getMatchRuleInfo().getMatchTypeId();
        }
        List<ActivityPrayerCardCollectionConfig> collectionConfigList = ActivityPrayerCardCollectionConfigData.getInstance()
                .getCollectionConfigByScene(sceneType, sceneTypeId);

        if (collectionConfigList == null || collectionConfigList.isEmpty()) {
            LOGGER.debug("ntfSceneCollectionPoolInfo get scene type {} id {} not exist", sceneType, sceneTypeId);
            return NKErrorCode.ResNotFound;
        }

        SdCommonOuterClass.NtfSceneCollectionPoolInfoRequest.Builder reqBuilder =
                SdCommonOuterClass.NtfSceneCollectionPoolInfoRequest.newBuilder()
                        .setDsId(battleId)
                        .setGameSessionId(String.valueOf(getSessionIdByBattleId(battleId)))
                        .setDsaInstId(battleInfo.getDsaInstanceID());
        for (ActivityPrayerCardCollectionConfig config : collectionConfigList) {
            reqBuilder.addSceneCollectionPoolInfoList(config.getCollectionPoolInfo());
        }

        try {
            SdCommonService.get()
                    .irpcNtfSceneCollectionPoolInfo(reqBuilder.build());
        } catch (Exception ex) {
            LOGGER.error("send irpcNtfSceneCollectionPoolInfo error:", ex);
            return NKErrorCode.IRpcSendFailed;
        }

        return NKErrorCode.OK;
    }
    public NKErrorCode reportDSInfo(long battleId, long gameSessionId, ReportDSInfo dsInfo) {
        BattleInfo battleInfo = BattleMgr.getInstance().getBattleInfoBySessionId(battleId, gameSessionId);
        if (battleInfo == null) {
            return NKErrorCode.BattleNotExist;
        }
        battleInfo.setReportDSInfo(dsInfo);
        return NKErrorCode.OK;
    }

    /**
     * ugc对局ai接管上报
     * @param req
     * @return
     */
    public int ugcAiTakeOverPlayerReport(DsRoomsvr.UgcAiTakeOverPlayerReportRequest req) {

        Monitor.getInstance().add.total(MonitorId.attr_ugc_ai_take_over_report, 1);

        // 判断对局是否存在
        BattleInfo battleInfo = BattleMgr.getInstance().getBattleInfo(req.getBattleId());
        if (battleInfo == null) {
            Monitor.getInstance().add.fail(MonitorId.attr_ugc_ai_take_over_report, 1);
            return NKErrorCode.BattleNotExist.getValue();
        }

        // 遍历上报请求中的玩家信息列表
        for (UgcAiTakeOverPlayerInfo playerInfo : req.getInfosList()) {
            if (battleInfo.getBattlePlayerMap().containsKey(playerInfo.getUid())) {
                // 上报ugc地图对局用户ai接管流水
                TlogFlowMgr.sendUgcMapAiControlFlow(playerInfo.getUid(), String.valueOf(req.getBattleId()),
                        String.valueOf(battleInfo.getUgcId()), battleInfo.getUgcType(), String.valueOf(battleInfo.getMatchID()),
                        battleInfo.getMatchRuleInfo().getMatchTypeId());
            }
        }

        Monitor.getInstance().add.succ(MonitorId.attr_ugc_ai_take_over_report, 1);

        return NKErrorCode.OK.getValue();
    }

    /**
     * 对局ai接管上报
     * @param req
     * @return
     */
    public int battleAiTakeOverPlayerReport(DsRoomsvr.BattleAiTakeOverPlayerReportRequest req) {

        Monitor.getInstance().add.total(MonitorId.attr_battle_ai_take_over_report, 1);

        // 判断对局是否存在
        BattleInfo battleInfo = BattleMgr.getInstance().getBattleInfoBySessionId(req.getBattleId(), req.getDsSessionId());
        if (battleInfo == null) {
            Monitor.getInstance().add.fail(MonitorId.attr_battle_ai_take_over_report, 1);
            return NKErrorCode.BattleNotExist.getValue();
        }

        // 遍历上报请求中的玩家信息列表
        for (BattleAiTakeOverPlayerInfo playerInfo : req.getInfosList()) {
            if (battleInfo.getBattlePlayerMap().containsKey(playerInfo.getUid())) {
                if (battleInfo.getUgcId() > 0L) {
                    // 上报ugc地图对局用户ai接管流水
                    TlogFlowMgr.sendUgcMapAiControlFlow(playerInfo.getUid(), String.valueOf(req.getBattleId()),
                            String.valueOf(battleInfo.getUgcId()), battleInfo.getUgcType(), String.valueOf(battleInfo.getMatchID()),
                            battleInfo.getMatchRuleInfo().getMatchTypeId());
                }
            }
        }

        Monitor.getInstance().add.succ(MonitorId.attr_battle_ai_take_over_report, 1);

        return NKErrorCode.OK.getValue();
    }

    public MobaAiLabBattleInfoReply.Builder getMobaAiLabBattleInfo(DsRoomsvr.MobaAiLabBattleInfoRequest req){
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (null == battleInfo) {
            LOGGER.error("getMobaAiLabBattleInfo, battleInfo is empty");
            Monitor.getInstance().add.fail(MonitorId.attr_battle_get_moba_ailab_battle_info, 1);
            return MobaAiLabBattleInfoReply.newBuilder().setBattleId(req.getBattleId());
        }
        MobaAiLabBattleInfoReply.Builder replyBuilder = getBattleInfoMetaAiComp().checkAndGetAiBotMatchInfoFromAiLab(battleInfo);
        if (replyBuilder == null) {
            LOGGER.error("getMobaAiLabBattleInfo, replyBuilder is empty");
            return MobaAiLabBattleInfoReply.newBuilder().setBattleId(req.getBattleId());
        }

        ArenaBattleDataHelper.fillArenaReplayInfo(battleInfo, replyBuilder);

        LOGGER.info("getMobaAiLabBattleInfo, replyBuilder return({})", replyBuilder.setBattleId(req.getBattleId()));

        return replyBuilder.setBattleId(req.getBattleId());
    }

    public NKErrorCode mobaFootballGoalBroadcast(DsRoomsvr.MobaFootballGoalBroadcastRequest req){
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (null == battleInfo) {
            LOGGER.error("mobaFootballGoalBroadcast, battleInfo is empty");
            return NKErrorCode.BattleNotExist;
        }
        try {
            GameService gameService = GameService.get();
            if (gameService == null) {
                return NKErrorCode.GameServiceError;
            }
            // 向battle的所有玩家广播
            for (var playerInfo : battleInfo.getBattlePlayerMap().values()) {
                if (playerInfo.isRobot()) {
                    continue;
                }
                gameService.rpcMobaFootballGoalBroadcastNtf(SsGamesvr.RpcMobaFootballGoalBroadcastNtfReq.newBuilder()
                        .setUid(playerInfo.getUid()).setBattleId(req.getBattleId()).setSendUid(req.getSendUid())
                        .setChunk(req.getChunk()).setChunkIndex(req.getChunkIndex()).setTotalChunks(req.getTotalChunks())
                        .setCompressedSize(req.getCompressedSize()).setUncompressedSize(req.getUncompressedSize())
                        .addAllChunkInt(req.getChunkIntList()));
            }
        } catch (Exception e) {
            LOGGER.error("mobaFootballGoalBroadcast error", e);
        }
        return NKErrorCode.OK;
    }

    public void addMemberLeaveList(long battleId, MemberBaseInfo.Builder memberBaseInfo, Map<Long, MemberPlayTime> memberLeaveMap) {
        if (memberBaseInfo.getIsMidJoin()) {
            LOGGER.debug("battleId:{} uid:{} player is mid join", battleId, memberBaseInfo.getUid());
            return;
        }
        if (memberLeaveMap.size() >= getBattleMemberLeaveListRecordMax()) {
            LOGGER.info("battleId:{} uid:{} playTime record max", battleId, memberBaseInfo.getUid());
            return;
        }
        long now = Framework.currentTimeMillis();
        MemberPlayTime.Builder memberPlayTime = MemberPlayTime.newBuilder();
        memberPlayTime.setUid(memberBaseInfo.getUid());
        memberPlayTime.setJoinTime(memberBaseInfo.getJoinTime());
        memberPlayTime.setLeaveTime(now);
        // 这里再检查一下
        if (memberLeaveMap.size() >= getBattleMemberLeaveListRecordMax()) {
            LOGGER.info("battleId:{} uid:{} playTime record max", battleId, memberBaseInfo.getUid());
            return;
        }
        // 更新结果
        memberLeaveMap.put(memberBaseInfo.getUid(), memberPlayTime.build());
    }

    /**
     * arena Ai选卡
     * @param req 来自DS的选卡请求
     * @return 选卡结果
     */
    public DsRoomsvr.ArenaAiSelectCardReply.Builder arenaAiSelectCard(DsRoomsvr.ArenaAiSelectCardRequest req){
        LOGGER.info("req:{}", TextFormat.shortDebugString(req));

        DsRoomsvr.ArenaAiSelectCardReply.Builder replyBuilder = DsRoomsvr.ArenaAiSelectCardReply.newBuilder();

        // 检查开关
        if (!PropertyFileReader.getRealTimeBooleanItem("arena_enable_ai_select_card", false)) {
            LOGGER.error("arena_enable_ai_select_card is false");
            return replyBuilder.setResult(NKErrorCode.InvalidReq.getValue());
        }
        // 检查对局是否存在
        BattleInfo battleInfo = getBattleInfo(req.getBattleId());
        if (null == battleInfo) {
            LOGGER.error("not found battle id({})", req.getBattleId());
            Monitor.getInstance().add.fail(MonitorId.attr_battle_arena_ai_select_card, 1);
            return replyBuilder.setResult(NKErrorCode.InvalidParams.getValue());
        }
        // 随机卡牌
        for (ArenaAiSelectCardInfo info : req.getInfoList()) {
            BattlePlayerInfo playerInfo = battleInfo.getBattlePlayerMap().get(info.getUid());
            if (playerInfo == null) {
                LOGGER.error("battle({}) not found player({})", req.getBattleId(), info.getUid());
                continue;
            }

            if (!playerInfo.isRobot()) {
                LOGGER.error("battle({}) player({}) is not robot", req.getBattleId(), info.getUid());
                continue;
            }

            ArenaAiSelectCardInfo.Builder rspInfoBuilder = ArenaAiSelectCardInfo.newBuilder(info);
            List<Integer> selectedCards = ArenaCardData.getInstance().randomCardsForAi(info.getHeroId());
            if (selectedCards.isEmpty()) {
                LOGGER.error("random cards for ai is empty, battleId({}) info({})", req.getBattleId(),
                        TextFormat.shortDebugString(info));
            }
            rspInfoBuilder.addAllCardId(selectedCards);
            replyBuilder.addInfo(rspInfoBuilder.build());
        }
        LOGGER.info("reply: {}", TextFormat.shortDebugString(replyBuilder));
        return replyBuilder;
    }

    private static class InstanceHolder {
        private static final BattleMgr INSTANCE = new BattleMgr();
    }
}

class AIServerInfo {
    public String aiServerCnPolarisSid = "";
    public String aiServerCnPolarisNs = "";
    public NKPair<String, Integer> aiServerAddr;
    public int aiServerAddrResuseTimes;
    public long aiServerAddrExpireTimeMs;
    public NKPair<String, Integer> aiServerAddrSafe;

    AIServerInfo() {
        aiServerAddr = new NKPair<>("", 0);
        aiServerAddrSafe = new NKPair<>("", 0);
    }
}
